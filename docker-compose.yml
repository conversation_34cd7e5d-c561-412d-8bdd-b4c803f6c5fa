# Monitor项目 - 生产环境Docker Compose配置
services:
  # Monitor主应用
  monitor:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERSION: 1.0.0
        BUILD_TIME: ${BUILD_TIME:-$(date '+%Y-%m-%d %H:%M:%S')}
        GIT_COMMIT: ${GIT_COMMIT:-unknown}
    container_name: monitor-app
    restart: unless-stopped
    volumes:
      # 配置文件映射（只读）
      - ./configs:/app/configs:ro
      - ./logs:/app/logs:rw
      - ./data:/app/data:rw
    networks:
      - monitor-network

# 网络配置
networks:
  monitor-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16


