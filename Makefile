# Monitor项目Makefile
# 用于调试和开发的常用任务

.PHONY: help dev debug build test clean deps fmt vet lint docker-up docker-down profile coverage docs install-tools air delve setup

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
RESET=\033[0m
RED=\033[31m
GREEN=\033[32m
YELLOW=\033[33m
BLUE=\033[34m
PURPLE=\033[35m
CYAN=\033[36m

# 项目信息
PROJECT_NAME=monitor
VERSION=1.0.0-debug
BUILD_TIME=$(shell date '+%Y-%m-%d %H:%M:%S')
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 目录定义
BUILD_DIR=./tmp
LOG_DIR=./logs
DATA_DIR=./data
PROFILE_DIR=./profiles
CONFIG_DIR=./configs

# Go参数
GO_VERSION=1.21
GO_FLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"
GO_DEBUG_FLAGS=-gcflags="-N -l"

# Docker配置
DOCKER_COMPOSE_FILE=docker-compose.debug.yml
DOCKER_PROJECT_NAME=monitor_debug

## help: 显示帮助信息
help:
	@echo ""
	@echo "$(PURPLE)Monitor项目调试环境管理$(RESET)"
	@echo ""
	@echo "$(CYAN)使用方法:$(RESET)"
	@echo "  make <target>"
	@echo ""
	@echo "$(CYAN)可用目标:$(RESET)"
	@awk 'BEGIN {FS = ":.*##"; printf ""} /^[a-zA-Z_-]+:.*?##/ { printf "  $(YELLOW)%-15s$(RESET) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(CYAN)快速开始:$(RESET)"
	@echo "  $(GREEN)make setup$(RESET)     # 初始化环境"
	@echo "  $(GREEN)make dev$(RESET)       # 开发模式"
	@echo "  $(GREEN)make debug$(RESET)     # 调试模式"
	@echo ""

## setup: 初始化开发环境
setup: deps install-tools create-dirs
	@echo "$(GREEN)✅ 开发环境初始化完成$(RESET)"

## dev: 启动开发环境 (热重载)
dev: docker-up create-dirs
	@echo "$(BLUE)🚀 启动开发环境...$(RESET)"
	@if [ ! -f .air.toml ]; then \
		echo "创建Air配置文件..."; \
		cp .air.toml.example .air.toml 2>/dev/null || \
		echo 'root = "."\ntmp_dir = "tmp"\n[build]\n  cmd = "go build -o ./tmp/monitor ./cmd/monitor"\n  bin = "./tmp/monitor"\n  args_bin = ["--config=configs/base.yaml"]\n  include_ext = ["go", "yaml", "yml"]\n  exclude_dir = ["tmp", "logs", "data", "profiles"]' > .air.toml; \
	fi
	air

## debug: 启动调试模式
debug: docker-up build-debug
	@echo "$(BLUE)🐛 启动调试模式...$(RESET)"
	./$(BUILD_DIR)/monitor-debug --config=$(CONFIG_DIR)/base.yaml

## build: 编译应用
build: create-dirs ## 编译生产版本
	@echo "$(BLUE)🔨 编译应用...$(RESET)"
	go build $(GO_FLAGS) -o $(BUILD_DIR)/$(PROJECT_NAME) ./cmd/monitor

## build-debug: 编译调试版本
build-debug: create-dirs ## 编译调试版本
	@echo "$(BLUE)🔨 编译调试版本...$(RESET)"
	go build $(GO_FLAGS) $(GO_DEBUG_FLAGS) -o $(BUILD_DIR)/$(PROJECT_NAME)-debug ./cmd/monitor

## test: 运行测试
test: ## 运行所有测试
	@echo "$(BLUE)🧪 运行测试...$(RESET)"
	go test -v -short ./...

## test-integration: 运行集成测试
test-integration: docker-up ## 运行集成测试
	@echo "$(BLUE)🧪 运行集成测试...$(RESET)"
	go test -v -tags=integration ./...

## coverage: 生成测试覆盖率报告
coverage: ## 生成测试覆盖率报告
	@echo "$(BLUE)📊 生成覆盖率报告...$(RESET)"
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)覆盖率报告已生成: coverage.html$(RESET)"

## bench: 运行性能测试
bench: ## 运行性能测试
	@echo "$(BLUE)⚡ 运行性能测试...$(RESET)"
	go test -bench=. -benchmem ./...

## profile: 生成性能分析
profile: create-dirs ## 生成性能分析
	@echo "$(BLUE)📈 生成性能分析...$(RESET)"
	go run ./cmd/monitor --config=$(CONFIG_DIR)/base.yaml --profile=true &
	@sleep 10
	@curl -s http://localhost:8081/debug/pprof/profile?seconds=30 > $(PROFILE_DIR)/cpu.prof
	@curl -s http://localhost:8081/debug/pprof/heap > $(PROFILE_DIR)/mem.prof
	@pkill -f monitor || true
	@echo "$(GREEN)性能分析文件已保存到 $(PROFILE_DIR)/$(RESET)"

## profile-view: 查看性能分析
profile-view: ## 查看CPU性能分析
	@if [ -f $(PROFILE_DIR)/cpu.prof ]; then \
		echo "$(BLUE)📊 启动性能分析查看器...$(RESET)"; \
		go tool pprof -http=:8090 $(PROFILE_DIR)/cpu.prof; \
	else \
		echo "$(RED)❌ 性能分析文件不存在，请先运行 make profile$(RESET)"; \
	fi

## deps: 安装Go依赖
deps: ## 下载并整理Go依赖
	@echo "$(BLUE)📦 安装Go依赖...$(RESET)"
	go mod download
	go mod tidy
	go mod verify

## fmt: 格式化代码
fmt: ## 格式化Go代码
	@echo "$(BLUE)🎨 格式化代码...$(RESET)"
	gofmt -w .
	goimports -w . 2>/dev/null || echo "提示: 安装goimports可获得更好的导入整理"

## vet: 静态代码分析
vet: ## 运行Go vet静态分析
	@echo "$(BLUE)🔍 静态代码分析...$(RESET)"
	go vet ./...

## lint: 代码质量检查
lint: ## 运行golangci-lint
	@echo "$(BLUE)🔍 代码质量检查...$(RESET)"
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "$(YELLOW)⚠️  golangci-lint未安装，跳过lint检查$(RESET)"; \
		echo "安装命令: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

## clean: 清理构建文件
clean: ## 清理构建文件和缓存
	@echo "$(BLUE)🧹 清理文件...$(RESET)"
	rm -rf $(BUILD_DIR)/*
	rm -f coverage.out coverage.html
	rm -f *.prof
	go clean -cache
	go clean -testcache
	@echo "$(GREEN)✅ 清理完成$(RESET)"

## clean-all: 深度清理
clean-all: clean docker-down ## 深度清理（包括Docker）
	@echo "$(BLUE)🧹 深度清理...$(RESET)"
	rm -rf $(LOG_DIR)/*
	rm -rf $(DATA_DIR)/*
	rm -rf $(PROFILE_DIR)/*
	docker system prune -f
	@echo "$(GREEN)✅ 深度清理完成$(RESET)"

## docker-up: 启动Docker服务
docker-up: ## 启动Docker依赖服务
	@echo "$(BLUE)🐳 启动Docker服务...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE_FILE) up -d redis-debug rabbitmq-debug
	@echo "等待服务启动..."
	@timeout 30 bash -c 'until docker exec monitor-redis-debug redis-cli ping 2>/dev/null; do sleep 1; done' || echo "Redis启动超时"
	@timeout 60 bash -c 'until docker exec monitor-rabbitmq-debug rabbitmq-diagnostics ping 2>/dev/null; do sleep 2; done' || echo "RabbitMQ启动超时"
	@echo "$(GREEN)✅ Docker服务已启动$(RESET)"

## docker-up-all: 启动所有Docker服务
docker-up-all: ## 启动所有Docker服务（包括监控）
	@echo "$(BLUE)🐳 启动所有Docker服务...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE_FILE) up -d

## docker-down: 停止Docker服务
docker-down: ## 停止Docker服务
	@echo "$(BLUE)🐳 停止Docker服务...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE_FILE) down
	@echo "$(GREEN)✅ Docker服务已停止$(RESET)"

## docker-logs: 查看Docker服务日志
docker-logs: ## 查看Docker服务日志
	docker-compose -f $(DOCKER_COMPOSE_FILE) logs -f

## docker-status: 查看Docker服务状态
docker-status: ## 查看Docker服务状态
	@echo "$(BLUE)🐳 Docker服务状态:$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE_FILE) ps

## logs: 查看应用日志
logs: ## 实时查看应用日志
	@if [ -f $(LOG_DIR)/debug.log ]; then \
		tail -f $(LOG_DIR)/debug.log; \
	else \
		echo "$(YELLOW)⚠️  日志文件不存在$(RESET)"; \
	fi

## install-tools: 安装开发工具
install-tools: ## 安装开发工具
	@echo "$(BLUE)🛠️  安装开发工具...$(RESET)"
	go install github.com/cosmtrek/air@latest
	go install github.com/go-delve/delve/cmd/dlv@latest
	go install golang.org/x/tools/cmd/goimports@latest
	@echo "$(GREEN)✅ 开发工具安装完成$(RESET)"

## air: 使用Air热重载启动
air: docker-up create-dirs ## 使用Air热重载启动
	@echo "$(BLUE)🔥 启动Air热重载...$(RESET)"
	air

## delve: 使用Delve调试器启动
delve: docker-up build-debug ## 使用Delve调试器启动
	@echo "$(BLUE)🐛 启动Delve调试器...$(RESET)"
	dlv exec $(BUILD_DIR)/$(PROJECT_NAME)-debug -- --config=$(CONFIG_DIR)/base.yaml

## create-dirs: 创建必要的目录
create-dirs: ## 创建必要的目录
	@mkdir -p $(BUILD_DIR) $(LOG_DIR) $(DATA_DIR) $(PROFILE_DIR)

## info: 显示项目信息
info: ## 显示项目信息
	@echo ""
	@echo "$(PURPLE)📋 项目信息$(RESET)"
	@echo "  项目名称: $(PROJECT_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Go版本: $(shell go version)"
	@echo ""
	@echo "$(PURPLE)📁 目录结构$(RESET)"
	@echo "  构建目录: $(BUILD_DIR)"
	@echo "  日志目录: $(LOG_DIR)"
	@echo "  数据目录: $(DATA_DIR)"
	@echo "  配置目录: $(CONFIG_DIR)"
	@echo ""
	@echo "$(PURPLE)🌐 服务地址$(RESET)"
	@echo "  应用服务: http://localhost:8080"
	@echo "  性能指标: http://localhost:8081/metrics"
	@echo "  健康检查: http://localhost:8082/health"
	@echo "  Redis: localhost:6379"
	@echo "  RabbitMQ: localhost:5672 (管理: http://localhost:15672)"
	@echo ""

## quick-start: 快速开始
quick-start: setup docker-up ## 一键启动开发环境
	@echo "$(GREEN)🚀 快速启动完成！可以选择以下方式启动:$(RESET)"
	@echo "  $(CYAN)make dev$(RESET)    - 开发模式 (热重载)"
	@echo "  $(CYAN)make debug$(RESET)  - 调试模式"
	@echo "  $(CYAN)make air$(RESET)    - Air热重载"

## restart: 重启应用
restart: ## 重启应用
	@echo "$(BLUE)🔄 重启应用...$(RESET)"
	@pkill -f monitor || echo "应用未运行"
	@sleep 2
	@make dev

## status: 检查服务状态
status: docker-status ## 检查所有服务状态
	@echo ""
	@echo "$(PURPLE)📊 应用进程状态:$(RESET)"
	@pgrep -f monitor && echo "Monitor应用: 运行中" || echo "Monitor应用: 未运行"
	@echo ""

# 验证Go版本
check-go-version:
	@go_version=$$(go version | cut -d' ' -f3 | sed 's/go//'); \
	required_version="$(GO_VERSION)"; \
	if [ "$$(printf '%s\n' "$$required_version" "$$go_version" | sort -V | head -n1)" != "$$required_version" ]; then \
		echo "$(RED)❌ Go版本过低。需要 $(GO_VERSION)+，当前: $$go_version$(RESET)"; \
		exit 1; \
	fi

# 内部目标，检查Docker
check-docker:
	@if ! command -v docker >/dev/null 2>&1; then \
		echo "$(RED)❌ Docker未安装$(RESET)"; \
		exit 1; \
	fi
	@if ! command -v docker-compose >/dev/null 2>&1; then \
		echo "$(RED)❌ Docker Compose未安装$(RESET)"; \
		exit 1; \
	fi

# 添加依赖检查到Docker目标
docker-up docker-down docker-up-all: check-docker

# 添加Go版本检查到构建目标
build build-debug dev debug: check-go-version 