# Git相关
.git
.gitignore
.gitattributes

# 构建产物
tmp/
bin/
*.exe
*.exe~
*.dll
*.so
*.dylib
test-monitor

# 测试文件
*_test.go
tests/
coverage.out
coverage.html
*.prof

# 日志文件
logs/
*.log

# 数据文件
data/
*.db
*.sqlite

# 配置文件（敏感信息）
.env
.env.local
.env.production
docker/prod.env
docker/dev.env

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 依赖和缓存
node_modules/
vendor/

# 文档
*.md
docs/
README*
LICENSE*
CHANGELOG*

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 开发工具配置
.air.toml
.air.toml.example
Makefile
profiles/

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 调试文件
__debug_bin*
*.pprof

# 压缩文件
*.zip
*.tar.gz
*.rar

# 其他
.env*
!.env.example
