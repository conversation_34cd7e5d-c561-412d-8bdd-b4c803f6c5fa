#!/bin/bash

# Monitor项目 - Docker管理脚本

set -euo pipefail

# 配置
readonly PROJECT_NAME="monitor"
readonly COMPOSE_FILE="docker-compose.yml"
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 工具函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# 环境检查
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker命令未找到，请先安装Docker"
        exit 1
    fi

    if ! command -v docker compose &> /dev/null; then
        log_error "Docker Compose命令未找到，请先安装"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi

    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose文件不存在: $COMPOSE_FILE"
        exit 1
    fi
}

# 准备宿主机目录
prepare_host_directories() {
    log_info "准备宿主机目录和权限..."

    # 创建必要的目录
    local dirs=("logs" "data")
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        fi

        # 设置目录权限，确保容器内的monitor用户(UID 1001)可以写入
        log_info "设置目录权限: $dir"
        chmod 755 "$dir"

        # 尝试设置所有者为1001:1001（对应容器内的monitor用户）
        # 如果失败（比如在macOS上），则使用当前用户
        if command -v chown &> /dev/null; then
            if ! chown 1001:1001 "$dir" 2>/dev/null; then
                log_warning "无法设置${dir}所有者为1001:1001，使用当前用户权限"
                # 在macOS等系统上，确保目录对所有用户可写
                chmod 777 "$dir"
            fi
        else
            log_warning "chown命令不可用，设置目录为全局可写"
            chmod 777 "$dir"
        fi
    done

    log_success "宿主机目录准备完成"
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."

    # 准备宿主机目录
    prepare_host_directories

    if ! docker build -t monitor:latest .; then
        log_error "Docker镜像构建失败"
        return 1
    fi

    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."

    # 准备宿主机目录
    prepare_host_directories

    # 检查镜像是否存在
    if ! docker images monitor:latest -q | grep -q .; then
        log_info "未找到Docker镜像，开始构建..."
        build_image || return 1
    fi

    if ! docker compose -f "$COMPOSE_FILE" up -d; then
        log_error "服务启动失败"
        return 1
    fi

    log_success "服务已启动"
    show_status
}

# 停止服务
stop_services() {
    log_info "停止Docker服务..."

    if ! docker compose -f "$COMPOSE_FILE" down; then
        log_error "服务停止失败"
        return 1
    fi

    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启Docker服务..."
    stop_services
    sleep 2
    start_services
}

# 查看状态
show_status() {
    echo
    log_info "服务状态:"
    docker compose -f "$COMPOSE_FILE" ps

    echo
    log_info "镜像信息:"
    docker images | grep monitor || log_warning "未找到monitor镜像"
}

# 查看日志
show_logs() {
    log_info "查看monitor服务日志 (最后100行，Ctrl+C 退出):"
    docker compose -f "$COMPOSE_FILE" logs --tail=100 -f monitor
}

# 清理资源
clean_docker() {
    log_info "清理Docker资源..."

    docker compose -f "$COMPOSE_FILE" down

    # 清理monitor镜像
    if docker images monitor:latest -q | grep -q .; then
        docker rmi monitor:latest 2>/dev/null || log_warning "清理镜像失败"
    fi

    # 清理monitor镜像
    if docker images app-monitor:latest -q | grep -q .; then
        docker rmi app-monitor:latest 2>/dev/null || log_warning "清理镜像失败"
    fi

    # 清理悬空镜像
    if docker images -f "dangling=true" -q | grep -q .; then
        docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true
    fi

    log_success "Docker资源清理完成"
}

# 显示菜单
show_menu() {
    while true; do
        echo
        echo -e "${CYAN}🐳 Monitor项目 Docker管理${NC}"
        echo -e "${CYAN}========================${NC}"
        echo
        echo "1) 启动服务"
        echo "2) 停止服务"
        echo "3) 重启服务"
        echo "4) 查看状态"
        echo "5) 查看日志"
        echo "6) 构建镜像"
        echo "7) 清理资源"
        echo "0) 退出"
        echo
        read -p "请选择 [0-7]: " choice

        case $choice in
            1) start_services ;;
            2) stop_services ;;
            3) restart_services ;;
            4) show_status ;;
            5) show_logs ;;
            6) build_image ;;
            7)
                read -p "确认清理? [y/N]: " confirm
                [[ "$confirm" =~ ^[Yy]$ ]] && clean_docker
                ;;
            0) log_info "退出"; exit 0 ;;
            *) log_warning "无效选择" ;;
        esac

        [[ "$choice" != "5" ]] && read -p "按回车继续..."
    done
}

# 主函数
main() {
    # 解析参数
    case "${1:-}" in
        start) check_environment; start_services ;;
        stop) check_environment; stop_services ;;
        restart) check_environment; restart_services ;;
        status) check_environment; show_status ;;
        logs) check_environment; show_logs ;;
        build) check_environment; build_image ;;
        clean) check_environment; clean_docker ;;
        *)
            echo -e "${CYAN}🐳 Monitor项目 Docker管理${NC}"
            echo -e "${CYAN}========================${NC}"
            echo
            check_environment
            show_menu
            ;;
    esac
}

# 脚本入口
main "$@"
