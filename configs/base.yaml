# 应用基础配置
app:
  name: 'Monitor'
  version: '1.0.0'

# 日志配置 - 现代化配置结构
logging:
  # 全局基础配置
  level: 'INFO'
  format: 'space'  # json, text 或 space

  # 输出配置
  outputs:
    # 控制台输出
    console:
      enabled: true
      format: 'space'  # 控制台使用空格分隔格式便于阅读
      level: 'INFO'   # 控制台日志级别

    # 文件输出
    file:
      enabled: true
      path: 'logs/monitor.log'
      format: 'space'  # 文件使用空格分隔格式便于查询
      level: 'INFO'   # 文件日志级别

      # 文件轮转配置
      rotation:
        max_size: 4      # MB (约100KB，用于测试)
        max_backups: 7     # 保留文件数
        max_age: 30        # 保留天数
        compress: true     # 是否压缩
        enabled: true      # 是否启用轮转

  # 组件特定配置
  components:
    # 爬虫日志配置
    spider:
      level: 'INFO'
      separate_files: true  # 按spider_type分文件
      file_pattern: 'logs/spider_{type}.log'  # 文件名模式

    # 服务日志配置
    service:
      level: 'INFO'

    # 中间件日志配置
    middleware:
      level: 'WARN'  # 中间件日志级别较高，减少噪音

    # 管道日志配置
    pipeline:
      level: 'WARN'  # 管道日志级别较高，减少噪音

    # HTTP客户端日志配置
    httpclient:
      level: 'WARN'  # HTTP客户端日志级别较高，减少TLS指纹噪音

  # 性能配置
  performance:
    async_write: true      # 异步写入
    buffer_size: 1024      # 缓冲区大小
    flush_interval: '1s'   # 刷新间隔

# Redis配置（增强版）
redis:
  # 基础连接配置
  host: ********
  port: 6379
  password: ''
  db: 0

  # 连接池配置
  pool_size: 20
  min_idle_conns: 5
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s
  pool_timeout: 4s

  # 重试配置
  max_retries: 3
  min_retry_backoff: 8ms
  max_retry_backoff: 512ms

  # TTL和键配置
  default_ttl: 24h
  key_prefix: "monitor:"

  # 去重配置（保持向后兼容）
  deduplication:
    enabled: true
    prefix: "dedup:"
    default_ttl: 24h
    compression: false
    encoding: "utf8"

  # 资源缓存配置
  resource_cache:
    enabled: true
    prefix: "resource:"
    default_ttl: 1h

  # 性能配置
  performance:
    enable_pipeline: true
    pipeline_size: 100
    enable_cluster: false

# RabbitMQ配置
rabbitmq:
  enabled: false
  url: '******************************************/'
  vhost: "/"
  heartbeat: 30
  reconnect_delay: 1
  max_reconnect_attempts: 10
  connection_timeout: 30

# 队列配置
queues:
  exchanges:
    instock: "instock"
    notifications: "notifications"
    config_updates: "config"
    error_logs: "error.logs"
  queues:
    instock: "instock"
    notifications: "notifications"
    config_updates: "config.updates"
    error_logs: "error.logs"

# 注释：移除了 http_client 配置，因为它无用且未被实际使用