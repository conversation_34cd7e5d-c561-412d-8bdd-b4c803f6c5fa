# TLS指纹优化配置
# 专注于指纹池管理和指纹选择策略
# 中间件相关配置请查看 configs/middleware.yaml

# TLS指纹优化配置（保持向后兼容）
# 针对风控检测的优化策略
# 全局强制使用Charles TLS指纹配置

# Charles TLS指纹专用配置
charles_fingerprint:
  # 强制Charles模式
  force_charles_only: true           # 强制所有请求使用Charles TLS指纹
  disable_other_fingerprints: true   # 禁用所有非Charles指纹

  # Charles指纹动态生成配置 - 大幅增加变体容量
  dynamic_generation:
    enabled: true                     # 启用Charles指纹动态生成
    base_template: "PopMart_Charles_Ultra_Exact"  # 基础模板
    variation_strength: 0.1           # 较低的变异强度，保持Charles特征
    max_variants: 100                 # 最大变体数量（20 → 100）

  # Charles指纹轮换策略
  rotation_strategy:
    prefer_ultra_exact: true          # 优先使用Ultra Exact版本
    fallback_to_charles_proxy: true   # 备用Charles Proxy版本
    never_use_non_charles: true       # 永不使用非Charles指纹

# 风控响应优化
risk_control:
  # 高风险指纹处理
  high_risk_profiles:
    profiles:
      # 代理类指纹 - 容易被检测
      - "Proxy_Like_v1_*"
      - "Proxy_Like_v2_*"
    # 处理策略
    action: "immediate_block"  # 立即封禁
    block_duration: "30m"      # 封禁30分钟

  # 中风险指纹处理
  medium_risk_profiles:
    profiles:
      - "Minimal_*"
    action: "temporary_block"   # 临时封禁
    block_duration: "15m"       # 封禁15分钟
    
  # 状态码风险映射
  status_code_risk:
    473: "HIGH"      # 自定义风控状态码
    472: "HIGH"      # 另一个风控状态码
    471: "HIGH"      # 自定义风控状态码
    470: "HIGH"      # 另一个风控状态码
    418: "MEDIUM"    # I'm a teapot
    403: "MEDIUM"    # Forbidden
    429: "LOW"       # Too Many Requests

# 指纹轮换优化
rotation_optimization:
  # 基于风控检测的动态轮换
  dynamic_rotation:
    enabled: true
    # 检测到风控后立即轮换
    immediate_rotation_on_risk: true
    # 风控检测后的冷却期
    cooldown_period: "20m"
    
  # 地区优先级策略
  region_priority:
    # PopMart专用地区优先级（按优先级排序）
    preferred_regions:
      - "US"        # 美国 - PopMart主要市场
      - "EU"        # 欧洲 - 重要市场
      - "ASIA"      # 亚洲 - 本土市场
      - "INTL"      # 国际版 - 通用
      - "GENERAL"   # 通用版 - 备用选择
    
    # 地区轮换策略
    region_rotation:
      enabled: true
      # 同一地区最大连续使用次数
      max_consecutive_uses: 5
      # 地区轮换间隔
      rotation_interval: "10m"

# 指纹类型优化
profile_type_optimization:
  # 指纹类型优先级（按推荐程度排序）- 全部使用Charles TLS指纹
  type_priority:
    1: "PopMart_Charles_Ultra_Exact"  # Charles超精确匹配指纹 - 最高优先级
    2: "PopMart_Charles_*"            # Charles抓包精确匹配指纹 - 次高优先级
    3: "Charles_Proxy"                # Charles代理指纹 - 第三优先级
    4: "Charles_*"                    # 所有Charles相关指纹 - 第四优先级
    # 注意：其他非Charles指纹类型已被禁用，强制使用Charles TLS指纹
    # 5: "Chrome_*_Real"              # 已禁用 - 不再使用
    # 6: "Firefox_*_Real"             # 已禁用 - 不再使用
    # 7: "Safari_*_Real"              # 已禁用 - 不再使用
    # 8: "Edge_*_Real"                # 已禁用 - 不再使用
    # 9: "Chrome_*"                   # 已禁用 - 不再使用
    # 10: "Firefox_*"                 # 已禁用 - 不再使用
    # 11: "Minimal_*"                 # 已禁用 - 不再使用
    # 12: "Proxy_Like_*"              # 已禁用 - 不再使用
    
  # 类型轮换策略
  type_rotation:
    enabled: true
    # 避免连续使用同类型指纹
    avoid_consecutive_same_type: true
    # 类型轮换概率
    type_rotation_probability: 0.3

# 默认指纹池配置（已禁用）
default_fingerprint_pool:
  # 基础配置
  enabled: false                            # 禁用默认指纹池 - 没有设置fingerprint的请求不使用自定义指纹

  # 指纹选择策略
  selection_strategy:
    # 强制使用Charles指纹
    force_charles_only: true
    # 最大指纹数量
    max_profiles: 15
    # 指纹轮换策略
    rotation_strategy: "least_recently_used"

  # 质量标准（最严格）
  quality_requirements:
    # 只使用经过验证的高质量指纹
    require_verified: true
    # 最小成功率要求
    min_success_rate: 0.9
    # 排除高风险指纹
    exclude_high_risk: true

# 指纹池配置系统
fingerprint_pools:
  # 全局配置
  global_settings:
    enabled: true                           # 启用指纹池系统 - 但只对明确设置fingerprint的请求生效
    use_middleware_domain_config: true      # 使用middleware.yaml中的域名配置
    default_pool_name: ""                   # 不设置默认指纹池名称
    auto_create_pools: false                # 禁用自动创建指纹池 - 只使用配置中明确定义的池
    max_pools: 50                          # 最大指纹池数量

  # 指纹池定义
  pool_definitions:
    # 注意：已移除默认指纹池配置
    # 没有设置fingerprint字段的请求将直接使用正常HTTP请求，不使用任何自定义指纹

    # PopMart EU指纹池
    popmart-eu:
      name: "PopMart EU指纹池"
      description: "PopMart欧洲地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "EU"

      # 轮换策略配置（从middleware.yaml迁移）
      rotation:
        max_requests_per_profile: 1       # 极低的请求数限制
        max_profile_duration: "15s"      # 极短的使用时长
        random_rotation_probability: 0.8  # 极高的随机轮换概率

      # 风控配置
      risk_control:
        consecutive_failure_threshold: 1  # 一次失败就封禁
        block_duration: "5m"              # 短封禁时间，快速重试
        enable_emergency_refresh: true    # 启用紧急刷新

      # 网络优化配置
      network_optimization:
        connection_timeout: "30s"         # 连接超时
        keep_alive_timeout: "60s"         # 保持连接
        max_idle_connections: 1           # 最小连接池
        disable_compression: true         # 禁用压缩

      # 动态指纹配置
      dynamic_fingerprint:
        enabled: true                     # 启用动态指纹生成
        generation_threshold: 2           # 2次失败后生成动态指纹
        max_dynamic_profiles: 10          # 最多保持10个动态指纹

    # PopMart GB指纹池
    popmart-gb:
      name: "PopMart GB指纹池"
      description: "PopMart英国地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "GB"

    # PopMart AU指纹池
    popmart-au:
      name: "PopMart AU指纹池"
      description: "PopMart澳洲地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "AU"

    # PopMart SG指纹池
    popmart-sg:
      name: "PopMart SG指纹池"
      description: "PopMart新加坡地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "ASIA"

    # PopMart TH指纹池
    popmart-th:
      name: "PopMart TH指纹池"
      description: "PopMart泰国地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "ASIA"

      # 轮换策略配置（泰国地区更激进）
      rotation:
        max_requests_per_profile: 1       # 每次请求都换指纹
        max_profile_duration: "15s"      # 15秒极速轮换
        random_rotation_probability: 1.0  # 100%随机轮换

      # 风控配置（更严格）
      risk_control:
        consecutive_failure_threshold: 1  # 一次失败就封禁
        block_duration: "3m"              # 更短封禁时间，快速重试
        enable_emergency_refresh: true    # 启用紧急刷新

      # 网络优化配置
      network_optimization:
        connection_timeout: "30s"         # 连接超时
        keep_alive_timeout: "60s"         # 保持连接
        max_idle_connections: 1           # 最小连接池
        disable_compression: true         # 禁用压缩

      # 动态指纹配置（更多动态指纹）
      dynamic_fingerprint:
        enabled: true                     # 启用动态指纹生成
        generation_threshold: 1           # 1次失败后生成动态指纹
        max_dynamic_profiles: 15          # 更多动态指纹

    # PopMart US指纹池
    popmart-us:
      name: "PopMart US指纹池"
      description: "PopMart美国地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "US"

    # PopMart HK指纹池
    popmart-hk:
      name: "PopMart HK指纹池"
      description: "PopMart香港地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "ASIA"

    # PopMart KR指纹池
    popmart-kr:
      name: "PopMart KR指纹池"
      description: "PopMart韩国地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "ASIA"

    # PopMart JP指纹池
    popmart-jp:
      name: "PopMart JP指纹池"
      description: "PopMart日本地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "ASIA"

    # PopMart MY指纹池
    popmart-my:
      name: "PopMart MY指纹池"
      description: "PopMart马来西亚地区专用指纹池"
      max_profiles: 30
      selection_strategy: "weighted_random"
      quality_requirements:
        require_verified: true
        min_success_rate: 0.85
        exclude_high_risk: true
      force_charles_only: true
      preferred_profile_types:
        - "PopMart_Charles_Ultra_Exact"
        - "PopMart_Charles_*"
        - "Charles_Proxy"
      region_preference: "ASIA"

# 爬虫指纹池映射配置（简化版 - 移除多余的域名映射）
spider_fingerprint_mapping:
  # 全局配置
  global_settings:
    enabled: true
    default_domain_group: "general"

  # 回退配置（当fingerprint字段为空时使用爬虫名称）
  fallback_spider_config:
    inherit_from_default: true
    max_profiles: 30

# 健康指纹池配置
healthy_fingerprint_pool:
  # 基础配置
  enabled: true

  # 数量配置 - 大幅增加指纹池容量
  pool_size:
    min_verified_count: 20     # 最小已验证指纹数（5 → 20）
    min_candidate_count: 15    # 最小候选指纹数（3 → 15）
    min_backup_count: 10       # 最小备用指纹数（2 → 10）
    max_total_count: 200       # 最大总指纹数（50 → 200）

  # 时间配置 - 简化配置，主要依赖容量管理
  timing:
    observation_period: "6h"    # 候选指纹观察期（保留）
    promotion_cooldown: "1h"    # 晋升冷却期（保留）
    generation_cooldown: "30m"  # 生成冷却期（保留）
    cleanup_interval: "0"       # 禁用定时清理，依赖容量管理

  # 质量标准
  quality_thresholds:
    promotion_success_rate: 0.85  # 晋升成功率阈值
    promotion_min_uses: 20        # 晋升最小使用次数
    demotion_success_rate: 0.70   # 降级成功率阈值
    cleanup_success_rate: 0.30    # 清理成功率阈值

  # 生成策略
  generation:
    enable_smart_generation: true  # 启用智能生成
    variation_strength: 0.3        # 变异强度
    max_failed_attempts: 3         # 最大失败尝试次数
    base_on_successful: true       # 基于成功指纹生成

  # 轮换策略 - 优化轮换参数以支持更大容量
  rotation:
    enable_smart_rotation: true    # 启用智能轮换
    max_usage_before_rotation: 100 # 轮换前最大使用次数（50 → 100）
    rotation_time_threshold: "30m" # 轮换时间阈值
    random_rotation_probability: 0.1 # 随机轮换概率

  # 维护策略
  maintenance:
    auto_maintenance: true         # 自动维护
    maintenance_interval: "1h"     # 维护间隔
    auto_cleanup_poor_profiles: true # 自动清理表现差的指纹
    unused_profile_threshold: "7d"  # 未使用指纹清理阈值

# 动态指纹生成优化
dynamic_generation:
  # 风控检测后自动生成新指纹
  auto_generate_on_risk: true

  # 生成策略 - 大幅增加动态生成容量
  generation_strategy:
    # 基于成功指纹的变异
    base_on_successful: true
    # 变异强度（0.1-1.0）
    mutation_strength: 0.3
    # 最大生成数量（10 → 50）
    max_generated_profiles: 50

  # 生成的指纹命名规则
  naming_pattern: "Dynamic_{timestamp}_{region}"

# 智能选择优化
smart_selection:
  # PopMart专用权重配置
  # 基于历史成功率的选择（最重要）
  success_rate_weight: 0.5

  # 基于指纹类型的选择（PopMart专用指纹优先）
  type_priority_weight: 0.3

  # 基于最近使用时间的选择
  freshness_weight: 0.15

  # 基于地区匹配的选择
  region_match_weight: 0.05

  # 最小成功率阈值（更严格）
  min_success_rate: 0.8

# 监控和告警
monitoring:
  # 风控检测告警阈值
  alert_thresholds:
    # 1小时内高风险事件数量
    high_risk_events_per_hour: 3
    # 1小时内同一指纹被检测次数
    same_profile_detections_per_hour: 2
    # 1小时内同一地区被检测次数
    same_region_detections_per_hour: 5
    
  # 自动优化触发条件
  auto_optimization_triggers:
    # 连续失败次数
    consecutive_failures: 2
    # 成功率低于阈值
    success_rate_below: 0.5
    # 最近检测时间间隔
    recent_detection_within: "5m"

# 备用策略
fallback_strategies:
  # 所有指纹都被检测时的策略
  all_profiles_blocked:
    action: "generate_dynamic"
    fallback_to_basic: true
    
  # 动态生成失败时的策略
  dynamic_generation_failed:
    action: "use_basic_profiles"
    ignore_risk_level: true
    
  # 紧急情况下的最小配置
  emergency_config:
    use_minimal_tls: true
    disable_fingerprinting: false
    basic_user_agent: "Mozilla/5.0 (compatible; Emergency)"

# 性能优化
performance:
  # 指纹缓存策略 - 优化缓存容量以防止内存泄露
  caching:
    # 缓存成功的指纹配置
    cache_successful_configs: true
    # 缓存时间
    cache_duration: "30m"  # 缩短缓存时间：1h → 30m
    # 最大缓存数量（150 → 100，防止内存泄露）
    max_cached_configs: 100

  # 并发优化 - 适度并发处理能力
  concurrency:
    # 最大并发指纹选择数（30 → 20，减少内存压力）
    max_concurrent_selections: 20
    # 选择超时时间
    selection_timeout: "5s"

  # 内存管理优化 - 平衡内存管理配置
  memory_management:
    # 动态指纹内存限制 - 平衡设置
    max_dynamic_profiles: 50           # 最大动态指纹数量（平衡值）
    dynamic_profile_ttl: "1h"          # 动态指纹生存时间（平衡值）
    emergency_cleanup_threshold: 100   # 紧急清理阈值（平衡值）

    # 清理间隔配置 - 禁用定时清理，依赖智能容量管理
    cleanup_intervals:
      manager_cleanup: "0"             # 禁用管理器定时清理
      pool_cleanup: "0"                # 禁用指纹池定时清理
      generator_cleanup: "0"           # 禁用生成器定时清理
      anti_detection_cleanup: "0"      # 禁用反风控定时清理

    # 各组件内存限制 - 平衡限制
    component_limits:
      # 健康指纹池限制 - 紧急降级，大幅减少内存使用
      healthy_pool:
        max_profiles_per_tier: 10      # 紧急降级：从60降到10
        max_generation_history: 50     # 紧急降级：从200降到50
        max_failed_patterns: 20        # 紧急降级：从100降到20
        # 移除时间相关配置，依赖智能淘汰机制
        # generation_history_ttl: "2h"   # 不再需要时间限制
        # failed_pattern_threshold: 5    # 不再需要阈值清理

      # 动态生成器限制 - 平衡设置
      dynamic_generator:
        max_variations: 50             # 最大变异记录（平衡值）
        max_success_rates: 200         # 最大成功率统计（平衡值）
        success_rate_threshold: 0.2    # 成功率清理阈值（平衡值）
        variation_cleanup_ratio: 0.6   # 变异清理比例（平衡值）

      # 反风控引擎限制 - 平衡设置
      anti_detection:
        max_detection_patterns: 30     # 最大检测模式（平衡值）
        max_blocked_fingerprints: 50   # 最大被封指纹记录（平衡值）
        pattern_ttl: "6h"              # 检测模式生存时间（平衡值）
        blocked_fingerprint_ttl: "1h"  # 被封指纹记录生存时间（平衡值）

    # 内存压力检测配置 - 业务友好设置
    memory_pressure:
      enabled: true                    # 启用内存压力检测
      check_interval: "1m"             # 检查间隔
      business_friendly: true          # 业务友好模式
      pressure_thresholds:
        warning_mb: 50                 # 警告阈值（MB）
        gc_trigger_mb: 100             # GC触发阈值（MB）
        critical_mb: 150               # 危险阈值（MB）
        consecutive_checks: 3          # 连续检查次数
      emergency_actions:
        auto_gc: true                  # 允许自动GC
        gentle_gc_only: true           # 只执行温和GC
        log_only_critical: true        # 危险情况只记录日志，不自动处理
        clear_all_caches: false        # 不清空所有缓存（保持功能性）
        reset_generating_set: false    # 不重置生成中集合（保持功能性）

    # 容量限制策略
    capacity_management:
      strategy: "quality_based"        # 基于质量的淘汰策略
      quality_factors:
        success_rate_weight: 0.6       # 成功率权重
        time_factor_weight: 0.25       # 时间因子权重
        use_count_weight: 0.15         # 使用次数权重
      auto_adjustment:
        enabled: true                  # 启用自动调整
        wind_control_factor: 1.2       # 风控强度调整因子
        min_capacity_ratio: 0.5        # 最小容量比例
        max_capacity_ratio: 2.0        # 最大容量比例

# 日志配置
logging:
  # 风控事件日志级别
  risk_event_level: "WARN"
  
  # 详细日志开关
  detailed_logging:
    profile_selection: false
    rotation_events: true
    risk_analysis: true
    optimization_suggestions: true
    
  # 日志格式
  log_format:
    include_timestamp: true
    include_profile_name: true
    include_risk_level: true
    include_recommendations: true

# 实验性功能
experimental:
  # AI驱动的指纹优化
  ai_optimization:
    enabled: false
    model: "risk_prediction_v1"
    
  # 机器学习风控预测
  ml_risk_prediction:
    enabled: false
    training_data_size: 1000
    
  # 自适应轮换算法
  adaptive_rotation:
    enabled: false
    learning_rate: 0.1
