# 数据库配置
database:
  driver: 'sqlite'
  dsn: 'data/monitor.db'
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: '1h'
  auto_migrate: true
  log_queries: false

# 数据库详细配置 (保留原有配置用于扩展)
database_detailed:
  # 数据库类型
  type: sqlite  # sqlite, mysql, postgresql
  
  # SQLite配置
  sqlite:
    path: data/monitor.db
    
  # MySQL配置
  mysql:
    host: localhost
    port: 3306
    database: monitor
    user: root
    password: password
    charset: utf8mb4
    
  # PostgreSQL配置
  postgresql:
    host: localhost
    port: 5432
    database: monitor
    user: postgres
    password: password
    
  # 连接池配置
  pool:
    size: 5  # 连接池大小
    max_overflow: 10  # 最大溢出连接数
    timeout: 30  # 连接超时时间（秒）
    recycle: 3600  # 连接回收时间（秒）
    
  # 表配置
  tables:
    product_status:
      # 索引配置
      indexes:
        - name: idx_product_key
          columns: [product_key]
          unique: true
        - name: idx_product_id
          columns: [productId]
        - name: idx_status_fingerprint
          columns: [status_fingerprint]
        - name: idx_last_seen
          columns: [last_seen]
          
  # 迁移配置
  migrations:
    enabled: true  # 是否启用自动迁移
    version_table: alembic_version  # 版本表名
    script_location: migrations  # 迁移脚本位置

# 数据库维护配置
maintenance:
  # 数据清理配置
  cleanup:
    enabled: true
    # 旧数据自动清理间隔（秒）
    interval: 604800  # 一周
    # 数据保留时间（秒）
    retention: 2592000  # 30天
  
  # 数据备份配置
  backup:
    enabled: true
    # 备份间隔（秒）
    interval: 86400  # 一天
    # 备份保留天数
    retention_days: 30
    # 备份路径
    path: 'data/backups'
  optimize:
    enabled: true
    interval: 604800  # 秒
  vacuum:
    enabled: true
    interval: 2592000  # 秒 