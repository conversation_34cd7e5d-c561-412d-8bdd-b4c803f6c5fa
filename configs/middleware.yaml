# 中间件配置
# 格式说明：
# 每个中间件配置包含 enabled、priority 和其他特定配置参数

# 代理中间件配置
proxy:
  enabled: true
  priority: 400
  # 故障转移配置
  failover:
    enabled: true # 启用故障转移
    allow_http_fallback: true # 允许SOCKS失败时尝试HTTP模式
    allow_direct: true # 允许在所有代理失败时直接连接
    max_failures: 20 # 失败代理记录清除阈值
  proxy_groups:
    us:
      - http://pool-basic-cc-us:<EMAIL>:8888
    it:
      - http://pool-basic-cc-it:<EMAIL>:8888
    de:
      - http://pool-basic-cc-de:<EMAIL>:8888
    es:
      - http://pool-basic-cc-es:<EMAIL>:8888
    fr:
      - http://pool-basic-cc-fr:<EMAIL>:8888
    gb:
      - http://pool-basic-cc-gb:<EMAIL>:8888
    au:
      - http://pool-basic-cc-au:<EMAIL>:8888
    sg:
      - http://pool-basic-cc-sg:<EMAIL>:8888
    th:
      - http://pool-basic-cc-th:<EMAIL>:8888
    jp:
      - http://pool-basic-cc-jp:<EMAIL>:8888
    isp:
      - socks5h://leftxrxi-rotate:<EMAIL>:80
  # 反向代理组配置
  reverse_proxies:
    cloudflare:
      - url: https://proxy.wink-wedge-8l.workers.dev
        type: reverse
        headers:
          X-Custom-Auth: "dXBAcDZBKHduWVpWQjhlVnJkWmNDdGtYaDgoaDhMTVM="
      - url: https://proxy.bxv2cdm89d.workers.dev
        type: reverse
        headers:
          X-Custom-Auth: "dXBAcDZBKHduWVpWQjhlVnJkWmNDdGtYaDgoaDhMTVM="
      - url: https://dark-leaf-5314.dynamos-reshoot-2h.workers.dev
        type: reverse
        headers:
          X-Custom-Auth: "dXBAcDZBKHduWVpWQjhlVnJkWmNDdGtYaDgoaDhMTVM="
      - url: https://proxy.bhtrkddzxt.workers.dev
        type: reverse
        headers:
          X-Custom-Auth: "dXBAcDZBKHduWVpWQjhlVnJkWmNDdGtYaDgoaDhMTVM="
      - url: https://proxy.tarots-fizz-9o.workers.dev
        type: reverse
        headers:
          X-Custom-Auth: "dXBAcDZBKHduWVpWQjhlVnJkWmNDdGtYaDgoaDhMTVM="
      - url: https://proxy.gin-marts-9j.workers.dev
        type: reverse
        headers:
          X-Custom-Auth: "dXBAcDZBKHduWVpWQjhlVnJkWmNDdGtYaDgoaDhMTVM="
      - url: https://proxy.ranges86diktat.workers.dev
        type: reverse
        headers:
          X-Custom-Auth: "dXBAcDZBKHduWVpWQjhlVnJkWmNDdGtYaDgoaDhMTVM="
      - url: https://proxy.wetland-47perky.workers.dev
        type: reverse
        headers:
          X-Custom-Auth: "dXBAcDZBKHduWVpWQjhlVnJkWmNDdGtYaDgoaDhMTVM="

# 重试中间件配置
retry:
  # 是否启用重试
  enabled: true
  priority: 100
  # 最大重试次数
  max_retries: 5
  # 需要重试的HTTP状态码
  retry_http_codes: [500, 501, 502, 503, 504, 522, 524, 408]
  # 请求超时时间(秒)
  timeout: 10

# 头部中间件配置
headers:
  # 是否启用头部中间件
  enabled: true
  priority: 200
  # 安卓设备信息
  android:
    versions: ['10.0.0', '11.0.0', '12.0.0', '13.0.0', '14.0.0']
    devices: [
      'SM-G991B', 'SM-G998B', 'SM-G781B', 'SM-G780G', 'SM-A525F',
      'SM-A326B', 'SM-A225F', 'SM-A127F', 'SM-A037G', 'SM-A032F',
      'SM-A022G', 'SM-A015F', 'SM-A013G', 'SM-A012F', 'SM-A015G',
      'SM-A022F', 'SM-A032G', 'SM-A037F', 'SM-A127G', 'SM-A225G',
      'SM-A326G', 'SM-A525G', 'SM-G780F', 'SM-G781F', 'SM-G998F',
      'SM-G991F', 'SM-G990F', 'SM-G990B', 'SM-G990U', 'SM-G990W'
    ]
    builds: [
      'QP1A.190711.020', 'QP1A.190711.020', 'QP1A.190711.020',
      'RP1A.200720.012', 'RP1A.200720.012', 'RP1A.200720.012',
      'SP1A.210812.016', 'SP1A.210812.016', 'SP1A.210812.016',
      'TP1A.220624.014', 'TP1A.220624.014', 'TP1A.220624.014'
    ]
  # iOS设备信息
  ios:
    versions: [
      '15_0', '15_1', '15_2', '15_3', '15_4', '15_5', '15_6', '15_7',
      '16_0', '16_1', '16_2', '16_3', '16_4', '16_5', '16_6', '16_7',
      '17_0', '17_1', '17_2', '17_3', '17_4', '17_5', '17_6', '17_7'
    ]
    devices: [
      'iPhone13,1', 'iPhone13,2', 'iPhone13,3', 'iPhone13,4',
      'iPhone14,2', 'iPhone14,3', 'iPhone14,4', 'iPhone14,5',
      'iPhone14,6', 'iPhone14,7', 'iPhone14,8', 'iPhone15,2',
      'iPhone15,3', 'iPhone15,4', 'iPhone15,5', 'iPhone16,1',
      'iPhone16,2', 'iPhone16,3', 'iPhone16,4', 'iPhone16,5'
    ]
  # 桌面版设备信息
  desktop:
    windows:
      os_versions: ['Windows NT 10.0', 'Windows NT 11.0']
      architectures: ['Win64; x64', 'WOW64']
    macos:
      os_versions: [
        'Macintosh; Intel Mac OS X 10_15_7',
        'Macintosh; Intel Mac OS X 11_6_0', 
        'Macintosh; Intel Mac OS X 12_4_0',
        'Macintosh; Apple M1 Mac OS X 12_5_0',
        'Macintosh; Apple M1 Mac OS X 13_2_1',
        'Macintosh; Apple M2 Mac OS X 13_4_1',
        'Macintosh; Apple M2 Mac OS X 14_0_0'
      ]
    linux:
      os_versions: ['X11; Linux x86_64', 'X11; Ubuntu; Linux x86_64']
    browsers:
      chrome: ['110.0.0.0', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '120.0.0.0', '*********']
      firefox: ['110.0', '111.0', '112.0', '113.0', '114.0', '115.0', '116.0', '117.0', '118.0', '119.0', '120.0', '121.0']
      edge: ['110.0.1587.41', '111.0.1661.44', '112.0.1722.34', '113.0.1774.42', '114.0.1823.37', '115.0.1901.183', '116.0.1938.54', '117.0.2045.31', '118.0.2088.46', '119.0.2151.58', '120.0.2210.61', '121.0.2277.83']
      safari: ['15.6.1', '16.0', '16.1', '16.2', '16.3', '16.4', '16.5', '16.6', '17.0', '17.1', '17.2', '17.3']
  # 请求头配置
  request_headers:
    Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9'
    Accept-Language: 'en-US,en;q=0.9'
    Accept-Encoding: 'gzip, deflate, br'
    Connection: 'keep-alive'
    Sec-Fetch-Dest: 'document'
    Sec-Fetch-Mode: 'navigate'
    Sec-Fetch-Site: 'none'
    Sec-Fetch-User: '?1'

# Cookie中间件配置
cookies:
  enabled: true
  priority: 300
  # 持久化配置
  persistence:
    enabled: true
    # 需要持久化cookie的爬虫类型
    spider_types: ["aliexpress", "amazon"]
    # Cookie名称配置：AliExpress + Amazon重要cookies
    cookie_names: [
      # AliExpress cookies
      "_m_h5_tk_enc", "_m_h5_tk",
      # Amazon cookies
      "session-token",
      "session-id", "session-id-time",
      "ubid-acbit", "ubid-acbde", "ubid-acbes", "ubid-acbfr", "ubid-acbuk",
      "i18n-prefs",
      "lc-acbit", "lc-acbde", "lc-acbes", "lc-acbfr", "lc-acbuk"
    ]
  cookie_groups:
    aliexpress-all:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=IT&b_locale=en_US"

    aliexpress-eu:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=IT&b_locale=en_US"
    
    aliexpress-it:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=IT&b_locale=en_US"
    
    aliexpress-es:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=ES&b_locale=en_US"

    aliexpress-de:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=DE&b_locale=en_US"

    aliexpress-gb:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=GB&b_locale=en_US"

    aliexpress-au:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=AU&b_locale=en_US"

    aliexpress-sg:
      enabled: true
      domain: "aliexpress.com"
      cookies:
        - "aep_usuc_f=site=glo&c_tp=USD&region=SG&b_locale=en_US"
        
    aliexpress-th:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=TH&b_locale=en_US"

    aliexpress-jp:
      enabled: true
      domain: "aliexpress.com"
      cookies: 
        - "aep_usuc_f=site=glo&c_tp=USD&region=JP&b_locale=en_US"

    amazon-it:
      enabled: true
      domain: "amazon.it"
      cookies: # 50065
        - "i18n-prefs=EUR"
        - "lc-acbit=en_GB"

    amazon-de:
      enabled: true
      domain: "amazon.de"
      cookies: # 70563
        - "i18n-prefs=EUR"
        - "lc-acbde=en_GB"

    amazon-es:
      enabled: true
      domain: "amazon.es"
      cookies: # 08025
        - "i18n-prefs=EUR"
        - "lc-acbes=en_GB"

    amazon-fr:
      enabled: true
      domain: "amazon.fr"
      cookies: # 75011
        - "i18n-prefs=EUR"
        - "lc-acbfr=en_GB"

    amazon-gb:
      enabled: true
      domain: "amazon.co.uk"
      cookies: # NW1 5LR
        - "i18n-prefs=GBP"
        - "lc-acbuk=en_GB"
    
    amazon-jp:
      enabled: true
      domain: "amazon.jp"
      cookies: # 100-0001
        - "i18n-prefs=JYP"
        - "lc-acbjp=en_US"

    amazon-it-wishlist:
      enabled: true
      domain: "amazon.it"
      cookies: # 50065
        - "i18n-prefs=EUR"
        - "lc-acbit=en_GB"

    amazon-de-wishlist:
      enabled: true
      domain: "amazon.de"
      cookies: # 70563
        - "i18n-prefs=EUR"
        - "lc-acbde=en_GB"

    amazon-es-wishlist:
      enabled: true
      domain: "amazon.es"
      cookies: # 08025
        - "i18n-prefs=EUR"
        - "lc-acbes=en_GB"

    amazon-fr-wishlist:
      enabled: true
      domain: "amazon.fr"
      cookies: # 75011
        - "i18n-prefs=EUR"
        - "lc-acbfr=en_GB"

    amazon-gb-wishlist:
      enabled: true
      domain: "amazon.co.uk"
      cookies: # NW1 5LR
        - "i18n-prefs=GBP"
        - "lc-acbuk=en_GB"
# 签名中间件配置
signature:
  enabled: true
  priority: 350
  # AliExpress签名配置
  aliexpress:
    sign_params: ["appKey", "data"]  # 需要参与签名计算的参数
    sign_format: "json"  # 签名格式："json"或默认
    debug: false  # 是否记录详细签名日志
  # Popmart签名配置
  popmart:
    debug: true  # 是否记录详细签名日志

# TLS指纹管理配置
tls_fingerprint:
  enabled: true
  priority: 450
  # 风控检测配置 - 中间件的唯一职责
  risk_control:
    # 被认定为风控的HTTP状态码（按严重程度分类）
    severe_status_codes: [470, 471, 472, 473]    # 重度风控状态码
    moderate_status_codes: [418]       # 中度风控状态码
    light_status_codes: [403, 429]     # 轻度风控状态码

