# 管道配置
# 格式说明：
# 每个管道配置包含 enabled、priority 和其他特定配置参数

# ValidationPipeline 配置
validation:
  enabled: true
  priority: 100
  # 是否启用严格模式，启用后会在类型错误时抛弃数据
  strict_mode: true
  # ProductItem 的必填字段
  required_fields:
    ProductItem:
      - productId
      - title
      - url
      - platform
      - price
      - currency
      - stock
      - inStock
      - availability
      - country
      - siteUrl
    NotificationItem:
      - level
      - title
      - message
  # 字段类型定义
  field_types:
    ProductItem:
      productId: str
      title: str
      url: str
      platform: str
      price: float
      currency: str
      stock: int
      inStock: bool
      availability: str
      country: str
      siteUrl: str
      skuId: [str, null]
      offerId: [str, null]
      imageUrl: [str, null]
      addition: [str, null]
      releaseDate: [str, null]
      notifications: list
      crawledAt: str
      metadata: dict

# DuplicatesPipeline 配置
duplicates:
  enabled: true
  priority: 200
  # 重复数据处理策略：keep_first(保留第一个) 或 keep_last(保留最后一个)
  strategy: keep_first
  # 指纹计算使用的字段
  fingerprint_fields:
    - ProductID
    - SkuID
    - URL
    - InStock
    - Price
    - ReleaseDate
  # Redis存储配置
  key_prefix: "dedup:"
  ttl: "168h"  # 产品状态在Redis中的存活时间（7天）
  # 库存确认配置（防止虚假缺货通知）
  stock_confirmation_enabled: true  # 启用库存确认机制
  out_of_stock_threshold: 3         # 缺货确认次数（连续4次才确认缺货）

# NotificationPipeline 配置
notification:
  enabled: true
  priority: 300
  # 通知队列配置
  queue: notification
  # 批量处理配置
  batch_processing:
    enabled: true
    batch_size: 10
    flush_interval: 5s
  # 重试配置
  retry:
    max_retries: 3
    retry_delay: 1s
    backoff_multiplier: 2