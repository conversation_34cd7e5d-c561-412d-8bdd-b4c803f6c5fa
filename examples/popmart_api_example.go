// Package main POP MART API 测试 - 使用 internal/services/request/service.go
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/foundation"
)

func main() {
	ctx := context.Background()

	fmt.Println("=== POP MART API 测试 (使用请求服务) ===")

	// 测试1: 默认配置
	fmt.Println("\n1. 测试默认配置")
	testDefaultConfig(ctx)

	// 测试2: HTTP/2 配置
	fmt.Println("\n2. 测试HTTP/2配置")
	testHTTP2Config(ctx)

	// 测试3: 代理配置 (模拟)
	fmt.Println("\n3. 测试代理配置")
	testProxyConfig(ctx)

	// 测试4: 完整的 POP MART 请求
	fmt.Println("\n4. 测试完整的 POP MART 请求")
	testFullPopMartRequest(ctx)

	fmt.Println("\n=== 所有测试完成 ===")
}

// testDefaultConfig 测试默认配置
func testDefaultConfig(ctx context.Context) {
	service := request.NewService()
	defer service.Close()

	req := createPopMartRequest()
	req.SpiderType = "popmart-default"

	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("默认配置请求失败: %v", err)
		return
	}

	fmt.Printf("默认配置 - 状态码: %d, 响应时间: %v, 响应大小: %d bytes\n",
		resp.StatusCode, resp.ResponseTime, resp.Size)

	// 解析响应
	if resp.StatusCode == 200 {
		parsePopMartResponse(resp.Body)
	}
}

// testHTTP2Config 测试HTTP/2配置
func testHTTP2Config(ctx context.Context) {
	// 创建HTTP/2优化配置
	config := httpclient.WithHTTP2()
	config.EnableMonitoring = true
	config.EnableRetry = true
	config.MaxRetries = 2
	config.UserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	req := createPopMartRequest()
	req.SpiderType = "popmart-http2"

	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("HTTP/2配置请求失败: %v", err)
		return
	}

	fmt.Printf("HTTP/2配置 - 状态码: %d, 响应时间: %v, 响应大小: %d bytes\n",
		resp.StatusCode, resp.ResponseTime, resp.Size)

	// 验证配置
	baseConfig := service.GetBaseConfig()
	fmt.Printf("HTTP版本: %s, HTTP/2推送: %t\n",
		baseConfig.HTTPVersion.String(), baseConfig.EnableHTTP2Push)

	// 解析响应
	if resp.StatusCode == 200 {
		parsePopMartResponse(resp.Body)
	}
}

// testProxyConfig 测试代理配置
func testProxyConfig(ctx context.Context) {
	// 创建代理配置 (使用示例代理，预期会失败)
	config := &foundation.Config{
		Timeout:         15 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",

		EnableMonitoring:  true,
		EnableRetry:       true,
		MaxRetries:        1,
		RetryDelay:        500 * time.Millisecond,
		RetryBackoff:      1.5,

		// 代理配置
		ProxyType: foundation.ProxyTypeHTTP,
		ProxyURL:  "http://proxy.example.com:8080",
		ProxyAuth: nil,

		// HTTP版本配置
		HTTPVersion:     foundation.HTTPVersionAuto,
		EnableHTTP2Push: false,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	req := createPopMartRequest()
	req.SpiderType = "popmart-proxy"

	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		fmt.Printf("代理配置请求失败（预期的）: %v\n", err)
	} else {
		fmt.Printf("代理配置 - 状态码: %d, 响应时间: %v\n",
			resp.StatusCode, resp.ResponseTime)
	}

	// 验证配置
	baseConfig := service.GetBaseConfig()
	fmt.Printf("代理类型: %s, 代理URL: %s\n",
		baseConfig.ProxyType.String(), baseConfig.ProxyURL)
}

// testFullPopMartRequest 测试完整的 POP MART 请求
func testFullPopMartRequest(ctx context.Context) {
	// 创建优化配置
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    100,
		MaxConnsPerHost: 10,
		UserAgent:       "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",

		EnableMonitoring:  true,
		EnableFingerprint: true,
		EnableRetry:       true,

		MaxRetries:   3,
		RetryDelay:   1 * time.Second,
		RetryBackoff: 2.0,

		// HTTP/2 配置
		HTTPVersion:     foundation.HTTPVersionHTTP2Preferred,
		EnableHTTP2Push: true,

		// 无代理
		ProxyType: foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	req := createPopMartRequest()
	req.SpiderType = "popmart-full"

	// 添加元数据用于中间件处理
	req.Metadata = map[string]interface{}{
		"platform":     "popmart",
		"country":      "IT",
		"fingerprint":  "default",
		"cookie_group": "popmart-it",
	}

	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("完整请求失败: %v", err)
		return
	}

	fmt.Printf("完整请求 - 状态码: %d, 响应时间: %v, 响应大小: %d bytes\n",
		resp.StatusCode, resp.ResponseTime, resp.Size)

	// 详细解析响应
	if resp.StatusCode == 200 {
		fmt.Println("成功获取 POP MART 数据:")
		parsePopMartResponse(resp.Body)
	} else {
		fmt.Printf("请求失败，状态码: %d\n", resp.StatusCode)
		if len(resp.Body) > 0 && len(resp.Body) < 500 {
			fmt.Printf("响应内容: %s\n", string(resp.Body))
		}
	}

	// 打印统计信息
	stats := service.GetStats()
	fmt.Printf("服务统计: 总请求: %v, 成功: %v, 失败: %v, 成功率: %.2f%%\n",
		stats["total_requests"], stats["success_count"], stats["failure_count"], stats["success_rate"])
}

// createPopMartRequest 创建 POP MART API 请求
func createPopMartRequest() *models.Request {
	// 请求体
	requestBody := map[string]interface{}{
		"pageSize":    20,
		"page":        1,
		"strategy":    "",
		"term":        "MONSTERS",
		"categoryIds": []interface{}{},
		"brandIds":    []interface{}{},
		"s":           "76772633e0f0999bf83b918f267bf19b",
		"t":           1754090366,
	}

	bodyBytes, _ := json.Marshal(requestBody)

	// 请求头
	headers := map[string]string{
		"Content-Type":         "application/json",
		"Accept":               "application/json, text/plain, */*",
		"Language":             "en",
		"X-Project-Id":         "eude",
		"X-Device-Os-Type":     "web",
		"X-Client-Country":     "IT",
		"Tz":                   "Europe/Rome",
		"Country":              "IT",
		"X-Sign":               "64dbde0600c687120c1a7ac60ef7f456,1754090366",
		"Clientkey":            "rmdxjisjk7gwykcix",
		"X-Client-Namespace":   "eurasian",
		"Origin":               "https://www.popmart.com",
		"Referer":              "https://www.popmart.com/",
		"Sec-Ch-Ua":            `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`,
		"Sec-Ch-Ua-Mobile":     "?0",
		"Sec-Ch-Ua-Platform":   `"macOS"`,
		"Sec-Fetch-Site":       "same-site",
		"Sec-Fetch-Mode":       "cors",
		"Sec-Fetch-Dest":       "empty",
		"Accept-Encoding":      "gzip, deflate, br, zstd",
		"Accept-Language":      "zh-CN,zh;q=0.9,en;q=0.8",
	}

	return &models.Request{
		URL:        "https://prod-intl-api.popmart.com/shop/v1/search",
		Method:     "POST",
		Headers:    headers,
		Body:       bodyBytes,
		MaxRetries: 3,
		Timeout:    30 * time.Second,
	}
}

// parsePopMartResponse 解析 POP MART 响应
func parsePopMartResponse(body []byte) {
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		fmt.Printf("解析响应失败: %v\n", err)
		return
	}

	// 检查响应结构
	if code, ok := response["code"].(float64); ok {
		fmt.Printf("响应代码: %.0f\n", code)
	}

	if message, ok := response["message"].(string); ok {
		fmt.Printf("响应消息: %s\n", message)
	}

	if data, ok := response["data"].(map[string]interface{}); ok {
		if products, ok := data["products"].([]interface{}); ok {
			fmt.Printf("找到产品数量: %d\n", len(products))
			
			// 显示前几个产品
			for i, product := range products {
				if i >= 3 { // 只显示前3个
					break
				}
				if productMap, ok := product.(map[string]interface{}); ok {
					if name, ok := productMap["name"].(string); ok {
						fmt.Printf("  产品 %d: %s\n", i+1, name)
					}
				}
			}
		}
	}
}
