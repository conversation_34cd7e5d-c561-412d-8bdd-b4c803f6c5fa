// Package main 详细验证指纹应用和SOCKS5代理
package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/foundation"
)

func main() {
	ctx := context.Background()

	fmt.Println("=== 详细验证指纹应用和SOCKS5代理 ===")

	// 测试1: 验证指纹是否真正应用到TLS连接
	fmt.Println("\n1. 验证指纹是否真正应用到TLS连接")
	testFingerprintApplication(ctx)

	// 测试2: 验证SOCKS5代理配置
	fmt.Println("\n2. 验证SOCKS5代理配置")
	testSOCKS5ProxyConfig(ctx)

	// 测试3: 直接测试TLS指纹
	fmt.Println("\n3. 直接测试TLS指纹")
	testDirectTLSFingerprint(ctx)

	// 测试4: 验证代理拨号器
	fmt.Println("\n4. 验证代理拨号器")
	testProxyDialer(ctx)

	// 测试5: 综合测试
	fmt.Println("\n5. 综合测试 - 指纹 + HTTP/2 + 代理")
	testCombinedFeatures(ctx)

	fmt.Println("\n=== 验证完成 ===")
}

// testFingerprintApplication 验证指纹是否真正应用到TLS连接
func testFingerprintApplication(ctx context.Context) {
	// 创建启用指纹的配置
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    10,
		MaxConnsPerHost: 2,
		UserAgent:       "Fingerprint-Verification/1.0",

		EnableMonitoring:  false,
		EnableRetry:       false,
		EnableFingerprint: true, // 启用指纹

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	client := httpclient.NewAdvancedClient(config)
	defer client.Close()

	// 测试不同指纹池
	pools := []string{"default", "chrome", "firefox", "safari"}

	for _, pool := range pools {
		fmt.Printf("  测试指纹池: %s\n", pool)

		req := &foundation.Request{
			URL:     "https://tls.browserleaks.com/json",
			Method:  "GET",
			Headers: map[string]string{"User-Agent": "Fingerprint-Verification/1.0"},
			Meta: map[string]interface{}{
				"fingerprint": pool,
			},
		}

		resp, err := client.Do(ctx, req)
		if err != nil {
			fmt.Printf("    ❌ 指纹池 %s 失败: %v\n", pool, err)
			continue
		}

		fmt.Printf("    ✅ 指纹池 %s: 状态码 %d, 响应大小: %d bytes\n", 
			pool, resp.StatusCode, len(resp.Body))

		// 检查是否有指纹信息返回
		if resp.Request != nil && resp.Request.Meta != nil {
			if selectedProfile, ok := resp.Request.Meta["selected_fingerprint"].(string); ok {
				fmt.Printf("      🎯 选择的指纹: %s\n", selectedProfile)
			}
			if tlsVersion, ok := resp.Request.Meta["tls_version"].(string); ok {
				fmt.Printf("      🔒 TLS版本: %s\n", tlsVersion)
			}
		}

		// 尝试解析响应中的TLS信息
		if len(resp.Body) > 0 && len(resp.Body) < 10000 {
			fmt.Printf("      📄 TLS指纹响应: %s\n", string(resp.Body)[:min(200, len(resp.Body))])
		}

		time.Sleep(500 * time.Millisecond)
	}
}

// testSOCKS5ProxyConfig 验证SOCKS5代理配置
func testSOCKS5ProxyConfig(ctx context.Context) {
	// 测试不同的SOCKS5代理配置
	proxyConfigs := []struct {
		name     string
		proxyURL string
		auth     *foundation.ProxyAuth
	}{
		{
			name:     "无认证SOCKS5",
			proxyURL: "socks5://127.0.0.1:1080",
			auth:     nil,
		},
		{
			name:     "有认证SOCKS5",
			proxyURL: "socks5://127.0.0.1:1080",
			auth: &foundation.ProxyAuth{
				Username: "testuser",
				Password: "testpass",
			},
		},
		{
			name:     "便捷函数SOCKS5",
			proxyURL: "",
			auth:     nil,
		},
	}

	for _, proxyConfig := range proxyConfigs {
		fmt.Printf("  测试: %s\n", proxyConfig.name)

		var config *foundation.Config
		if proxyConfig.name == "便捷函数SOCKS5" {
			config = httpclient.WithSOCKS5("socks5://127.0.0.1:1080", "user", "pass")
		} else {
			config = &foundation.Config{
				Timeout:         15 * time.Second,
				MaxIdleConns:    10,
				MaxConnsPerHost: 2,
				UserAgent:       "SOCKS5-Test/1.0",

				EnableMonitoring:  false,
				EnableRetry:       false,
				EnableFingerprint: false,

				ProxyType: foundation.ProxyTypeSOCKS5,
				ProxyURL:  proxyConfig.proxyURL,
				ProxyAuth: proxyConfig.auth,

				HTTPVersion: foundation.HTTPVersionAuto,
			}
		}

		client := httpclient.NewAdvancedClient(config)

		req := &foundation.Request{
			URL:     "https://httpbin.org/ip",
			Method:  "GET",
			Headers: map[string]string{"User-Agent": "SOCKS5-Test/1.0"},
		}

		resp, err := client.Do(ctx, req)
		if err != nil {
			fmt.Printf("    ❌ %s 失败（预期的）: %v\n", proxyConfig.name, err)
		} else {
			fmt.Printf("    ✅ %s 成功: 状态码 %d\n", proxyConfig.name, resp.StatusCode)
			if len(resp.Body) > 0 && len(resp.Body) < 500 {
				fmt.Printf("      📄 响应: %s\n", string(resp.Body))
			}
		}

		client.Close()
		time.Sleep(200 * time.Millisecond)
	}
}

// testDirectTLSFingerprint 直接测试TLS指纹
func testDirectTLSFingerprint(ctx context.Context) {
	fmt.Printf("  直接验证TLS连接和指纹:\n")

	// 创建自定义TLS配置
	tlsConfig := &tls.Config{
		ServerName: "httpbin.org",
		MinVersion: tls.VersionTLS12,
		MaxVersion: tls.VersionTLS13,
		CipherSuites: []uint16{
			tls.TLS_AES_128_GCM_SHA256,
			tls.TLS_AES_256_GCM_SHA384,
			tls.TLS_CHACHA20_POLY1305_SHA256,
		},
		NextProtos: []string{"h2", "http/1.1"},
	}

	// 创建自定义传输
	transport := &http.Transport{
		TLSClientConfig: tlsConfig,
		DialTLSContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			fmt.Printf("    🔗 建立TLS连接到: %s\n", addr)
			
			dialer := &net.Dialer{Timeout: 10 * time.Second}
			conn, err := dialer.DialContext(ctx, network, addr)
			if err != nil {
				return nil, err
			}

			tlsConn := tls.Client(conn, tlsConfig)
			if err := tlsConn.HandshakeContext(ctx); err != nil {
				conn.Close()
				return nil, err
			}

			state := tlsConn.ConnectionState()
			fmt.Printf("      🔒 TLS版本: %s\n", getTLSVersionString(state.Version))
			fmt.Printf("      🔐 密码套件: %s\n", getCipherSuiteName(state.CipherSuite))
			fmt.Printf("      📋 协议: %s\n", state.NegotiatedProtocol)

			return tlsConn, nil
		},
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   30 * time.Second,
	}

	req, err := http.NewRequestWithContext(ctx, "GET", "https://httpbin.org/get", nil)
	if err != nil {
		fmt.Printf("    ❌ 创建请求失败: %v\n", err)
		return
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("    ❌ 直接TLS请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("    ✅ 直接TLS请求成功: 状态码 %d, 协议 %s\n", resp.StatusCode, resp.Proto)
}

// testProxyDialer 验证代理拨号器
func testProxyDialer(ctx context.Context) {
	fmt.Printf("  测试代理拨号器配置:\n")

	// 测试HTTP代理
	fmt.Printf("    测试HTTP代理配置:\n")
	httpConfig := httpclient.WithHTTPProxy("http://proxy.example.com:8080")
	httpClient := httpclient.NewAdvancedClient(httpConfig)
	
	req := &foundation.Request{
		URL:     "https://httpbin.org/ip",
		Method:  "GET",
		Headers: map[string]string{"User-Agent": "Proxy-Test/1.0"},
	}

	_, err := httpClient.Do(ctx, req)
	if err != nil {
		fmt.Printf("      ❌ HTTP代理失败（预期的）: %v\n", err)
	} else {
		fmt.Printf("      ✅ HTTP代理成功\n")
	}
	httpClient.Close()

	// 测试HTTPS代理
	fmt.Printf("    测试HTTPS代理配置:\n")
	httpsConfig := httpclient.WithHTTPSProxy("https://proxy.example.com:8080")
	httpsClient := httpclient.NewAdvancedClient(httpsConfig)
	
	_, err = httpsClient.Do(ctx, req)
	if err != nil {
		fmt.Printf("      ❌ HTTPS代理失败（预期的）: %v\n", err)
	} else {
		fmt.Printf("      ✅ HTTPS代理成功\n")
	}
	httpsClient.Close()
}

// testCombinedFeatures 综合测试
func testCombinedFeatures(ctx context.Context) {
	fmt.Printf("  综合测试 - 指纹 + HTTP/2 + 代理:\n")

	// 创建综合配置
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    20,
		MaxConnsPerHost: 5,
		UserAgent:       "Combined-Test/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: true,

		// HTTP/2配置
		HTTPVersion:     foundation.HTTPVersionHTTP2Preferred,
		EnableHTTP2Push: true,
		HTTP2Settings: map[string]uint32{
			"MAX_CONCURRENT_STREAMS": 50,
			"INITIAL_WINDOW_SIZE":    32768,
		},

		// 代理配置（无代理，避免连接失败）
		ProxyType: foundation.ProxyTypeNone,
		ProxyURL:  "",
		ProxyAuth: nil,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	// 测试综合功能
	testCases := []struct {
		name        string
		fingerprint string
		url         string
	}{
		{"Chrome指纹+HTTP/2", "chrome", "https://httpbin.org/get"},
		{"Firefox指纹+HTTP/2", "firefox", "https://www.google.com"},
		{"Safari指纹+HTTP/2", "safari", "https://github.com"},
	}

	for _, testCase := range testCases {
		fmt.Printf("    测试: %s\n", testCase.name)

		req := &models.Request{
			URL:        testCase.url,
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Combined-Test/1.0"},
			SpiderType: "combined-test",
			MaxRetries: 0,
			Metadata: map[string]interface{}{
				"fingerprint": testCase.fingerprint,
			},
		}

		resp, err := service.SendRequest(ctx, req)
		if err != nil {
			fmt.Printf("      ❌ %s 失败: %v\n", testCase.name, err)
			continue
		}

		selectedFingerprint := "unknown"
		if resp.Metadata != nil {
			if fp, ok := resp.Metadata["selected_fingerprint"].(string); ok {
				selectedFingerprint = fp
			}
		}

		fmt.Printf("      ✅ %s 成功: 状态码 %d, 响应时间 %v, 指纹 %s\n", 
			testCase.name, resp.StatusCode, resp.ResponseTime, selectedFingerprint)

		time.Sleep(300 * time.Millisecond)
	}

	// 打印最终统计
	stats := service.GetStats()
	fmt.Printf("    📊 综合测试统计: 总请求 %v, 成功 %v, 失败 %v\n",
		stats["total_requests"], stats["success_count"], stats["failure_count"])
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func getTLSVersionString(version uint16) string {
	switch version {
	case tls.VersionTLS10:
		return "TLS 1.0"
	case tls.VersionTLS11:
		return "TLS 1.1"
	case tls.VersionTLS12:
		return "TLS 1.2"
	case tls.VersionTLS13:
		return "TLS 1.3"
	default:
		return fmt.Sprintf("Unknown (0x%04X)", version)
	}
}

func getCipherSuiteName(suite uint16) string {
	switch suite {
	case tls.TLS_AES_128_GCM_SHA256:
		return "TLS_AES_128_GCM_SHA256"
	case tls.TLS_AES_256_GCM_SHA384:
		return "TLS_AES_256_GCM_SHA384"
	case tls.TLS_CHACHA20_POLY1305_SHA256:
		return "TLS_CHACHA20_POLY1305_SHA256"
	default:
		return fmt.Sprintf("Unknown (0x%04X)", suite)
	}
}
