// Package main HTTP/2和代理功能集成测试
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/foundation"
)

func main() {
	ctx := context.Background()

	fmt.Println("=== 请求服务 HTTP/2 和代理功能集成测试 ===")

	// 测试1: 默认配置
	fmt.Println("\n1. 测试默认配置")
	testDefaultConfig(ctx)

	// 测试2: HTTP/2 优化配置
	fmt.Println("\n2. 测试HTTP/2优化配置")
	testHTTP2Config(ctx)

	// 测试3: 代理配置
	fmt.Println("\n3. 测试代理配置")
	testProxyConfig(ctx)

	// 测试4: 混合配置（HTTP/2 + 代理）
	fmt.Println("\n4. 测试混合配置（HTTP/2 + 代理）")
	testMixedConfig(ctx)

	// 测试5: 配置更新
	fmt.Println("\n5. 测试配置更新")
	testConfigUpdate(ctx)

	fmt.Println("\n=== 所有测试完成 ===")
}

// testDefaultConfig 测试默认配置
func testDefaultConfig(ctx context.Context) {
	service := request.NewService()
	defer service.Close()

	// 创建测试请求
	req := &models.Request{
		URL:        "https://httpbin.org/get",
		Method:     "GET",
		Headers:    map[string]string{"User-Agent": "Test-Client/1.0"},
		SpiderType: "test",
		MaxRetries: 2,
	}

	// 发送请求
	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("默认配置请求失败: %v", err)
		return
	}

	fmt.Printf("默认配置 - 状态码: %d, 响应时间: %v, 响应大小: %d bytes\n",
		resp.StatusCode, resp.ResponseTime, resp.Size)

	// 打印统计信息
	stats := service.GetStats()
	fmt.Printf("统计信息: %+v\n", stats)
}

// testHTTP2Config 测试HTTP/2配置
func testHTTP2Config(ctx context.Context) {
	// 创建HTTP/2优化配置
	config := httpclient.WithHTTP2()
	config.EnableMonitoring = true
	config.EnableRetry = true
	config.MaxRetries = 2

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	// 创建测试请求
	req := &models.Request{
		URL:        "https://httpbin.org/get",
		Method:     "GET",
		Headers:    map[string]string{"User-Agent": "HTTP2-Test-Client/1.0"},
		SpiderType: "http2-test",
		MaxRetries: 2,
	}

	// 发送请求
	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("HTTP/2配置请求失败: %v", err)
		return
	}

	fmt.Printf("HTTP/2配置 - 状态码: %d, 响应时间: %v, 响应大小: %d bytes\n",
		resp.StatusCode, resp.ResponseTime, resp.Size)

	// 验证配置
	baseConfig := service.GetBaseConfig()
	fmt.Printf("HTTP版本: %s, HTTP/2推送: %t\n",
		baseConfig.HTTPVersion.String(), baseConfig.EnableHTTP2Push)
}

// testProxyConfig 测试代理配置
func testProxyConfig(ctx context.Context) {
	// 创建HTTP代理配置（使用示例代理，预期会失败）
	config := httpclient.WithHTTPProxy("http://proxy.example.com:8080")
	config.EnableMonitoring = true
	config.EnableRetry = true
	config.MaxRetries = 1 // 减少重试次数，因为代理会失败

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	// 创建测试请求
	req := &models.Request{
		URL:        "https://httpbin.org/ip",
		Method:     "GET",
		Headers:    map[string]string{"User-Agent": "Proxy-Test-Client/1.0"},
		SpiderType: "proxy-test",
		MaxRetries: 1,
	}

	// 发送请求
	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		fmt.Printf("代理配置请求失败（预期的）: %v\n", err)
	} else {
		fmt.Printf("代理配置 - 状态码: %d, 响应时间: %v\n",
			resp.StatusCode, resp.ResponseTime)
	}

	// 验证配置
	baseConfig := service.GetBaseConfig()
	fmt.Printf("代理类型: %s, 代理URL: %s\n",
		baseConfig.ProxyType.String(), baseConfig.ProxyURL)
}

// testMixedConfig 测试混合配置
func testMixedConfig(ctx context.Context) {
	// 创建混合配置：HTTP/2 + 无代理
	config := &foundation.Config{
		Timeout:         15 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Mixed-Config-Client/1.0",

		EnableMonitoring:  true,
		EnableFingerprint: false,
		EnableRetry:       true,

		MaxRetries:   2,
		RetryDelay:   500 * time.Millisecond,
		RetryBackoff: 1.5,

		// HTTP/2 配置
		HTTPVersion:     foundation.HTTPVersionHTTP2Preferred,
		EnableHTTP2Push: true,
		HTTP2Settings: map[string]uint32{
			"MAX_CONCURRENT_STREAMS": 50,
			"INITIAL_WINDOW_SIZE":    32768,
		},

		// 无代理
		ProxyType: foundation.ProxyTypeNone,
		ProxyURL:  "",
		ProxyAuth: nil,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	// 创建测试请求
	req := &models.Request{
		URL:        "https://httpbin.org/user-agent",
		Method:     "GET",
		Headers:    map[string]string{"Custom-Header": "Mixed-Config-Test"},
		SpiderType: "mixed-test",
		MaxRetries: 2,
	}

	// 发送请求
	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("混合配置请求失败: %v", err)
		return
	}

	fmt.Printf("混合配置 - 状态码: %d, 响应时间: %v, 响应大小: %d bytes\n",
		resp.StatusCode, resp.ResponseTime, resp.Size)

	// 验证配置
	baseConfig := service.GetBaseConfig()
	fmt.Printf("HTTP版本: %s, HTTP/2推送: %t, 代理类型: %s\n",
		baseConfig.HTTPVersion.String(), baseConfig.EnableHTTP2Push, baseConfig.ProxyType.String())
}

// testConfigUpdate 测试配置更新
func testConfigUpdate(ctx context.Context) {
	// 创建初始服务
	service := request.NewService()
	defer service.Close()

	fmt.Println("初始配置测试...")
	initialConfig := service.GetBaseConfig()
	fmt.Printf("初始HTTP版本: %s, 代理类型: %s\n",
		initialConfig.HTTPVersion.String(), initialConfig.ProxyType.String())

	// 发送初始请求
	req := &models.Request{
		URL:        "https://httpbin.org/get",
		Method:     "GET",
		Headers:    map[string]string{"Test-Phase": "Initial"},
		SpiderType: "config-update-test",
		MaxRetries: 1,
	}

	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("初始请求失败: %v", err)
	} else {
		fmt.Printf("初始请求 - 状态码: %d, 响应时间: %v\n",
			resp.StatusCode, resp.ResponseTime)
	}

	// 更新配置为HTTP/2
	fmt.Println("\n更新配置为HTTP/2...")
	newConfig := httpclient.WithHTTP2()
	newConfig.EnableMonitoring = true
	newConfig.EnableRetry = true
	newConfig.MaxRetries = 2

	service.SetBaseConfig(newConfig)

	// 验证配置更新
	updatedConfig := service.GetBaseConfig()
	fmt.Printf("更新后HTTP版本: %s, HTTP/2推送: %t\n",
		updatedConfig.HTTPVersion.String(), updatedConfig.EnableHTTP2Push)

	// 发送更新后的请求
	req.Headers["Test-Phase"] = "Updated"
	resp, err = service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("更新后请求失败: %v", err)
	} else {
		fmt.Printf("更新后请求 - 状态码: %d, 响应时间: %v\n",
			resp.StatusCode, resp.ResponseTime)
	}

	// 打印最终统计信息
	stats := service.GetStats()
	fmt.Printf("最终统计信息: 总请求: %v, 成功: %v, 失败: %v, 成功率: %.2f%%\n",
		stats["total_requests"], stats["success_count"], stats["failure_count"], stats["success_rate"])
}
