// Package main 动态指纹功能测试
package main

import (
	"context"
	"fmt"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/httpclient/foundation"
)

func main() {
	ctx := context.Background()

	fmt.Println("=== 动态指纹功能测试 ===")

	// 测试1: 触发动态指纹生成（通过耗尽指纹池）
	fmt.Println("\n1. 测试动态指纹生成")
	testDynamicGeneration(ctx)

	// 测试2: 测试指纹变异
	fmt.Println("\n2. 测试指纹变异")
	testFingerprintVariation(ctx)

	// 测试3: 测试不同模板的动态生成
	fmt.Println("\n3. 测试不同模板的动态生成")
	testDifferentTemplates(ctx)

	// 测试4: 测试动态指纹的性能
	fmt.Println("\n4. 测试动态指纹性能")
	testDynamicPerformance(ctx)

	// 测试5: 模拟风控场景下的动态指纹
	fmt.Println("\n5. 模拟风控场景下的动态指纹")
	testRiskControlScenario(ctx)

	fmt.Println("\n=== 测试完成 ===")
}

// testDynamicGeneration 测试动态指纹生成
func testDynamicGeneration(ctx context.Context) {
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Dynamic-Test-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: true,

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	fmt.Printf("  发送多个请求以触发动态指纹生成:\n")

	var usedFingerprints []string
	dynamicCount := 0

	// 发送多个请求，尝试耗尽指纹池以触发动态生成
	for i := 1; i <= 10; i++ {
		req := &models.Request{
			URL:        "https://httpbin.org/get",
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Dynamic-Test-Client/1.0"},
			SpiderType: "dynamic-test",
			MaxRetries: 0,
			Metadata: map[string]interface{}{
				"fingerprint": "default",
				// 强制轮转以增加动态生成的可能性
				"fingerprint_switch_required": true,
			},
		}

		resp, err := service.SendRequest(ctx, req)
		if err != nil {
			fmt.Printf("    ❌ 请求 %d 失败: %v\n", i, err)
			continue
		}

		currentFingerprint := "unknown"
		isDynamic := false

		if resp.Metadata != nil {
			if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
				currentFingerprint = selectedProfile
			}
			if dynamic, ok := resp.Metadata["is_dynamic_fingerprint"].(bool); ok {
				isDynamic = dynamic
			}
		}

		status := "静态"
		if isDynamic {
			status = "动态"
			dynamicCount++
		}

		fmt.Printf("    请求 %d: 状态码 %d, 指纹: %s (%s)\n",
			i, resp.StatusCode, currentFingerprint, status)

		usedFingerprints = append(usedFingerprints, currentFingerprint)

		// 短暂延迟
		time.Sleep(100 * time.Millisecond)
	}

	fmt.Printf("  📊 统计: 总请求 10, 动态指纹 %d, 静态指纹 %d\n",
		dynamicCount, 10-dynamicCount)
	fmt.Printf("  🔄 指纹多样性: %d 个不同指纹\n", countUnique(usedFingerprints))
}

// testFingerprintVariation 测试指纹变异
func testFingerprintVariation(ctx context.Context) {
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Variation-Test-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: true,

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	fmt.Printf("  测试指纹变异功能:\n")

	// 模拟风控检测，触发指纹变异
	for i := 1; i <= 5; i++ {
		req := &models.Request{
			URL:        "https://httpbin.org/get",
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Variation-Test-Client/1.0"},
			SpiderType: "variation-test",
			MaxRetries: 0,
			Metadata: map[string]interface{}{
				"fingerprint": "chrome",
				// 模拟风控检测
				"tls_risk_detected":           true,
				"tls_risk_level":              "moderate",
				"fingerprint_switch_required": true,
			},
		}

		resp, err := service.SendRequest(ctx, req)
		if err != nil {
			fmt.Printf("    ❌ 变异请求 %d 失败: %v\n", i, err)
			continue
		}

		currentFingerprint := "unknown"
		riskDetected := false

		if resp.Metadata != nil {
			if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
				currentFingerprint = selectedProfile
			}
			if risk, ok := resp.Metadata["tls_risk_detected"].(bool); ok {
				riskDetected = risk
			}
		}

		riskStatus := "正常"
		if riskDetected {
			riskStatus = "风控"
		}

		fmt.Printf("    变异 %d: 状态码 %d, 指纹: %s (%s)\n",
			i, resp.StatusCode, currentFingerprint, riskStatus)

		time.Sleep(200 * time.Millisecond)
	}
}

// testDifferentTemplates 测试不同模板的动态生成
func testDifferentTemplates(ctx context.Context) {
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Template-Test-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: true,

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	// 测试不同的指纹池/模板
	templates := []string{"chrome", "firefox", "safari", "edge", "minimal"}

	for _, template := range templates {
		fmt.Printf("  测试模板: %s\n", template)

		req := &models.Request{
			URL:        "https://httpbin.org/get",
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Template-Test-Client/1.0"},
			SpiderType: "template-test",
			MaxRetries: 0,
			Metadata: map[string]interface{}{
				"fingerprint": template,
				// 强制使用动态生成
				"force_dynamic_generation": true,
			},
		}

		resp, err := service.SendRequest(ctx, req)
		if err != nil {
			fmt.Printf("    ❌ 模板 %s 失败: %v\n", template, err)
			continue
		}

		currentFingerprint := "unknown"
		if resp.Metadata != nil {
			if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
				currentFingerprint = selectedProfile
			}
		}

		fmt.Printf("    ✅ 模板 %s: 状态码 %d, 生成指纹: %s\n",
			template, resp.StatusCode, currentFingerprint)
	}
}

// testDynamicPerformance 测试动态指纹性能
func testDynamicPerformance(ctx context.Context) {
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Performance-Test-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: true,

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	fmt.Printf("  性能测试 - 静态 vs 动态指纹:\n")

	// 测试静态指纹性能
	staticTimes := make([]time.Duration, 0, 5)
	for i := 0; i < 5; i++ {
		req := &models.Request{
			URL:        "https://httpbin.org/get",
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Performance-Test-Client/1.0"},
			SpiderType: "perf-static",
			MaxRetries: 0,
			Metadata: map[string]interface{}{
				"fingerprint": "default",
			},
		}

		start := time.Now()
		resp, err := service.SendRequest(ctx, req)
		duration := time.Since(start)

		if err == nil && resp.StatusCode == 200 {
			staticTimes = append(staticTimes, duration)
		}
	}

	// 测试动态指纹性能（通过强制轮转）
	dynamicTimes := make([]time.Duration, 0, 5)
	for i := 0; i < 5; i++ {
		req := &models.Request{
			URL:        "https://httpbin.org/get",
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Performance-Test-Client/1.0"},
			SpiderType: "perf-dynamic",
			MaxRetries: 0,
			Metadata: map[string]interface{}{
				"fingerprint":                 "default",
				"fingerprint_switch_required": true,
				"force_dynamic_generation":    true,
			},
		}

		start := time.Now()
		resp, err := service.SendRequest(ctx, req)
		duration := time.Since(start)

		if err == nil && resp.StatusCode == 200 {
			dynamicTimes = append(dynamicTimes, duration)
		}
	}

	// 计算平均时间
	staticAvg := calculateAverage(staticTimes)
	dynamicAvg := calculateAverage(dynamicTimes)

	fmt.Printf("    静态指纹平均响应时间: %v\n", staticAvg)
	fmt.Printf("    动态指纹平均响应时间: %v\n", dynamicAvg)

	if dynamicAvg > staticAvg {
		overhead := float64(dynamicAvg-staticAvg) / float64(staticAvg) * 100
		fmt.Printf("    动态指纹开销: +%.2f%%\n", overhead)
	} else {
		fmt.Printf("    动态指纹性能相当或更好\n")
	}
}

// testRiskControlScenario 模拟风控场景下的动态指纹
func testRiskControlScenario(ctx context.Context) {
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Risk-Test-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       true, // 启用重试以测试风控场景
		MaxRetries:        2,
		EnableFingerprint: true,

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	fmt.Printf("  模拟风控检测和指纹切换:\n")

	// 模拟一系列风控场景
	scenarios := []struct {
		name       string
		riskLevel  string
		statusCode int
	}{
		{"轻微风控", "low", 403},
		{"中等风控", "moderate", 429},
		{"严重风控", "severe", 403},
	}

	for _, scenario := range scenarios {
		fmt.Printf("    场景: %s\n", scenario.name)

		req := &models.Request{
			URL:        "https://httpbin.org/get",
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Risk-Test-Client/1.0"},
			SpiderType: "risk-test",
			MaxRetries: 2,
			Metadata: map[string]interface{}{
				"fingerprint": "default",
				// 模拟风控检测
				"tls_risk_detected":           true,
				"tls_risk_level":              scenario.riskLevel,
				"tls_risk_status_code":        scenario.statusCode,
				"fingerprint_switch_required": true,
			},
		}

		resp, err := service.SendRequest(ctx, req)
		if err != nil {
			fmt.Printf("      ❌ %s 失败: %v\n", scenario.name, err)
			continue
		}

		currentFingerprint := "unknown"
		if resp.Metadata != nil {
			if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
				currentFingerprint = selectedProfile
			}
		}

		fmt.Printf("      ✅ %s: 状态码 %d, 切换到指纹: %s\n",
			scenario.name, resp.StatusCode, currentFingerprint)
	}
}

// 辅助函数
func countUnique(items []string) int {
	unique := make(map[string]bool)
	for _, item := range items {
		unique[item] = true
	}
	return len(unique)
}

func calculateAverage(times []time.Duration) time.Duration {
	if len(times) == 0 {
		return 0
	}

	var total time.Duration
	for _, t := range times {
		total += t
	}

	return total / time.Duration(len(times))
}
