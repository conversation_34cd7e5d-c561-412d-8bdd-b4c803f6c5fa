// Package main 指纹功能验证测试
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/foundation"
)

func main() {
	ctx := context.Background()

	fmt.Println("=== 指纹功能验证测试 ===")

	// 测试1: 不使用指纹
	fmt.Println("\n1. 测试不使用指纹")
	testWithoutFingerprint(ctx)

	// 测试2: 使用默认指纹池
	fmt.Println("\n2. 测试使用默认指纹池")
	testWithDefaultFingerprint(ctx)

	// 测试3: 使用特定指纹池
	fmt.Println("\n3. 测试使用特定指纹池")
	testWithSpecificFingerprint(ctx)

	// 测试4: 测试指纹轮转
	fmt.Println("\n4. 测试指纹轮转")
	testFingerprintRotation(ctx)

	// 测试5: 直接使用 httpclient 测试指纹
	fmt.Println("\n5. 直接使用 httpclient 测试指纹")
	testDirectHTTPClientFingerprint(ctx)

	fmt.Println("\n=== 测试完成 ===")
}

// testWithoutFingerprint 测试不使用指纹
func testWithoutFingerprint(ctx context.Context) {
	// 创建禁用指纹的配置
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "No-Fingerprint-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: false, // 禁用指纹

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	req := &models.Request{
		URL:        "https://httpbin.org/get",
		Method:     "GET",
		Headers:    map[string]string{"User-Agent": "No-Fingerprint-Client/1.0"},
		SpiderType: "no-fingerprint-test",
		MaxRetries: 0,
		// 不设置指纹相关元数据
	}

	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("无指纹请求失败: %v", err)
		return
	}

	fmt.Printf("  ✅ 状态码: %d, 响应时间: %v\n", resp.StatusCode, resp.ResponseTime)
	fmt.Printf("  📋 配置: 指纹功能禁用\n")
}

// testWithDefaultFingerprint 测试使用默认指纹池
func testWithDefaultFingerprint(ctx context.Context) {
	// 创建启用指纹的配置
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Default-Fingerprint-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: true, // 启用指纹

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	req := &models.Request{
		URL:        "https://httpbin.org/get",
		Method:     "GET",
		Headers:    map[string]string{"User-Agent": "Default-Fingerprint-Client/1.0"},
		SpiderType: "default-fingerprint-test",
		MaxRetries: 0,
		Metadata: map[string]interface{}{
			"fingerprint": "default", // 使用默认指纹池
		},
	}

	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		log.Printf("默认指纹请求失败: %v", err)
		return
	}

	fmt.Printf("  ✅ 状态码: %d, 响应时间: %v\n", resp.StatusCode, resp.ResponseTime)
	fmt.Printf("  🔒 指纹池: default\n")
	
	// 检查响应元数据中的指纹信息
	if resp.Metadata != nil {
		if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
			fmt.Printf("  🎯 选择的指纹: %s\n", selectedProfile)
		}
	}
}

// testWithSpecificFingerprint 测试使用特定指纹池
func testWithSpecificFingerprint(ctx context.Context) {
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Specific-Fingerprint-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: true,

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	// 测试不同的指纹池
	fingerprintPools := []string{"chrome", "firefox", "safari", "edge"}

	for _, pool := range fingerprintPools {
		fmt.Printf("\n  测试指纹池: %s\n", pool)
		
		req := &models.Request{
			URL:        "https://httpbin.org/get",
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Specific-Fingerprint-Client/1.0"},
			SpiderType: "specific-fingerprint-test",
			MaxRetries: 0,
			Metadata: map[string]interface{}{
				"fingerprint": pool,
			},
		}

		resp, err := service.SendRequest(ctx, req)
		if err != nil {
			fmt.Printf("    ❌ 指纹池 %s 请求失败: %v\n", pool, err)
			continue
		}

		fmt.Printf("    ✅ 状态码: %d, 响应时间: %v\n", resp.StatusCode, resp.ResponseTime)
		
		// 检查响应元数据中的指纹信息
		if resp.Metadata != nil {
			if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
				fmt.Printf("    🎯 选择的指纹: %s\n", selectedProfile)
			}
		}
	}
}

// testFingerprintRotation 测试指纹轮转
func testFingerprintRotation(ctx context.Context) {
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Rotation-Test-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: true,

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	fmt.Printf("  发送多个请求测试指纹轮转:\n")

	var lastFingerprint string
	rotationCount := 0

	for i := 1; i <= 5; i++ {
		req := &models.Request{
			URL:        "https://httpbin.org/get",
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "Rotation-Test-Client/1.0"},
			SpiderType: "rotation-test",
			MaxRetries: 0,
			Metadata: map[string]interface{}{
				"fingerprint": "default",
			},
		}

		resp, err := service.SendRequest(ctx, req)
		if err != nil {
			fmt.Printf("    ❌ 请求 %d 失败: %v\n", i, err)
			continue
		}

		currentFingerprint := "unknown"
		if resp.Metadata != nil {
			if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
				currentFingerprint = selectedProfile
			}
		}

		fmt.Printf("    请求 %d: 状态码 %d, 指纹: %s", i, resp.StatusCode, currentFingerprint)
		
		if lastFingerprint != "" && currentFingerprint != lastFingerprint {
			rotationCount++
			fmt.Printf(" (轮转)")
		}
		fmt.Printf("\n")

		lastFingerprint = currentFingerprint
		
		// 短暂延迟
		time.Sleep(100 * time.Millisecond)
	}

	fmt.Printf("  📊 指纹轮转次数: %d/4\n", rotationCount)
}

// testDirectHTTPClientFingerprint 直接使用 httpclient 测试指纹
func testDirectHTTPClientFingerprint(ctx context.Context) {
	// 创建启用指纹的客户端
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Direct-Fingerprint-Client/1.0",

		EnableMonitoring:  false,
		EnableRetry:       false,
		EnableFingerprint: true, // 启用指纹

		HTTPVersion: foundation.HTTPVersionAuto,
		ProxyType:   foundation.ProxyTypeNone,
	}

	client := httpclient.NewAdvancedClient(config)
	defer client.Close()

	// 创建带指纹元数据的请求
	req := &foundation.Request{
		URL:     "https://httpbin.org/get",
		Method:  "GET",
		Headers: map[string]string{"User-Agent": "Direct-Fingerprint-Client/1.0"},
		Meta: map[string]interface{}{
			"fingerprint": "default", // 指定使用默认指纹池
		},
	}

	resp, err := client.Do(ctx, req)
	if err != nil {
		log.Printf("直接指纹客户端请求失败: %v", err)
		return
	}

	fmt.Printf("  ✅ 直接客户端状态码: %d\n", resp.StatusCode)
	fmt.Printf("  ⏱️  直接客户端响应时间: %v\n", resp.ResponseTime)
	fmt.Printf("  📦 直接客户端响应大小: %d bytes\n", len(resp.Body))

	// 检查是否有指纹相关的元数据返回
	if resp.Request != nil && resp.Request.Meta != nil {
		if selectedProfile, ok := resp.Request.Meta["selected_fingerprint"].(string); ok {
			fmt.Printf("  🎯 直接客户端选择的指纹: %s\n", selectedProfile)
		} else {
			fmt.Printf("  ⚠️  直接客户端未返回指纹信息\n")
		}
	}

	// 测试不同指纹池
	fmt.Printf("\n  测试不同指纹池:\n")
	pools := []string{"chrome", "firefox", "safari"}
	
	for _, pool := range pools {
		req.Meta["fingerprint"] = pool
		
		resp, err := client.Do(ctx, req)
		if err != nil {
			fmt.Printf("    ❌ 指纹池 %s 失败: %v\n", pool, err)
			continue
		}
		
		fmt.Printf("    ✅ 指纹池 %s: 状态码 %d\n", pool, resp.StatusCode)
		
		if resp.Request != nil && resp.Request.Meta != nil {
			if selectedProfile, ok := resp.Request.Meta["selected_fingerprint"].(string); ok {
				fmt.Printf("      🎯 选择的指纹: %s\n", selectedProfile)
			}
		}
	}
}
