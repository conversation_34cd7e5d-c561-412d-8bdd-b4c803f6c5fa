// Package main HTTP/2 验证测试
package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"net/http"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/foundation"
)

func main() {
	ctx := context.Background()

	fmt.Println("=== HTTP/2 协议验证测试 ===")

	// 测试1: 验证 HTTP/2 是否真正启用
	fmt.Println("\n1. 测试 HTTP/2 配置和实际协议版本")
	testHTTP2Protocol(ctx)

	// 测试2: 对比 HTTP/1.1 和 HTTP/2 的性能
	fmt.Println("\n2. 对比 HTTP/1.1 vs HTTP/2 性能")
	compareHTTPVersions(ctx)

	// 测试3: 验证 HTTP/2 特性
	fmt.Println("\n3. 验证 HTTP/2 特性")
	testHTTP2Features(ctx)

	fmt.Println("\n=== 测试完成 ===")
}

// testHTTP2Protocol 测试 HTTP/2 协议
func testHTTP2Protocol(ctx context.Context) {
	// 创建强制 HTTP/2 配置
	config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "HTTP2-Test-Client/1.0",

		EnableMonitoring:  true,
		EnableRetry:       false, // 禁用重试以获得准确结果
		EnableFingerprint: false, // 禁用指纹以简化测试

		// 强制使用 HTTP/2
		HTTPVersion:     foundation.HTTPVersionHTTP2Only,
		EnableHTTP2Push: true,
		HTTP2Settings: map[string]uint32{
			"MAX_CONCURRENT_STREAMS": 100,
			"INITIAL_WINDOW_SIZE":    65535,
		},

		ProxyType: foundation.ProxyTypeNone,
	}

	service := request.NewServiceWithConfig(config)
	defer service.Close()

	// 测试支持 HTTP/2 的网站
	testURLs := []string{
		"https://httpbin.org/get",
		"https://www.google.com",
		"https://github.com",
	}

	for _, url := range testURLs {
		fmt.Printf("\n测试 URL: %s\n", url)
		
		req := &models.Request{
			URL:        url,
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": "HTTP2-Test-Client/1.0"},
			SpiderType: "http2-test",
			MaxRetries: 0,
		}

		start := time.Now()
		resp, err := service.SendRequest(ctx, req)
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("  ❌ 请求失败: %v\n", err)
			continue
		}

		fmt.Printf("  ✅ 状态码: %d\n", resp.StatusCode)
		fmt.Printf("  ⏱️  响应时间: %v\n", duration)
		fmt.Printf("  📦 响应大小: %d bytes\n", resp.Size)
		
		// 检查响应头中的协议信息
		if resp.Headers != nil {
			if server := resp.Headers["Server"]; len(server) > 0 {
				fmt.Printf("  🖥️  服务器: %s\n", server[0])
			}
		}
	}

	// 验证配置
	baseConfig := service.GetBaseConfig()
	fmt.Printf("\n📋 配置验证:\n")
	fmt.Printf("  HTTP版本: %s\n", baseConfig.HTTPVersion.String())
	fmt.Printf("  HTTP/2推送: %t\n", baseConfig.EnableHTTP2Push)
	fmt.Printf("  HTTP/2设置: %+v\n", baseConfig.HTTP2Settings)
}

// compareHTTPVersions 对比不同 HTTP 版本的性能
func compareHTTPVersions(ctx context.Context) {
	testURL := "https://httpbin.org/get"
	
	// HTTP/1.1 配置
	http1Config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "HTTP1-Test-Client/1.0",

		EnableMonitoring:  false,
		EnableRetry:       false,
		EnableFingerprint: false,

		HTTPVersion:     foundation.HTTPVersionHTTP1Only,
		EnableHTTP2Push: false,
		ProxyType:       foundation.ProxyTypeNone,
	}

	// HTTP/2 配置
	http2Config := &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "HTTP2-Test-Client/1.0",

		EnableMonitoring:  false,
		EnableRetry:       false,
		EnableFingerprint: false,

		HTTPVersion:     foundation.HTTPVersionHTTP2Only,
		EnableHTTP2Push: true,
		ProxyType:       foundation.ProxyTypeNone,
	}

	// 测试 HTTP/1.1
	fmt.Printf("🔄 测试 HTTP/1.1...\n")
	http1Times := testMultipleRequests(ctx, http1Config, testURL, 5)
	
	// 测试 HTTP/2
	fmt.Printf("🔄 测试 HTTP/2...\n")
	http2Times := testMultipleRequests(ctx, http2Config, testURL, 5)

	// 计算平均时间
	http1Avg := calculateAverage(http1Times)
	http2Avg := calculateAverage(http2Times)

	fmt.Printf("\n📊 性能对比结果:\n")
	fmt.Printf("  HTTP/1.1 平均响应时间: %v\n", http1Avg)
	fmt.Printf("  HTTP/2   平均响应时间: %v\n", http2Avg)
	
	if http2Avg < http1Avg {
		improvement := float64(http1Avg-http2Avg) / float64(http1Avg) * 100
		fmt.Printf("  🚀 HTTP/2 性能提升: %.2f%%\n", improvement)
	} else {
		degradation := float64(http2Avg-http1Avg) / float64(http1Avg) * 100
		fmt.Printf("  ⚠️  HTTP/2 性能下降: %.2f%%\n", degradation)
	}
}

// testMultipleRequests 测试多个请求
func testMultipleRequests(ctx context.Context, config *foundation.Config, url string, count int) []time.Duration {
	service := request.NewServiceWithConfig(config)
	defer service.Close()

	times := make([]time.Duration, 0, count)

	for i := 0; i < count; i++ {
		req := &models.Request{
			URL:        url,
			Method:     "GET",
			Headers:    map[string]string{"User-Agent": config.UserAgent},
			SpiderType: "perf-test",
			MaxRetries: 0,
		}

		start := time.Now()
		resp, err := service.SendRequest(ctx, req)
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("  ❌ 请求 %d 失败: %v\n", i+1, err)
			continue
		}

		if resp.StatusCode == 200 {
			times = append(times, duration)
			fmt.Printf("  ✅ 请求 %d: %v\n", i+1, duration)
		} else {
			fmt.Printf("  ⚠️  请求 %d: 状态码 %d\n", i+1, resp.StatusCode)
		}
	}

	return times
}

// calculateAverage 计算平均时间
func calculateAverage(times []time.Duration) time.Duration {
	if len(times) == 0 {
		return 0
	}

	var total time.Duration
	for _, t := range times {
		total += t
	}

	return total / time.Duration(len(times))
}

// testHTTP2Features 测试 HTTP/2 特性
func testHTTP2Features(ctx context.Context) {
	// 创建自定义 HTTP 客户端来验证 HTTP/2
	fmt.Printf("🔍 直接验证 HTTP/2 协议支持...\n")

	// 创建支持 HTTP/2 的客户端
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				NextProtos: []string{"h2", "http/1.1"},
			},
			ForceAttemptHTTP2: true,
		},
		Timeout: 30 * time.Second,
	}

	req, err := http.NewRequestWithContext(ctx, "GET", "https://httpbin.org/get", nil)
	if err != nil {
		log.Printf("创建请求失败: %v", err)
		return
	}

	req.Header.Set("User-Agent", "HTTP2-Direct-Test/1.0")

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("直接 HTTP/2 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("  ✅ 状态码: %d\n", resp.StatusCode)
	fmt.Printf("  🔗 协议版本: %s\n", resp.Proto)
	fmt.Printf("  🔒 TLS版本: %s\n", getTLSVersion(resp.TLS))
	
	if resp.ProtoMajor == 2 {
		fmt.Printf("  🎉 成功使用 HTTP/2 协议!\n")
	} else {
		fmt.Printf("  ⚠️  使用的是 HTTP/%d.%d 协议\n", resp.ProtoMajor, resp.ProtoMinor)
	}

	// 测试我们的 httpclient 是否真正使用 HTTP/2
	fmt.Printf("\n🔍 验证我们的 httpclient HTTP/2 实现...\n")
	
	config := httpclient.WithHTTP2()
	config.EnableMonitoring = false
	config.EnableRetry = false
	config.EnableFingerprint = false

	ourClient := httpclient.NewAdvancedClient(config)
	defer ourClient.Close()

	ourReq := &foundation.Request{
		URL:     "https://httpbin.org/get",
		Method:  "GET",
		Headers: map[string]string{"User-Agent": "Our-HTTP2-Client/1.0"},
	}

	ourResp, err := ourClient.Do(ctx, ourReq)
	if err != nil {
		log.Printf("我们的 HTTP/2 客户端请求失败: %v", err)
		return
	}

	fmt.Printf("  ✅ 我们的客户端状态码: %d\n", ourResp.StatusCode)
	fmt.Printf("  ⏱️  我们的客户端响应时间: %v\n", ourResp.ResponseTime)
	fmt.Printf("  📦 我们的客户端响应大小: %d bytes\n", len(ourResp.Body))
}

// getTLSVersion 获取 TLS 版本信息
func getTLSVersion(state *tls.ConnectionState) string {
	if state == nil {
		return "未知"
	}

	switch state.Version {
	case tls.VersionTLS10:
		return "TLS 1.0"
	case tls.VersionTLS11:
		return "TLS 1.1"
	case tls.VersionTLS12:
		return "TLS 1.2"
	case tls.VersionTLS13:
		return "TLS 1.3"
	default:
		return fmt.Sprintf("TLS 0x%04X", state.Version)
	}
}
