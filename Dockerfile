# Monitor项目 - 简化Docker镜像
# 使用预编译的二进制文件

FROM alpine:3.19

# 安装运行时依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S monitor && \
    adduser -u 1001 -S monitor -G monitor

# 设置工作目录
WORKDIR /app

# 创建必要的目录并设置权限
RUN mkdir -p /app/configs /app/logs /app/data && \
    chown -R monitor:monitor /app && \
    chmod -R 775 /app/logs /app/data

# 复制预编译的二进制文件
COPY --chown=monitor:monitor monitor /app/monitor

# 复制配置文件
COPY --chown=monitor:monitor configs/ /app/configs/

# 复制启动脚本
COPY --chown=monitor:monitor entrypoint.sh /app/entrypoint.sh

# 最终权限确认
RUN chown -R monitor:monitor /app && \
    chmod -R 775 /app/logs /app/data && \
    chmod +x /app/entrypoint.sh

# 设置时区
ENV TZ=Asia/Shanghai

# 切换到非root用户
USER monitor

# 设置启动脚本和默认命令
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["/app/monitor"]
