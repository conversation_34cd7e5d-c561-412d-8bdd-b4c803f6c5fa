"""
Popmart爬虫实现
"""
import json
from time import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Set
from urllib.parse import quote_plus

from models import ProductItem
from utils.logger import logger
from .base import BaseSpider

class PopmartSpider(BaseSpider):
    """
    Popmart商品爬虫组件
    
    负责Popmart商品数据的爬取和解析
    """

    def __init__(self):
        """
        初始化Popmart爬虫组件
        """
        super().__init__("popmart")

    def get_product_ids(self, config: Dict[str, Any]) -> Set[str]:
        """
        获取产品ID集合
        
        从配置中获取产品ID集合
        
        Args:
            config: 爬虫配置
            
        Returns:
            产品ID集合
        """
        product_ids = config.get('product_ids', [])
        return {str(product_id) for product_id in product_ids}
    
    def prepare_request(self, product_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备请求参数
        
        为产品ID构建请求参数
        
        Args:
            product_id: 产品ID
            config: 爬虫配置
            
        Returns:
            请求参数
        """
       
        url = config['api'] + "/shop/v1/shop/productDetails"

        # 构建基础请求参数，签名将由中间件处理
        params = {'spuId': product_id}

        # 获取中间件需要的配置参数
        spider_type = config.get('spider_type', 'popmart')
        proxies = config.get('proxies', ['default'])[0] if config.get('proxies') else 'default'
        cookies = config.get('cookies', ['default'])[0] if config.get('cookies') else None
        country = config.get('country', 'US')
        name = config.get('name')
        return {
            'url': url,
            'method': 'GET',
            'params': params,
            'product_id': product_id,
            # 中间件需要的参数
            'spider_type': spider_type,
            'proxy_group': proxies,
            'cookie_group': cookies,
            'country': country,
            'name': name
        }
    
    def parse_response(self, response: Dict[str, Any], config: Dict[str, Any] = None) -> Optional[List[Any]]:
        """
        解析响应数据
        
        从API响应中提取产品信息
        
        Args:
            response: 响应数据
            config: 爬虫配置（可选）
            
        Returns:
            解析后的产品数据列表，或None（如果解析失败）
        """
        config = config or {}
        
        try:
            # 获取产品ID
            product_id = response.get('product_id')
            if not product_id:
                self.logger.error("响应中没有产品ID", self.spider_type)
                return None
            
            # 解析响应内容
            content = json.loads(response.get('content', '{}'))
            
            # 提取商品信息
            product_data = self.extract_product_data(product_id, content, config['name'])
            if not product_data:
                return None
                
            skus_data = product_data.get('skus', [])
            title = product_data.get('title', '')
            upTime = product_data.get('upTime', int(time()))
            downTime = product_data.get('downTime', 0)
            isPublish = product_data.get('isPublish', False)
            releaseDate = f'<t:{upTime}:f>' if upTime>int(time()) else ''

            # 没有发布的产品
            if downTime < int(time()) and not isPublish:
                return None

            product_items=[]
            for sku in skus_data:
                sku_item = self.process_sku_data(product_id, title, sku, releaseDate)
                if sku_item:
                    product_items.append(self.parse_product(sku_item, config))

            return product_items
        except Exception as e:
            self.logger.error(f"监控 {config['name']}: 解析响应失败: {str(e)}", self.spider_type)
            return None
    
    def extract_product_data(self, productId: str, content: dict, monitor_name: str) -> Optional[Dict]:
        """从JSON响应中提取产品数据"""
        if content.get('code') != 'OK' or not content.get('data'):
            self.logger.error(f"监控 {monitor_name}: 商品数据无效: {productId}", self.spider_type)
            return None
            
        return content.get('data', {})
        
    def process_sku_data(self, productId: str, title: str, sku: dict, releaseDate: str = '', monitor_name: str = 'monitor') -> Optional[Dict]:
        """处理单个SKU数据"""
        try:
            sku_id = sku.get('id')
            if not sku_id:
                return None
                
            sku_title = sku.get('title', '')
            discount_price = sku.get('discountPrice', 0)
            
            if discount_price:
                discount_price = discount_price / 100
            stock_info = sku.get('stock', {})
            stock = stock_info.get('onlineStock', 0)
            if not stock:
                stock = stock_info.get('onlineLockStock', 0)

            inStock = stock > 0
            image_url = sku.get('mainImage', '')
            
            return {
                "productId": productId,
                "skuId": sku_id,
                'addition': sku_title,
                "title": f"{title} - {sku_title}",
                "price": str(discount_price),
                "image_url": image_url,
                "stock": stock,
                "inStock": inStock,
                'releaseDate': releaseDate
            }
        except Exception as e:
            self.logger.error(f"监控 {monitor_name}: 处理SKU数据失败: {sku_id if 'sku_id' in locals() else '未知'}, {str(e)}", self.spider_type)
            return None
    
    def parse_product(self, product_data: dict, config: Dict[str, Any]) -> Optional[ProductItem]:
        """解析单个商品数据"""
        try: 
            productId = product_data.get('productId')
            if not productId:
                return None
                
            skuId = product_data.get('skuId')
            product_url = f'{config["url"]}/products/{productId}?skuId={skuId}'
            title = product_data.get('title')
            stock = product_data.get('stock', 0)
            inStock = product_data.get('inStock', False)
            price_str = product_data.get('price', '0')
            
            # 提取价格和货币
            try:
                price_value = float(price_str)
                currency = config.get('currency', '$')  # Popmart默认使用CNY
            except ValueError:
                price_value = 0.0
                currency = config.get('currency', '$')
            
            # 获取通知组配置
            notifications = config.get('notifications', [])
            
            # 获取国家信息
            country = config.get('name', 'US')
            
            # 创建产品数据项
            return ProductItem(
                # 基础信息
                productId=productId,
                title=title,
                url=product_url,
                platform='popmart',
                
                # 价格信息
                price=price_value,
                currency=currency,
                
                # 库存信息
                stock=stock,
                inStock=inStock,
                availability='In Stock' if inStock else 'Out of Stock',
                country=country,
                siteUrl=config['url'],
                
                # 可选参数
                skuId=skuId,
                offerId=None,
                
                # 商品详情
                imageUrl=product_data.get('image_url'),
                addition=product_data.get('addition'),
                releaseDate=product_data.get('releaseDate'),
                
                # 通知配置
                notifications=notifications,
                
                # 系统元数据
                crawledAt=datetime.now(timezone.utc).isoformat(),
                metadata={
                    'spider_type': 'popmart',
                    'sku_data': product_data,
                    'releaseDate': product_data.get('releaseDate')
                }
            )
            
        except Exception as e:
            self.logger.error(f"监控 {config['name']}: 解析商品数据失败: {str(e)}", self.spider_type)
            return None