// Package httpclient_test HTTP响应解压缩测试
package httpclient_test

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"go-monitor/pkg/httpclient"

	"github.com/andybalholm/brotli"
)

// TestGzipDecompression 测试gzip解压缩
func TestGzipDecompression(t *testing.T) {
	originalData := "这是一个测试数据，用于验证gzip压缩和解压缩功能。This is test data for gzip compression and decompression."

	// 创建gzip压缩的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 设置gzip编码头
		w.Header().Set("Content-Encoding", "gzip")
		w.Header().Set("Content-Type", "text/plain")

		// 创建gzip压缩数据
		var buf bytes.Buffer
		gzipWriter := gzip.NewWriter(&buf)
		gzipWriter.Write([]byte(originalData))
		gzipWriter.Close()

		// 发送压缩数据
		w.WriteHeader(http.StatusOK)
		w.Write(buf.Bytes())
	}))
	defer server.Close()

	ctx := context.Background()

	t.Run("BasicClientGzipDecompression", func(t *testing.T) {
		// 使用基础客户端测试
		resp, err := httpclient.Get(ctx, server.URL, nil)
		if err != nil {
			t.Fatalf("请求失败: %v", err)
		}

		// 验证响应
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
		}

		// 验证解压缩后的数据
		if string(resp.Body) != originalData {
			t.Errorf("解压缩数据不匹配:\n期望: %s\n实际: %s", originalData, string(resp.Body))
		}

		t.Logf("✅ gzip解压缩成功，数据长度: %d", len(resp.Body))
	})

	t.Run("PoolClientGzipDecompression", func(t *testing.T) {
		// 使用连接池客户端测试
		resp, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
		if err != nil {
			t.Fatalf("请求失败: %v", err)
		}

		// 验证响应
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
		}

		// 验证解压缩后的数据
		if string(resp.Body) != originalData {
			t.Errorf("解压缩数据不匹配:\n期望: %s\n实际: %s", originalData, string(resp.Body))
		}

		t.Logf("✅ 连接池模式gzip解压缩成功")
	})

	// 清理连接池
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("清理连接池失败: %v", err)
		}
	}()
}

// TestDeflateDecompression 测试deflate解压缩
func TestDeflateDecompression(t *testing.T) {
	originalData := "这是deflate压缩测试数据。This is deflate compression test data."

	// 创建deflate压缩的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Encoding", "deflate")
		w.Header().Set("Content-Type", "text/plain")

		// 创建deflate压缩数据
		var buf bytes.Buffer
		deflateWriter, _ := flate.NewWriter(&buf, flate.DefaultCompression)
		deflateWriter.Write([]byte(originalData))
		deflateWriter.Close()

		w.WriteHeader(http.StatusOK)
		w.Write(buf.Bytes())
	}))
	defer server.Close()

	ctx := context.Background()

	resp, err := httpclient.Get(ctx, server.URL, nil)
	if err != nil {
		t.Fatalf("请求失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}

	if string(resp.Body) != originalData {
		t.Errorf("deflate解压缩数据不匹配:\n期望: %s\n实际: %s", originalData, string(resp.Body))
	}

	t.Logf("✅ deflate解压缩成功，数据长度: %d", len(resp.Body))
}

// TestBrotliDecompression 测试Brotli解压缩
func TestBrotliDecompression(t *testing.T) {
	originalData := "这是Brotli压缩测试数据。This is Brotli compression test data with more content to make compression effective."

	// 创建Brotli压缩的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Encoding", "br")
		w.Header().Set("Content-Type", "text/plain")

		// 创建Brotli压缩数据
		var buf bytes.Buffer
		brotliWriter := brotli.NewWriter(&buf)
		brotliWriter.Write([]byte(originalData))
		brotliWriter.Close()

		w.WriteHeader(http.StatusOK)
		w.Write(buf.Bytes())
	}))
	defer server.Close()

	ctx := context.Background()

	resp, err := httpclient.Get(ctx, server.URL, nil)
	if err != nil {
		t.Fatalf("请求失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}

	if string(resp.Body) != originalData {
		t.Errorf("Brotli解压缩数据不匹配:\n期望: %s\n实际: %s", originalData, string(resp.Body))
	}

	t.Logf("✅ Brotli解压缩成功，数据长度: %d", len(resp.Body))
}

// TestNoCompression 测试无压缩响应
func TestNoCompression(t *testing.T) {
	originalData := "这是无压缩的测试数据。This is uncompressed test data."

	// 创建无压缩的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/plain")
		// 不设置Content-Encoding头

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(originalData))
	}))
	defer server.Close()

	ctx := context.Background()

	resp, err := httpclient.Get(ctx, server.URL, nil)
	if err != nil {
		t.Fatalf("请求失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}

	if string(resp.Body) != originalData {
		t.Errorf("无压缩数据不匹配:\n期望: %s\n实际: %s", originalData, string(resp.Body))
	}

	t.Logf("✅ 无压缩数据处理成功，数据长度: %d", len(resp.Body))
}

// TestMixedCompressionTypes 测试混合压缩类型
func TestMixedCompressionTypes(t *testing.T) {
	testCases := []struct {
		name       string
		encoding   string
		data       string
		compressFn func([]byte) []byte
	}{
		{
			name:     "gzip",
			encoding: "gzip",
			data:     "gzip测试数据",
			compressFn: func(data []byte) []byte {
				var buf bytes.Buffer
				w := gzip.NewWriter(&buf)
				w.Write(data)
				w.Close()
				return buf.Bytes()
			},
		},
		{
			name:     "deflate",
			encoding: "deflate",
			data:     "deflate测试数据",
			compressFn: func(data []byte) []byte {
				var buf bytes.Buffer
				w, _ := flate.NewWriter(&buf, flate.DefaultCompression)
				w.Write(data)
				w.Close()
				return buf.Bytes()
			},
		},
		{
			name:     "brotli",
			encoding: "br",
			data:     "brotli测试数据",
			compressFn: func(data []byte) []byte {
				var buf bytes.Buffer
				w := brotli.NewWriter(&buf)
				w.Write(data)
				w.Close()
				return buf.Bytes()
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建压缩数据
			compressedData := tc.compressFn([]byte(tc.data))

			// 创建测试服务器
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Encoding", tc.encoding)
				w.Header().Set("Content-Type", "text/plain")
				w.WriteHeader(http.StatusOK)
				w.Write(compressedData)
			}))
			defer server.Close()

			ctx := context.Background()
			resp, err := httpclient.Get(ctx, server.URL, nil)
			if err != nil {
				t.Fatalf("%s 请求失败: %v", tc.name, err)
			}

			if resp.StatusCode != http.StatusOK {
				t.Errorf("%s 期望状态码 200，实际 %d", tc.name, resp.StatusCode)
			}

			if string(resp.Body) != tc.data {
				t.Errorf("%s 解压缩数据不匹配:\n期望: %s\n实际: %s", tc.name, tc.data, string(resp.Body))
			}

			t.Logf("✅ %s 解压缩成功", tc.name)
		})
	}
}

// TestCompressionError 测试压缩错误处理
func TestCompressionError(t *testing.T) {
	// 创建返回无效gzip数据的服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Encoding", "gzip")
		w.Header().Set("Content-Type", "text/plain")
		w.WriteHeader(http.StatusOK)
		// 发送无效的gzip数据
		w.Write([]byte("这不是有效的gzip数据"))
	}))
	defer server.Close()

	ctx := context.Background()

	_, err := httpclient.Get(ctx, server.URL, nil)
	if err == nil {
		t.Error("期望解压缩失败，但请求成功了")
	}

	if !strings.Contains(err.Error(), "gzip") {
		t.Errorf("期望错误信息包含 'gzip'，实际: %v", err)
	}

	t.Logf("✅ 压缩错误处理正确: %v", err)
}
