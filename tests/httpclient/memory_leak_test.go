// Package httpclient_test 长时间内存泄漏测试
package httpclient_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"runtime"
	"sync"
	"testing"
	"time"

	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/client"
	"go-monitor/pkg/httpclient/foundation"
)

// TestLongRunningMemoryLeak 长时间运行内存泄漏测试
func TestLongRunningMemoryLeak(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过长时间内存泄漏测试（短测试模式）")
	}

	// 测试持续时间
	testDuration := 5 * time.Minute
	if duration := getTestDuration(); duration > 0 {
		testDuration = duration
	}

	t.Logf("开始长时间内存泄漏测试，持续时间: %v", testDuration)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟一些处理时间
		time.Sleep(1 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// 记录初始内存状态
	runtime.GC()
	runtime.GC() // 两次GC确保清理
	var initialStats runtime.MemStats
	runtime.ReadMemStats(&initialStats)

	t.Logf("初始内存状态:")
	t.Logf("  Alloc: %d bytes", initialStats.Alloc)
	t.Logf("  TotalAlloc: %d bytes", initialStats.TotalAlloc)
	t.Logf("  Sys: %d bytes", initialStats.Sys)
	t.Logf("  NumGC: %d", initialStats.NumGC)

	// 开始测试
	startTime := time.Now()
	endTime := startTime.Add(testDuration)

	var requestCount int64
	var errorCount int64
	var mu sync.Mutex

	// 启动多个goroutine进行并发测试
	const numWorkers = 10
	var wg sync.WaitGroup

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			// 每个worker创建自己的客户端
			config := &foundation.Config{
				EnableMonitoring:  true,
				EnableRetry:       true,
				EnableFingerprint: false,
				MaxRetries:        2,
				RetryDelay:        100 * time.Millisecond,
			}

			advancedClient := client.NewAdvancedClient(config)
			defer advancedClient.Close()

			ctx := context.Background()
			workerRequestCount := 0
			workerErrorCount := 0

			for time.Now().Before(endTime) {
				// 执行HTTP请求
				_, err := advancedClient.Get(ctx, server.URL, map[string]string{
					"X-Worker-ID": fmt.Sprintf("%d", workerID),
				})

				workerRequestCount++
				if err != nil {
					workerErrorCount++
				}

				// 每100个请求休息一下
				if workerRequestCount%100 == 0 {
					time.Sleep(10 * time.Millisecond)
				}
			}

			// 更新总计数
			mu.Lock()
			requestCount += int64(workerRequestCount)
			errorCount += int64(workerErrorCount)
			mu.Unlock()

			t.Logf("Worker %d 完成: 请求 %d, 错误 %d", workerID, workerRequestCount, workerErrorCount)
		}(i)
	}

	// 定期监控内存使用情况
	monitorDone := make(chan bool)
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				runtime.GC()
				var currentStats runtime.MemStats
				runtime.ReadMemStats(&currentStats)

				elapsed := time.Since(startTime)
				t.Logf("运行时间 %v - 内存状态:", elapsed.Round(time.Second))
				t.Logf("  Alloc: %d bytes (+%d)", currentStats.Alloc, int64(currentStats.Alloc)-int64(initialStats.Alloc))
				t.Logf("  TotalAlloc: %d bytes", currentStats.TotalAlloc)
				t.Logf("  Sys: %d bytes (+%d)", currentStats.Sys, int64(currentStats.Sys)-int64(initialStats.Sys))
				t.Logf("  NumGC: %d (+%d)", currentStats.NumGC, currentStats.NumGC-initialStats.NumGC)
				t.Logf("  请求总数: %d, 错误总数: %d", requestCount, errorCount)

			case <-monitorDone:
				return
			}
		}
	}()

	// 等待所有worker完成
	wg.Wait()
	close(monitorDone)

	// 强制垃圾回收
	runtime.GC()
	runtime.GC()
	runtime.GC() // 三次GC确保彻底清理

	// 等待一下让GC完成
	time.Sleep(1 * time.Second)

	// 记录最终内存状态
	var finalStats runtime.MemStats
	runtime.ReadMemStats(&finalStats)

	actualDuration := time.Since(startTime)

	t.Logf("测试完成，实际运行时间: %v", actualDuration.Round(time.Second))
	t.Logf("总请求数: %d", requestCount)
	t.Logf("总错误数: %d", errorCount)
	t.Logf("请求成功率: %.2f%%", float64(requestCount-errorCount)/float64(requestCount)*100)
	t.Logf("平均RPS: %.2f", float64(requestCount)/actualDuration.Seconds())

	t.Logf("最终内存状态:")
	t.Logf("  Alloc: %d bytes", finalStats.Alloc)
	t.Logf("  TotalAlloc: %d bytes", finalStats.TotalAlloc)
	t.Logf("  Sys: %d bytes", finalStats.Sys)
	t.Logf("  NumGC: %d", finalStats.NumGC)

	// 计算内存变化
	allocDiff := int64(finalStats.Alloc) - int64(initialStats.Alloc)
	sysDiff := int64(finalStats.Sys) - int64(initialStats.Sys)

	t.Logf("内存变化:")
	t.Logf("  Alloc差异: %d bytes", allocDiff)
	t.Logf("  Sys差异: %d bytes", sysDiff)
	t.Logf("  GC次数增加: %d", finalStats.NumGC-initialStats.NumGC)

	// 内存泄漏判断
	// 允许一定的内存增长，但不应该有显著的泄漏
	maxAllowedAllocGrowth := int64(10 * 1024 * 1024) // 10MB
	maxAllowedSysGrowth := int64(50 * 1024 * 1024)   // 50MB

	if allocDiff > maxAllowedAllocGrowth {
		t.Errorf("检测到可能的内存泄漏: Alloc增长 %d bytes，超过允许的 %d bytes",
			allocDiff, maxAllowedAllocGrowth)
	}

	if sysDiff > maxAllowedSysGrowth {
		t.Errorf("检测到可能的内存泄漏: Sys增长 %d bytes，超过允许的 %d bytes",
			sysDiff, maxAllowedSysGrowth)
	}

	// 计算每个请求的平均内存使用
	if requestCount > 0 {
		avgAllocPerRequest := float64(allocDiff) / float64(requestCount)
		t.Logf("每个请求平均内存增长: %.2f bytes", avgAllocPerRequest)

		if avgAllocPerRequest > 1000 { // 每个请求超过1KB增长认为有问题
			t.Errorf("每个请求内存增长过大: %.2f bytes", avgAllocPerRequest)
		}
	}

	if allocDiff <= maxAllowedAllocGrowth && sysDiff <= maxAllowedSysGrowth {
		t.Logf("✅ 内存泄漏测试通过！")
	}
}

// TestStressMemoryLeak 压力测试内存泄漏
func TestStressMemoryLeak(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过压力内存泄漏测试（短测试模式）")
	}

	testDuration := 2 * time.Minute
	t.Logf("开始压力内存泄漏测试，持续时间: %v", testDuration)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// 记录初始内存
	runtime.GC()
	var initialStats runtime.MemStats
	runtime.ReadMemStats(&initialStats)

	startTime := time.Now()
	endTime := startTime.Add(testDuration)

	var requestCount int64
	var clientCount int64

	// 不断创建和销毁客户端
	for time.Now().Before(endTime) {
		// 创建客户端
		config := &foundation.Config{
			EnableMonitoring:  true,
			EnableRetry:       true,
			EnableFingerprint: false,
		}

		advancedClient := client.NewAdvancedClient(config)
		clientCount++

		// 执行一些请求
		ctx := context.Background()
		for i := 0; i < 10; i++ {
			_, err := advancedClient.Get(ctx, server.URL, nil)
			if err == nil {
				requestCount++
			}
		}

		// 关闭客户端
		advancedClient.Close()

		// 每创建100个客户端后强制GC一次
		if clientCount%100 == 0 {
			runtime.GC()

			var currentStats runtime.MemStats
			runtime.ReadMemStats(&currentStats)
			allocDiff := int64(currentStats.Alloc) - int64(initialStats.Alloc)

			t.Logf("已创建 %d 个客户端，执行 %d 个请求，内存增长: %d bytes",
				clientCount, requestCount, allocDiff)
		}
	}

	// 最终清理和检查
	runtime.GC()
	runtime.GC()

	var finalStats runtime.MemStats
	runtime.ReadMemStats(&finalStats)

	allocDiff := int64(finalStats.Alloc) - int64(initialStats.Alloc)

	t.Logf("压力测试完成:")
	t.Logf("  创建客户端数: %d", clientCount)
	t.Logf("  执行请求数: %d", requestCount)
	t.Logf("  内存增长: %d bytes", allocDiff)

	// 压力测试的内存增长限制更严格
	maxAllowedGrowth := int64(5 * 1024 * 1024) // 5MB
	if allocDiff > maxAllowedGrowth {
		t.Errorf("压力测试检测到内存泄漏: 增长 %d bytes，超过允许的 %d bytes",
			allocDiff, maxAllowedGrowth)
	} else {
		t.Logf("✅ 压力测试内存泄漏检查通过！")
	}
}

// TestConvenienceFunctionMemoryLeak 便捷函数内存泄漏测试
func TestConvenienceFunctionMemoryLeak(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过便捷函数内存泄漏测试（短测试模式）")
	}

	testDuration := 1 * time.Minute
	t.Logf("开始便捷函数内存泄漏测试，持续时间: %v", testDuration)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// 记录初始内存
	runtime.GC()
	var initialStats runtime.MemStats
	runtime.ReadMemStats(&initialStats)

	startTime := time.Now()
	endTime := startTime.Add(testDuration)

	var requestCount int64
	ctx := context.Background()

	// 使用便捷函数进行大量请求
	for time.Now().Before(endTime) {
		// 测试各种便捷函数
		_, err1 := httpclient.Get(ctx, server.URL, nil)
		_, err2 := httpclient.Post(ctx, server.URL, []byte("test"), nil)
		_, err3 := httpclient.Put(ctx, server.URL, []byte("test"), nil)
		_, err4 := httpclient.Delete(ctx, server.URL, nil)

		if err1 == nil {
			requestCount++
		}
		if err2 == nil {
			requestCount++
		}
		if err3 == nil {
			requestCount++
		}
		if err4 == nil {
			requestCount++
		}

		// 每1000个请求检查一次内存
		if requestCount%1000 == 0 {
			runtime.GC()
			var currentStats runtime.MemStats
			runtime.ReadMemStats(&currentStats)
			allocDiff := int64(currentStats.Alloc) - int64(initialStats.Alloc)

			t.Logf("执行 %d 个便捷函数请求，内存增长: %d bytes", requestCount, allocDiff)
		}
	}

	// 最终检查
	runtime.GC()
	runtime.GC()

	var finalStats runtime.MemStats
	runtime.ReadMemStats(&finalStats)

	allocDiff := int64(finalStats.Alloc) - int64(initialStats.Alloc)

	t.Logf("便捷函数测试完成:")
	t.Logf("  执行请求数: %d", requestCount)
	t.Logf("  内存增长: %d bytes", allocDiff)

	maxAllowedGrowth := int64(2 * 1024 * 1024) // 2MB
	if allocDiff > maxAllowedGrowth {
		t.Errorf("便捷函数检测到内存泄漏: 增长 %d bytes，超过允许的 %d bytes",
			allocDiff, maxAllowedGrowth)
	} else {
		t.Logf("✅ 便捷函数内存泄漏检查通过！")
	}
}

// 辅助函数

func getTestDuration() time.Duration {
	// 可以通过环境变量设置测试时间
	// export MEMORY_LEAK_TEST_DURATION=10m
	if durationStr := os.Getenv("MEMORY_LEAK_TEST_DURATION"); durationStr != "" {
		if duration, err := time.ParseDuration(durationStr); err == nil {
			return duration
		}
	}
	return 0
}
