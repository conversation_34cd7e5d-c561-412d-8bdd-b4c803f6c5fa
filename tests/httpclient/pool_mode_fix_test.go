// Package httpclient_test 连接池模式修复测试
package httpclient_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"sync/atomic"
	"testing"
	"time"

	"go-monitor/pkg/httpclient"
)

// TestPoolModeFix 测试连接池模式修复
func TestPoolModeFix(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	ctx := context.Background()

	t.Run("BasicPoolTest", func(t *testing.T) {
		// 测试基本的连接池功能
		resp, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
		if err != nil {
			t.Fatalf("连接池模式请求失败: %v", err)
		}

		if resp.StatusCode != http.StatusOK {
			t.<PERSON><PERSON><PERSON>("期望状态码 200，实际 %d", resp.StatusCode)
		}

		if string(resp.Body) != "OK" {
			t.<PERSON><PERSON><PERSON>("期望响应体 'OK'，实际 '%s'", string(resp.Body))
		}

		t.Logf("✅ 基本连接池测试通过")
	})

	t.Run("PoolReuseTest", func(t *testing.T) {
		// 测试连接池复用
		for i := 0; i < 5; i++ {
			resp, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
			if err != nil {
				t.Errorf("第 %d 个请求失败: %v", i+1, err)
				continue
			}

			if resp.StatusCode != http.StatusOK {
				t.Errorf("第 %d 个请求状态码错误: %d", i+1, resp.StatusCode)
			}
		}

		t.Logf("✅ 连接池复用测试通过")
	})

	t.Run("ConcurrentPoolTest", func(t *testing.T) {
		// 测试并发使用连接池
		const numGoroutines = 10
		const requestsPerGoroutine = 5

		errChan := make(chan error, numGoroutines*requestsPerGoroutine)

		for i := 0; i < numGoroutines; i++ {
			go func(goroutineID int) {
				for j := 0; j < requestsPerGoroutine; j++ {
					resp, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
					if err != nil {
						errChan <- err
						return
					}

					if resp.StatusCode != http.StatusOK {
						errChan <- fmt.Errorf("goroutine %d 请求 %d 状态码错误: %d",
							goroutineID, j+1, resp.StatusCode)
						return
					}
				}
			}(i)
		}

		// 等待所有goroutine完成
		time.Sleep(2 * time.Second)
		close(errChan)

		// 检查错误
		var errors []error
		for err := range errChan {
			errors = append(errors, err)
		}

		if len(errors) > 0 {
			t.Errorf("并发测试发现 %d 个错误:", len(errors))
			for i, err := range errors {
				if i < 5 { // 只显示前5个错误
					t.Errorf("  错误 %d: %v", i+1, err)
				}
			}
		} else {
			t.Logf("✅ 并发连接池测试通过 (%d goroutines × %d requests)",
				numGoroutines, requestsPerGoroutine)
		}
	})

	t.Run("PoolVsNewClientComparison", func(t *testing.T) {
		// 对比连接池模式和新建客户端模式
		const numRequests = 20

		// 测试新建客户端模式
		start := time.Now()
		for i := 0; i < numRequests; i++ {
			_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithNewClient())
			if err != nil {
				t.Errorf("新建客户端模式第 %d 个请求失败: %v", i+1, err)
			}
		}
		newClientDuration := time.Since(start)

		// 测试连接池模式
		start = time.Now()
		for i := 0; i < numRequests; i++ {
			_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
			if err != nil {
				t.Errorf("连接池模式第 %d 个请求失败: %v", i+1, err)
			}
		}
		poolClientDuration := time.Since(start)

		t.Logf("性能对比 (%d 个请求):", numRequests)
		t.Logf("  新建客户端模式: %v (平均 %v/请求)",
			newClientDuration, newClientDuration/numRequests)
		t.Logf("  连接池模式: %v (平均 %v/请求)",
			poolClientDuration, poolClientDuration/numRequests)

		// 连接池模式应该更快（但不是绝对的，因为测试环境可能有差异）
		if poolClientDuration < newClientDuration {
			speedup := float64(newClientDuration) / float64(poolClientDuration)
			t.Logf("✅ 连接池模式比新建客户端模式快 %.2fx", speedup)
		} else {
			t.Logf("⚠️  连接池模式没有显示出明显的性能优势（可能是测试环境因素）")
		}
	})

	t.Run("AllHTTPMethods", func(t *testing.T) {
		// 测试所有HTTP方法在连接池模式下的工作情况

		// GET
		_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
		if err != nil {
			t.Errorf("GET请求失败: %v", err)
		}

		// POST
		_, err = httpclient.Post(ctx, server.URL, []byte("test"), nil, httpclient.WithPoolClient())
		if err != nil {
			t.Errorf("POST请求失败: %v", err)
		}

		// PUT
		_, err = httpclient.Put(ctx, server.URL, []byte("test"), nil, httpclient.WithPoolClient())
		if err != nil {
			t.Errorf("PUT请求失败: %v", err)
		}

		// DELETE
		_, err = httpclient.Delete(ctx, server.URL, nil, httpclient.WithPoolClient())
		if err != nil {
			t.Errorf("DELETE请求失败: %v", err)
		}

		t.Logf("✅ 所有HTTP方法在连接池模式下正常工作")
	})

	// 清理全局连接池
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("关闭全局连接池失败: %v", err)
		} else {
			t.Logf("✅ 全局连接池已正常关闭")
		}
	}()
}

// TestPoolModeLifecycle 测试连接池生命周期管理
func TestPoolModeLifecycle(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	ctx := context.Background()

	t.Run("PoolRecreationAfterClose", func(t *testing.T) {
		// 测试关闭后重新创建连接池

		// 第一次使用连接池
		_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
		if err != nil {
			t.Fatalf("第一次使用连接池失败: %v", err)
		}

		// 关闭全局连接池
		err = httpclient.CloseGlobalPool()
		if err != nil {
			t.Fatalf("关闭全局连接池失败: %v", err)
		}

		// 再次使用连接池（应该自动重新创建）
		_, err = httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
		if err != nil {
			t.Fatalf("重新创建连接池后使用失败: %v", err)
		}

		t.Logf("✅ 连接池重新创建测试通过")
	})

	// 最终清理
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("最终清理失败: %v", err)
		}
	}()
}

// TestPoolModeStability 测试连接池模式稳定性
func TestPoolModeStability(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过稳定性测试（短测试模式）")
	}

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟一些处理时间
		time.Sleep(10 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	ctx := context.Background()
	const testDuration = 30 * time.Second
	const maxConcurrency = 5

	t.Logf("开始连接池稳定性测试，持续时间: %v，最大并发: %d", testDuration, maxConcurrency)

	startTime := time.Now()
	endTime := startTime.Add(testDuration)

	var totalRequests int64
	var totalErrors int64

	// 启动多个并发goroutine
	for i := 0; i < maxConcurrency; i++ {
		go func(workerID int) {
			for time.Now().Before(endTime) {
				_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
				atomic.AddInt64(&totalRequests, 1)

				if err != nil {
					atomic.AddInt64(&totalErrors, 1)
					if atomic.LoadInt64(&totalErrors) <= 5 { // 只记录前5个错误
						t.Logf("Worker %d 请求失败: %v", workerID, err)
					}
				}

				// 短暂休息
				time.Sleep(100 * time.Millisecond)
			}
		}(i)
	}

	// 等待测试完成
	time.Sleep(testDuration + 1*time.Second)

	finalRequests := atomic.LoadInt64(&totalRequests)
	finalErrors := atomic.LoadInt64(&totalErrors)

	t.Logf("稳定性测试完成:")
	t.Logf("  总请求数: %d", finalRequests)
	t.Logf("  总错误数: %d", finalErrors)

	if finalRequests > 0 {
		errorRate := float64(finalErrors) / float64(finalRequests) * 100
		t.Logf("  错误率: %.2f%%", errorRate)

		if errorRate > 5.0 { // 错误率超过5%认为有问题
			t.Errorf("连接池稳定性测试失败，错误率过高: %.2f%%", errorRate)
		} else {
			t.Logf("✅ 连接池稳定性测试通过")
		}
	}

	// 清理
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("清理全局连接池失败: %v", err)
		}
	}()
}
