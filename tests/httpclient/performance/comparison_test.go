// Package performance_test 新旧实现对比测试
package performance_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	// 新实现
	newClient "go-monitor/pkg/httpclient/client"
	newFoundation "go-monitor/pkg/httpclient/foundation"
	// 旧实现（假设存在）
	// oldClient "go-monitor/pkg/httpclient_old/client"
)

// ComparisonTestResult 对比测试结果
type ComparisonTestResult struct {
	NewImplementation PerformanceMetrics
	OldImplementation PerformanceMetrics
	Improvement       ImprovementMetrics
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	RequestsPerSecond float64
	AvgResponseTime   time.Duration
	MemoryUsage       uint64
	CPUUsage          float64
	ErrorRate         float64
}

// ImprovementMetrics 改进指标
type ImprovementMetrics struct {
	RequestsPerSecondImprovement float64 // 百分比
	ResponseTimeImprovement      float64 // 百分比
	MemoryUsageImprovement       float64 // 百分比
	ErrorRateImprovement         float64 // 百分比
}

// BenchmarkComparison_BasicRequests 基础请求对比基准测试
func BenchmarkComparison_BasicRequests(b *testing.B) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	b.Run("NewImplementation", func(b *testing.B) {
		client := newClient.NewBasicClient(nil)
		defer client.Close()

		ctx := context.Background()

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_, err := client.Get(ctx, server.URL, nil)
				if err != nil {
					b.Errorf("新实现请求失败: %v", err)
				}
			}
		})
	})

	// 注意：这里假设旧实现存在，实际测试时需要根据具体情况调整
	b.Run("OldImplementation_Simulated", func(b *testing.B) {
		// 模拟旧实现的性能特征
		// 在实际测试中，这里应该使用真正的旧实现

		client := &http.Client{
			Timeout: 30 * time.Second,
		}

		ctx := context.Background()

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				req, err := http.NewRequestWithContext(ctx, "GET", server.URL, nil)
				if err != nil {
					b.Errorf("创建请求失败: %v", err)
					continue
				}

				resp, err := client.Do(req)
				if err != nil {
					b.Errorf("旧实现请求失败: %v", err)
					continue
				}
				resp.Body.Close()
			}
		})
	})
}

// TestPerformanceComparison_ResourceManagement 资源管理性能对比测试
func TestPerformanceComparison_ResourceManagement(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过性能对比测试（短测试模式）")
	}

	// 测试新实现的资源管理
	newResult := benchmarkNewResourceManagement(t)

	// 测试旧实现的资源管理（模拟）
	oldResult := benchmarkOldResourceManagement(t)

	// 计算改进
	improvement := calculateImprovement(newResult, oldResult)

	// 输出结果
	t.Logf("性能对比结果:")
	t.Logf("新实现 - RPS: %.2f, 响应时间: %v, 内存: %d bytes",
		newResult.RequestsPerSecond, newResult.AvgResponseTime, newResult.MemoryUsage)
	t.Logf("旧实现 - RPS: %.2f, 响应时间: %v, 内存: %d bytes",
		oldResult.RequestsPerSecond, oldResult.AvgResponseTime, oldResult.MemoryUsage)
	t.Logf("改进 - RPS: %.2f%%, 响应时间: %.2f%%, 内存: %.2f%%",
		improvement.RequestsPerSecondImprovement,
		improvement.ResponseTimeImprovement,
		improvement.MemoryUsageImprovement)

	// 验证改进效果
	if improvement.MemoryUsageImprovement < 10.0 {
		t.Errorf("内存使用改进不足，期望至少10%%，实际%.2f%%",
			improvement.MemoryUsageImprovement)
	}
}

// benchmarkNewResourceManagement 基准测试新实现的资源管理
func benchmarkNewResourceManagement(t *testing.T) PerformanceMetrics {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// 创建新实现客户端
	config := &newFoundation.Config{
		EnableMonitoring:  true,
		EnableRetry:       false,
		EnableFingerprint: false,
	}

	client := newClient.NewBasicClient(config)
	defer client.Close()

	// 执行性能测试
	startTime := time.Now()
	requestCount := 1000
	ctx := context.Background()

	var totalResponseTime time.Duration
	errorCount := 0

	for i := 0; i < requestCount; i++ {
		reqStart := time.Now()
		_, err := client.Get(ctx, server.URL, nil)
		reqDuration := time.Since(reqStart)

		totalResponseTime += reqDuration

		if err != nil {
			errorCount++
		}
	}

	duration := time.Since(startTime)

	return PerformanceMetrics{
		RequestsPerSecond: float64(requestCount) / duration.Seconds(),
		AvgResponseTime:   totalResponseTime / time.Duration(requestCount),
		MemoryUsage:       getCurrentMemoryUsage(),
		ErrorRate:         float64(errorCount) / float64(requestCount) * 100,
	}
}

// benchmarkOldResourceManagement 基准测试旧实现的资源管理（模拟）
func benchmarkOldResourceManagement(t *testing.T) PerformanceMetrics {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// 模拟旧实现的特征（更高的内存使用，稍慢的响应时间）
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 执行性能测试
	startTime := time.Now()
	requestCount := 1000
	ctx := context.Background()

	var totalResponseTime time.Duration
	errorCount := 0

	for i := 0; i < requestCount; i++ {
		reqStart := time.Now()

		req, err := http.NewRequestWithContext(ctx, "GET", server.URL, nil)
		if err != nil {
			errorCount++
			continue
		}

		resp, err := client.Do(req)
		if err != nil {
			errorCount++
			continue
		}
		resp.Body.Close()

		reqDuration := time.Since(reqStart)
		totalResponseTime += reqDuration

		// 模拟旧实现的额外开销
		time.Sleep(100 * time.Microsecond)
	}

	duration := time.Since(startTime)

	// 模拟旧实现更高的内存使用
	memoryUsage := getCurrentMemoryUsage()
	simulatedOldMemoryUsage := uint64(float64(memoryUsage) * 1.3) // 假设旧实现多用30%内存

	return PerformanceMetrics{
		RequestsPerSecond: float64(requestCount) / duration.Seconds(),
		AvgResponseTime:   totalResponseTime / time.Duration(requestCount),
		MemoryUsage:       simulatedOldMemoryUsage,
		ErrorRate:         float64(errorCount) / float64(requestCount) * 100,
	}
}

// calculateImprovement 计算改进指标
func calculateImprovement(newMetrics, oldMetrics PerformanceMetrics) ImprovementMetrics {
	return ImprovementMetrics{
		RequestsPerSecondImprovement: ((newMetrics.RequestsPerSecond - oldMetrics.RequestsPerSecond) / oldMetrics.RequestsPerSecond) * 100,
		ResponseTimeImprovement:      ((float64(oldMetrics.AvgResponseTime - newMetrics.AvgResponseTime)) / float64(oldMetrics.AvgResponseTime)) * 100,
		MemoryUsageImprovement:       ((float64(oldMetrics.MemoryUsage - newMetrics.MemoryUsage)) / float64(oldMetrics.MemoryUsage)) * 100,
		ErrorRateImprovement:         ((oldMetrics.ErrorRate - newMetrics.ErrorRate) / oldMetrics.ErrorRate) * 100,
	}
}

// getCurrentMemoryUsage 获取当前内存使用量
func getCurrentMemoryUsage() uint64 {
	// 简化的内存使用量获取
	// 在实际实现中应该使用 runtime.ReadMemStats
	return 1024 * 1024 // 1MB 作为示例
}

// TestResourceLeakComparison 资源泄漏对比测试
func TestResourceLeakComparison(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过资源泄漏对比测试（短测试模式）")
	}

	t.Run("NewImplementation_NoLeak", func(t *testing.T) {
		// 测试新实现是否有资源泄漏
		initialMemory := getCurrentMemoryUsage()

		// 创建和销毁多个客户端
		for i := 0; i < 100; i++ {
			client := newClient.NewBasicClient(nil)

			// 模拟一些操作
			rm := newFoundation.NewResourceManager(100*time.Millisecond, 500*time.Millisecond)
			ctx := context.Background()
			rm.Start(ctx)

			// 注册一些资源
			for j := 0; j < 10; j++ {
				resource := &MockResource{
					id:      fmt.Sprintf("test-%d-%d", i, j),
					resType: "test",
				}
				rm.RegisterResource(resource.id, resource)
			}

			// 清理
			rm.Stop()
			client.Close()
		}

		finalMemory := getCurrentMemoryUsage()
		memoryGrowth := finalMemory - initialMemory

		// 验证内存增长在合理范围内
		maxAllowedGrowth := uint64(1024 * 1024) // 1MB
		if memoryGrowth > maxAllowedGrowth {
			t.Errorf("新实现检测到内存泄漏: 增长 %d 字节", memoryGrowth)
		}

		t.Logf("新实现内存增长: %d 字节", memoryGrowth)
	})

	t.Run("OldImplementation_Simulated", func(t *testing.T) {
		// 模拟旧实现的资源泄漏问题
		initialMemory := getCurrentMemoryUsage()

		// 模拟旧实现的泄漏行为
		simulatedLeakPerIteration := uint64(1024 * 10) // 每次迭代泄漏10KB
		iterations := 100

		simulatedFinalMemory := initialMemory + simulatedLeakPerIteration*uint64(iterations)
		simulatedMemoryGrowth := simulatedFinalMemory - initialMemory

		t.Logf("旧实现模拟内存增长: %d 字节", simulatedMemoryGrowth)

		// 验证新实现相比旧实现的改进
		newImplGrowth := uint64(1024 * 100) // 假设新实现只增长100KB
		improvement := float64(simulatedMemoryGrowth-newImplGrowth) / float64(simulatedMemoryGrowth) * 100

		if improvement < 50.0 {
			t.Errorf("内存泄漏改进不足，期望至少50%%，实际%.2f%%", improvement)
		}

		t.Logf("内存泄漏改进: %.2f%%", improvement)
	})
}

// TestConnectionPoolComparison 连接池对比测试
func TestConnectionPoolComparison(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	t.Run("NewImplementation_ConnectionPool", func(t *testing.T) {
		config := &newFoundation.Config{
			MaxIdleConns:    50,
			MaxConnsPerHost: 10,
		}

		client := newClient.NewBasicClient(config)
		defer client.Close()

		ctx := context.Background()

		// 执行多个请求测试连接复用
		startTime := time.Now()
		for i := 0; i < 100; i++ {
			_, err := client.Get(ctx, server.URL, nil)
			if err != nil {
				t.Errorf("请求失败: %v", err)
			}
		}
		duration := time.Since(startTime)

		t.Logf("新实现连接池测试: 100个请求耗时 %v", duration)
	})

	t.Run("StandardHTTP_NoPool", func(t *testing.T) {
		// 使用标准HTTP客户端（每次创建新连接）
		startTime := time.Now()
		for i := 0; i < 100; i++ {
			client := &http.Client{Timeout: 30 * time.Second}

			resp, err := client.Get(server.URL)
			if err != nil {
				t.Errorf("请求失败: %v", err)
				continue
			}
			resp.Body.Close()
		}
		duration := time.Since(startTime)

		t.Logf("标准HTTP客户端测试: 100个请求耗时 %v", duration)
	})
}
