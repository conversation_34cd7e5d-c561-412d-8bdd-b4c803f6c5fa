// Package performance_test 性能测试和基准测试
package performance_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"runtime"
	"sync"
	"testing"
	"time"
	
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/client"
	"go-monitor/pkg/httpclient/foundation"
)

// BenchmarkBasicClient_Get 基础客户端GET请求基准测试
func BenchmarkBasicClient_Get(b *testing.B) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	// 创建客户端
	basicClient := client.NewBasicClient(nil)
	defer basicClient.Close()
	
	ctx := context.Background()
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := basicClient.Get(ctx, server.URL, nil)
			if err != nil {
				b.<PERSON><PERSON><PERSON>("请求失败: %v", err)
			}
		}
	})
}

// BenchmarkAdvancedClient_Get 高级客户端GET请求基准测试
func BenchmarkAdvancedClient_Get(b *testing.B) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	// 创建高级客户端（启用监控但禁用重试和指纹）
	config := &foundation.Config{
		EnableMonitoring:   true,
		EnableRetry:        false,
		EnableFingerprint:  false,
	}
	
	advancedClient := client.NewAdvancedClient(config)
	defer advancedClient.Close()
	
	ctx := context.Background()
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := advancedClient.Get(ctx, server.URL, nil)
			if err != nil {
				b.Errorf("请求失败: %v", err)
			}
		}
	})
}

// BenchmarkHTTPClient_Convenience 便捷函数基准测试
func BenchmarkHTTPClient_Convenience(b *testing.B) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := httpclient.Get(ctx, server.URL, nil)
			if err != nil {
				b.Errorf("请求失败: %v", err)
			}
		}
	})
}

// BenchmarkResourceManager_RegisterUnregister 资源管理器注册注销基准测试
func BenchmarkResourceManager_RegisterUnregister(b *testing.B) {
	rm := foundation.NewResourceManager(1*time.Second, 5*time.Second)
	defer rm.Stop()
	
	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		b.Fatalf("启动资源管理器失败: %v", err)
	}
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			id := fmt.Sprintf("resource-%d", i)
			resource := &MockResource{id: id, resType: "test"}
			
			// 注册资源
			err := rm.RegisterResource(id, resource)
			if err != nil {
				b.Errorf("注册资源失败: %v", err)
			}
			
			// 注销资源
			err = rm.UnregisterResource(id)
			if err != nil {
				b.Errorf("注销资源失败: %v", err)
			}
			
			i++
		}
	})
}

// MockResource 模拟资源（用于基准测试）
type MockResource struct {
	id      string
	resType string
	active  bool
	lastUsed time.Time
}

func (mr *MockResource) ID() string                           { return mr.id }
func (mr *MockResource) Type() string                         { return mr.resType }
func (mr *MockResource) IsActive() bool                       { return mr.active }
func (mr *MockResource) LastUsed() time.Time                  { return mr.lastUsed }
func (mr *MockResource) Close() error                         { return nil }
func (mr *MockResource) Stats() map[string]interface{}        { return nil }

// TestMemoryLeak_ResourceManager 内存泄漏测试 - 资源管理器
func TestMemoryLeak_ResourceManager(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过内存泄漏测试（短测试模式）")
	}
	
	// 记录初始内存使用
	runtime.GC()
	var initialStats runtime.MemStats
	runtime.ReadMemStats(&initialStats)
	
	// 创建和销毁多个资源管理器
	for i := 0; i < 100; i++ {
		rm := foundation.NewResourceManager(100*time.Millisecond, 500*time.Millisecond)
		
		ctx := context.Background()
		if err := rm.Start(ctx); err != nil {
			t.Fatalf("启动资源管理器失败: %v", err)
		}
		
		// 注册大量资源
		for j := 0; j < 50; j++ {
			id := fmt.Sprintf("resource-%d-%d", i, j)
			resource := &MockResource{id: id, resType: "test", active: true, lastUsed: time.Now()}
			rm.RegisterResource(id, resource)
		}
		
		// 停止资源管理器
		rm.Stop()
	}
	
	// 强制垃圾回收
	runtime.GC()
	runtime.GC() // 两次GC确保清理完成
	
	// 检查内存使用
	var finalStats runtime.MemStats
	runtime.ReadMemStats(&finalStats)
	
	// 计算内存增长
	memoryGrowth := finalStats.Alloc - initialStats.Alloc
	
	// 允许一定的内存增长（1MB），但不应该有显著泄漏
	maxAllowedGrowth := uint64(1024 * 1024) // 1MB
	if memoryGrowth > maxAllowedGrowth {
		t.Errorf("检测到内存泄漏: 内存增长 %d 字节，超过允许的 %d 字节", 
			memoryGrowth, maxAllowedGrowth)
	}
	
	t.Logf("内存使用情况: 初始 %d 字节, 最终 %d 字节, 增长 %d 字节", 
		initialStats.Alloc, finalStats.Alloc, memoryGrowth)
}

// TestMemoryLeak_HTTPClient 内存泄漏测试 - HTTP客户端
func TestMemoryLeak_HTTPClient(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过内存泄漏测试（短测试模式）")
	}
	
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	// 记录初始内存使用
	runtime.GC()
	var initialStats runtime.MemStats
	runtime.ReadMemStats(&initialStats)
	
	// 创建和销毁多个客户端，执行大量请求
	for i := 0; i < 50; i++ {
		basicClient := client.NewBasicClient(nil)
		
		ctx := context.Background()
		
		// 执行多个请求
		for j := 0; j < 20; j++ {
			_, err := basicClient.Get(ctx, server.URL, nil)
			if err != nil {
				t.Errorf("请求失败: %v", err)
			}
		}
		
		// 关闭客户端
		basicClient.Close()
	}
	
	// 强制垃圾回收
	runtime.GC()
	runtime.GC()
	
	// 检查内存使用
	var finalStats runtime.MemStats
	runtime.ReadMemStats(&finalStats)
	
	// 计算内存增长
	memoryGrowth := finalStats.Alloc - initialStats.Alloc
	
	// 允许一定的内存增长（2MB），但不应该有显著泄漏
	maxAllowedGrowth := uint64(2 * 1024 * 1024) // 2MB
	if memoryGrowth > maxAllowedGrowth {
		t.Errorf("检测到内存泄漏: 内存增长 %d 字节，超过允许的 %d 字节", 
			memoryGrowth, maxAllowedGrowth)
	}
	
	t.Logf("内存使用情况: 初始 %d 字节, 最终 %d 字节, 增长 %d 字节", 
		initialStats.Alloc, finalStats.Alloc, memoryGrowth)
}

// TestConcurrentAccess_ResourceManager 并发访问测试 - 资源管理器
func TestConcurrentAccess_ResourceManager(t *testing.T) {
	rm := foundation.NewResourceManager(100*time.Millisecond, 500*time.Millisecond)
	defer rm.Stop()
	
	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		t.Fatalf("启动资源管理器失败: %v", err)
	}
	
	const numGoroutines = 50
	const numOperations = 100
	
	var wg sync.WaitGroup
	wg.Add(numGoroutines)
	
	// 并发注册和注销资源
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer wg.Done()
			
			for j := 0; j < numOperations; j++ {
				id := fmt.Sprintf("concurrent-%d-%d", goroutineID, j)
				resource := &MockResource{
					id:       id,
					resType:  "test",
					active:   true,
					lastUsed: time.Now(),
				}
				
				// 注册资源
				err := rm.RegisterResource(id, resource)
				if err != nil {
					t.Errorf("并发注册资源失败: %v", err)
					continue
				}
				
				// 获取资源
				_, exists := rm.GetResource(id)
				if !exists {
					t.Errorf("并发获取资源失败: %s", id)
				}
				
				// 注销资源
				err = rm.UnregisterResource(id)
				if err != nil {
					t.Errorf("并发注销资源失败: %v", err)
				}
			}
		}(i)
	}
	
	wg.Wait()
	
	// 验证最终状态
	list := rm.ListResources()
	if len(list) != 0 {
		t.Errorf("并发测试后应该没有剩余资源，实际剩余 %d 个", len(list))
	}
}

// TestConcurrentAccess_HTTPClient 并发访问测试 - HTTP客户端
func TestConcurrentAccess_HTTPClient(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	// 创建客户端
	basicClient := client.NewBasicClient(nil)
	defer basicClient.Close()
	
	const numGoroutines = 20
	const numRequests = 50
	
	var wg sync.WaitGroup
	wg.Add(numGoroutines)
	
	// 并发执行HTTP请求
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer wg.Done()
			
			ctx := context.Background()
			
			for j := 0; j < numRequests; j++ {
				resp, err := basicClient.Get(ctx, server.URL, nil)
				if err != nil {
					t.Errorf("并发请求失败 (goroutine %d, request %d): %v", 
						goroutineID, j, err)
					continue
				}
				
				if resp.StatusCode != http.StatusOK {
					t.Errorf("并发请求状态码错误 (goroutine %d, request %d): %d", 
						goroutineID, j, resp.StatusCode)
				}
			}
		}(i)
	}
	
	wg.Wait()
}
