// Package integration_test 集成测试
package integration_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"
	
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/client"
	"go-monitor/pkg/httpclient/foundation"
)

// TestFullIntegration_BasicToAdvanced 完整集成测试：从基础到高级功能
func TestFullIntegration_BasicToAdvanced(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟不同的响应场景
		switch r.URL.Path {
		case "/success":
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("Success"))
		case "/timeout":
			time.Sleep(2 * time.Second) // 模拟慢响应
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("Slow"))
		case "/error":
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte("Server Error"))
		case "/retry":
			// 前两次失败，第三次成功
			if r.Header.Get("X-Retry-Count") == "2" {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("Success after retry"))
			} else {
				w.WriteHeader(http.StatusServiceUnavailable)
				w.Write([]byte("Service Unavailable"))
			}
		default:
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte("Not Found"))
		}
	}))
	defer server.Close()
	
	t.Run("BasicClient", func(t *testing.T) {
		basicClient := client.NewBasicClient(nil)
		defer basicClient.Close()
		
		ctx := context.Background()
		
		// 测试成功请求
		resp, err := basicClient.Get(ctx, server.URL+"/success", nil)
		if err != nil {
			t.Fatalf("基础客户端请求失败: %v", err)
		}
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
		}
		
		if string(resp.Body) != "Success" {
			t.Errorf("期望响应体 'Success'，实际 '%s'", string(resp.Body))
		}
	})
	
	t.Run("AdvancedClient_WithMonitoring", func(t *testing.T) {
		config := &foundation.Config{
			EnableMonitoring:   true,
			EnableRetry:        false,
			EnableFingerprint:  false,
		}
		
		advancedClient := client.NewAdvancedClient(config)
		defer advancedClient.Close()
		
		ctx := context.Background()
		
		// 执行多个请求
		for i := 0; i < 5; i++ {
			_, err := advancedClient.Get(ctx, server.URL+"/success", nil)
			if err != nil {
				t.Errorf("高级客户端请求 %d 失败: %v", i, err)
			}
		}
		
		// 检查监控统计
		stats := advancedClient.GetStats()
		if stats == nil {
			t.Error("监控统计不应该为空")
		}
		
		t.Logf("监控统计: %+v", stats)
	})
	
	t.Run("AdvancedClient_WithRetry", func(t *testing.T) {
		config := &foundation.Config{
			EnableMonitoring:   true,
			EnableRetry:        true,
			EnableFingerprint:  false,
			MaxRetries:         3,
			RetryDelay:         100 * time.Millisecond,
		}
		
		advancedClient := client.NewAdvancedClient(config)
		defer advancedClient.Close()
		
		ctx := context.Background()
		
		// 测试重试功能（这里简化测试，实际重试逻辑更复杂）
		_, err := advancedClient.Get(ctx, server.URL+"/error", nil)
		if err == nil {
			t.Error("期望错误响应，但请求成功了")
		}
		
		// 验证重试功能正常工作
		t.Logf("重试测试完成，错误: %v", err)
	})
}

// TestRealWorldScenario_WebScraping 真实场景测试：网页抓取
func TestRealWorldScenario_WebScraping(t *testing.T) {
	// 创建模拟的电商网站
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/products":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"products": [
					{"id": "1", "name": "Product 1", "price": 99.99},
					{"id": "2", "name": "Product 2", "price": 149.99}
				]
			}`))
		case "/product/1":
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`<html><body><h1>Product 1</h1><p>Price: $99.99</p></body></html>`))
		case "/product/2":
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`<html><body><h1>Product 2</h1><p>Price: $149.99</p></body></html>`))
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer server.Close()
	
	// 创建爬虫客户端
	spiderConfig := &MockSpiderConfig{
		platform: "test-ecommerce",
		name:     "product-scraper",
		country:  "US",
	}
	
	config := &foundation.Config{
		EnableMonitoring:   true,
		EnableRetry:        true,
		EnableFingerprint:  false,
		MaxRetries:         2,
	}
	
	spiderClient := client.NewSpiderClient(config, spiderConfig)
	defer spiderClient.Close()
	
	ctx := context.Background()
	
	// 1. 获取产品列表
	resp, err := spiderClient.Get(ctx, server.URL+"/products", map[string]string{
		"Accept": "application/json",
	})
	if err != nil {
		t.Fatalf("获取产品列表失败: %v", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}
	
	// 2. 获取具体产品页面
	productURLs := []string{"/product/1", "/product/2"}
	
	for _, productURL := range productURLs {
		resp, err := spiderClient.Get(ctx, server.URL+productURL, map[string]string{
			"Accept": "text/html",
		})
		if err != nil {
			t.Errorf("获取产品页面 %s 失败: %v", productURL, err)
			continue
		}
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("产品页面 %s 状态码错误: %d", productURL, resp.StatusCode)
		}
		
		if !strings.Contains(string(resp.Body), "Product") {
			t.Errorf("产品页面 %s 内容不正确", productURL)
		}
	}
	
	// 3. 检查爬虫统计
	stats := spiderClient.GetStats()
	if stats == nil {
		t.Error("爬虫统计不应该为空")
	}
	
	t.Logf("爬虫统计: %+v", stats)
}

// MockSpiderConfig 模拟爬虫配置
type MockSpiderConfig struct {
	platform string
	name     string
	country  string
}

func (msc *MockSpiderConfig) GetPlatform() string { return msc.platform }
func (msc *MockSpiderConfig) GetName() string     { return msc.name }
func (msc *MockSpiderConfig) GetCountry() string  { return msc.country }
func (msc *MockSpiderConfig) GetSpiderSettings() map[string]interface{} {
	return map[string]interface{}{
		"user_agent": "Test-Spider/1.0",
		"delay":      "1s",
	}
}

// TestConcurrentRequests_LoadTesting 并发请求负载测试
func TestConcurrentRequests_LoadTesting(t *testing.T) {
	// 创建测试服务器
	requestCount := int64(0)
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 原子递增请求计数
		count := requestCount
		requestCount++
		
		// 模拟一些处理时间
		time.Sleep(10 * time.Millisecond)
		
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(fmt.Sprintf("Request %d", count)))
	}))
	defer server.Close()
	
	// 创建高级客户端
	config := &foundation.Config{
		EnableMonitoring:   true,
		EnableRetry:        true,
		EnableFingerprint:  false,
		MaxIdleConns:       50,
		MaxConnsPerHost:    20,
	}
	
	advancedClient := client.NewAdvancedClient(config)
	defer advancedClient.Close()
	
	// 并发测试参数
	const numGoroutines = 20
	const requestsPerGoroutine = 50
	const totalRequests = numGoroutines * requestsPerGoroutine
	
	var wg sync.WaitGroup
	var successCount int64
	var errorCount int64
	
	startTime := time.Now()
	
	// 启动并发goroutines
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			
			ctx := context.Background()
			
			for j := 0; j < requestsPerGoroutine; j++ {
				resp, err := advancedClient.Get(ctx, server.URL, map[string]string{
					"X-Goroutine-ID": fmt.Sprintf("%d", goroutineID),
					"X-Request-ID":   fmt.Sprintf("%d-%d", goroutineID, j),
				})
				
				if err != nil {
					errorCount++
					t.Errorf("并发请求失败 (goroutine %d, request %d): %v", 
						goroutineID, j, err)
					continue
				}
				
				if resp.StatusCode != http.StatusOK {
					errorCount++
					t.Errorf("并发请求状态码错误 (goroutine %d, request %d): %d", 
						goroutineID, j, resp.StatusCode)
					continue
				}
				
				successCount++
			}
		}(i)
	}
	
	// 等待所有请求完成
	wg.Wait()
	duration := time.Since(startTime)
	
	// 验证结果
	if successCount != totalRequests {
		t.Errorf("成功请求数不匹配: 期望 %d，实际 %d", totalRequests, successCount)
	}
	
	if errorCount > 0 {
		t.Errorf("有 %d 个请求失败", errorCount)
	}
	
	// 计算性能指标
	requestsPerSecond := float64(totalRequests) / duration.Seconds()
	
	t.Logf("负载测试结果:")
	t.Logf("- 总请求数: %d", totalRequests)
	t.Logf("- 成功请求: %d", successCount)
	t.Logf("- 失败请求: %d", errorCount)
	t.Logf("- 总耗时: %v", duration)
	t.Logf("- 请求/秒: %.2f", requestsPerSecond)
	
	// 检查监控统计
	stats := advancedClient.GetStats()
	t.Logf("监控统计: %+v", stats)
	
	// 性能要求验证
	if requestsPerSecond < 100 {
		t.Errorf("性能不达标: 期望至少100 RPS，实际 %.2f RPS", requestsPerSecond)
	}
}

// TestErrorHandling_Integration 错误处理集成测试
func TestErrorHandling_Integration(t *testing.T) {
	// 创建会产生各种错误的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/timeout":
			time.Sleep(5 * time.Second) // 超过客户端超时时间
			w.WriteHeader(http.StatusOK)
		case "/500":
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte("Internal Server Error"))
		case "/404":
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte("Not Found"))
		case "/connection-close":
			// 立即关闭连接
			hj, ok := w.(http.Hijacker)
			if ok {
				conn, _, _ := hj.Hijack()
				conn.Close()
			}
		default:
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("OK"))
		}
	}))
	defer server.Close()
	
	// 创建带重试的高级客户端
	config := &foundation.Config{
		Timeout:            1 * time.Second, // 短超时用于测试
		EnableMonitoring:   true,
		EnableRetry:        true,
		EnableFingerprint:  false,
		MaxRetries:         2,
		RetryDelay:         100 * time.Millisecond,
	}
	
	advancedClient := client.NewAdvancedClient(config)
	defer advancedClient.Close()
	
	ctx := context.Background()
	
	// 测试各种错误场景
	testCases := []struct {
		name        string
		path        string
		expectError bool
		errorType   string
	}{
		{"Timeout", "/timeout", true, "timeout"},
		{"ServerError", "/500", false, ""}, // 500错误不算客户端错误
		{"NotFound", "/404", false, ""},    // 404错误不算客户端错误
		{"ConnectionClose", "/connection-close", true, "connection"},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			resp, err := advancedClient.Get(ctx, server.URL+tc.path, nil)
			
			if tc.expectError {
				if err == nil {
					t.Errorf("期望错误，但请求成功了")
				} else {
					// 验证错误类型
					if tc.errorType != "" && !strings.Contains(err.Error(), tc.errorType) {
						t.Errorf("期望错误类型包含 '%s'，实际错误: %v", tc.errorType, err)
					}
					t.Logf("正确捕获错误: %v", err)
				}
			} else {
				if err != nil {
					t.Errorf("不期望错误，但请求失败: %v", err)
				} else if resp == nil {
					t.Error("响应不应该为空")
				}
			}
		})
	}
}

// TestConvenienceFunctions_Integration 便捷函数集成测试
func TestConvenienceFunctions_Integration(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case "GET":
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("GET OK"))
		case "POST":
			w.WriteHeader(http.StatusCreated)
			w.Write([]byte("POST OK"))
		case "PUT":
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("PUT OK"))
		case "DELETE":
			w.WriteHeader(http.StatusNoContent)
		default:
			w.WriteHeader(http.StatusMethodNotAllowed)
		}
	}))
	defer server.Close()
	
	ctx := context.Background()
	
	// 测试便捷函数
	t.Run("GET", func(t *testing.T) {
		resp, err := httpclient.Get(ctx, server.URL, nil)
		if err != nil {
			t.Fatalf("GET请求失败: %v", err)
		}
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
		}
		
		if string(resp.Body) != "GET OK" {
			t.Errorf("期望响应体 'GET OK'，实际 '%s'", string(resp.Body))
		}
	})
	
	t.Run("POST", func(t *testing.T) {
		body := []byte("test data")
		resp, err := httpclient.Post(ctx, server.URL, body, nil)
		if err != nil {
			t.Fatalf("POST请求失败: %v", err)
		}
		
		if resp.StatusCode != http.StatusCreated {
			t.Errorf("期望状态码 201，实际 %d", resp.StatusCode)
		}
	})
	
	t.Run("PUT", func(t *testing.T) {
		body := []byte("update data")
		resp, err := httpclient.Put(ctx, server.URL, body, nil)
		if err != nil {
			t.Fatalf("PUT请求失败: %v", err)
		}
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
		}
	})
	
	t.Run("DELETE", func(t *testing.T) {
		resp, err := httpclient.Delete(ctx, server.URL, nil)
		if err != nil {
			t.Fatalf("DELETE请求失败: %v", err)
		}
		
		if resp.StatusCode != http.StatusNoContent {
			t.Errorf("期望状态码 204，实际 %d", resp.StatusCode)
		}
	})
}
