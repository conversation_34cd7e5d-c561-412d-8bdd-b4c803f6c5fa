// Package httpclient_test 便捷函数测试
package httpclient_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"runtime"
	"testing"
	"time"
	
	"go-monitor/pkg/httpclient"
)

// TestConvenienceFunctions_NewClientMode 测试新建客户端模式
func TestConvenienceFunctions_NewClientMode(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	
	// 测试默认模式（新建客户端）
	t.Run("DefaultMode", func(t *testing.T) {
		resp, err := httpclient.Get(ctx, server.URL, nil)
		if err != nil {
			t.Fatalf("GET请求失败: %v", err)
		}
		
		if resp.StatusCode != http.StatusOK {
			t.<PERSON><PERSON>rf("期望状态码 200，实际 %d", resp.StatusCode)
		}
		
		if string(resp.Body) != "OK" {
			t.<PERSON><PERSON><PERSON>("期望响应体 'OK'，实际 '%s'", string(resp.Body))
		}
	})
	
	// 测试显式指定新建客户端模式
	t.Run("ExplicitNewMode", func(t *testing.T) {
		resp, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithNewClient())
		if err != nil {
			t.Fatalf("GET请求失败: %v", err)
		}
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
		}
	})
	
	// 测试所有HTTP方法
	t.Run("AllMethods", func(t *testing.T) {
		// GET
		_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithNewClient())
		if err != nil {
			t.Errorf("GET请求失败: %v", err)
		}
		
		// POST
		_, err = httpclient.Post(ctx, server.URL, []byte("test"), nil, httpclient.WithNewClient())
		if err != nil {
			t.Errorf("POST请求失败: %v", err)
		}
		
		// PUT
		_, err = httpclient.Put(ctx, server.URL, []byte("test"), nil, httpclient.WithNewClient())
		if err != nil {
			t.Errorf("PUT请求失败: %v", err)
		}
		
		// DELETE
		_, err = httpclient.Delete(ctx, server.URL, nil, httpclient.WithNewClient())
		if err != nil {
			t.Errorf("DELETE请求失败: %v", err)
		}
	})
}

// TestConvenienceFunctions_PoolMode 测试连接池模式
func TestConvenienceFunctions_PoolMode(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	
	// 测试连接池模式
	t.Run("PoolMode", func(t *testing.T) {
		resp, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
		if err != nil {
			t.Fatalf("GET请求失败: %v", err)
		}
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
		}
		
		if string(resp.Body) != "OK" {
			t.Errorf("期望响应体 'OK'，实际 '%s'", string(resp.Body))
		}
	})
	
	// 测试连接池复用
	t.Run("PoolReuse", func(t *testing.T) {
		// 发送多个请求，应该复用连接池
		for i := 0; i < 5; i++ {
			resp, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
			if err != nil {
				t.Errorf("第 %d 个请求失败: %v", i+1, err)
				continue
			}
			
			if resp.StatusCode != http.StatusOK {
				t.Errorf("第 %d 个请求状态码错误: %d", i+1, resp.StatusCode)
			}
		}
	})
	
	// 清理全局连接池
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("关闭全局连接池失败: %v", err)
		}
	}()
}

// TestConvenienceFunctions_MemoryComparison 对比两种模式的内存使用
func TestConvenienceFunctions_MemoryComparison(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	const numRequests = 100
	
	// 测试新建客户端模式的内存使用
	t.Run("NewClientMemory", func(t *testing.T) {
		runtime.GC()
		var startStats runtime.MemStats
		runtime.ReadMemStats(&startStats)
		
		for i := 0; i < numRequests; i++ {
			_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithNewClient())
			if err != nil {
				t.Errorf("第 %d 个请求失败: %v", i+1, err)
			}
		}
		
		runtime.GC()
		var endStats runtime.MemStats
		runtime.ReadMemStats(&endStats)
		
		allocDiff := int64(endStats.Alloc) - int64(startStats.Alloc)
		t.Logf("新建客户端模式 - %d 个请求内存增长: %d bytes", numRequests, allocDiff)
	})
	
	// 测试连接池模式的内存使用
	t.Run("PoolClientMemory", func(t *testing.T) {
		runtime.GC()
		var startStats runtime.MemStats
		runtime.ReadMemStats(&startStats)
		
		for i := 0; i < numRequests; i++ {
			_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
			if err != nil {
				t.Errorf("第 %d 个请求失败: %v", i+1, err)
			}
		}
		
		runtime.GC()
		var endStats runtime.MemStats
		runtime.ReadMemStats(&endStats)
		
		allocDiff := int64(endStats.Alloc) - int64(startStats.Alloc)
		t.Logf("连接池模式 - %d 个请求内存增长: %d bytes", numRequests, allocDiff)
	})
	
	// 清理全局连接池
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("关闭全局连接池失败: %v", err)
		}
	}()
}

// TestConvenienceFunctions_PerformanceComparison 对比两种模式的性能
func TestConvenienceFunctions_PerformanceComparison(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	const numRequests = 50
	
	// 测试新建客户端模式的性能
	t.Run("NewClientPerformance", func(t *testing.T) {
		start := time.Now()
		
		for i := 0; i < numRequests; i++ {
			_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithNewClient())
			if err != nil {
				t.Errorf("第 %d 个请求失败: %v", i+1, err)
			}
		}
		
		duration := time.Since(start)
		t.Logf("新建客户端模式 - %d 个请求耗时: %v (平均 %v/请求)", 
			numRequests, duration, duration/numRequests)
	})
	
	// 测试连接池模式的性能
	t.Run("PoolClientPerformance", func(t *testing.T) {
		start := time.Now()
		
		for i := 0; i < numRequests; i++ {
			_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
			if err != nil {
				t.Errorf("第 %d 个请求失败: %v", i+1, err)
			}
		}
		
		duration := time.Since(start)
		t.Logf("连接池模式 - %d 个请求耗时: %v (平均 %v/请求)", 
			numRequests, duration, duration/numRequests)
	})
	
	// 清理全局连接池
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("关闭全局连接池失败: %v", err)
		}
	}()
}

// TestConvenienceFunctions_AntiDetection 测试反风控效果
func TestConvenienceFunctions_AntiDetection(t *testing.T) {
	// 创建测试服务器，模拟检测客户端特征
	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		
		// 模拟风控检测：如果是同一个连接发送多个请求，可能被检测
		if requestCount > 3 {
			// 模拟被风控
			w.WriteHeader(http.StatusTooManyRequests)
			w.Write([]byte("Rate Limited"))
			return
		}
		
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	
	t.Run("NewClientAntiDetection", func(t *testing.T) {
		requestCount = 0 // 重置计数器
		
		// 使用新建客户端模式，每次都是新连接，不容易被风控
		successCount := 0
		for i := 0; i < 5; i++ {
			resp, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithNewClient())
			if err != nil {
				t.Errorf("第 %d 个请求失败: %v", i+1, err)
				continue
			}
			
			if resp.StatusCode == http.StatusOK {
				successCount++
			}
		}
		
		t.Logf("新建客户端模式 - 成功请求数: %d/5", successCount)
		
		// 新建客户端模式应该有更好的反风控效果
		if successCount < 3 {
			t.Errorf("新建客户端模式反风控效果不佳，成功率: %d/5", successCount)
		}
	})
	
	// 清理全局连接池
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("关闭全局连接池失败: %v", err)
		}
	}()
}
