package httpclient

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"sync"
	"testing"
	"time"
)

// TestDeepNetworkFix 测试深层网络修复
func TestDeepNetworkFix(t *testing.T) {
	// 测试1：连接池优化
	t.Run("ConnectionPoolOptimization", func(t *testing.T) {
		config := &Config{
			Timeout:     10 * time.Second,
			MaxIdleConn: 100,
		}

		// 创建多个客户端测试连接池
		clients := make([]*Client, 10)
		for i := 0; i < 10; i++ {
			clients[i] = NewClient(config)
		}

		// 创建测试服务器
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			time.Sleep(100 * time.Millisecond) // 模拟处理时间
			w.WriteHeader(200)
			w.Write([]byte("ok"))
		}))
		defer server.Close()

		// 并发请求测试
		var wg sync.WaitGroup
		errors := make(chan error, 100)

		for i := 0; i < 100; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				client := clients[id%len(clients)]
				request := &Request{
					URL:    server.URL,
					Method: "GET",
				}

				ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()

				_, err := client.Do(ctx, request)
				if err != nil {
					errors <- fmt.Errorf("请求%d失败: %v", id, err)
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		// 检查错误
		errorCount := 0
		for err := range errors {
			t.Logf("并发请求错误: %v", err)
			errorCount++
		}

		if errorCount > 5 { // 允许少量错误
			t.Errorf("并发请求错误过多: %d/100", errorCount)
		}

		t.Logf("连接池优化测试完成，错误率: %d%%", errorCount)
	})

	// 测试2：连接监控
	t.Run("ConnectionMonitoring", func(t *testing.T) {
		monitor := GetConnectionMonitor()

		// 创建客户端（会自动注册到监控器）
		client := NewClient(&Config{
			Timeout: 5 * time.Second,
		})

		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(200)
		}))
		defer server.Close()

		// 发送一些请求
		for i := 0; i < 5; i++ {
			request := &Request{
				URL:    server.URL,
				Method: "GET",
			}

			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			_, err := client.Do(ctx, request)
			cancel()

			if err != nil {
				t.Logf("请求%d失败: %v", i, err)
			}
		}

		// 检查监控统计
		stats := monitor.GetStats()
		if len(stats) == 0 {
			t.Error("连接监控器没有统计信息")
		}

		t.Logf("连接监控统计: %+v", stats)

		// 测试强制清理
		monitor.ForceCleanupAll()
		t.Log("连接监控测试完成")
	})

	// 测试3：网络I/O监控
	t.Run("NetworkIOMonitoring", func(t *testing.T) {
		networkMonitor := GetNetworkIOMonitor()

		// 创建慢响应服务器
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			time.Sleep(2 * time.Second)
			w.WriteHeader(200)
		}))
		defer server.Close()

		client := NewClient(&Config{
			Timeout: 5 * time.Second,
		})

		// 启动请求
		go func() {
			request := &Request{
				URL:    server.URL,
				Method: "GET",
			}

			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			_, err := client.Do(ctx, request)
			if err != nil {
				t.Logf("慢请求失败: %v", err)
			}
		}()

		// 等待请求开始
		time.Sleep(500 * time.Millisecond)

		// 检查活跃请求
		activeCount := networkMonitor.GetActiveRequests()
		if activeCount == 0 {
			t.Error("应该有活跃请求")
		}

		t.Logf("活跃请求数: %d", activeCount)

		// 等待请求完成
		time.Sleep(3 * time.Second)

		// 测试强制清理
		networkMonitor.ForceCleanupRequests()
		t.Log("网络I/O监控测试完成")
	})

	// 测试4：TLS指纹安全模式
	t.Run("TLSFingerprintSafeMode", func(t *testing.T) {
		config := &Config{
			Timeout:              5 * time.Second,
			EnableTLSFingerprint: true,
		}

		client := NewClientWithFingerprint(config)

		request := &Request{
			URL:    "https://httpbin.org/get",
			Method: "GET",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 8*time.Second)
		defer cancel()

		start := time.Now()
		response, err := client.Do(ctx, request)
		duration := time.Since(start)

		if err != nil {
			t.Logf("TLS指纹请求失败（可能是网络问题）: %v", err)
		} else {
			t.Logf("TLS指纹请求成功，状态码: %d", response.StatusCode)
		}

		// 检查是否在合理时间内完成（不应该阻塞）
		if duration > 10*time.Second {
			t.Errorf("TLS指纹请求耗时过长：%v", duration)
		}

		t.Logf("TLS指纹安全模式测试完成，耗时：%v", duration)
	})
}

// TestGoroutineLeakPrevention 测试goroutine泄漏防护
func TestGoroutineLeakPrevention(t *testing.T) {
	// 创建会导致阻塞的服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 不发送响应，导致客户端阻塞
		time.Sleep(30 * time.Second)
		w.WriteHeader(200)
	}))
	defer server.Close()

	client := NewClient(&Config{
		Timeout: 2 * time.Second, // 短超时
	})

	const numRequests = 10
	var wg sync.WaitGroup
	errors := make(chan error, numRequests)

	// 启动多个可能阻塞的请求
	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			request := &Request{
				URL:    server.URL,
				Method: "GET",
			}

			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			_, err := client.Do(ctx, request)
			if err != nil {
				errors <- fmt.Errorf("请求%d超时（预期）: %v", id, err)
			}
		}(i)
	}

	// 等待所有请求完成（应该都超时）
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		t.Log("所有请求已完成（超时）")
	case <-time.After(10 * time.Second):
		t.Error("请求没有在预期时间内超时，可能存在goroutine泄漏")
	}

	close(errors)

	// 统计超时错误
	timeoutCount := 0
	for err := range errors {
		t.Logf("预期的超时错误: %v", err)
		timeoutCount++
	}

	if timeoutCount != numRequests {
		t.Errorf("期望%d个超时错误，实际%d个", numRequests, timeoutCount)
	}

	// 执行紧急清理
	EmergencyCleanup()
	t.Log("goroutine泄漏防护测试完成")
}

// TestEmergencyCleanup 测试紧急清理功能
func TestEmergencyCleanup(t *testing.T) {
	// 创建一些客户端和请求
	clients := make([]*Client, 5)
	for i := 0; i < 5; i++ {
		clients[i] = NewClient(&Config{
			Timeout: 10 * time.Second,
		})
	}

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(5 * time.Second)
		w.WriteHeader(200)
	}))
	defer server.Close()

	// 启动一些长时间运行的请求
	for i, client := range clients {
		go func(id int, c *Client) {
			request := &Request{
				URL:    server.URL,
				Method: "GET",
			}

			ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
			defer cancel()

			_, err := c.Do(ctx, request)
			if err != nil {
				t.Logf("长时间请求%d结果: %v", id, err)
			}
		}(i, client)
	}

	// 等待请求开始
	time.Sleep(1 * time.Second)

	// 检查活跃请求
	networkMonitor := GetNetworkIOMonitor()
	activeCount := networkMonitor.GetActiveRequests()
	t.Logf("紧急清理前活跃请求数: %d", activeCount)

	// 执行紧急清理
	EmergencyCleanup()

	// 等待清理生效
	time.Sleep(1 * time.Second)

	// 检查清理后的状态
	activeCountAfter := networkMonitor.GetActiveRequests()
	t.Logf("紧急清理后活跃请求数: %d", activeCountAfter)

	if activeCountAfter >= activeCount {
		t.Error("紧急清理没有减少活跃请求数")
	}

	t.Log("紧急清理测试完成")
}

// BenchmarkDeepNetworkPerformance 深层网络性能基准测试
func BenchmarkDeepNetworkPerformance(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(200)
		w.Write([]byte("benchmark"))
	}))
	defer server.Close()

	client := NewClient(&Config{
		Timeout:     5 * time.Second,
		MaxIdleConn: 100,
	})

	b.ResetTimer()

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			request := &Request{
				URL:    server.URL,
				Method: "GET",
			}

			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			_, err := client.Do(ctx, request)
			cancel()

			if err != nil {
				b.Errorf("基准测试请求失败: %v", err)
			}
		}
	})
}

// 示例函数已移除 - 引用了不存在的标识符
func exampleDeepNetworkFix() {
	// 创建优化的客户端
	client := NewClient(&Config{
		Timeout:     10 * time.Second,
		MaxIdleConn: 100,
	})

	request := &Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 8*time.Second)
	defer cancel()

	response, err := client.Do(ctx, request)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	fmt.Printf("请求成功，状态码: %d\n", response.StatusCode)

	// 获取连接统计
	monitor := GetConnectionMonitor()
	stats := monitor.GetStats()
	fmt.Printf("连接统计: %+v\n", stats)

	// 如果需要，可以执行紧急清理
	// EmergencyCleanup()

	// Output:
	// 请求成功，状态码: 200
}
