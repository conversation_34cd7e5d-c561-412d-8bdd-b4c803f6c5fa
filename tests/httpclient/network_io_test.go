package httpclient

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

// TestNetworkIOTimeouts 测试网络I/O超时修复
func TestNetworkIOTimeouts(t *testing.T) {
	// 测试1：响应头超时
	t.Run("ResponseHeaderTimeout", func(t *testing.T) {
		// 创建一个慢响应的测试服务器
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 延迟发送响应头
			time.Sleep(15 * time.Second)
			w.WriteHeader(200)
			w.Write([]byte("slow response"))
		}))
		defer server.Close()

		client := NewClient(&Config{
			Timeout: 20 * time.Second,
		})

		request := &Request{
			URL:    server.URL,
			Method: "GET",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 12*time.Second)
		defer cancel()

		start := time.Now()
		_, err := client.Do(ctx, request)
		duration := time.Since(start)

		// 应该在ResponseHeaderTimeout(10s)内失败
		if err == nil {
			t.Error("期望请求因响应头超时而失败")
		}

		if duration > 12*time.Second {
			t.Errorf("响应头超时时间过长：%v", duration)
		}

		t.Logf("响应头超时测试完成，耗时：%v，错误：%v", duration, err)
	})

	// 测试2：TLS握手超时
	t.Run("TLSHandshakeTimeout", func(t *testing.T) {
		// 使用一个不存在的HTTPS地址来触发TLS握手超时
		client := NewClient(&Config{
			Timeout: 20 * time.Second,
		})

		request := &Request{
			URL:    "https://192.0.2.1:443", // RFC5737测试地址，应该无法连接
			Method: "GET",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		start := time.Now()
		_, err := client.Do(ctx, request)
		duration := time.Since(start)

		// 应该在TLSHandshakeTimeout(5s)内失败
		if err == nil {
			t.Error("期望请求因TLS握手超时而失败")
		}

		if duration > 8*time.Second {
			t.Errorf("TLS握手超时时间过长：%v", duration)
		}

		t.Logf("TLS握手超时测试完成，耗时：%v，错误：%v", duration, err)
	})

	// 测试3：连接建立超时
	t.Run("DialTimeout", func(t *testing.T) {
		client := NewClient(&Config{
			Timeout: 20 * time.Second,
		})

		request := &Request{
			URL:    "http://192.0.2.1:80", // RFC5737测试地址，应该无法连接
			Method: "GET",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		start := time.Now()
		_, err := client.Do(ctx, request)
		duration := time.Since(start)

		// 应该在DialTimeout(5s)内失败
		if err == nil {
			t.Error("期望请求因连接超时而失败")
		}

		if duration > 8*time.Second {
			t.Errorf("连接超时时间过长：%v", duration)
		}

		t.Logf("连接超时测试完成，耗时：%v，错误：%v", duration, err)
	})

	// 测试4：上下文取消保护
	t.Run("ContextCancellation", func(t *testing.T) {
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			time.Sleep(10 * time.Second)
			w.WriteHeader(200)
		}))
		defer server.Close()

		client := NewClient(&Config{
			Timeout: 30 * time.Second,
		})

		request := &Request{
			URL:    server.URL,
			Method: "GET",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		start := time.Now()
		_, err := client.Do(ctx, request)
		duration := time.Since(start)

		// 应该因为上下文取消而快速失败
		if err == nil {
			t.Error("期望请求因上下文取消而失败")
		}

		if duration > 4*time.Second {
			t.Errorf("上下文取消响应时间过长：%v", duration)
		}

		t.Logf("上下文取消测试完成，耗时：%v，错误：%v", duration, err)
	})
}

// TestTLSFingerprintTimeout 测试TLS指纹配置超时
func TestTLSFingerprintTimeout(t *testing.T) {
	// 启用TLS指纹
	config := &Config{
		Timeout:              10 * time.Second,
		EnableTLSFingerprint: true,
	}

	client := NewClientWithFingerprint(config)

	request := &Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	start := time.Now()
	response, err := client.Do(ctx, request)
	duration := time.Since(start)

	if err != nil {
		t.Logf("TLS指纹请求失败（可能是网络问题）: %v", err)
	} else {
		t.Logf("TLS指纹请求成功，状态码: %d", response.StatusCode)
	}

	// 检查是否在合理时间内完成
	if duration > 20*time.Second {
		t.Errorf("TLS指纹请求耗时过长：%v", duration)
	}

	t.Logf("TLS指纹超时测试完成，耗时：%v", duration)
}

// TestTimeoutConfiguration 测试超时配置
func TestTimeoutConfiguration(t *testing.T) {
	testCases := []struct {
		name           string
		configTimeout  time.Duration
		requestTimeout time.Duration
		expectedMin    time.Duration
		expectedMax    time.Duration
	}{
		{
			name:           "NormalTimeout",
			configTimeout:  10 * time.Second,
			requestTimeout: 0,
			expectedMin:    5 * time.Second,  // 最小超时
			expectedMax:    10 * time.Second, // 配置超时
		},
		{
			name:           "RequestOverride",
			configTimeout:  10 * time.Second,
			requestTimeout: 5 * time.Second,
			expectedMin:    5 * time.Second, // 请求超时
			expectedMax:    5 * time.Second, // 请求超时
		},
		{
			name:           "TooLongTimeout",
			configTimeout:  120 * time.Second,
			requestTimeout: 0,
			expectedMin:    60 * time.Second, // 最大超时限制
			expectedMax:    60 * time.Second, // 最大超时限制
		},
		{
			name:           "TooShortTimeout",
			configTimeout:  1 * time.Second,
			requestTimeout: 0,
			expectedMin:    5 * time.Second, // 最小超时限制
			expectedMax:    5 * time.Second, // 最小超时限制
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &Config{
				Timeout: tc.configTimeout,
			}

			requestConfig := &RequestConfig{}
			if tc.requestTimeout > 0 {
				requestConfig.Timeout = tc.requestTimeout
			}

			httpClient, err := CreateClientForRequest(config, requestConfig)
			if err != nil {
				t.Fatalf("创建客户端失败: %v", err)
			}

			// 检查实际超时设置
			actualTimeout := httpClient.Timeout
			if actualTimeout < tc.expectedMin || actualTimeout > tc.expectedMax {
				t.Errorf("超时配置不符合预期，实际：%v，期望范围：%v-%v",
					actualTimeout, tc.expectedMin, tc.expectedMax)
			}

			t.Logf("超时配置测试通过，实际超时：%v", actualTimeout)
		})
	}
}

// BenchmarkNetworkIOPerformance 网络I/O性能基准测试
func BenchmarkNetworkIOPerformance(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(200)
		w.Write([]byte("benchmark response"))
	}))
	defer server.Close()

	client := NewClient(&Config{
		Timeout: 10 * time.Second,
	})

	request := &Request{
		URL:    server.URL,
		Method: "GET",
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		_, err := client.Do(ctx, request)
		cancel()

		if err != nil {
			b.Fatalf("基准测试请求失败: %v", err)
		}
	}
}

// 示例函数已移除 - 引用了不存在的标识符
func exampleNetworkIOFix() {
	// 创建配置了优化超时的客户端
	client := NewClient(&Config{
		Timeout:     15 * time.Second, // 总超时
		MaxIdleConn: 50,               // 连接池大小
	})

	request := &Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
	}

	// 使用上下文控制超时
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	response, err := client.Do(ctx, request)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	fmt.Printf("请求成功，状态码: %d\n", response.StatusCode)

	// Output:
	// 请求成功，状态码: 200
}
