package httpclient

import (
	"context"
	"math/rand"
	"net/http"
	"net/http/httptest"
	"sync"
	"testing"
	"time"
)

// TestBalancedNetworkFix 测试平衡的网络修复
func TestBalancedNetworkFix(t *testing.T) {
	// 测试1：超时配置平衡性
	t.Run("BalancedTimeoutConfig", func(t *testing.T) {
		config := &Config{
			Timeout:     20 * time.Second,
			MaxIdleConn: 100,
		}

		httpClient, err := CreateClientForRequest(config, nil)
		if err != nil {
			t.Fatalf("创建客户端失败: %v", err)
		}

		transport := httpClient.Transport.(*http.Transport)

		// 验证平衡的超时配置
		if transport.TLSHandshakeTimeout != 10*time.Second {
			t.Errorf("TLS握手超时应为10s，实际为%v", transport.TLSHandshakeTimeout)
		}

		if transport.ResponseHeaderTimeout != 15*time.Second {
			t.<PERSON><PERSON><PERSON>("响应头超时应为15s，实际为%v", transport.ResponseHeaderTimeout)
		}

		if transport.IdleConnTimeout != 90*time.Second {
			t.Errorf("空闲连接超时应为90s，实际为%v", transport.IdleConnTimeout)
		}

		t.Logf("平衡超时配置验证通过: TLS=%v, Header=%v, Idle=%v",
			transport.TLSHandshakeTimeout, transport.ResponseHeaderTimeout, transport.IdleConnTimeout)
	})

	// 测试2：上下文管理优化
	t.Run("SmartContextManagement", func(t *testing.T) {
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			time.Sleep(2 * time.Second) // 模拟处理时间
			w.WriteHeader(200)
			w.Write([]byte("context test"))
		}))
		defer server.Close()

		client := NewClient(&Config{
			Timeout: 10 * time.Second,
		})

		// 测试有超时的上下文
		t.Run("WithTimeout", func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			request := &Request{
				URL:    server.URL,
				Method: "GET",
			}

			start := time.Now()
			response, err := client.Do(ctx, request)
			duration := time.Since(start)

			if err != nil {
				t.Logf("带超时上下文请求失败（可能是预期的）: %v", err)
			} else {
				t.Logf("带超时上下文请求成功，状态码: %d，耗时: %v", response.StatusCode, duration)
			}

			// 应该在5秒左右完成（上下文超时）或2秒左右完成（服务器响应）
			if duration > 6*time.Second {
				t.Errorf("请求耗时过长: %v", duration)
			}
		})

		// 测试无超时的上下文
		t.Run("WithoutTimeout", func(t *testing.T) {
			ctx := context.Background()

			request := &Request{
				URL:    server.URL,
				Method: "GET",
			}

			start := time.Now()
			response, err := client.Do(ctx, request)
			duration := time.Since(start)

			if err != nil {
				t.Errorf("无超时上下文请求失败: %v", err)
			} else {
				t.Logf("无超时上下文请求成功，状态码: %d，耗时: %v", response.StatusCode, duration)
			}

			// 应该在2秒左右完成（服务器响应时间）
			if duration > 4*time.Second {
				t.Errorf("请求耗时过长: %v", duration)
			}
		})
	})

	// 测试3：连接清理优化
	t.Run("OptimizedConnectionCleanup", func(t *testing.T) {
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(200)
			w.Write([]byte("cleanup test"))
		}))
		defer server.Close()

		client := NewClient(&Config{
			Timeout: 5 * time.Second,
		})

		// 发送一些请求创建连接
		for i := 0; i < 5; i++ {
			request := &Request{
				URL:    server.URL,
				Method: "GET",
			}

			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			_, err := client.Do(ctx, request)
			cancel()

			if err != nil {
				t.Logf("请求%d失败: %v", i, err)
			}
		}

		// 检查连接监控
		monitor := GetConnectionMonitor()
		stats := monitor.GetStats()
		t.Logf("连接统计: %+v", stats)

		// 测试清理不会影响活跃请求
		networkMonitor := GetNetworkIOMonitor()
		initialActive := networkMonitor.GetActiveRequests()

		// 启动一个长时间请求
		go func() {
			request := &Request{
				URL:    server.URL + "?delay=true",
				Method: "GET",
			}

			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			client.Do(ctx, request)
		}()

		time.Sleep(500 * time.Millisecond) // 等待请求开始

		// 尝试清理连接
		monitor.ForceCleanupAll()

		// 检查活跃请求是否受影响
		activeAfterCleanup := networkMonitor.GetActiveRequests()
		t.Logf("清理前活跃请求: %d, 清理后活跃请求: %d", initialActive, activeAfterCleanup)

		// 活跃请求不应该被强制清理
		if activeAfterCleanup == 0 && initialActive > 0 {
			t.Error("连接清理不应该影响活跃请求")
		}
	})

	// 测试4：长时间运行稳定性
	t.Run("LongRunningStability", func(t *testing.T) {
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 随机延迟
			delay := time.Duration(100+rand.Intn(300)) * time.Millisecond
			time.Sleep(delay)
			w.WriteHeader(200)
			w.Write([]byte("stability test"))
		}))
		defer server.Close()

		client := NewClient(&Config{
			Timeout:     15 * time.Second,
			MaxIdleConn: 50,
		})

		const numRequests = 30
		const duration = 30 * time.Second

		var wg sync.WaitGroup
		successCount := int64(0)
		errorCount := int64(0)
		var mu sync.Mutex

		start := time.Now()

		// 持续发送请求30秒
		for time.Since(start) < duration {
			if successCount+errorCount >= numRequests {
				break
			}

			wg.Add(1)
			go func() {
				defer wg.Done()

				request := &Request{
					URL:    server.URL,
					Method: "GET",
				}

				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()

				_, err := client.Do(ctx, request)

				mu.Lock()
				if err != nil {
					errorCount++
				} else {
					successCount++
				}
				mu.Unlock()
			}()

			time.Sleep(1 * time.Second) // 每秒一个请求
		}

		wg.Wait()
		totalDuration := time.Since(start)

		successRate := float64(successCount) / float64(successCount+errorCount) * 100
		t.Logf("长时间运行测试结果:")
		t.Logf("  运行时间: %v", totalDuration)
		t.Logf("  总请求数: %d", successCount+errorCount)
		t.Logf("  成功请求: %d", successCount)
		t.Logf("  失败请求: %d", errorCount)
		t.Logf("  成功率: %.1f%%", successRate)

		if successRate < 90 {
			t.Errorf("长时间运行成功率过低: %.1f%%", successRate)
		}

		// 检查最终状态
		networkMonitor := GetNetworkIOMonitor()
		finalActive := networkMonitor.GetActiveRequests()
		t.Logf("最终活跃请求数: %d", finalActive)
	})
}

// TestContextCancellationHandling 测试上下文取消处理
func TestContextCancellationHandling(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(10 * time.Second) // 长时间处理
		w.WriteHeader(200)
	}))
	defer server.Close()

	client := NewClient(&Config{
		Timeout: 20 * time.Second,
	})

	// 测试主动取消
	t.Run("ActiveCancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())

		request := &Request{
			URL:    server.URL,
			Method: "GET",
		}

		// 2秒后取消请求
		go func() {
			time.Sleep(2 * time.Second)
			cancel()
		}()

		start := time.Now()
		_, err := client.Do(ctx, request)
		duration := time.Since(start)

		if err == nil {
			t.Error("期望请求因取消而失败")
		}

		if duration > 3*time.Second {
			t.Errorf("取消响应时间过长: %v", duration)
		}

		t.Logf("主动取消测试完成，耗时: %v，错误: %v", duration, err)
	})

	// 测试超时取消
	t.Run("TimeoutCancellation", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		request := &Request{
			URL:    server.URL,
			Method: "GET",
		}

		start := time.Now()
		_, err := client.Do(ctx, request)
		duration := time.Since(start)

		if err == nil {
			t.Error("期望请求因超时而失败")
		}

		if duration > 4*time.Second {
			t.Errorf("超时响应时间过长: %v", duration)
		}

		t.Logf("超时取消测试完成，耗时: %v，错误: %v", duration, err)
	})
}

// 示例函数已移除 - 引用了不存在的标识符
