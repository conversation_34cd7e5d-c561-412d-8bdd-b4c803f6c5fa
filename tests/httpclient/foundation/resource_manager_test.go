// Package foundation_test 资源管理器测试
package foundation_test

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"go-monitor/pkg/httpclient/foundation"
)

// MockResource 模拟资源
type MockResource struct {
	id       string
	resType  string
	active   bool
	lastUsed time.Time
	closed   bool
	mu       sync.RWMutex
}

func NewMockResource(id, resType string) *MockResource {
	return &MockResource{
		id:       id,
		resType:  resType,
		active:   true,
		lastUsed: time.Now(),
		closed:   false,
	}
}

func (mr *MockResource) ID() string {
	return mr.id
}

func (mr *MockResource) Type() string {
	return mr.resType
}

func (mr *MockResource) IsActive() bool {
	mr.mu.RLock()
	defer mr.mu.RUnlock()
	return mr.active && !mr.closed
}

func (mr *MockResource) LastUsed() time.Time {
	mr.mu.RLock()
	defer mr.mu.RUnlock()
	return mr.lastUsed
}

func (mr *MockResource) Close() error {
	mr.mu.Lock()
	defer mr.mu.Unlock()
	mr.closed = true
	mr.active = false
	return nil
}

func (mr *MockResource) Stats() map[string]interface{} {
	mr.mu.RLock()
	defer mr.mu.RUnlock()
	return map[string]interface{}{
		"id":        mr.id,
		"type":      mr.resType,
		"active":    mr.active,
		"last_used": mr.lastUsed,
		"closed":    mr.closed,
	}
}

func (mr *MockResource) SetActive(active bool) {
	mr.mu.Lock()
	defer mr.mu.Unlock()
	mr.active = active
}

func (mr *MockResource) UpdateLastUsed() {
	mr.mu.Lock()
	defer mr.mu.Unlock()
	mr.lastUsed = time.Now()
}

func TestResourceManager_RegisterResource(t *testing.T) {
	rm := foundation.NewResourceManager(1*time.Second, 5*time.Second)
	defer rm.Stop()

	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		t.Fatalf("启动资源管理器失败: %v", err)
	}

	// 测试注册资源
	resource := NewMockResource("test-1", "mock")
	err := rm.RegisterResource("test-1", resource)
	if err != nil {
		t.Errorf("注册资源失败: %v", err)
	}

	// 测试获取资源
	retrieved, exists := rm.GetResource("test-1")
	if !exists {
		t.Error("资源不存在")
	}

	if retrieved.ID() != "test-1" {
		t.Errorf("资源ID不匹配: 期望 test-1, 实际 %s", retrieved.ID())
	}

	// 测试重复注册
	err = rm.RegisterResource("test-1", resource)
	if err == nil {
		t.Error("重复注册应该失败")
	}
}

func TestResourceManager_UnregisterResource(t *testing.T) {
	rm := foundation.NewResourceManager(1*time.Second, 5*time.Second)
	defer rm.Stop()

	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		t.Fatalf("启动资源管理器失败: %v", err)
	}

	// 注册资源
	resource := NewMockResource("test-2", "mock")
	err := rm.RegisterResource("test-2", resource)
	if err != nil {
		t.Fatalf("注册资源失败: %v", err)
	}

	// 注销资源
	err = rm.UnregisterResource("test-2")
	if err != nil {
		t.Errorf("注销资源失败: %v", err)
	}

	// 验证资源已被关闭
	if !resource.closed {
		t.Error("资源应该被关闭")
	}

	// 验证资源不存在
	_, exists := rm.GetResource("test-2")
	if exists {
		t.Error("资源应该不存在")
	}

	// 测试注销不存在的资源
	err = rm.UnregisterResource("non-existent")
	if err == nil {
		t.Error("注销不存在的资源应该失败")
	}
}

func TestResourceManager_ListResources(t *testing.T) {
	rm := foundation.NewResourceManager(1*time.Second, 5*time.Second)
	defer rm.Stop()

	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		t.Fatalf("启动资源管理器失败: %v", err)
	}

	// 注册多个资源
	resources := []string{"test-3", "test-4", "test-5"}
	for _, id := range resources {
		resource := NewMockResource(id, "mock")
		err := rm.RegisterResource(id, resource)
		if err != nil {
			t.Fatalf("注册资源 %s 失败: %v", id, err)
		}
	}

	// 获取资源列表
	list := rm.ListResources()
	if len(list) != len(resources) {
		t.Errorf("资源数量不匹配: 期望 %d, 实际 %d", len(resources), len(list))
	}

	// 验证所有资源都在列表中
	resourceMap := make(map[string]bool)
	for _, id := range list {
		resourceMap[id] = true
	}

	for _, id := range resources {
		if !resourceMap[id] {
			t.Errorf("资源 %s 不在列表中", id)
		}
	}
}

func TestResourceManager_Cleanup(t *testing.T) {
	rm := foundation.NewResourceManager(100*time.Millisecond, 200*time.Millisecond)
	defer rm.Stop()

	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		t.Fatalf("启动资源管理器失败: %v", err)
	}

	// 注册活跃资源
	activeResource := NewMockResource("active", "mock")
	err := rm.RegisterResource("active", activeResource)
	if err != nil {
		t.Fatalf("注册活跃资源失败: %v", err)
	}

	// 注册非活跃资源
	inactiveResource := NewMockResource("inactive", "mock")
	inactiveResource.SetActive(false)
	err = rm.RegisterResource("inactive", inactiveResource)
	if err != nil {
		t.Fatalf("注册非活跃资源失败: %v", err)
	}

	// 等待清理
	time.Sleep(300 * time.Millisecond)

	// 验证非活跃资源被清理
	_, exists := rm.GetResource("inactive")
	if exists {
		t.Error("非活跃资源应该被清理")
	}

	// 验证活跃资源仍然存在
	_, exists = rm.GetResource("active")
	if !exists {
		t.Error("活跃资源应该仍然存在")
	}
}

func TestResourceManager_ForceCleanup(t *testing.T) {
	rm := foundation.NewResourceManager(1*time.Second, 5*time.Second)
	defer rm.Stop()

	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		t.Fatalf("启动资源管理器失败: %v", err)
	}

	// 注册多个资源
	resources := []*MockResource{}
	for i := 0; i < 5; i++ {
		id := fmt.Sprintf("test-%d", i)
		resource := NewMockResource(id, "mock")
		resources = append(resources, resource)

		err := rm.RegisterResource(id, resource)
		if err != nil {
			t.Fatalf("注册资源 %s 失败: %v", id, err)
		}
	}

	// 强制清理
	err := rm.ForceCleanup()
	if err != nil {
		t.Errorf("强制清理失败: %v", err)
	}

	// 验证所有资源都被关闭
	for i, resource := range resources {
		if !resource.closed {
			t.Errorf("资源 %d 应该被关闭", i)
		}
	}

	// 验证资源列表为空
	list := rm.ListResources()
	if len(list) != 0 {
		t.Errorf("资源列表应该为空，实际长度: %d", len(list))
	}
}

func TestResourceManager_GetStats(t *testing.T) {
	rm := foundation.NewResourceManager(1*time.Second, 5*time.Second)
	defer rm.Stop()

	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		t.Fatalf("启动资源管理器失败: %v", err)
	}

	// 注册资源
	resource := NewMockResource("stats-test", "mock")
	err := rm.RegisterResource("stats-test", resource)
	if err != nil {
		t.Fatalf("注册资源失败: %v", err)
	}

	// 获取统计信息
	stats := rm.GetStats()

	// 验证统计信息
	if totalResources, ok := stats["total_resources"].(int64); !ok || totalResources != 1 {
		t.Errorf("总资源数不正确: %v", stats["total_resources"])
	}

	if activeResources, ok := stats["active_resources"].(int64); !ok || activeResources != 1 {
		t.Errorf("活跃资源数不正确: %v", stats["active_resources"])
	}

	if running, ok := stats["running"].(bool); !ok || !running {
		t.Errorf("运行状态不正确: %v", stats["running"])
	}
}

func TestResourceManager_ConcurrentAccess(t *testing.T) {
	rm := foundation.NewResourceManager(1*time.Second, 5*time.Second)
	defer rm.Stop()

	ctx := context.Background()
	if err := rm.Start(ctx); err != nil {
		t.Fatalf("启动资源管理器失败: %v", err)
	}

	const numGoroutines = 10
	const numOperations = 100

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	// 并发注册和注销资源
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < numOperations; j++ {
				id := fmt.Sprintf("concurrent-%d-%d", goroutineID, j)
				resource := NewMockResource(id, "mock")

				// 注册资源
				err := rm.RegisterResource(id, resource)
				if err != nil {
					t.Errorf("并发注册资源失败: %v", err)
					continue
				}

				// 获取资源
				_, exists := rm.GetResource(id)
				if !exists {
					t.Errorf("并发获取资源失败: %s", id)
				}

				// 注销资源
				err = rm.UnregisterResource(id)
				if err != nil {
					t.Errorf("并发注销资源失败: %v", err)
				}
			}
		}(i)
	}

	wg.Wait()
}
