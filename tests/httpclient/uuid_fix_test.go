// Package httpclient_test UUID修复验证测试
package httpclient_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"sync/atomic"
	"testing"
	"time"
	
	"go-monitor/pkg/httpclient"
)

// TestUUIDFix 测试UUID修复效果
func TestUUIDFix(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(10 * time.Millisecond) // 模拟处理时间
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	
	t.Run("NoWarningsTest", func(t *testing.T) {
		// 测试5分钟，确保没有警告
		testDuration := 5 * time.Minute
		t.Logf("开始UUID修复验证测试，持续时间: %v", testDuration)
		
		startTime := time.Now()
		endTime := startTime.Add(testDuration)
		
		var requestCount int64
		var errorCount int64
		
		// 使用连接池模式进行高频请求
		for time.Now().Before(endTime) {
			_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
			atomic.AddInt64(&requestCount, 1)
			
			if err != nil {
				atomic.AddInt64(&errorCount, 1)
			}
			
			// 短暂休息
			time.Sleep(50 * time.Millisecond)
		}
		
		finalRequests := atomic.LoadInt64(&requestCount)
		finalErrors := atomic.LoadInt64(&errorCount)
		
		t.Logf("UUID修复验证完成:")
		t.Logf("  总请求数: %d", finalRequests)
		t.Logf("  总错误数: %d", finalErrors)
		
		if finalRequests > 0 {
			errorRate := float64(finalErrors) / float64(finalRequests) * 100
			t.Logf("  错误率: %.2f%%", errorRate)
			
			if errorRate > 1.0 { // 错误率超过1%认为有问题
				t.Errorf("错误率过高: %.2f%%", errorRate)
			}
		}
		
		if finalRequests < 100 {
			t.Errorf("请求数太少: %d，可能测试时间不够", finalRequests)
		} else {
			t.Logf("✅ UUID修复验证测试通过！")
			t.Logf("✅ 应该没有看到以下警告:")
			t.Logf("   - '请求ID不存在' 警告")
			t.Logf("   - '关闭资源失败' 警告")
		}
	})
	
	t.Run("ConcurrentUUIDTest", func(t *testing.T) {
		// 测试并发情况下UUID的唯一性
		const numGoroutines = 20
		const requestsPerGoroutine = 10
		
		var requestCount int64
		var errorCount int64
		
		// 启动多个并发goroutine
		for i := 0; i < numGoroutines; i++ {
			go func(goroutineID int) {
				for j := 0; j < requestsPerGoroutine; j++ {
					_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
					atomic.AddInt64(&requestCount, 1)
					
					if err != nil {
						atomic.AddInt64(&errorCount, 1)
					}
				}
			}(i)
		}
		
		// 等待所有goroutine完成
		time.Sleep(5 * time.Second)
		
		finalRequests := atomic.LoadInt64(&requestCount)
		finalErrors := atomic.LoadInt64(&errorCount)
		
		expectedRequests := int64(numGoroutines * requestsPerGoroutine)
		
		t.Logf("并发UUID测试完成:")
		t.Logf("  期望请求数: %d", expectedRequests)
		t.Logf("  实际请求数: %d", finalRequests)
		t.Logf("  错误数: %d", finalErrors)
		
		if finalRequests != expectedRequests {
			t.Errorf("请求数不匹配: 期望 %d，实际 %d", expectedRequests, finalRequests)
		}
		
		if finalErrors > 0 {
			errorRate := float64(finalErrors) / float64(finalRequests) * 100
			t.Errorf("并发测试有错误: %d (%.2f%%)", finalErrors, errorRate)
		} else {
			t.Logf("✅ 并发UUID测试通过！")
		}
	})
	
	// 清理全局连接池
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("清理全局连接池失败: %v", err)
		} else {
			t.Logf("✅ 全局连接池已正常清理")
		}
	}()
}

// TestUUIDFormat 测试UUID格式
func TestUUIDFormat(t *testing.T) {
	// 这个测试主要是验证日志中的ID格式
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	
	t.Run("ConnectionIDFormat", func(t *testing.T) {
		// 发送几个请求，检查日志中的连接ID格式
		for i := 0; i < 3; i++ {
			_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
			if err != nil {
				t.Errorf("请求 %d 失败: %v", i+1, err)
			}
		}
		
		t.Logf("✅ 连接ID格式测试完成")
		t.Logf("✅ 检查日志中的连接ID应该是UUID格式:")
		t.Logf("   - 连接ID: conn_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx")
		t.Logf("   - 请求ID: req_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx")
	})
	
	// 清理
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("清理失败: %v", err)
		}
	}()
}

// TestNoMoreTimeBasedIDs 确保不再使用基于时间的ID
func TestNoMoreTimeBasedIDs(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	ctx := context.Background()
	
	t.Run("RapidRequests", func(t *testing.T) {
		// 快速发送多个请求，如果使用时间戳ID可能会冲突
		const numRequests = 100
		var errorCount int64
		
		for i := 0; i < numRequests; i++ {
			go func(requestNum int) {
				_, err := httpclient.Get(ctx, server.URL, nil, httpclient.WithPoolClient())
				if err != nil {
					atomic.AddInt64(&errorCount, 1)
					t.Logf("请求 %d 失败: %v", requestNum, err)
				}
			}(i)
		}
		
		// 等待所有请求完成
		time.Sleep(3 * time.Second)
		
		finalErrors := atomic.LoadInt64(&errorCount)
		
		t.Logf("快速请求测试完成:")
		t.Logf("  总请求数: %d", numRequests)
		t.Logf("  错误数: %d", finalErrors)
		
		if finalErrors > 0 {
			errorRate := float64(finalErrors) / float64(numRequests) * 100
			t.Errorf("快速请求有错误: %d (%.2f%%)", finalErrors, errorRate)
		} else {
			t.Logf("✅ 快速请求测试通过！")
			t.Logf("✅ 没有ID冲突问题")
		}
	})
	
	// 清理
	defer func() {
		if err := httpclient.CloseGlobalPool(); err != nil {
			t.Logf("清理失败: %v", err)
		}
	}()
}
