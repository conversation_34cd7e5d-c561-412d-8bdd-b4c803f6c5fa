// Package client_test 基础客户端测试
package client_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"
	
	"go-monitor/pkg/httpclient/client"
	"go-monitor/pkg/httpclient/foundation"
)

func TestBasicClient_Get(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			t.Errorf("期望 GET 方法，实际 %s", r.Method)
		}
		w.<PERSON>rite<PERSON>eader(http.StatusOK)
		w.Write([]byte("Hello, World!"))
	}))
	defer server.Close()
	
	// 创建客户端
	config := &foundation.Config{
		Timeout:         5 * time.Second,
		MaxIdleConns:    10,
		MaxConnsPerHost: 5,
		UserAgent:       "Test-Client/1.0",
	}
	
	basicClient := client.NewBasicClient(config)
	defer basicClient.Close()
	
	// 执行GET请求
	ctx := context.Background()
	headers := map[string]string{
		"Accept": "text/plain",
	}
	
	resp, err := basicClient.Get(ctx, server.URL, headers)
	if err != nil {
		t.Fatalf("GET请求失败: %v", err)
	}
	
	// 验证响应
	if resp.StatusCode != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}
	
	if string(resp.Body) != "Hello, World!" {
		t.Errorf("期望响应体 'Hello, World!'，实际 '%s'", string(resp.Body))
	}
	
	if resp.URL != server.URL {
		t.Errorf("期望URL %s，实际 %s", server.URL, resp.URL)
	}
	
	if resp.ResponseTime <= 0 {
		t.Error("响应时间应该大于0")
	}
}

func TestBasicClient_Post(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			t.Errorf("期望 POST 方法，实际 %s", r.Method)
		}
		
		// 读取请求体
		body := make([]byte, r.ContentLength)
		r.Body.Read(body)
		
		if string(body) != "test data" {
			t.Errorf("期望请求体 'test data'，实际 '%s'", string(body))
		}
		
		w.WriteHeader(http.StatusCreated)
		w.Write([]byte("Created"))
	}))
	defer server.Close()
	
	// 创建客户端
	basicClient := client.NewBasicClient(nil) // 使用默认配置
	defer basicClient.Close()
	
	// 执行POST请求
	ctx := context.Background()
	body := []byte("test data")
	headers := map[string]string{
		"Content-Type": "text/plain",
	}
	
	resp, err := basicClient.Post(ctx, server.URL, body, headers)
	if err != nil {
		t.Fatalf("POST请求失败: %v", err)
	}
	
	// 验证响应
	if resp.StatusCode != http.StatusCreated {
		t.Errorf("期望状态码 201，实际 %d", resp.StatusCode)
	}
	
	if string(resp.Body) != "Created" {
		t.Errorf("期望响应体 'Created'，实际 '%s'", string(resp.Body))
	}
}

func TestBasicClient_WithParams(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证查询参数
		if r.URL.Query().Get("param1") != "value1" {
			t.Errorf("期望 param1=value1，实际 param1=%s", r.URL.Query().Get("param1"))
		}
		
		if r.URL.Query().Get("param2") != "value2" {
			t.Errorf("期望 param2=value2，实际 param2=%s", r.URL.Query().Get("param2"))
		}
		
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	// 创建客户端
	basicClient := client.NewBasicClient(nil)
	defer basicClient.Close()
	
	// 创建带参数的请求
	req := &foundation.Request{
		URL:    server.URL,
		Method: "GET",
		Params: map[string]string{
			"param1": "value1",
			"param2": "value2",
		},
		Headers: map[string]string{
			"Accept": "text/plain",
		},
	}
	
	ctx := context.Background()
	resp, err := basicClient.Do(ctx, req)
	if err != nil {
		t.Fatalf("带参数请求失败: %v", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}
}

func TestBasicClient_WithCookies(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证Cookies
		cookie1, err := r.Cookie("session")
		if err != nil || cookie1.Value != "abc123" {
			t.Errorf("期望 session=abc123，实际 %v", cookie1)
		}
		
		cookie2, err := r.Cookie("user")
		if err != nil || cookie2.Value != "john" {
			t.Errorf("期望 user=john，实际 %v", cookie2)
		}
		
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	// 创建客户端
	basicClient := client.NewBasicClient(nil)
	defer basicClient.Close()
	
	// 创建带Cookies的请求
	req := &foundation.Request{
		URL:    server.URL,
		Method: "GET",
		Cookies: map[string]string{
			"session": "abc123",
			"user":    "john",
		},
	}
	
	ctx := context.Background()
	resp, err := basicClient.Do(ctx, req)
	if err != nil {
		t.Fatalf("带Cookies请求失败: %v", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}
}

func TestBasicClient_Timeout(t *testing.T) {
	// 创建慢响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second) // 延迟2秒
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Slow response"))
	}))
	defer server.Close()
	
	// 创建短超时的客户端
	config := &foundation.Config{
		Timeout: 500 * time.Millisecond, // 500ms超时
	}
	
	basicClient := client.NewBasicClient(config)
	defer basicClient.Close()
	
	// 执行请求（应该超时）
	ctx := context.Background()
	_, err := basicClient.Get(ctx, server.URL, nil)
	
	if err == nil {
		t.Error("期望超时错误，但请求成功了")
	}
	
	// 验证是否是超时错误
	if !strings.Contains(err.Error(), "timeout") && !strings.Contains(err.Error(), "超时") {
		t.Errorf("期望超时错误，实际错误: %v", err)
	}
}

func TestBasicClient_ContextCancellation(t *testing.T) {
	// 创建慢响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Slow response"))
	}))
	defer server.Close()
	
	// 创建客户端
	basicClient := client.NewBasicClient(nil)
	defer basicClient.Close()
	
	// 创建可取消的context
	ctx, cancel := context.WithCancel(context.Background())
	
	// 在500ms后取消请求
	go func() {
		time.Sleep(500 * time.Millisecond)
		cancel()
	}()
	
	// 执行请求（应该被取消）
	_, err := basicClient.Get(ctx, server.URL, nil)
	
	if err == nil {
		t.Error("期望取消错误，但请求成功了")
	}
	
	// 验证是否是取消错误
	if !strings.Contains(err.Error(), "cancel") && !strings.Contains(err.Error(), "取消") {
		t.Errorf("期望取消错误，实际错误: %v", err)
	}
}

func TestBasicClient_UpdateConfig(t *testing.T) {
	// 创建客户端
	basicClient := client.NewBasicClient(nil)
	defer basicClient.Close()
	
	// 获取初始配置
	initialConfig := basicClient.GetConfig()
	initialTimeout := initialConfig.Timeout
	
	// 更新配置
	newConfig := &foundation.Config{
		Timeout:         10 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 10,
		UserAgent:       "Updated-Client/1.0",
	}
	
	err := basicClient.UpdateConfig(newConfig)
	if err != nil {
		t.Fatalf("更新配置失败: %v", err)
	}
	
	// 验证配置已更新
	updatedConfig := basicClient.GetConfig()
	if updatedConfig.Timeout == initialTimeout {
		t.Error("配置未更新")
	}
	
	if updatedConfig.Timeout != 10*time.Second {
		t.Errorf("期望超时时间 10s，实际 %v", updatedConfig.Timeout)
	}
	
	if updatedConfig.UserAgent != "Updated-Client/1.0" {
		t.Errorf("期望User-Agent 'Updated-Client/1.0'，实际 '%s'", updatedConfig.UserAgent)
	}
}

func TestBasicClient_Close(t *testing.T) {
	// 创建客户端
	basicClient := client.NewBasicClient(nil)
	
	// 关闭客户端
	err := basicClient.Close()
	if err != nil {
		t.Errorf("关闭客户端失败: %v", err)
	}
	
	// 验证关闭后无法使用
	ctx := context.Background()
	_, err = basicClient.Get(ctx, "http://example.com", nil)
	if err == nil {
		t.Error("期望关闭后的错误，但请求成功了")
	}
	
	if !strings.Contains(err.Error(), "关闭") && !strings.Contains(err.Error(), "closed") {
		t.Errorf("期望关闭错误，实际错误: %v", err)
	}
}
