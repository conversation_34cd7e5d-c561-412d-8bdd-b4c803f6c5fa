# HTTPClient 测试说明

## 测试文件位置

为了保持源码目录的整洁，所有测试文件已移动到 `tests/httpclient/` 目录：

### 主要测试文件
- `tests/httpclient/balanced_test.go` - 平衡网络测试
- `tests/httpclient/integration_test.go` - 集成测试
- `tests/httpclient/network_io_test.go` - 网络IO测试
- `tests/httpclient/network_test.go` - 网络功能测试

### 指纹模块测试文件
- `tests/httpclient/fingerprint/concurrency_test.go` - 并发测试
- `tests/httpclient/fingerprint/healthy_pool_test.go` - 健康池测试
- `tests/httpclient/fingerprint/integration_test.go` - 指纹集成测试
- `tests/httpclient/fingerprint/lock_management_test.go` - 锁管理测试
- `tests/httpclient/fingerprint/manager_test.go` - 管理器测试
- `tests/httpclient/fingerprint/math_test.go` - 数学函数测试
- `tests/httpclient/fingerprint/performance_test.go` - 性能测试

## 运行测试

```bash
# 运行所有httpclient测试
go test ./tests/httpclient/...

# 运行特定测试
go test ./tests/httpclient -v

# 运行指纹模块测试
go test ./tests/httpclient/fingerprint -v

# 运行性能测试
go test ./tests/httpclient/fingerprint -bench=.
```

## 测试覆盖率

```bash
# 生成测试覆盖率报告
go test ./pkg/httpclient/... -coverprofile=coverage.out
go tool cover -html=coverage.out
```

## 注意事项

- 测试文件使用 `package httpclient` 声明，可以访问包的内部函数
- 集成测试可能需要网络连接
- 性能测试建议在稳定环境中运行
