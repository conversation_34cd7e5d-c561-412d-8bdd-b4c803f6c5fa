// Package features_test 重试功能测试
package features_test

import (
	"context"
	"errors"
	"testing"
	"time"
	
	"go-monitor/pkg/httpclient/errors"
	"go-monitor/pkg/httpclient/features/retry"
	"go-monitor/pkg/httpclient/foundation"
)

func TestRetryManager_ExecuteWithRetry_Success(t *testing.T) {
	config := retry.RetryConfig{
		MaxRetries:    3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        false,
	}
	
	rm := retry.NewRetryManager(config)
	
	// 模拟第一次失败，第二次成功的操作
	attemptCount := 0
	operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
		attemptCount++
		if attemptCount == 1 {
			return nil, errors.NewTimeoutError("模拟超时", 5*time.Second)
		}
		
		return &foundation.Response{
			StatusCode: 200,
			Body:       []byte("Success"),
		}, nil
	}
	
	ctx := context.Background()
	resp, err := rm.ExecuteWithRetry(ctx, operation)
	
	if err != nil {
		t.Fatalf("重试操作失败: %v", err)
	}
	
	if resp.StatusCode != 200 {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}
	
	if attemptCount != 2 {
		t.Errorf("期望尝试次数 2，实际 %d", attemptCount)
	}
}

func TestRetryManager_ExecuteWithRetry_MaxRetriesExceeded(t *testing.T) {
	config := retry.RetryConfig{
		MaxRetries:    2,
		InitialDelay:  50 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        false,
	}
	
	rm := retry.NewRetryManager(config)
	
	// 模拟总是失败的操作
	attemptCount := 0
	operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
		attemptCount++
		return nil, errors.NewTimeoutError("持续超时", 5*time.Second)
	}
	
	ctx := context.Background()
	_, err := rm.ExecuteWithRetry(ctx, operation)
	
	if err == nil {
		t.Error("期望重试失败错误，但操作成功了")
	}
	
	// 验证尝试次数 = MaxRetries + 1（初始尝试）
	expectedAttempts := config.MaxRetries + 1
	if attemptCount != expectedAttempts {
		t.Errorf("期望尝试次数 %d，实际 %d", expectedAttempts, attemptCount)
	}
}

func TestRetryManager_ExecuteWithRetry_NonRetryableError(t *testing.T) {
	config := retry.RetryConfig{
		MaxRetries:    3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        false,
	}
	
	rm := retry.NewRetryManager(config)
	
	// 模拟不可重试的错误
	attemptCount := 0
	operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
		attemptCount++
		return nil, errors.NewRequestError("请求格式错误", nil)
	}
	
	ctx := context.Background()
	_, err := rm.ExecuteWithRetry(ctx, operation)
	
	if err == nil {
		t.Error("期望请求错误，但操作成功了")
	}
	
	// 不可重试的错误应该只尝试一次
	if attemptCount != 1 {
		t.Errorf("期望尝试次数 1，实际 %d", attemptCount)
	}
}

func TestRetryManager_ExecuteWithRetry_ContextCancellation(t *testing.T) {
	config := retry.RetryConfig{
		MaxRetries:    5,
		InitialDelay:  200 * time.Millisecond,
		MaxDelay:      2 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        false,
	}
	
	rm := retry.NewRetryManager(config)
	
	// 模拟总是失败的操作
	operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
		return nil, errors.NewTimeoutError("模拟超时", 5*time.Second)
	}
	
	// 创建会被取消的context
	ctx, cancel := context.WithCancel(context.Background())
	
	// 在300ms后取消context
	go func() {
		time.Sleep(300 * time.Millisecond)
		cancel()
	}()
	
	_, err := rm.ExecuteWithRetry(ctx, operation)
	
	if err == nil {
		t.Error("期望取消错误，但操作成功了")
	}
	
	// 验证是取消错误
	if ctx.Err() != context.Canceled {
		t.Errorf("期望context被取消，实际错误: %v", err)
	}
}

func TestRetryManager_CalculateDelay(t *testing.T) {
	config := retry.RetryConfig{
		MaxRetries:    3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        false,
	}
	
	rm := retry.NewRetryManager(config)
	
	// 测试延迟计算
	testCases := []struct {
		attempt      int
		expectedMin  time.Duration
		expectedMax  time.Duration
	}{
		{0, 100 * time.Millisecond, 100 * time.Millisecond},
		{1, 200 * time.Millisecond, 200 * time.Millisecond},
		{2, 400 * time.Millisecond, 400 * time.Millisecond},
		{3, 800 * time.Millisecond, 800 * time.Millisecond},
		{4, 1 * time.Second, 1 * time.Second}, // 受MaxDelay限制
	}
	
	for _, tc := range testCases {
		// 使用反射访问私有方法进行测试
		// 这里简化为直接测试公开的行为
		
		// 创建一个总是失败的操作来测试延迟
		attemptCount := 0
		var delays []time.Duration
		
		operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
			if attemptCount > 0 {
				// 记录实际延迟（简化测试）
				delays = append(delays, time.Since(time.Now()))
			}
			attemptCount++
			
			if attemptCount > tc.attempt+1 {
				return &foundation.Response{StatusCode: 200}, nil
			}
			
			return nil, errors.NewTimeoutError("测试超时", 5*time.Second)
		}
		
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		rm.ExecuteWithRetry(ctx, operation)
		cancel()
	}
}

func TestRetryManager_WithJitter(t *testing.T) {
	config := retry.RetryConfig{
		MaxRetries:    2,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        true, // 启用抖动
	}
	
	rm := retry.NewRetryManager(config)
	
	// 记录多次重试的延迟时间
	var delays []time.Duration
	attemptCount := 0
	
	operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
		if attemptCount > 0 {
			// 简化的延迟记录
			delays = append(delays, time.Duration(attempt)*100*time.Millisecond)
		}
		attemptCount++
		
		if attemptCount <= 2 {
			return nil, errors.NewTimeoutError("测试超时", 5*time.Second)
		}
		
		return &foundation.Response{StatusCode: 200}, nil
	}
	
	ctx := context.Background()
	_, err := rm.ExecuteWithRetry(ctx, operation)
	
	if err != nil {
		t.Fatalf("重试操作失败: %v", err)
	}
	
	// 验证启用抖动时的行为（这里简化验证）
	if attemptCount != 3 {
		t.Errorf("期望尝试次数 3，实际 %d", attemptCount)
	}
}

func TestRetryManager_DefaultConfig(t *testing.T) {
	rm := retry.NewDefaultRetryManager()
	
	// 测试默认配置的重试行为
	attemptCount := 0
	operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
		attemptCount++
		if attemptCount <= 2 {
			return nil, errors.NewConnectionError("连接失败", nil)
		}
		return &foundation.Response{StatusCode: 200}, nil
	}
	
	ctx := context.Background()
	resp, err := rm.ExecuteWithRetry(ctx, operation)
	
	if err != nil {
		t.Fatalf("默认重试管理器失败: %v", err)
	}
	
	if resp.StatusCode != 200 {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}
	
	if attemptCount != 3 {
		t.Errorf("期望尝试次数 3，实际 %d", attemptCount)
	}
}

func TestRetryManager_AggressiveConfig(t *testing.T) {
	rm := retry.NewAggressiveRetryManager()
	
	// 测试激进配置的重试行为
	attemptCount := 0
	operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
		attemptCount++
		if attemptCount <= 3 {
			return nil, errors.NewResponseError("服务器错误", 500)
		}
		return &foundation.Response{StatusCode: 200}, nil
	}
	
	ctx := context.Background()
	resp, err := rm.ExecuteWithRetry(ctx, operation)
	
	if err != nil {
		t.Fatalf("激进重试管理器失败: %v", err)
	}
	
	if resp.StatusCode != 200 {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}
	
	if attemptCount != 4 {
		t.Errorf("期望尝试次数 4，实际 %d", attemptCount)
	}
}

func TestRetryManager_ConservativeConfig(t *testing.T) {
	rm := retry.NewConservativeRetryManager()
	
	// 测试保守配置的重试行为
	attemptCount := 0
	operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
		attemptCount++
		if attemptCount <= 1 {
			return nil, errors.NewTimeoutError("超时", 5*time.Second)
		}
		return &foundation.Response{StatusCode: 200}, nil
	}
	
	ctx := context.Background()
	resp, err := rm.ExecuteWithRetry(ctx, operation)
	
	if err != nil {
		t.Fatalf("保守重试管理器失败: %v", err)
	}
	
	if resp.StatusCode != 200 {
		t.Errorf("期望状态码 200，实际 %d", resp.StatusCode)
	}
	
	if attemptCount != 2 {
		t.Errorf("期望尝试次数 2，实际 %d", attemptCount)
	}
}
