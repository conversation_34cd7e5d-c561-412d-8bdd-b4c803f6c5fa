package httpclient

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"
)

// TestFingerprintIntegration 测试指纹系统集成
func TestFingerprintIntegration(t *testing.T) {
	// 测试1：自动初始化
	t.Run("AutoInitialization", func(t *testing.T) {
		client := NewClientWithFingerprint(nil)

		request := &Request{
			URL:    "https://httpbin.org/get",
			Method: "GET",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		response, err := client.Do(ctx, request)
		if err != nil {
			t.Logf("请求失败（可能是网络问题）: %v", err)
			return // 不作为错误，因为可能是网络问题
		}

		if response.StatusCode != 200 {
			t.Errorf("期望状态码200，得到%d", response.StatusCode)
		}

		// 检查指纹管理器是否已初始化
		manager := GetFingerprintManager()
		if manager == nil {
			t.Error("指纹管理器未初始化")
		}

		stats := manager.GetStats()
		if stats == nil {
			t.Error("无法获取指纹统计")
		}

		t.Logf("指纹统计: %+v", stats)
	})

	// 测试2：手动初始化
	t.Run("ManualInitialization", func(t *testing.T) {
		config := map[string]interface{}{
			"enabled": true,
			"rotation": map[string]interface{}{
				"max_requests_per_profile":    10,
				"max_profile_duration":        "5m",
				"random_rotation_probability": 0.5,
			},
		}

		err := InitializeTLSFingerprint(config)
		if err != nil {
			t.Fatalf("初始化失败: %v", err)
		}

		manager := GetFingerprintManager()
		if manager == nil {
			t.Error("指纹管理器未初始化")
		}
	})

	// 测试3：强制初始化
	t.Run("ForceInitialization", func(t *testing.T) {
		MustInitializeFingerprint()

		manager := GetFingerprintManager()
		if manager == nil {
			t.Error("强制初始化失败")
		}

		stats := GetTLSFingerprintStats()
		if stats == nil {
			t.Error("无法获取统计信息")
		}

		if errorMsg, hasError := stats["error"]; hasError {
			t.Errorf("统计信息包含错误: %v", errorMsg)
		}
	})

	// 测试4：排除指纹功能
	t.Run("ExcludeProfiles", func(t *testing.T) {
		client := NewClientWithFingerprint(nil)

		request := &Request{
			URL:    "https://httpbin.org/get",
			Method: "GET",
		}

		excludeProfiles := []string{"Chrome_120_Win11_Real", "Safari_17_macOS_Real"}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		response, err := client.DoWithFingerprint(ctx, request, excludeProfiles)
		if err != nil {
			t.Logf("排除指纹请求失败（可能是网络问题）: %v", err)
			return
		}

		if response.StatusCode != 200 {
			t.Errorf("期望状态码200，得到%d", response.StatusCode)
		}

		// 检查元数据是否正确设置
		if request.Meta == nil {
			t.Error("请求元数据未设置")
		} else {
			if excludeList, ok := request.Meta["exclude_tls_profiles"]; !ok {
				t.Error("排除指纹列表未设置到元数据")
			} else if profiles, ok := excludeList.([]string); !ok {
				t.Error("排除指纹列表类型错误")
			} else if len(profiles) != len(excludeProfiles) {
				t.Errorf("排除指纹列表长度不匹配，期望%d，得到%d", len(excludeProfiles), len(profiles))
			}
		}
	})
}

// BenchmarkFingerprintPerformance 性能基准测试
func BenchmarkFingerprintPerformance(b *testing.B) {
	// 初始化
	MustInitializeFingerprint()
	manager := GetFingerprintManager()

	b.ResetTimer()

	b.Run("ProfileSelection", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _, err := manager.CreateManagedTLSConfig("test-server.com")
			if err != nil {
				b.Fatalf("指纹选择失败: %v", err)
			}
		}
	})

	b.Run("StatsRetrieval", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			stats := manager.GetStats()
			if stats == nil {
				b.Fatal("无法获取统计信息")
			}
		}
	})
}

// 示例函数已移除 - 引用了不存在的标识符
func exampleQuickStart() {
	// 最简单的使用方式
	client := NewClientWithFingerprint(nil)

	request := &Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
		Headers: map[string]string{
			"Accept": "application/json",
		},
	}

	ctx := context.Background()
	response, err := client.Do(ctx, request)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	fmt.Printf("请求成功，状态码: %d\n", response.StatusCode)

	// 获取指纹使用统计
	stats := GetTLSFingerprintStats()
	fmt.Printf("指纹统计: %+v\n", stats)

	// Output:
	// 请求成功，状态码: 200
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	// 测试未初始化时的行为
	t.Run("UninitializedManager", func(t *testing.T) {
		// 重置全局管理器（仅用于测试）
		globalFingerprintManager = nil
		fingerprintManagerOnce = sync.Once{}

		// 现在应该自动初始化
		manager := GetFingerprintManager()
		if manager == nil {
			t.Error("自动初始化失败")
		}
	})

	// 测试无效配置
	t.Run("InvalidConfig", func(t *testing.T) {
		invalidConfig := map[string]interface{}{
			"enabled": "not_a_boolean", // 错误的类型
		}

		// 这不应该导致崩溃
		err := InitializeTLSFingerprint(invalidConfig)
		if err != nil {
			t.Logf("预期的配置错误: %v", err)
		}
	})
}

// TestConcurrentAccess 测试并发访问
func TestConcurrentAccess(t *testing.T) {
	MustInitializeFingerprint()

	const numGoroutines = 10
	const numOperations = 5

	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			manager := GetFingerprintManager()

			for j := 0; j < numOperations; j++ {
				// 测试指纹选择
				_, _, err := manager.CreateManagedTLSConfig("test-server.com")
				if err != nil {
					t.Errorf("Goroutine %d: 指纹选择失败: %v", id, err)
					return
				}

				// 测试统计获取
				stats := manager.GetStats()
				if stats == nil {
					t.Errorf("Goroutine %d: 无法获取统计", id)
					return
				}

				// 测试结果记录
				manager.RecordRequestResult("test-profile", true, 100*time.Millisecond, 200, nil)
			}
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}
}
