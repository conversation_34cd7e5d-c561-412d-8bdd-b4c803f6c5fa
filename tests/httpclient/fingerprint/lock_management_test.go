package fingerprint

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

// TestLockManagement 测试锁管理修复
func TestLockManagement(t *testing.T) {
	// 测试1：基本锁管理
	t.Run("BasicLockManagement", func(t *testing.T) {
		manager := NewManager()

		const numGoroutines = 20
		const numOperations = 30

		var wg sync.WaitGroup
		errors := make(chan error, numGoroutines*numOperations)

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						errors <- fmt.Errorf("goroutine %d panic: %v", id, r)
					}
				}()

				serverName := fmt.Sprintf("lock-test-%d.com", id%3)

				for j := 0; j < numOperations; j++ {
					// 测试不同的返回路径
					switch j % 4 {
					case 0:
						// 正常选择路径
						_, err := manager.SelectProfileWithExclusions(serverName, nil)
						if err != nil {
							// 忽略业务错误
						}
					case 1:
						// 智能选择器路径
						_, err := manager.SelectProfileWithExclusions(serverName, nil)
						if err != nil {
							// 忽略业务错误
						}
					case 2:
						// 动态生成路径（通过排除大量指纹触发）
						excludeProfiles := []string{
							"Chrome_Real_v1", "Firefox_Real_v1", "Safari_Real_v1",
							"Chrome_v1", "Firefox_v1",
						}
						_, err := manager.SelectProfileWithExclusions(serverName, excludeProfiles)
						if err != nil {
							// 忽略业务错误
						}
					case 3:
						// 错误返回路径（通过排除所有指纹触发）
						excludeProfiles := []string{
							"Chrome_Real_v1", "Chrome_Real_v2",
							"Firefox_Real_v1", "Firefox_Real_v2",
							"Safari_Real_v1", "Safari_Real_v2",
							"Chrome_v1", "Chrome_v2",
							"Firefox_v1", "Firefox_v2",
							"Minimal_v1_INTL", "Minimal_v1_ASIA",
							"Minimal_v1_NA", "Minimal_v1_GENERAL",
						}
						_, err := manager.SelectProfileWithExclusions(serverName, excludeProfiles)
						if err != nil {
							// 这是预期的错误
						}
					}
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		// 检查是否有panic
		errorCount := 0
		for err := range errors {
			t.Errorf("锁管理错误: %v", err)
			errorCount++
		}

		if errorCount == 0 {
			t.Logf("基本锁管理测试通过: %d个goroutine，每个%d次操作",
				numGoroutines, numOperations)
		}
	})

	// 测试2：高频率锁操作
	t.Run("HighFrequencyLockOperations", func(t *testing.T) {
		manager := NewManager()

		const numGoroutines = 50
		const duration = 2 * time.Second

		var wg sync.WaitGroup
		stop := make(chan struct{})
		operationCount := int64(0)
		errorCount := int64(0)
		var mu sync.Mutex

		start := time.Now()

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						mu.Lock()
						errorCount++
						mu.Unlock()
						t.Errorf("高频锁操作 goroutine %d panic: %v", id, r)
					}
				}()

				serverName := fmt.Sprintf("highfreq-test-%d.com", id%5)
				localOps := int64(0)

				for {
					select {
					case <-stop:
						mu.Lock()
						operationCount += localOps
						mu.Unlock()
						return
					default:
						// 快速连续调用
						manager.SelectProfileWithExclusions(serverName, nil)
						localOps++
					}
				}
			}(i)
		}

		time.Sleep(duration)
		close(stop)
		wg.Wait()

		actualDuration := time.Since(start)

		if errorCount == 0 {
			t.Logf("高频锁操作测试通过: %d个goroutine，%d次操作，耗时: %v",
				numGoroutines, operationCount, actualDuration)
			t.Logf("平均操作速度: %.0f ops/sec",
				float64(operationCount)/actualDuration.Seconds())
		} else {
			t.Errorf("高频锁操作测试失败: 发生%d次错误", errorCount)
		}
	})

	// 测试3：混合锁操作模式
	t.Run("MixedLockOperations", func(t *testing.T) {
		manager := NewManager()

		const numReaders = 30
		const numWriters = 10
		const duration = 3 * time.Second

		var wg sync.WaitGroup
		stop := make(chan struct{})
		readOps := int64(0)
		writeOps := int64(0)
		errorCount := int64(0)
		var mu sync.Mutex

		// 启动读取goroutine
		for i := 0; i < numReaders; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						mu.Lock()
						errorCount++
						mu.Unlock()
						t.Errorf("读取 goroutine %d panic: %v", id, r)
					}
				}()

				serverName := fmt.Sprintf("reader-%d.com", id)
				localReads := int64(0)

				for {
					select {
					case <-stop:
						mu.Lock()
						readOps += localReads
						mu.Unlock()
						return
					default:
						manager.SelectProfileWithExclusions(serverName, nil)
						localReads++
						time.Sleep(5 * time.Millisecond)
					}
				}
			}(i)
		}

		// 启动写入goroutine（通过动态生成触发写入）
		for i := 0; i < numWriters; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						mu.Lock()
						errorCount++
						mu.Unlock()
						t.Errorf("写入 goroutine %d panic: %v", id, r)
					}
				}()

				serverName := fmt.Sprintf("writer-%d.com", id)
				localWrites := int64(0)

				for {
					select {
					case <-stop:
						mu.Lock()
						writeOps += localWrites
						mu.Unlock()
						return
					default:
						// 尝试触发动态生成（写入操作）
						excludeProfiles := []string{
							"Chrome_Real_v1", "Firefox_Real_v1",
						}
						manager.SelectProfileWithExclusions(serverName, excludeProfiles)
						localWrites++
						time.Sleep(20 * time.Millisecond)
					}
				}
			}(i)
		}

		time.Sleep(duration)
		close(stop)
		wg.Wait()

		if errorCount == 0 {
			t.Logf("混合锁操作测试通过:")
			t.Logf("  读操作: %d次", readOps)
			t.Logf("  写操作: %d次", writeOps)
			t.Logf("  总操作: %d次", readOps+writeOps)
		} else {
			t.Errorf("混合锁操作测试失败: 发生%d次错误", errorCount)
		}
	})
}

// TestRWMutexUnlockError 测试RWMutex解锁错误预防
func TestRWMutexUnlockError(t *testing.T) {
	manager := NewManager()

	const numGoroutines = 30
	const numOperations = 100

	var wg sync.WaitGroup
	unlockErrors := int64(0)
	var mu sync.Mutex

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					if fmt.Sprintf("%v", r) == "sync: unlock of unlocked RWMutex" {
						mu.Lock()
						unlockErrors++
						mu.Unlock()
					}
					t.Errorf("RWMutex错误 goroutine %d: %v", id, r)
				}
			}()

			serverName := fmt.Sprintf("unlock-test-%d.com", id)

			for j := 0; j < numOperations; j++ {
				// 测试所有可能的返回路径
				switch j % 5 {
				case 0:
					// 正常成功路径
					manager.SelectProfileWithExclusions(serverName, nil)
				case 1:
					// 智能选择器路径
					manager.SelectProfileWithExclusions(serverName, []string{"nonexistent"})
				case 2:
					// 动态生成路径
					excludeProfiles := []string{"Chrome_Real_v1", "Firefox_Real_v1"}
					manager.SelectProfileWithExclusions(serverName, excludeProfiles)
				case 3:
					// 错误返回路径
					excludeProfiles := []string{
						"Chrome_Real_v1", "Chrome_Real_v2",
						"Firefox_Real_v1", "Firefox_Real_v2",
						"Safari_Real_v1", "Safari_Real_v2",
					}
					manager.SelectProfileWithExclusions(serverName, excludeProfiles)
				case 4:
					// 统计信息访问（不同的锁路径）
					manager.GetStats()
				}
			}
		}(i)
	}

	wg.Wait()

	if unlockErrors == 0 {
		t.Logf("RWMutex解锁错误预防测试通过: %d个goroutine，每个%d次操作",
			numGoroutines, numOperations)
	} else {
		t.Errorf("检测到%d次RWMutex解锁错误", unlockErrors)
	}
}

// BenchmarkLockManagement 锁管理性能基准测试
func BenchmarkLockManagement(b *testing.B) {
	manager := NewManager()

	b.RunParallel(func(pb *testing.PB) {
		serverName := "benchmark-lock.com"
		counter := 0

		for pb.Next() {
			switch counter % 3 {
			case 0:
				manager.SelectProfileWithExclusions(serverName, nil)
			case 1:
				manager.SelectProfileWithExclusions(serverName, []string{"Chrome_Real_v1"})
			case 2:
				manager.GetStats()
			}
			counter++
		}
	})
}

