package fingerprint

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

// TestManagerConcurrency 测试Manager的并发安全
func TestManagerConcurrency(t *testing.T) {
	// 测试1：并发SelectProfileWithExclusions
	t.Run("ConcurrentSelectProfile", func(t *testing.T) {
		manager := NewManager()
		
		const numGoroutines = 50
		const numOperations = 20
		
		var wg sync.WaitGroup
		errors := make(chan error, numGoroutines*numOperations)
		
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						errors <- fmt.Errorf("goroutine %d panic: %v", id, r)
					}
				}()
				
				serverName := fmt.Sprintf("test-server-%d.com", id%5)
				
				for j := 0; j < numOperations; j++ {
					// 并发调用SelectProfileWithExclusions
					_, err := manager.SelectProfileWithExclusions(serverName, nil)
					if err != nil {
						// 忽略业务错误，专注于并发安全
					}
					
					// 偶尔使用排除列表
					if j%3 == 0 {
						excludeProfiles := []string{"Chrome_Real_v1", "Firefox_Real_v1"}
						_, err := manager.SelectProfileWithExclusions(serverName, excludeProfiles)
						if err != nil {
							// 忽略业务错误
						}
					}
				}
			}(i)
		}
		
		wg.Wait()
		close(errors)
		
		// 检查是否有panic
		errorCount := 0
		for err := range errors {
			t.Errorf("并发错误: %v", err)
			errorCount++
		}
		
		if errorCount == 0 {
			t.Logf("并发SelectProfile测试通过: %d个goroutine，每个%d次操作", 
				numGoroutines, numOperations)
		}
	})
	
	// 测试2：并发动态指纹生成
	t.Run("ConcurrentDynamicGeneration", func(t *testing.T) {
		manager := NewManager()
		
		const numGoroutines = 30
		const numOperations = 10
		
		var wg sync.WaitGroup
		successCount := int64(0)
		var mu sync.Mutex
		
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						t.Errorf("动态生成 goroutine %d panic: %v", id, r)
					}
				}()
				
				serverName := fmt.Sprintf("dynamic-test-%d.com", id)
				
				for j := 0; j < numOperations; j++ {
					// 尝试触发动态指纹生成
					// 通过排除所有已知指纹来强制生成动态指纹
					excludeProfiles := []string{
						"Chrome_Real_v1", "Chrome_Real_v2",
						"Firefox_Real_v1", "Firefox_Real_v2",
						"Safari_Real_v1", "Safari_Real_v2",
					}
					
					profile, err := manager.SelectProfileWithExclusions(serverName, excludeProfiles)
					if err == nil && profile != nil {
						mu.Lock()
						successCount++
						mu.Unlock()
					}
				}
			}(i)
		}
		
		wg.Wait()
		
		t.Logf("并发动态生成测试完成: %d次成功生成", successCount)
	})
	
	// 测试3：混合读写操作
	t.Run("ConcurrentMixedOperations", func(t *testing.T) {
		manager := NewManager()
		
		const numReaders = 30
		const numWriters = 10
		const duration = 2 * time.Second
		
		var wg sync.WaitGroup
		stop := make(chan struct{})
		
		// 启动读取goroutine
		for i := 0; i < numReaders; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						t.Errorf("读取 goroutine %d panic: %v", id, r)
					}
				}()
				
				serverName := fmt.Sprintf("reader-test-%d.com", id%3)
				
				for {
					select {
					case <-stop:
						return
					default:
						manager.SelectProfileWithExclusions(serverName, nil)
						time.Sleep(10 * time.Millisecond)
					}
				}
			}(i)
		}
		
		// 启动写入goroutine（通过动态生成触发写入）
		for i := 0; i < numWriters; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						t.Errorf("写入 goroutine %d panic: %v", id, r)
					}
				}()
				
				serverName := fmt.Sprintf("writer-test-%d.com", id)
				
				for {
					select {
					case <-stop:
						return
					default:
						// 尝试触发动态指纹生成（写入操作）
						excludeProfiles := []string{"Chrome_Real_v1", "Firefox_Real_v1"}
						manager.SelectProfileWithExclusions(serverName, excludeProfiles)
						time.Sleep(50 * time.Millisecond)
					}
				}
			}(i)
		}
		
		// 运行指定时间
		time.Sleep(duration)
		close(stop)
		wg.Wait()
		
		t.Logf("混合读写操作测试完成，运行时间: %v", duration)
	})
	
	// 测试4：统计信息并发访问
	t.Run("ConcurrentStats", func(t *testing.T) {
		manager := NewManager()
		
		const numGoroutines = 20
		const numOperations = 50
		
		var wg sync.WaitGroup
		
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						t.Errorf("统计 goroutine %d panic: %v", id, r)
					}
				}()
				
				for j := 0; j < numOperations; j++ {
					// 并发访问统计信息
					stats := manager.GetStats()
					if stats == nil {
						t.Errorf("获取统计信息失败")
					}
					
					// 并发选择指纹
					serverName := fmt.Sprintf("stats-test-%d.com", id)
					manager.SelectProfileWithExclusions(serverName, nil)
				}
			}(i)
		}
		
		wg.Wait()
		
		t.Logf("统计信息并发访问测试完成")
	})
}

// TestManagerDeadlockPrevention 测试死锁预防
func TestManagerDeadlockPrevention(t *testing.T) {
	manager := NewManager()
	
	const numGoroutines = 20
	const duration = 3 * time.Second
	
	var wg sync.WaitGroup
	stop := make(chan struct{})
	operationCount := int64(0)
	var mu sync.Mutex
	
	start := time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("死锁测试 goroutine %d panic: %v", id, r)
				}
			}()
			
			serverName := fmt.Sprintf("deadlock-test-%d.com", id)
			localOps := int64(0)
			
			for {
				select {
				case <-stop:
					mu.Lock()
					operationCount += localOps
					mu.Unlock()
					return
				default:
					// 尝试触发可能的死锁场景
					excludeProfiles := []string{
						"Chrome_Real_v1", "Firefox_Real_v1", "Safari_Real_v1",
						"Chrome_v1", "Firefox_v1", "Minimal_v1_INTL",
					}
					
					_, err := manager.SelectProfileWithExclusions(serverName, excludeProfiles)
					if err != nil {
						// 忽略业务错误
					}
					
					localOps++
				}
			}
		}(i)
	}
	
	// 运行指定时间
	time.Sleep(duration)
	close(stop)
	wg.Wait()
	
	actualDuration := time.Since(start)
	
	t.Logf("死锁预防测试完成:")
	t.Logf("  运行时间: %v", actualDuration)
	t.Logf("  总操作数: %d", operationCount)
	t.Logf("  平均操作速度: %.0f ops/sec", float64(operationCount)/actualDuration.Seconds())
	
	// 如果能正常完成，说明没有死锁
	if actualDuration < duration+1*time.Second {
		t.Logf("✅ 死锁预防测试通过")
	} else {
		t.Errorf("❌ 可能存在死锁，运行时间过长")
	}
}

// BenchmarkManagerConcurrency Manager并发性能基准测试
func BenchmarkManagerConcurrency(b *testing.B) {
	manager := NewManager()
	
	b.RunParallel(func(pb *testing.PB) {
		serverName := "benchmark-test.com"
		counter := 0
		
		for pb.Next() {
			if counter%5 == 0 {
				// 偶尔使用排除列表
				excludeProfiles := []string{"Chrome_Real_v1"}
				manager.SelectProfileWithExclusions(serverName, excludeProfiles)
			} else {
				manager.SelectProfileWithExclusions(serverName, nil)
			}
			counter++
		}
	})
}

