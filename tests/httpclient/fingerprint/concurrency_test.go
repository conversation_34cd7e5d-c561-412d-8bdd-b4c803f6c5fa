package fingerprint

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

// TestConcurrencyFix 测试并发安全修复
func TestConcurrencyFix(t *testing.T) {
	// 测试1：并发UpdateSuccessRate
	t.Run("ConcurrentUpdateSuccessRate", func(t *testing.T) {
		generator := NewDynamicGenerator()

		const numGoroutines = 100
		const numOperations = 50

		var wg sync.WaitGroup

		// 启动多个goroutine并发更新成功率
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				profileName := fmt.Sprintf("test_profile_%d", id%10) // 10个不同的profile

				for j := 0; j < numOperations; j++ {
					success := (id+j)%2 == 0 // 交替成功失败
					generator.UpdateSuccessRate(profileName, success)
				}
			}(i)
		}

		wg.Wait()

		t.Logf("并发UpdateSuccessRate测试完成，无panic发生")
	})

	// 测试2：并发读写混合
	t.Run("ConcurrentReadWrite", func(t *testing.T) {
		generator := NewDynamicGenerator()

		const numReaders = 50
		const numWriters = 50
		const duration = 2 * time.Second

		var wg sync.WaitGroup
		stop := make(chan struct{})

		// 启动写入goroutine
		for i := 0; i < numWriters; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				profileName := fmt.Sprintf("profile_%d", id%5)
				counter := 0

				for {
					select {
					case <-stop:
						return
					default:
						generator.UpdateSuccessRate(profileName, counter%2 == 0)
						counter++
						time.Sleep(10 * time.Millisecond)
					}
				}
			}(i)
		}

		// 启动读取goroutine
		for i := 0; i < numReaders; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				for {
					select {
					case <-stop:
						return
					default:
						// 调用getBestTemplate会读取successRates
						template := generator.getBestTemplate()
						if template != nil {
							// 模拟使用template
							_ = template.Name
						}
						time.Sleep(5 * time.Millisecond)
					}
				}
			}(i)
		}

		// 运行指定时间
		time.Sleep(duration)
		close(stop)
		wg.Wait()

		t.Logf("并发读写混合测试完成，运行时间: %v", duration)
	})

	// 测试3：高压力并发测试
	t.Run("HighStressConcurrency", func(t *testing.T) {
		generator := NewDynamicGenerator()

		const numGoroutines = 200
		const numOperations = 100

		var wg sync.WaitGroup
		errors := make(chan error, numGoroutines)

		start := time.Now()

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				defer func() {
					if r := recover(); r != nil {
						errors <- fmt.Errorf("goroutine %d panic: %v", id, r)
					}
				}()

				profileName := fmt.Sprintf("stress_profile_%d", id%20)

				for j := 0; j < numOperations; j++ {
					// 混合操作
					if j%3 == 0 {
						// 读操作
						generator.getBestTemplate()
					} else {
						// 写操作
						generator.UpdateSuccessRate(profileName, j%2 == 0)
					}

					// 偶尔调用GenerateAdaptiveVariation
					if j%10 == 0 {
						_, err := generator.GenerateAdaptiveVariation("test-server.com")
						if err != nil {
							// 忽略生成错误，专注于并发安全
						}
					}
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		duration := time.Since(start)

		// 检查是否有panic
		errorCount := 0
		for err := range errors {
			t.Errorf("并发错误: %v", err)
			errorCount++
		}

		if errorCount == 0 {
			t.Logf("高压力并发测试通过，%d个goroutine，每个%d次操作，耗时: %v",
				numGoroutines, numOperations, duration)
		} else {
			t.Errorf("高压力并发测试失败，发生%d个错误", errorCount)
		}
	})

	// 测试4：数据一致性验证
	t.Run("DataConsistency", func(t *testing.T) {
		generator := NewDynamicGenerator()
		profileName := "consistency_test"

		const numGoroutines = 50
		const numOperations = 100

		var wg sync.WaitGroup
		successCount := int64(0)
		totalCount := int64(0)

		var mu sync.Mutex

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				for j := 0; j < numOperations; j++ {
					success := j%2 == 0 // 50%成功率
					generator.UpdateSuccessRate(profileName, success)

					mu.Lock()
					totalCount++
					if success {
						successCount++
					}
					mu.Unlock()
				}
			}(i)
		}

		wg.Wait()

		// 验证数据一致性
		expectedRate := float64(successCount) / float64(totalCount)

		// 获取实际成功率（需要通过getBestTemplate间接获取）
		generator.mu.RLock()
		actualRate := generator.successRates[profileName]
		generator.mu.RUnlock()

		t.Logf("数据一致性验证:")
		t.Logf("  总操作数: %d", totalCount)
		t.Logf("  成功操作数: %d", successCount)
		t.Logf("  期望成功率: %.3f", expectedRate)
		t.Logf("  实际成功率: %.3f", actualRate)

		// 由于使用指数移动平均，实际成功率可能与期望有差异，但应该在合理范围内
		if actualRate < 0.0 || actualRate > 1.0 {
			t.Errorf("成功率超出有效范围: %f", actualRate)
		}
	})
}

// TestRaceConditionDetection 测试竞态条件检测
func TestRaceConditionDetection(t *testing.T) {
	// 这个测试应该在启用race detector的情况下运行
	// go test -race

	generator := NewDynamicGenerator()

	const numGoroutines = 10
	const duration = 1 * time.Second

	var wg sync.WaitGroup
	stop := make(chan struct{})

	// 启动多个goroutine进行并发操作
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			profileName := fmt.Sprintf("race_test_%d", id)
			counter := 0

			for {
				select {
				case <-stop:
					return
				default:
					// 混合读写操作
					generator.UpdateSuccessRate(profileName, counter%2 == 0)
					generator.getBestTemplate()

					if counter%5 == 0 {
						generator.GenerateAdaptiveVariation("race-test.com")
					}

					counter++
				}
			}
		}(i)
	}

	time.Sleep(duration)
	close(stop)
	wg.Wait()

	t.Logf("竞态条件检测测试完成，运行时间: %v", duration)
}

// BenchmarkConcurrentAccess 并发访问性能基准测试
func BenchmarkConcurrentAccess(b *testing.B) {
	generator := NewDynamicGenerator()

	b.RunParallel(func(pb *testing.PB) {
		profileName := "benchmark_profile"
		counter := 0

		for pb.Next() {
			if counter%3 == 0 {
				generator.getBestTemplate()
			} else {
				generator.UpdateSuccessRate(profileName, counter%2 == 0)
			}
			counter++
		}
	})
}
