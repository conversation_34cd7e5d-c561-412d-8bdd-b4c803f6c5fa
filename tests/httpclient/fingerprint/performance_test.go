package fingerprint

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	metrics map[string]*PerformanceMetric
	mu      sync.RWMutex
}

// PerformanceMetric 性能指标
type PerformanceMetric struct {
	Name          string
	TotalCalls    int64
	TotalDuration time.Duration
	MinDuration   time.Duration
	MaxDuration   time.Duration
	AvgDuration   time.Duration
	LastUpdated   time.Time
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor() *PerformanceMonitor {
	return &PerformanceMonitor{
		metrics: make(map[string]*PerformanceMetric),
	}
}

// RecordMetric 记录性能指标
func (pm *PerformanceMonitor) RecordMetric(name string, duration time.Duration) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	metric, exists := pm.metrics[name]
	if !exists {
		metric = &PerformanceMetric{
			Name:        name,
			MinDuration: duration,
			MaxDuration: duration,
		}
		pm.metrics[name] = metric
	}

	metric.TotalCalls++
	metric.TotalDuration += duration
	metric.AvgDuration = time.Duration(int64(metric.TotalDuration) / metric.TotalCalls)
	metric.LastUpdated = time.Now()

	if duration < metric.MinDuration {
		metric.MinDuration = duration
	}
	if duration > metric.MaxDuration {
		metric.MaxDuration = duration
	}
}

// GetMetrics 获取所有性能指标
func (pm *PerformanceMonitor) GetMetrics() map[string]*PerformanceMetric {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	result := make(map[string]*PerformanceMetric)
	for k, v := range pm.metrics {
		result[k] = v
	}
	return result
}

// BenchmarkFingerprintSelection 基准测试指纹选择性能
func BenchmarkFingerprintSelection(b *testing.B) {
	manager := NewManager()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.SelectProfile("prod-intl-api.popmart.com")
		if err != nil {
			b.Fatalf("指纹选择失败: %v", err)
		}
	}
}

// BenchmarkDynamicGeneration 基准测试动态指纹生成性能
func BenchmarkDynamicGeneration(b *testing.B) {
	generator := NewDynamicGenerator()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := generator.GenerateVariation("chrome_120_base", 2)
		if err != nil {
			b.Fatalf("动态指纹生成失败: %v", err)
		}
	}
}

// BenchmarkSmartSelection 基准测试智能选择器性能（已移除智能选择器）
func BenchmarkSmartSelection(b *testing.B) {
	manager := NewManager()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.SelectProfileWithExclusions("prod-intl-api.popmart.com", nil)
		if err != nil {
			b.Fatalf("指纹选择失败: %v", err)
		}
	}
}

// TestConcurrentAccess 测试并发访问安全性
func TestConcurrentAccess(t *testing.T) {
	manager := NewManager()

	const numGoroutines = 100
	const numOperations = 10

	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines*numOperations)

	// 启动多个goroutine进行并发测试
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			for j := 0; j < numOperations; j++ {
				// 测试指纹选择
				_, err := manager.SelectProfile("test-server.com")
				if err != nil {
					errors <- fmt.Errorf("goroutine %d operation %d: %v", id, j, err)
					return
				}

				// 测试结果记录
				manager.RecordRequestResult("test-profile", true, 100*time.Millisecond, 200, nil)

				// 测试统计获取
				_ = manager.GetStats()
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	// 检查是否有错误
	for err := range errors {
		t.Errorf("并发测试错误: %v", err)
	}
}

// TestMemoryUsage 测试内存使用情况
func TestMemoryUsage(t *testing.T) {
	manager := NewManager()

	// 生成大量动态指纹
	for i := 0; i < 1000; i++ {
		profile, err := manager.dynamicGenerator.GenerateVariation("chrome_120_base", 2)
		if err != nil {
			t.Fatalf("生成动态指纹失败: %v", err)
		}

		// 添加到动态指纹池
		manager.dynamicProfiles[profile.Name] = profile
		manager.healthTrackers[profile.Name] = NewHealth(profile.Name, "TEST")
	}

	// 检查指纹数量
	if len(manager.dynamicProfiles) != 1000 {
		t.Errorf("期望1000个动态指纹，实际得到%d个", len(manager.dynamicProfiles))
	}

	// 测试清理功能
	// 这里可以添加内存清理测试
}

// LoadTest 负载测试
func LoadTest() {
	manager := NewManager()
	monitor := NewPerformanceMonitor()

	const numRequests = 10000
	const numWorkers = 50

	requests := make(chan int, numRequests)
	var wg sync.WaitGroup

	// 填充请求队列
	for i := 0; i < numRequests; i++ {
		requests <- i
	}
	close(requests)

	// 启动工作协程
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for requestID := range requests {
				start := time.Now()

				// 执行指纹选择
				_, err := manager.SelectProfile("load-test-server.com")
				if err != nil {
					fmt.Printf("Worker %d Request %d 失败: %v\n", workerID, requestID, err)
					continue
				}

				duration := time.Since(start)
				monitor.RecordMetric("fingerprint_selection", duration)

				// 模拟请求结果记录
				manager.RecordRequestResult("test-profile", true, duration, 200, nil)
			}
		}(i)
	}

	wg.Wait()

	// 输出性能统计
	metrics := monitor.GetMetrics()
	for name, metric := range metrics {
		fmt.Printf("指标: %s\n", name)
		fmt.Printf("  总调用次数: %d\n", metric.TotalCalls)
		fmt.Printf("  平均耗时: %v\n", metric.AvgDuration)
		fmt.Printf("  最小耗时: %v\n", metric.MinDuration)
		fmt.Printf("  最大耗时: %v\n", metric.MaxDuration)
		fmt.Printf("  总耗时: %v\n", metric.TotalDuration)
		fmt.Printf("\n")
	}
}

// OptimizationSuggestions 性能优化建议
func OptimizationSuggestions() []string {
	return []string{
		"1. 使用对象池减少内存分配",
		"2. 实现指纹缓存机制",
		"3. 优化锁的粒度，减少锁竞争",
		"4. 使用更高效的数据结构",
		"5. 实现异步的健康检查",
		"6. 添加指纹预热机制",
		"7. 优化随机数生成",
		"8. 实现智能的垃圾回收",
		"9. 使用内存映射文件存储大量指纹",
		"10. 实现分布式指纹管理",
	}
}
