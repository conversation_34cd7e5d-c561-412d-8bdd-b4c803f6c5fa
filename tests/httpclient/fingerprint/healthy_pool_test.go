package fingerprint

import (
	"crypto/tls"
	"fmt"
	"sync"
	"testing"
	"time"
)

// TestHealthyFingerprintPool_BasicOperations 测试基础操作
func TestHealthyFingerprintPool_BasicOperations(t *testing.T) {
	config := GetDefaultPoolConfig()
	config.MinVerifiedCount = 2
	config.MinCandidateCount = 2
	config.MinBackupCount = 1

	pool := NewHealthyFingerprintPool(config)

	// 创建测试指纹
	testProfile := &Profile{
		Name:        "TestProfile_1",
		BrowserType: "Chrome",
		OSType:      "Windows",
		Version:     "120.0.0.0",
		MinVersion:  0x0303,
		MaxVersion:  0x0304,
		IsDynamic:   false,
		SuccessRate: 1.0,
		CreatedAt:   time.Now(),
	}

	// 测试添加指纹
	err := pool.AddProfile("TEST", testProfile, TierVerified)
	if err != nil {
		t.Fatalf("添加指纹失败: %v", err)
	}

	// 测试获取指纹
	profile, err := pool.GetBestProfile("TEST", nil)
	if err != nil {
		t.Fatalf("获取指纹失败: %v", err)
	}

	if profile.Name != testProfile.Name {
		t.Errorf("期望指纹名称 %s，实际得到 %s", testProfile.Name, profile.Name)
	}

	// 测试统计信息
	stats := pool.GetPoolStats()
	if stats.TotalProfiles != 1 {
		t.Errorf("期望总指纹数 1，实际得到 %d", stats.TotalProfiles)
	}
	if stats.VerifiedCount != 1 {
		t.Errorf("期望已验证指纹数 1，实际得到 %d", stats.VerifiedCount)
	}
}

// TestHealthyFingerprintPool_ConcurrentAccess 测试并发安全性
func TestHealthyFingerprintPool_ConcurrentAccess(t *testing.T) {
	config := GetDefaultPoolConfig()
	pool := NewHealthyFingerprintPool(config)

	// 添加一些测试指纹
	for i := 0; i < 10; i++ {
		profile := &Profile{
			Name:        fmt.Sprintf("ConcurrentTest_%d", i),
			BrowserType: "Chrome",
			OSType:      "Windows",
			Version:     "120.0.0.0",
			MinVersion:  0x0303,
			MaxVersion:  0x0304,
			IsDynamic:   false,
			SuccessRate: 0.9,
			CreatedAt:   time.Now(),
		}
		pool.AddProfile("CONCURRENT", profile, TierVerified)
	}

	// 并发测试
	var wg sync.WaitGroup
	errors := make(chan error, 100)

	// 并发读取
	for i := 0; i < 50; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			_, err := pool.GetBestProfile("CONCURRENT", nil)
			if err != nil {
				errors <- err
			}
		}()
	}

	// 并发更新统计
	for i := 0; i < 50; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			profileName := fmt.Sprintf("ConcurrentTest_%d", index%10)
			pool.UpdateProfileStats(profileName, true, 100*time.Millisecond)
		}(i)
	}

	wg.Wait()
	close(errors)

	// 检查是否有错误
	for err := range errors {
		t.Errorf("并发操作错误: %v", err)
	}
}

// TestHealthyFingerprintPool_PromotionDemotion 测试晋升和降级机制
func TestHealthyFingerprintPool_PromotionDemotion(t *testing.T) {
	config := GetDefaultPoolConfig()
	config.ObservationPeriod = 1 * time.Second // 缩短观察期用于测试
	config.PromotionMinUses = 2
	config.PromotionSuccessRate = 0.8

	pool := NewHealthyFingerprintPool(config)

	// 添加候选指纹
	candidateProfile := &Profile{
		Name:        "CandidateTest",
		BrowserType: "Chrome",
		OSType:      "Windows",
		Version:     "120.0.0.0",
		MinVersion:  0x0303,
		MaxVersion:  0x0304,
		IsDynamic:   false,
		SuccessRate: 0.9,
		CreatedAt:   time.Now(),
	}

	err := pool.AddProfile("PROMOTION", candidateProfile, TierCandidate)
	if err != nil {
		t.Fatalf("添加候选指纹失败: %v", err)
	}

	// 等待观察期
	time.Sleep(1100 * time.Millisecond)

	// 模拟成功使用
	for i := 0; i < 5; i++ {
		pool.UpdateProfileStats("CandidateTest", true, 100*time.Millisecond)
	}

	// 检查是否晋升
	stats := pool.GetPoolStats()
	if stats.PromotionCount == 0 {
		t.Log("注意：指纹可能尚未晋升，这可能是正常的")
	}
}

// TestHealthyFingerprintPool_Rotation 测试轮换机制
func TestHealthyFingerprintPool_Rotation(t *testing.T) {
	config := GetDefaultPoolConfig()
	pool := NewHealthyFingerprintPool(config)

	// 添加多个指纹
	profiles := []string{"Rotation_1", "Rotation_2", "Rotation_3"}
	for _, name := range profiles {
		profile := &Profile{
			Name:        name,
			BrowserType: "Chrome",
			OSType:      "Windows",
			Version:     "120.0.0.0",
			MinVersion:  0x0303,
			MaxVersion:  0x0304,
			IsDynamic:   false,
			SuccessRate: 0.9,
			CreatedAt:   time.Now(),
		}
		pool.AddProfile("ROTATION", profile, TierVerified)
	}

	// 测试轮换
	currentProfile := "Rotation_1"
	excludeProfiles := []string{currentProfile}

	rotatedProfile, err := pool.RotateProfile("ROTATION", currentProfile, excludeProfiles)
	if err != nil {
		t.Fatalf("轮换失败: %v", err)
	}

	if rotatedProfile.Name == currentProfile {
		t.Errorf("轮换后得到相同指纹: %s", rotatedProfile.Name)
	}

	// 测试轮换建议
	recommendation := pool.GetRotationRecommendation("ROTATION", currentProfile)
	if recommendation == nil {
		t.Error("轮换建议为空")
	}
}

// TestHealthyFingerprintPool_Configuration 测试配置加载
func TestHealthyFingerprintPool_Configuration(t *testing.T) {
	// 测试默认配置
	defaultConfig := GetDefaultPoolConfig()
	if defaultConfig.MinVerifiedCount != 5 {
		t.Errorf("默认最小已验证指纹数期望 5，实际得到 %d", defaultConfig.MinVerifiedCount)
	}

	// 测试配置文件加载（如果文件存在）
	configPath := "configs/fingerprint_optimization.yaml"
	loadedConfig, err := LoadPoolConfigFromFile(configPath)
	if err != nil {
		t.Logf("配置文件加载失败（可能文件不存在）: %v", err)
		return
	}

	// 验证配置加载
	if loadedConfig.MinVerifiedCount <= 0 {
		t.Error("加载的配置中最小已验证指纹数无效")
	}
}

// TestHealthyFingerprintPool_SmartGeneration 测试智能生成
func TestHealthyFingerprintPool_SmartGeneration(t *testing.T) {
	config := GetDefaultPoolConfig()
	config.EnableSmartGeneration = true
	config.MinCandidateCount = 1

	pool := NewHealthyFingerprintPool(config)

	// 添加成功的模板指纹
	templateProfile := &Profile{
		Name:             "SuccessfulTemplate",
		BrowserType:      "Chrome",
		OSType:           "Windows",
		Version:          "120.0.0.0",
		MinVersion:       0x0303,
		MaxVersion:       0x0304,
		CipherSuites:     []uint16{0x1301, 0x1302, 0x1303},
		CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
		NextProtos:       []string{"h2", "http/1.1"},
		IsDynamic:        false,
		SuccessRate:      0.95,
		CreatedAt:        time.Now(),
	}

	pool.AddProfile("GENERATION", templateProfile, TierVerified)

	// 模拟成功使用以建立模板
	for i := 0; i < 10; i++ {
		pool.UpdateProfileStats("SuccessfulTemplate", true, 100*time.Millisecond)
	}

	// 创建一个模拟的动态生成器
	mockGenerator := &DynamicGenerator{}

	// 测试确保健康池充足
	err := pool.EnsureHealthyPool("GENERATION", mockGenerator)
	if err != nil {
		t.Logf("确保健康池充足时出现错误（可能是正常的）: %v", err)
	}

	// 验证池状态
	stats := pool.GetPoolStats()
	if stats.TotalProfiles == 0 {
		t.Error("健康池中没有指纹")
	}
}

// BenchmarkHealthyFingerprintPool_GetBestProfile 性能基准测试
func BenchmarkHealthyFingerprintPool_GetBestProfile(b *testing.B) {
	config := GetDefaultPoolConfig()
	pool := NewHealthyFingerprintPool(config)

	// 添加测试指纹
	for i := 0; i < 100; i++ {
		profile := &Profile{
			Name:        fmt.Sprintf("BenchProfile_%d", i),
			BrowserType: "Chrome",
			OSType:      "Windows",
			Version:     "120.0.0.0",
			MinVersion:  0x0303,
			MaxVersion:  0x0304,
			IsDynamic:   false,
			SuccessRate: 0.9,
			CreatedAt:   time.Now(),
		}
		pool.AddProfile("BENCH", profile, TierVerified)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := pool.GetBestProfile("BENCH", nil)
		if err != nil {
			b.Fatalf("获取指纹失败: %v", err)
		}
	}
}
