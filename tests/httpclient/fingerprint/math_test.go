package fingerprint

import (
	"math"
	"testing"
)

// TestSuccessRateCalculation 测试成功率计算修复
func TestSuccessRateCalculation(t *testing.T) {
	generator := NewDynamicGenerator()
	profileName := "test_profile"

	// 测试1：初始成功
	t.Run("InitialSuccess", func(t *testing.T) {
		generator.UpdateSuccessRate(profileName, true)
		rate := generator.successRates[profileName]

		if rate != 1.0 {
			t.Errorf("初始成功率应该为1.0，实际为%f", rate)
		}
	})

	// 测试2：初始失败
	t.Run("InitialFailure", func(t *testing.T) {
		generator2 := NewDynamicGenerator()
		generator2.UpdateSuccessRate(profileName, false)
		rate := generator2.successRates[profileName]

		if rate != 0.0 {
			t.Errorf("初始失败率应该为0.0，实际为%f", rate)
		}
	})

	// 测试3：指数移动平均计算
	t.Run("ExponentialMovingAverage", func(t *testing.T) {
		generator3 := NewDynamicGenerator()
		alpha := 0.1

		// 设置初始成功率为0.5
		generator3.successRates[profileName] = 0.5

		// 记录一次成功
		generator3.UpdateSuccessRate(profileName, true)
		expectedRate := alpha*1.0 + (1-alpha)*0.5 // 0.1*1.0 + 0.9*0.5 = 0.55
		actualRate := generator3.successRates[profileName]

		if math.Abs(actualRate-expectedRate) > 0.001 {
			t.Errorf("成功后的成功率计算错误，期望%f，实际%f", expectedRate, actualRate)
		}

		// 记录一次失败
		generator3.UpdateSuccessRate(profileName, false)
		expectedRate = alpha*0.0 + (1-alpha)*actualRate // 0.1*0.0 + 0.9*0.55 = 0.495
		actualRate = generator3.successRates[profileName]

		if math.Abs(actualRate-expectedRate) > 0.001 {
			t.Errorf("失败后的成功率计算错误，期望%f，实际%f", expectedRate, actualRate)
		}
	})

	// 测试4：连续成功的收敛性
	t.Run("SuccessConvergence", func(t *testing.T) {
		generator4 := NewDynamicGenerator()
		generator4.successRates[profileName] = 0.0 // 从0开始

		// 连续100次成功
		for i := 0; i < 100; i++ {
			generator4.UpdateSuccessRate(profileName, true)
		}

		finalRate := generator4.successRates[profileName]

		// 应该接近1.0但不会完全等于1.0（除非无限次）
		if finalRate < 0.99 {
			t.Errorf("连续成功后成功率应该接近1.0，实际为%f", finalRate)
		}

		if finalRate >= 1.0 {
			t.Errorf("连续成功后成功率不应该等于或超过1.0，实际为%f", finalRate)
		}
	})

	// 测试5：连续失败的收敛性
	t.Run("FailureConvergence", func(t *testing.T) {
		generator5 := NewDynamicGenerator()
		generator5.successRates[profileName] = 1.0 // 从1开始

		// 连续100次失败
		for i := 0; i < 100; i++ {
			generator5.UpdateSuccessRate(profileName, false)
		}

		finalRate := generator5.successRates[profileName]

		// 应该接近0.0但不会完全等于0.0（除非无限次）
		if finalRate > 0.01 {
			t.Errorf("连续失败后成功率应该接近0.0，实际为%f", finalRate)
		}

		if finalRate <= 0.0 {
			t.Errorf("连续失败后成功率不应该等于或小于0.0，实际为%f", finalRate)
		}
	})

	// 测试6：交替成功失败的稳定性
	t.Run("AlternatingStability", func(t *testing.T) {
		generator6 := NewDynamicGenerator()
		generator6.successRates[profileName] = 0.5 // 从0.5开始

		// 交替成功失败100次
		for i := 0; i < 100; i++ {
			generator6.UpdateSuccessRate(profileName, i%2 == 0) // 偶数成功，奇数失败
		}

		finalRate := generator6.successRates[profileName]

		// 应该稳定在0.5附近
		if math.Abs(finalRate-0.5) > 0.1 {
			t.Errorf("交替成功失败后成功率应该稳定在0.5附近，实际为%f", finalRate)
		}
	})
}

// TestMathematicalProperties 测试数学性质
func TestMathematicalProperties(t *testing.T) {
	generator := NewDynamicGenerator()
	profileName := "math_test_profile"

	// 测试指数移动平均的数学性质
	t.Run("EMAProperties", func(t *testing.T) {
		// 设置初始值
		initialRate := 0.3
		generator.successRates[profileName] = initialRate

		// 记录成功率变化
		rates := []float64{initialRate}

		// 连续5次成功
		for i := 0; i < 5; i++ {
			generator.UpdateSuccessRate(profileName, true)
			rates = append(rates, generator.successRates[profileName])
		}

		// 验证每次更新都是单调递增的（因为都是成功）
		for i := 1; i < len(rates); i++ {
			if rates[i] <= rates[i-1] {
				t.Errorf("连续成功时成功率应该单调递增，第%d次：%f，第%d次：%f",
					i-1, rates[i-1], i, rates[i])
			}
		}

		// 验证增长率递减（指数移动平均的特性）
		increments := make([]float64, len(rates)-1)
		for i := 1; i < len(rates); i++ {
			increments[i-1] = rates[i] - rates[i-1]
		}

		for i := 1; i < len(increments); i++ {
			if increments[i] >= increments[i-1] {
				t.Errorf("指数移动平均的增长率应该递减，第%d次增长：%f，第%d次增长：%f",
					i-1, increments[i-1], i, increments[i])
			}
		}
	})

	// 测试边界条件
	t.Run("BoundaryConditions", func(t *testing.T) {
		// 测试成功率永远不会超过1.0
		generator2 := NewDynamicGenerator()
		generator2.successRates[profileName] = 0.99

		for i := 0; i < 1000; i++ {
			generator2.UpdateSuccessRate(profileName, true)
			if generator2.successRates[profileName] > 1.0 {
				t.Errorf("成功率不应该超过1.0，实际为%f", generator2.successRates[profileName])
				break
			}
		}

		// 测试成功率永远不会低于0.0
		generator3 := NewDynamicGenerator()
		generator3.successRates[profileName] = 0.01

		for i := 0; i < 1000; i++ {
			generator3.UpdateSuccessRate(profileName, false)
			if generator3.successRates[profileName] < 0.0 {
				t.Errorf("成功率不应该低于0.0，实际为%f", generator3.successRates[profileName])
				break
			}
		}
	})
}

// BenchmarkSuccessRateUpdate 成功率更新性能测试
func BenchmarkSuccessRateUpdate(b *testing.B) {
	generator := NewDynamicGenerator()
	profileName := "benchmark_profile"

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		generator.UpdateSuccessRate(profileName, i%2 == 0)
	}
}

// TestConcurrentSuccessRateUpdate 并发成功率更新测试
func TestConcurrentSuccessRateUpdate(t *testing.T) {
	generator := NewDynamicGenerator()
	profileName := "concurrent_test_profile"

	// 并发更新成功率
	const numGoroutines = 10
	const numUpdates = 100

	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			for j := 0; j < numUpdates; j++ {
				generator.UpdateSuccessRate(profileName, (id+j)%2 == 0)
			}
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// 检查最终成功率是否在合理范围内
	finalRate := generator.successRates[profileName]
	if finalRate < 0.0 || finalRate > 1.0 {
		t.Errorf("并发更新后成功率超出范围[0,1]，实际为%f", finalRate)
	}

	t.Logf("并发更新后的最终成功率：%f", finalRate)
}
