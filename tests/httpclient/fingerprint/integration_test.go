package fingerprint

import (
	"fmt"
	"go-monitor/pkg/httpclient/fingerprint"
	"testing"
	"time"
)

// TestManager_HealthyPoolIntegration 测试Manager与健康指纹池的集成
func TestManager_HealthyPoolIntegration(t *testing.T) {
	// 创建管理器
	manager := fingerprint.NewManager()

	// 等待初始化完成
	time.Sleep(100 * time.Millisecond)

	// 测试指纹选择
	profile, err := manager.SelectProfile("test.example.com")
	if err != nil {
		t.Fatalf("选择指纹失败: %v", err)
	}

	if profile == nil {
		t.Fatal("选择的指纹为空")
	}

	t.Logf("选择的指纹: %s", profile.Name)

	// 测试记录请求结果
	manager.RecordRequestResult(profile.Name, true, 100*time.Millisecond, 200, nil)

	// 测试获取统计信息
	stats := manager.GetStats()
	if stats == nil {
		t.Fatal("统计信息为空")
	}

	// 检查健康指纹池统计
	if healthyPoolStats, exists := stats["healthy_pool"]; exists {
		t.Logf("健康指纹池统计: %+v", healthyPoolStats)
	} else {
		t.Log("健康指纹池统计不存在（可能未启用）")
	}
}

// TestManager_HealthyPoolStats 测试健康指纹池统计功能
func TestManager_HealthyPoolStats(t *testing.T) {
	manager := NewManager()

	// 获取健康指纹池详细统计
	poolStats := manager.GetHealthyPoolStats()
	if poolStats == nil {
		t.Fatal("健康指纹池统计为空")
	}

	enabled, exists := poolStats["enabled"]
	if !exists {
		t.Fatal("健康指纹池启用状态未知")
	}

	if enabled.(bool) {
		t.Log("健康指纹池已启用")

		// 检查统计字段
		expectedFields := []string{
			"total_profiles", "verified_count", "candidate_count", "backup_count",
			"promotion_count", "demotion_count", "generation_count", "avg_success_rate",
		}

		for _, field := range expectedFields {
			if _, exists := poolStats[field]; !exists {
				t.Errorf("缺少统计字段: %s", field)
			}
		}
	} else {
		t.Log("健康指纹池未启用")
	}
}

// TestManager_RotationRecommendations 测试轮换建议功能
func TestManager_RotationRecommendations(t *testing.T) {
	manager := NewManager()

	// 选择一个指纹
	profile, err := manager.SelectProfile("rotation.example.com")
	if err != nil {
		t.Fatalf("选择指纹失败: %v", err)
	}

	// 获取轮换建议
	recommendations := manager.GetRotationRecommendations("rotation.example.com", profile.Name)
	if recommendations == nil {
		t.Fatal("轮换建议为空")
	}

	// 检查建议字段
	expectedFields := []string{"current_profile", "should_rotate", "reason", "urgency", "region"}
	for _, field := range expectedFields {
		if _, exists := recommendations[field]; !exists {
			t.Errorf("缺少轮换建议字段: %s", field)
		}
	}

	t.Logf("轮换建议: %+v", recommendations)
}

// TestManager_ConfigurationReload 测试配置重新加载
func TestManager_ConfigurationReload(t *testing.T) {
	manager := NewManager()

	// 获取初始统计
	initialStats := manager.GetHealthyPoolStats()

	// 尝试重新加载配置
	err := manager.ReloadHealthyPoolConfig()
	if err != nil {
		t.Logf("重新加载配置失败（可能配置文件不存在）: %v", err)
		return
	}

	// 获取重新加载后的统计
	reloadedStats := manager.GetHealthyPoolStats()

	// 比较统计信息
	if initialStats["enabled"] != reloadedStats["enabled"] {
		t.Log("配置重新加载后启用状态发生变化")
	}

	t.Log("配置重新加载成功")
}

// TestManager_TLSConfigCreation 测试TLS配置创建
func TestManager_TLSConfigCreation(t *testing.T) {
	manager := NewManager()

	// 创建TLS配置
	tlsConfig, profileName, err := manager.CreateManagedTLSConfig("tls.example.com")
	if err != nil {
		t.Fatalf("创建TLS配置失败: %v", err)
	}

	if tlsConfig == nil {
		t.Fatal("TLS配置为空")
	}

	if profileName == "" {
		t.Fatal("指纹名称为空")
	}

	// 验证TLS配置基本字段
	if tlsConfig.ServerName != "tls.example.com" {
		t.Errorf("期望服务器名称 'tls.example.com'，实际得到 '%s'", tlsConfig.ServerName)
	}

	if len(tlsConfig.CipherSuites) == 0 {
		t.Error("密码套件为空")
	}

	t.Logf("创建TLS配置成功，使用指纹: %s", profileName)
}

// TestManager_ExclusionHandling 测试排除指纹处理
func TestManager_ExclusionHandling(t *testing.T) {
	manager := NewManager()

	// 选择第一个指纹
	profile1, err := manager.SelectProfile("exclusion.example.com")
	if err != nil {
		t.Fatalf("选择第一个指纹失败: %v", err)
	}

	// 排除第一个指纹，选择第二个
	excludeProfiles := []string{profile1.Name}
	profile2, err := manager.SelectProfileWithExclusions("exclusion.example.com", excludeProfiles)
	if err != nil {
		t.Fatalf("选择第二个指纹失败: %v", err)
	}

	// 验证选择了不同的指纹
	if profile1.Name == profile2.Name {
		t.Error("排除机制失效，选择了相同的指纹")
	}

	t.Logf("排除指纹 %s，选择了 %s", profile1.Name, profile2.Name)
}

// TestManager_PerformanceUnderLoad 测试负载下的性能
func TestManager_PerformanceUnderLoad(t *testing.T) {
	manager := NewManager()

	// 并发选择指纹
	const numGoroutines = 100
	const numRequests = 10

	results := make(chan error, numGoroutines*numRequests)

	start := time.Now()

	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			for j := 0; j < numRequests; j++ {
				serverName := fmt.Sprintf("load-test-%d.example.com", goroutineID)

				profile, err := manager.SelectProfile(serverName)
				if err != nil {
					results <- fmt.Errorf("goroutine %d, request %d: %v", goroutineID, j, err)
					continue
				}

				// 模拟请求结果记录
				success := j%3 != 0 // 大约67%成功率
				manager.RecordRequestResult(profile.Name, success, time.Duration(j)*time.Millisecond, 200, nil)

				results <- nil
			}
		}(i)
	}

	// 收集结果
	errorCount := 0
	for i := 0; i < numGoroutines*numRequests; i++ {
		if err := <-results; err != nil {
			errorCount++
			if errorCount <= 5 { // 只打印前5个错误
				t.Logf("负载测试错误: %v", err)
			}
		}
	}

	duration := time.Since(start)

	if errorCount > 0 {
		t.Logf("负载测试完成，%d/%d 请求失败，耗时: %v", errorCount, numGoroutines*numRequests, duration)
	} else {
		t.Logf("负载测试完成，所有请求成功，耗时: %v", duration)
	}

	// 检查最终统计
	finalStats := manager.GetStats()
	if healthyPoolStats, exists := finalStats["healthy_pool"]; exists {
		t.Logf("负载测试后健康指纹池统计: %+v", healthyPoolStats)
	}
}

// TestManager_ErrorRecovery 测试错误恢复能力
func TestManager_ErrorRecovery(t *testing.T) {
	manager := NewManager()

	// 选择一个指纹
	profile, err := manager.SelectProfile("error-recovery.example.com")
	if err != nil {
		t.Fatalf("选择指纹失败: %v", err)
	}

	// 模拟连续失败
	for i := 0; i < 10; i++ {
		manager.RecordRequestResult(profile.Name, false, 5*time.Second, 403, fmt.Errorf("模拟错误 %d", i))
	}

	// 等待一段时间让系统处理
	time.Sleep(100 * time.Millisecond)

	// 尝试选择新指纹
	newProfile, err := manager.SelectProfile("error-recovery.example.com")
	if err != nil {
		t.Fatalf("错误恢复后选择指纹失败: %v", err)
	}

	// 验证系统仍然可以工作
	if newProfile == nil {
		t.Fatal("错误恢复后指纹为空")
	}

	t.Logf("错误恢复测试完成，原指纹: %s，新指纹: %s", profile.Name, newProfile.Name)
}

// BenchmarkManager_SelectProfile 指纹选择性能基准测试
func BenchmarkManager_SelectProfile(b *testing.B) {
	manager := NewManager()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		serverName := fmt.Sprintf("bench-%d.example.com", i%100)
		_, err := manager.SelectProfile(serverName)
		if err != nil {
			b.Fatalf("选择指纹失败: %v", err)
		}
	}
}

// BenchmarkManager_RecordResult 结果记录性能基准测试
func BenchmarkManager_RecordResult(b *testing.B) {
	manager := NewManager()

	// 预先选择一个指纹
	profile, err := manager.SelectProfile("benchmark.example.com")
	if err != nil {
		b.Fatalf("选择指纹失败: %v", err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		success := i%2 == 0
		manager.RecordRequestResult(profile.Name, success, 100*time.Millisecond, 200, nil)
	}
}

// TestRiskControlDetection 测试风控检测功能
func TestRiskControlDetection(t *testing.T) {
	manager := fingerprint.NewManager()

	// 等待初始化
	time.Sleep(100 * time.Millisecond)

	// 选择一个指纹
	profile, err := manager.SelectProfile("popmart.example.com")
	if err != nil {
		t.Fatalf("选择指纹失败: %v", err)
	}

	t.Logf("测试指纹: %s", profile.Name)

	// 测试470风控检测
	t.Run("470风控检测", func(t *testing.T) {
		// 记录470状态码
		manager.RecordRequestResultWithDetails(profile.Name, false, 100*time.Millisecond,
			"popmart.example.com", 470, fmt.Errorf("request blocked"))

		// 检查健康指纹池
		healthyPool := manager.GetHealthyPool()
		if healthyPool != nil {
			riskStats := healthyPool.GetRiskControlStats()
			t.Logf("风控统计: %+v", riskStats)

			// 验证风控计数
			if totalRisk, ok := riskStats["total_risk_control"].(int64); ok && totalRisk > 0 {
				t.Logf("✅ 成功检测到风控事件: %d", totalRisk)
			} else {
				t.Error("❌ 未检测到风控事件")
			}

			// 检查被阻止的指纹
			blockedProfiles := healthyPool.GetBlockedProfiles()
			if len(blockedProfiles) > 0 {
				t.Logf("✅ 指纹已被阻止: %+v", blockedProfiles)
			} else {
				t.Log("⚠️ 没有指纹被阻止")
			}
		} else {
			t.Log("健康指纹池未启用")
		}
	})

	// 测试471风控检测
	t.Run("471风控检测", func(t *testing.T) {
		// 记录471状态码
		manager.RecordRequestResultWithDetails(profile.Name, false, 150*time.Millisecond,
			"popmart.example.com", 471, fmt.Errorf("access denied"))

		// 检查风控统计
		healthyPool := manager.GetHealthyPool()
		if healthyPool != nil {
			riskStats := healthyPool.GetRiskControlStats()

			// 验证重度风控计数
			if severeRisk, ok := riskStats["severe_risk_count"].(int64); ok && severeRisk > 0 {
				t.Logf("✅ 成功检测到重度风控: %d", severeRisk)
			} else {
				t.Error("❌ 未检测到重度风控")
			}
		}
	})
}

// TestFingerprintBlocking 测试指纹阻止机制
func TestFingerprintBlocking(t *testing.T) {
	manager := NewManager()

	// 等待初始化
	time.Sleep(100 * time.Millisecond)

	healthyPool := manager.GetHealthyPool()
	if healthyPool == nil {
		t.Skip("健康指纹池未启用，跳过测试")
	}

	// 手动阻止指纹
	t.Run("手动阻止指纹", func(t *testing.T) {
		success := healthyPool.BlockProfile("test-profile", "测试阻止", 5*time.Minute)
		if success {
			t.Log("✅ 成功阻止指纹")
		} else {
			t.Log("⚠️ 指纹阻止失败（可能指纹不存在）")
		}

		// 检查被阻止的指纹
		blockedProfiles := healthyPool.GetBlockedProfiles()
		t.Logf("被阻止的指纹: %+v", blockedProfiles)
	})

	// 解除指纹阻止
	t.Run("解除指纹阻止", func(t *testing.T) {
		success := healthyPool.UnblockProfile("test-profile")
		if success {
			t.Log("✅ 成功解除指纹阻止")
		} else {
			t.Log("⚠️ 解除指纹阻止失败")
		}
	})
}
