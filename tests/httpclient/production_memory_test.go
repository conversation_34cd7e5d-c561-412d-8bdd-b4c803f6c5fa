// Package httpclient_test 生产环境内存泄漏测试
package httpclient_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"runtime"
	"sync"
	"testing"
	"time"
	
	"go-monitor/pkg/httpclient/client"
	"go-monitor/pkg/httpclient/foundation"
)

// TestProductionScenarioMemoryLeak 模拟生产环境的内存泄漏测试
func TestProductionScenarioMemoryLeak(t *testing.T) {
	if testing.Short() {
		t.<PERSON><PERSON>("跳过生产环境内存泄漏测试（短测试模式）")
	}
	
	// 模拟监控程序的真实场景：
	// - 多个爬虫客户端同时运行
	// - 每个客户端定期发送请求
	// - 运行较长时间（30分钟）
	testDuration := 30 * time.Minute
	
	t.Logf("开始生产环境内存泄漏测试，持续时间: %v", testDuration)
	t.Logf("模拟场景: 5个爬虫客户端，每个每5秒发送一次请求")
	
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟真实API响应时间
		time.Sleep(100 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "ok", "data": "test response"}`))
	}))
	defer server.Close()
	
	// 记录初始内存状态
	runtime.GC()
	runtime.GC()
	var initialStats runtime.MemStats
	runtime.ReadMemStats(&initialStats)
	
	t.Logf("初始内存状态:")
	t.Logf("  Alloc: %d bytes (%.2f MB)", initialStats.Alloc, float64(initialStats.Alloc)/1024/1024)
	t.Logf("  Sys: %d bytes (%.2f MB)", initialStats.Sys, float64(initialStats.Sys)/1024/1024)
	t.Logf("  NumGC: %d", initialStats.NumGC)
	
	startTime := time.Now()
	endTime := startTime.Add(testDuration)
	
	var totalRequests int64
	var totalErrors int64
	var mu sync.Mutex
	
	// 启动5个爬虫客户端
	const numSpiders = 5
	var wg sync.WaitGroup
	
	for i := 0; i < numSpiders; i++ {
		wg.Add(1)
		go func(spiderID int) {
			defer wg.Done()
			
			// 创建爬虫客户端
			config := &foundation.Config{
				EnableMonitoring:   true,
				EnableRetry:        true,
				EnableFingerprint:  false,
				MaxRetries:         2,
				RetryDelay:         1 * time.Second,
				Timeout:           10 * time.Second,
			}
			
			spiderClient := client.NewAdvancedClient(config)
			defer spiderClient.Close()
			
			ctx := context.Background()
			requestCount := 0
			errorCount := 0
			
			// 每5秒发送一次请求
			ticker := time.NewTicker(5 * time.Second)
			defer ticker.Stop()
			
			for {
				select {
				case <-ticker.C:
					if time.Now().After(endTime) {
						goto done
					}
					
					// 发送请求
					_, err := spiderClient.Get(ctx, server.URL, map[string]string{
						"User-Agent": fmt.Sprintf("Spider-%d", spiderID),
						"X-Spider-ID": fmt.Sprintf("%d", spiderID),
					})
					
					requestCount++
					if err != nil {
						errorCount++
					}
					
				case <-time.After(testDuration):
					goto done
				}
			}
			
		done:
			// 更新总计数
			mu.Lock()
			totalRequests += int64(requestCount)
			totalErrors += int64(errorCount)
			mu.Unlock()
			
			t.Logf("Spider %d 完成: 请求 %d, 错误 %d", spiderID, requestCount, errorCount)
		}(i)
	}
	
	// 定期监控内存使用情况
	monitorDone := make(chan bool)
	go func() {
		ticker := time.NewTicker(5 * time.Minute) // 每5分钟检查一次
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				runtime.GC()
				var currentStats runtime.MemStats
				runtime.ReadMemStats(&currentStats)
				
				elapsed := time.Since(startTime)
				allocDiff := int64(currentStats.Alloc) - int64(initialStats.Alloc)
				sysDiff := int64(currentStats.Sys) - int64(initialStats.Sys)
				
				t.Logf("运行时间 %v - 内存状态:", elapsed.Round(time.Minute))
				t.Logf("  Alloc: %d bytes (%.2f MB, +%.2f MB)", 
					currentStats.Alloc, 
					float64(currentStats.Alloc)/1024/1024,
					float64(allocDiff)/1024/1024)
				t.Logf("  Sys: %d bytes (%.2f MB, +%.2f MB)", 
					currentStats.Sys, 
					float64(currentStats.Sys)/1024/1024,
					float64(sysDiff)/1024/1024)
				t.Logf("  NumGC: %d (+%d)", currentStats.NumGC, currentStats.NumGC-initialStats.NumGC)
				t.Logf("  请求总数: %d, 错误总数: %d", totalRequests, totalErrors)
				
				// 检查是否有明显的内存泄漏
				allocMB := float64(allocDiff) / 1024 / 1024
				if allocMB > 100 { // 超过100MB认为有问题
					t.Errorf("检测到严重内存泄漏: Alloc增长 %.2f MB", allocMB)
					close(monitorDone)
					return
				}
				
			case <-monitorDone:
				return
			}
		}
	}()
	
	// 等待所有爬虫完成
	wg.Wait()
	close(monitorDone)
	
	// 强制垃圾回收
	runtime.GC()
	runtime.GC()
	runtime.GC()
	
	// 等待GC完成
	time.Sleep(2 * time.Second)
	
	// 记录最终内存状态
	var finalStats runtime.MemStats
	runtime.ReadMemStats(&finalStats)
	
	actualDuration := time.Since(startTime)
	
	t.Logf("生产环境测试完成，实际运行时间: %v", actualDuration.Round(time.Minute))
	t.Logf("总请求数: %d", totalRequests)
	t.Logf("总错误数: %d", totalErrors)
	if totalRequests > 0 {
		t.Logf("请求成功率: %.2f%%", float64(totalRequests-totalErrors)/float64(totalRequests)*100)
		t.Logf("平均RPS: %.2f", float64(totalRequests)/actualDuration.Seconds())
	}
	
	// 计算内存变化
	allocDiff := int64(finalStats.Alloc) - int64(initialStats.Alloc)
	sysDiff := int64(finalStats.Sys) - int64(initialStats.Sys)
	
	t.Logf("最终内存状态:")
	t.Logf("  Alloc: %d bytes (%.2f MB)", finalStats.Alloc, float64(finalStats.Alloc)/1024/1024)
	t.Logf("  Sys: %d bytes (%.2f MB)", finalStats.Sys, float64(finalStats.Sys)/1024/1024)
	t.Logf("  NumGC: %d", finalStats.NumGC)
	
	t.Logf("内存变化:")
	t.Logf("  Alloc差异: %d bytes (%.2f MB)", allocDiff, float64(allocDiff)/1024/1024)
	t.Logf("  Sys差异: %d bytes (%.2f MB)", sysDiff, float64(sysDiff)/1024/1024)
	t.Logf("  GC次数增加: %d", finalStats.NumGC-initialStats.NumGC)
	
	// 生产环境的内存泄漏判断标准
	// 对于监控程序，30分钟内存增长应该控制在很小的范围内
	maxAllowedAllocGrowthMB := float64(50) // 50MB
	maxAllowedSysGrowthMB := float64(100)  // 100MB
	
	allocGrowthMB := float64(allocDiff) / 1024 / 1024
	sysGrowthMB := float64(sysDiff) / 1024 / 1024
	
	if allocGrowthMB > maxAllowedAllocGrowthMB {
		t.Errorf("检测到内存泄漏: Alloc增长 %.2f MB，超过允许的 %.2f MB", 
			allocGrowthMB, maxAllowedAllocGrowthMB)
	}
	
	if sysGrowthMB > maxAllowedSysGrowthMB {
		t.Errorf("检测到内存泄漏: Sys增长 %.2f MB，超过允许的 %.2f MB", 
			sysGrowthMB, maxAllowedSysGrowthMB)
	}
	
	// 计算每个请求的平均内存使用
	if totalRequests > 0 {
		avgAllocPerRequest := float64(allocDiff) / float64(totalRequests)
		t.Logf("每个请求平均内存增长: %.2f bytes", avgAllocPerRequest)
		
		if avgAllocPerRequest > 10000 { // 每个请求超过10KB增长认为有问题
			t.Errorf("每个请求内存增长过大: %.2f bytes", avgAllocPerRequest)
		}
	}
	
	// 检查内存增长率
	if actualDuration.Hours() > 0 {
		allocGrowthPerHour := allocGrowthMB / actualDuration.Hours()
		t.Logf("每小时内存增长率: %.2f MB/h", allocGrowthPerHour)
		
		// 如果按这个速度增长，24小时内存增长不应超过500MB
		if allocGrowthPerHour > 20 { // 20MB/小时
			t.Errorf("内存增长率过高: %.2f MB/h，24小时将增长 %.2f MB", 
				allocGrowthPerHour, allocGrowthPerHour*24)
		}
	}
	
	if allocGrowthMB <= maxAllowedAllocGrowthMB && sysGrowthMB <= maxAllowedSysGrowthMB {
		t.Logf("✅ 生产环境内存泄漏测试通过！")
		t.Logf("✅ 内存增长在可接受范围内，适合长期运行")
	}
}

// TestQuickMemoryLeakCheck 快速内存泄漏检查（5分钟）
func TestQuickMemoryLeakCheck(t *testing.T) {
	testDuration := 5 * time.Minute
	
	t.Logf("开始快速内存泄漏检查，持续时间: %v", testDuration)
	
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(10 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()
	
	// 记录初始内存
	runtime.GC()
	runtime.GC()
	var initialStats runtime.MemStats
	runtime.ReadMemStats(&initialStats)
	
	startTime := time.Now()
	endTime := startTime.Add(testDuration)
	
	var requestCount int64
	
	// 创建单个客户端进行测试
	config := &foundation.Config{
		EnableMonitoring:   true,
		EnableRetry:        true,
		EnableFingerprint:  false,
	}
	
	advancedClient := client.NewAdvancedClient(config)
	defer advancedClient.Close()
	
	ctx := context.Background()
	
	// 持续发送请求
	for time.Now().Before(endTime) {
		_, err := advancedClient.Get(ctx, server.URL, nil)
		if err == nil {
			requestCount++
		}
		
		// 每1000个请求休息一下
		if requestCount%1000 == 0 {
			time.Sleep(10 * time.Millisecond)
		}
	}
	
	// 强制GC
	runtime.GC()
	runtime.GC()
	time.Sleep(1 * time.Second)
	
	var finalStats runtime.MemStats
	runtime.ReadMemStats(&finalStats)
	
	allocDiff := int64(finalStats.Alloc) - int64(initialStats.Alloc)
	allocMB := float64(allocDiff) / 1024 / 1024
	
	t.Logf("快速检查完成:")
	t.Logf("  执行请求数: %d", requestCount)
	t.Logf("  内存增长: %.2f MB", allocMB)
	t.Logf("  每请求内存: %.2f bytes", float64(allocDiff)/float64(requestCount))
	
	// 5分钟内存增长不应超过10MB
	if allocMB > 10 {
		t.Errorf("快速检查发现内存泄漏: 增长 %.2f MB", allocMB)
	} else {
		t.Logf("✅ 快速内存泄漏检查通过！")
	}
}
