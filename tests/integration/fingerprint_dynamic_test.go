// Package integration 动态指纹生成和不健康指纹标记测试
package integration

import (
	"context"
	"testing"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/httpclient/features/fingerprint"
	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/logging"
)

// TestDynamicFingerprintGeneration 测试动态指纹生成
func TestDynamicFingerprintGeneration(t *testing.T) {
	// 初始化日志
	_ = logging.GetLogger("fingerprint.dynamic.test")

	t.Run("动态指纹生成", func(t *testing.T) {
		testDynamicGeneration(t)
	})

	t.Run("不健康指纹标记", func(t *testing.T) {
		testUnhealthyMarking(t)
	})

	t.Run("指纹池为空时的动态生成", func(t *testing.T) {
		testEmptyPoolDynamicGeneration(t)
	})
}

// testDynamicGeneration 测试动态指纹生成
func testDynamicGeneration(t *testing.T) {
	// 先初始化全局指纹管理器
	err := fingerprint.InitializeGlobalManager()
	if err != nil {
		t.Fatalf("初始化全局指纹管理器失败: %v", err)
	}
	defer func() {
		if err := fingerprint.CloseGlobalManager(); err != nil {
			t.Errorf("关闭全局指纹管理器失败: %v", err)
		}
	}()

	// 获取全局管理器
	manager := fingerprint.GetGlobalManager()
	if manager == nil {
		t.Fatal("无法获取全局指纹管理器")
	}

	// 测试动态生成（通过模拟池为空的情况）
	ctx := context.Background()

	// 创建一个会触发动态生成的请求
	fingerprintReq := &core.FingerprintRequest{
		PoolName:        "nonexistent-pool", // 不存在的池，会触发动态生成
		ServerName:      "example.com",
		ExcludeProfiles: []string{}, // 不排除任何指纹
	}

	// 尝试选择指纹，应该会触发动态生成
	profile, err := manager.SelectFingerprint(ctx, fingerprintReq)
	if err != nil {
		t.Logf("选择指纹失败（可能是预期的）: %v", err)
		// 这可能是预期的，因为不存在的池可能不会触发动态生成
	} else {
		t.Logf("选择指纹成功: %s", profile.Name)

		// 验证指纹的属性
		if profile.Name == "" {
			t.Error("指纹名称不应该为空")
		}
	}
}

// testUnhealthyMarking 测试不健康指纹标记
func testUnhealthyMarking(t *testing.T) {
	// 先初始化全局指纹管理器
	err := fingerprint.InitializeGlobalManager()
	if err != nil {
		t.Fatalf("初始化全局指纹管理器失败: %v", err)
	}
	defer func() {
		if err := fingerprint.CloseGlobalManager(); err != nil {
			t.Errorf("关闭全局指纹管理器失败: %v", err)
		}
	}()

	// 创建请求服务
	service := request.NewService()
	defer service.Close()

	// 模拟标记不健康指纹的请求
	req := &models.Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
		Headers: map[string]string{
			"Accept": "application/json",
		},
		Metadata: map[string]interface{}{
			"fingerprint":    "popmart-us",
			"mark_unhealthy": "PopMart_Charles_Ultra_Exact", // 标记这个指纹为不健康
			"status_code":    403,                           // 触发不健康的状态码
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 执行请求
	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		t.Errorf("标记不健康指纹的请求失败: %v", err)
	} else {
		t.Logf("标记不健康指纹的请求成功: status=%d", resp.StatusCode)
	}

	// 验证指纹是否被标记为不健康
	// 再次请求，应该避免使用被标记的指纹
	req2 := &models.Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
		Headers: map[string]string{
			"Accept": "application/json",
		},
		Metadata: map[string]interface{}{
			"fingerprint": "popmart-us",
		},
	}

	resp2, err := service.SendRequest(ctx, req2)
	if err != nil {
		t.Errorf("验证不健康指纹排除的请求失败: %v", err)
	} else {
		t.Logf("验证不健康指纹排除的请求成功: status=%d", resp2.StatusCode)

		// 检查是否使用了不同的指纹
		if selectedProfile, ok := resp2.Metadata["selected_fingerprint"].(string); ok {
			if selectedProfile == "PopMart_Charles_Ultra_Exact" {
				t.Logf("注意：仍然使用了被标记为不健康的指纹: %s（可能是池中只有这一个指纹）", selectedProfile)
			} else {
				t.Logf("成功避免了不健康指纹，使用了: %s", selectedProfile)
			}
		}
	}
}

// testEmptyPoolDynamicGeneration 测试指纹池为空时的动态生成
func testEmptyPoolDynamicGeneration(t *testing.T) {
	// 先初始化全局指纹管理器
	err := fingerprint.InitializeGlobalManager()
	if err != nil {
		t.Fatalf("初始化全局指纹管理器失败: %v", err)
	}
	defer func() {
		if err := fingerprint.CloseGlobalManager(); err != nil {
			t.Errorf("关闭全局指纹管理器失败: %v", err)
		}
	}()

	// 创建请求服务
	service := request.NewService()
	defer service.Close()

	// 模拟排除所有指纹的请求（强制触发动态生成）
	req := &models.Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
		Headers: map[string]string{
			"Accept": "application/json",
		},
		Metadata: map[string]interface{}{
			"fingerprint": "popmart-us",
			"exclude_fingerprints": []string{
				"PopMart_Charles_Ultra_Exact",
				"PopMart_Charles_Ultra",
				"Minimal_TLS13_v1",
				"Minimal_TLS13_v2",
				"Proxy_Like_v1",
				"Proxy_Like_v2",
			},
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 执行请求
	resp, err := service.SendRequest(ctx, req)
	if err != nil {
		t.Logf("排除所有指纹的请求失败（预期的）: %v", err)
		// 这是预期的，因为可能没有动态生成器或者动态生成失败
	} else {
		t.Logf("排除所有指纹的请求成功: status=%d", resp.StatusCode)

		// 检查是否使用了动态生成的指纹
		if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
			t.Logf("使用的指纹: %s", selectedProfile)

			// 检查是否为动态指纹
			if isDynamic, ok := resp.Metadata["is_dynamic"].(bool); ok && isDynamic {
				t.Logf("成功使用了动态生成的指纹: %s", selectedProfile)
			}
		}
	}
}

// TestFingerprintHealthTrackingDynamic 测试指纹健康状态跟踪（动态版本）
func TestFingerprintHealthTrackingDynamic(t *testing.T) {
	// 先初始化全局指纹管理器
	err := fingerprint.InitializeGlobalManager()
	if err != nil {
		t.Fatalf("初始化全局指纹管理器失败: %v", err)
	}
	defer func() {
		if err := fingerprint.CloseGlobalManager(); err != nil {
			t.Errorf("关闭全局指纹管理器失败: %v", err)
		}
	}()

	// 获取初始健康状态
	initialStats := fingerprint.GlobalManagerStats()
	if initialStats != nil {
		if healthStats, ok := initialStats["health_stats"].(map[string]interface{}); ok {
			t.Logf("初始健康状态: %+v", healthStats)
		}
	}

	// 创建请求服务
	service := request.NewService()
	defer service.Close()

	// 执行一些请求来更新健康状态
	testCases := []struct {
		name          string
		fingerprint   string
		markUnhealthy string
		statusCode    int
	}{
		{
			name:        "正常请求",
			fingerprint: "popmart-us",
		},
		{
			name:          "标记不健康",
			fingerprint:   "popmart-us",
			markUnhealthy: "PopMart_Charles_Ultra_Exact",
			statusCode:    403,
		},
		{
			name:        "再次正常请求",
			fingerprint: "popmart-us",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			req := &models.Request{
				URL:    "https://httpbin.org/get",
				Method: "GET",
				Headers: map[string]string{
					"Accept": "application/json",
				},
				Metadata: map[string]interface{}{
					"fingerprint": tc.fingerprint,
				},
			}

			if tc.markUnhealthy != "" {
				req.Metadata["mark_unhealthy"] = tc.markUnhealthy
				req.Metadata["status_code"] = tc.statusCode
			}

			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			resp, err := service.SendRequest(ctx, req)
			cancel()

			if err != nil {
				t.Logf("%s 失败: %v", tc.name, err)
			} else {
				t.Logf("%s 成功: status=%d, profile=%s",
					tc.name, resp.StatusCode, resp.Metadata["selected_fingerprint"])
			}
		})
	}

	// 获取最终健康状态
	finalStats := fingerprint.GlobalManagerStats()
	if finalStats != nil {
		if healthStats, ok := finalStats["health_stats"].(map[string]interface{}); ok {
			t.Logf("最终健康状态: %+v", healthStats)
		}
	}
}
