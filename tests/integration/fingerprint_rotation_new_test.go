// Package integration 新的指纹轮转和风控检测测试
package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/httpclient/features/fingerprint"
	"go-monitor/pkg/logging"
)

// TestNewFingerprintRotation 测试新的指纹轮转机制
func TestNewFingerprintRotation(t *testing.T) {
	// 初始化日志
	_ = logging.GetLogger("fingerprint.rotation.new.test")

	t.Run("每次请求都轮转指纹", func(t *testing.T) {
		testForceRotation(t)
	})

	t.Run("风控状态码检测", func(t *testing.T) {
		testRiskDetectionByStatusCode(t)
	})

	t.Run("不健康指纹自动排除", func(t *testing.T) {
		testUnhealthyProfileExclusion(t)
	})
}

// testForceRotation 测试强制轮转指纹
func testForceRotation(t *testing.T) {
	// 先初始化全局指纹管理器
	err := fingerprint.InitializeGlobalManager()
	if err != nil {
		t.Fatalf("初始化全局指纹管理器失败: %v", err)
	}
	defer func() {
		if err := fingerprint.CloseGlobalManager(); err != nil {
			t.Errorf("关闭全局指纹管理器失败: %v", err)
		}
	}()

	// 创建请求服务
	service := request.NewService()
	defer service.Close()

	// 执行多次请求，验证指纹轮转
	const numRequests = 5
	usedProfiles := make([]string, numRequests)

	for i := 0; i < numRequests; i++ {
		req := &models.Request{
			URL:    "https://httpbin.org/get",
			Method: "GET",
			Headers: map[string]string{
				"Accept": "application/json",
			},
			Metadata: map[string]interface{}{
				"fingerprint": "popmart-us",
			},
		}

		ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
		resp, err := service.SendRequest(ctx, req)
		cancel()

		if err != nil {
			t.Errorf("第%d次请求失败: %v", i+1, err)
			continue
		}

		// 记录使用的指纹
		if selectedProfile, ok := resp.Metadata["selected_fingerprint"].(string); ok {
			usedProfiles[i] = selectedProfile
			t.Logf("第%d次请求使用指纹: %s", i+1, selectedProfile)
		} else {
			t.Logf("第%d次请求未返回指纹信息", i+1)
		}
	}

	// 验证指纹轮转效果
	uniqueProfiles := make(map[string]bool)
	for _, profile := range usedProfiles {
		if profile != "" {
			uniqueProfiles[profile] = true
		}
	}

	t.Logf("总共使用了 %d 个不同的指纹: %v", len(uniqueProfiles), getKeys(uniqueProfiles))

	// 检查是否有轮转（至少使用了2个不同的指纹）
	if len(uniqueProfiles) >= 2 {
		t.Logf("✅ 指纹轮转正常工作，使用了多个不同指纹")
	} else {
		t.Logf("⚠️ 指纹轮转可能未生效，只使用了 %d 个指纹", len(uniqueProfiles))
	}
}

// testRiskDetectionByStatusCode 测试基于状态码的风控检测
func testRiskDetectionByStatusCode(t *testing.T) {
	// 先初始化全局指纹管理器
	err := fingerprint.InitializeGlobalManager()
	if err != nil {
		t.Fatalf("初始化全局指纹管理器失败: %v", err)
	}
	defer func() {
		if err := fingerprint.CloseGlobalManager(); err != nil {
			t.Errorf("关闭全局指纹管理器失败: %v", err)
		}
	}()

	// 创建请求服务
	service := request.NewService()
	defer service.Close()

	// 测试不同的风控状态码
	testCases := []struct {
		name       string
		statusCode int
		riskLevel  string
	}{
		{"高风险状态码 473", 473, "HIGH"},
		{"高风险状态码 472", 472, "HIGH"},
		{"中等风险状态码 403", 403, "MEDIUM"},
		{"中等风险状态码 418", 418, "MEDIUM"},
		{"低风险状态码 429", 429, "LOW"},
		{"正常状态码 200", 200, ""},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 使用httpbin.org的状态码端点
			url := "https://httpbin.org/get"
			if tc.statusCode != 200 {
				url = fmt.Sprintf("https://httpbin.org/status/%d", tc.statusCode)
			}

			req := &models.Request{
				URL:    url,
				Method: "GET",
				Headers: map[string]string{
					"Accept": "application/json",
				},
				Metadata: map[string]interface{}{
					"fingerprint": "popmart-us",
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
			resp, err := service.SendRequest(ctx, req)
			cancel()

			if err != nil {
				t.Logf("%s 请求失败: %v", tc.name, err)
			} else {
				t.Logf("%s 请求成功: status=%d, profile=%s",
					tc.name, resp.StatusCode, resp.Metadata["selected_fingerprint"])

				// 验证状态码
				if resp.StatusCode == tc.statusCode {
					t.Logf("✅ 状态码正确: %d", resp.StatusCode)
				} else {
					t.Logf("⚠️ 状态码不匹配: 期望 %d, 实际 %d", tc.statusCode, resp.StatusCode)
				}
			}
		})
	}
}

// testUnhealthyProfileExclusion 测试不健康指纹自动排除
func testUnhealthyProfileExclusion(t *testing.T) {
	// 先初始化全局指纹管理器
	err := fingerprint.InitializeGlobalManager()
	if err != nil {
		t.Fatalf("初始化全局指纹管理器失败: %v", err)
	}
	defer func() {
		if err := fingerprint.CloseGlobalManager(); err != nil {
			t.Errorf("关闭全局指纹管理器失败: %v", err)
		}
	}()

	// 创建请求服务
	service := request.NewService()
	defer service.Close()

	// 第一步：正常请求，记录使用的指纹
	req1 := &models.Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
		Headers: map[string]string{
			"Accept": "application/json",
		},
		Metadata: map[string]interface{}{
			"fingerprint": "popmart-us",
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	resp1, err := service.SendRequest(ctx, req1)
	cancel()

	if err != nil {
		t.Errorf("第一次请求失败: %v", err)
		return
	}

	firstProfile, _ := resp1.Metadata["selected_fingerprint"].(string)
	t.Logf("第一次请求使用指纹: %s", firstProfile)

	// 第二步：模拟风控响应（使用403状态码）
	req2 := &models.Request{
		URL:    "https://httpbin.org/status/403", // 403会被检测为风控
		Method: "GET",
		Headers: map[string]string{
			"Accept": "application/json",
		},
		Metadata: map[string]interface{}{
			"fingerprint": "popmart-us",
		},
	}

	ctx, cancel = context.WithTimeout(context.Background(), 15*time.Second)
	resp2, err := service.SendRequest(ctx, req2)
	cancel()

	if err != nil {
		t.Logf("风控请求失败（可能是预期的）: %v", err)
	} else {
		secondProfile, _ := resp2.Metadata["selected_fingerprint"].(string)
		t.Logf("风控请求使用指纹: %s, 状态码: %d", secondProfile, resp2.StatusCode)
	}

	// 第三步：再次正常请求，验证是否避免了被标记的指纹
	req3 := &models.Request{
		URL:    "https://httpbin.org/get",
		Method: "GET",
		Headers: map[string]string{
			"Accept": "application/json",
		},
		Metadata: map[string]interface{}{
			"fingerprint": "popmart-us",
		},
	}

	ctx, cancel = context.WithTimeout(context.Background(), 15*time.Second)
	resp3, err := service.SendRequest(ctx, req3)
	cancel()

	if err != nil {
		t.Errorf("第三次请求失败: %v", err)
		return
	}

	thirdProfile, _ := resp3.Metadata["selected_fingerprint"].(string)
	t.Logf("第三次请求使用指纹: %s", thirdProfile)

	// 验证指纹排除效果
	if firstProfile != "" && thirdProfile != "" {
		if firstProfile != thirdProfile {
			t.Logf("✅ 指纹轮转正常，第一次和第三次使用了不同指纹")
		} else {
			t.Logf("⚠️ 第一次和第三次使用了相同指纹: %s", firstProfile)
		}
	}
}

// getKeys 获取map的所有键
func getKeys(m map[string]bool) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}
