"""
签名助手模块

处理各个站点API的签名生成和认证参数
"""
import hashlib
import json
import time
from random import randint
from typing import Union, Optional
from uuid import uuid4

country_to_timezone = {
    "AD": "Europe/Andorra",
    "AE": "Asia/Dubai",
    "AF": "Asia/Kabul",
    "AG": "America/Puerto_Rico",
    "AI": "America/Puerto_Rico",
    "AL": "Europe/Tirane",
    "AM": "Asia/Yerevan",
    "AO": "Africa/Lagos",
    "AQ": "Antarctica/Casey",
    "AR": "America/Argentina/Buenos_Aires",
    "AS": "Pacific/Pago_Pago",
    "AT": "Europe/Vienna",
    "AU": "Australia/Lord_Howe",
    "AW": "America/Puerto_Rico",
    "AX": "Europe/Helsinki",
    "AZ": "Asia/Baku",
    "BA": "Europe/Belgrade",
    "BB": "America/Barbados",
    "BD": "Asia/Dhaka",
    "BE": "Europe/Brussels",
    "BF": "Africa/Abidjan",
    "BG": "Europe/Sofia",
    "BH": "Asia/Qatar",
    "BI": "Africa/Maputo",
    "BJ": "Africa/Lagos",
    "BL": "America/Puerto_Rico",
    "BM": "Atlantic/Bermuda",
    "BN": "Asia/Kuching",
    "BO": "America/La_Paz",
    "BQ": "America/Puerto_Rico",
    "BR": "America/Noronha",
    "BS": "America/Toronto",
    "BT": "Asia/Thimphu",
    "BW": "Africa/Maputo",
    "BY": "Europe/Minsk",
    "BZ": "America/Belize",
    "CA": "America/St_Johns",
    "CC": "Asia/Yangon",
    "CD": "Africa/Maputo",
    "CF": "Africa/Lagos",
    "CG": "Africa/Lagos",
    "CH": "Europe/Zurich",
    "CI": "Africa/Abidjan",
    "CK": "Pacific/Rarotonga",
    "CL": "America/Santiago",
    "CM": "Africa/Lagos",
    "CN": "Asia/Shanghai",
    "CO": "America/Bogota",
    "CR": "America/Costa_Rica",
    "CU": "America/Havana",
    "CV": "Atlantic/Cape_Verde",
    "CW": "America/Puerto_Rico",
    "CX": "Asia/Bangkok",
    "CY": "Asia/Nicosia",
    "CZ": "Europe/Prague",
    "DE": "Europe/Zurich",
    "DJ": "Africa/Nairobi",
    "DK": "Europe/Berlin",
    "DM": "America/Puerto_Rico",
    "DO": "America/Santo_Domingo",
    "DZ": "Africa/Algiers",
    "EC": "America/Guayaquil",
    "EE": "Europe/Tallinn",
    "EG": "Africa/Cairo",
    "EH": "Africa/El_Aaiun",
    "ER": "Africa/Nairobi",
    "ES": "Europe/Madrid",
    "ET": "Africa/Nairobi",
    "FI": "Europe/Helsinki",
    "FJ": "Pacific/Fiji",
    "FK": "Atlantic/Stanley",
    "FM": "Pacific/Kosrae",
    "FO": "Atlantic/Faroe",
    "FR": "Europe/Paris",
    "GA": "Africa/Lagos",
    "GB": "Europe/London",
    "GD": "America/Puerto_Rico",
    "GE": "Asia/Tbilisi",
    "GF": "America/Cayenne",
    "GG": "Europe/London",
    "GH": "Africa/Abidjan",
    "GI": "Europe/Gibraltar",
    "GL": "America/Nuuk",
    "GM": "Africa/Abidjan",
    "GN": "Africa/Abidjan",
    "GP": "America/Puerto_Rico",
    "GQ": "Africa/Lagos",
    "GR": "Europe/Athens",
    "GS": "Atlantic/South_Georgia",
    "GT": "America/Guatemala",
    "GU": "Pacific/Guam",
    "GW": "Africa/Bissau",
    "GY": "America/Guyana",
    "HK": "Asia/Hong_Kong",
    "HN": "America/Tegucigalpa",
    "HR": "Europe/Belgrade",
    "HT": "America/Port-au-Prince",
    "HU": "Europe/Budapest",
    "ID": "Asia/Jakarta",
    "IE": "Europe/Dublin",
    "IL": "Asia/Jerusalem",
    "IM": "Europe/London",
    "IN": "Asia/Kolkata",
    "IO": "Indian/Chagos",
    "IQ": "Asia/Baghdad",
    "IR": "Asia/Tehran",
    "IS": "Africa/Abidjan",
    "IT": "Europe/Rome",
    "JE": "Europe/London",
    "JM": "America/Jamaica",
    "JO": "Asia/Amman",
    "JP": "Asia/Tokyo",
    "KE": "Africa/Nairobi",
    "KG": "Asia/Bishkek",
    "KH": "Asia/Bangkok",
    "KI": "Pacific/Tarawa",
    "KM": "Africa/Nairobi",
    "KN": "America/Puerto_Rico",
    "KP": "Asia/Pyongyang",
    "KR": "Asia/Seoul",
    "KW": "Asia/Riyadh",
    "KY": "America/Panama",
    "KZ": "Asia/Almaty",
    "LA": "Asia/Bangkok",
    "LB": "Asia/Beirut",
    "LC": "America/Puerto_Rico",
    "LI": "Europe/Zurich",
    "LK": "Asia/Colombo",
    "LR": "Africa/Monrovia",
    "LS": "Africa/Johannesburg",
    "LT": "Europe/Vilnius",
    "LU": "Europe/Brussels",
    "LV": "Europe/Riga",
    "LY": "Africa/Tripoli",
    "MA": "Africa/Casablanca",
    "MC": "Europe/Paris",
    "MD": "Europe/Chisinau",
    "ME": "Europe/Belgrade",
    "MF": "America/Puerto_Rico",
    "MG": "Africa/Nairobi",
    "MH": "Pacific/Tarawa",
    "MK": "Europe/Belgrade",
    "ML": "Africa/Abidjan",
    "MM": "Asia/Yangon",
    "MN": "Asia/Ulaanbaatar",
    "MO": "Asia/Macau",
    "MP": "Pacific/Guam",
    "MQ": "America/Martinique",
    "MR": "Africa/Abidjan",
    "MS": "America/Puerto_Rico",
    "MT": "Europe/Malta",
    "MU": "Indian/Mauritius",
    "MV": "Indian/Maldives",
    "MW": "Africa/Maputo",
    "MX": "America/Mexico_City",
    "MY": "Asia/Kuching",
    "MZ": "Africa/Maputo",
    "NA": "Africa/Windhoek",
    "NC": "Pacific/Noumea",
    "NE": "Africa/Lagos",
    "NF": "Pacific/Norfolk",
    "NG": "Africa/Lagos",
    "NI": "America/Managua",
    "NL": "Europe/Brussels",
    "NO": "Europe/Berlin",
    "NP": "Asia/Kathmandu",
    "NR": "Pacific/Nauru",
    "NU": "Pacific/Niue",
    "NZ": "Pacific/Auckland",
    "OM": "Asia/Dubai",
    "PA": "America/Panama",
    "PE": "America/Lima",
    "PF": "Pacific/Tahiti",
    "PG": "Pacific/Port_Moresby",
    "PH": "Asia/Manila",
    "PK": "Asia/Karachi",
    "PL": "Europe/Warsaw",
    "PM": "America/Miquelon",
    "PN": "Pacific/Pitcairn",
    "PR": "America/Puerto_Rico",
    "PS": "Asia/Gaza",
    "PT": "Europe/Lisbon",
    "PW": "Pacific/Palau",
    "PY": "America/Asuncion",
    "QA": "Asia/Qatar",
    "RE": "Asia/Dubai",
    "RO": "Europe/Bucharest",
    "RS": "Europe/Belgrade",
    "RU": "Europe/Kaliningrad",
    "RW": "Africa/Maputo",
    "SA": "Asia/Riyadh",
    "SB": "Pacific/Guadalcanal",
    "SC": "Asia/Dubai",
    "SD": "Africa/Khartoum",
    "SE": "Europe/Berlin",
    "SG": "Asia/Singapore",
    "SH": "Africa/Abidjan",
    "SI": "Europe/Belgrade",
    "SJ": "Europe/Berlin",
    "SK": "Europe/Prague",
    "SL": "Africa/Abidjan",
    "SM": "Europe/Rome",
    "SN": "Africa/Abidjan",
    "SO": "Africa/Nairobi",
    "SR": "America/Paramaribo",
    "SS": "Africa/Juba",
    "ST": "Africa/Sao_Tome",
    "SV": "America/El_Salvador",
    "SX": "America/Puerto_Rico",
    "SY": "Asia/Damascus",
    "SZ": "Africa/Johannesburg",
    "TC": "America/Grand_Turk",
    "TD": "Africa/Ndjamena",
    "TF": "Asia/Dubai",
    "TG": "Africa/Abidjan",
    "TH": "Asia/Bangkok",
    "TJ": "Asia/Dushanbe",
    "TK": "Pacific/Fakaofo",
    "TL": "Asia/Dili",
    "TM": "Asia/Ashgabat",
    "TN": "Africa/Tunis",
    "TO": "Pacific/Tongatapu",
    "TR": "Europe/Istanbul",
    "TT": "America/Puerto_Rico",
    "TV": "Pacific/Tarawa",
    "TW": "Asia/Taipei",
    "TZ": "Africa/Nairobi",
    "UA": "Europe/Simferopol",
    "UG": "Africa/Nairobi",
    "UM": "Pacific/Pago_Pago",
    "US": "America/New_York",
    "UY": "America/Montevideo",
    "UZ": "Asia/Samarkand",
    "VA": "Europe/Rome",
    "VC": "America/Puerto_Rico",
    "VE": "America/Caracas",
    "VG": "America/Puerto_Rico",
    "VI": "America/Puerto_Rico",
    "VN": "Asia/Bangkok",
    "VU": "Pacific/Efate",
    "WF": "Pacific/Tarawa",
    "WS": "Pacific/Apia",
    "YE": "Asia/Riyadh",
    "YT": "Africa/Nairobi",
    "ZA": "Africa/Johannesburg",
    "ZM": "Africa/Maputo",
    "ZW": "Africa/Maputo"
}
# 根据国家选择时区
locales = {
    "HK": {
        "cluster": "hk",
        "namespace": "hk",
        "key": "xzriem686i2i2dkwo"
    },
    "TH": {
        "cluster": "thailand",
        "namespace": "thailand",
        "key": "dbaom9yv13gz80n3j"
    },
    "GB": {
        "cluster": "uk",
        "namespace": "eurasianuk",
        "key": "xzriem686i2i2dkwo"
    },
    "US": {
        "cluster": "naus",
        "namespace": "america",
        "key": "nw3b089qrgw9m7b7i"
    },
    "EU": {
        "cluster": "eude",
        "namespace": "eurasian",
        "key": "rmdxjisjk7gwykcix"
    }
}

def _md5(text: str) -> str:
    """返回十六进制格式的 MD5 哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def _sort_and_clean(obj: Union[dict, list, str, int, float], method: str) -> Union[dict, list, str, int, float]:
    """递归排序并清理字典，用于签名前数据标准化"""
    if isinstance(obj, dict):
        result = {}
        for key in sorted(obj):
            value = _sort_and_clean(obj[key], method)
            if method == 'get':
                if value not in [None, '', []]:
                    result[key] = str(value)
            else:
                result[key] = value
        return result
    elif isinstance(obj, list):
        return [_sort_and_clean(i, method) for i in obj]
    else:
        return obj

def generate_signature(data: dict, timestamp: int, method: str) -> str:
    """生成签名字符串"""
    sorted_data = _sort_and_clean(data, method)
    json_string = json.dumps(sorted_data, separators=(',', ':'), ensure_ascii=False)
    combined = f"{json_string}W_ak^moHpMla{timestamp}"
    return _md5(combined)

def attach_signature(method: str, data: Union[dict, str, None]) -> dict:
    """
    返回添加签名后的新参数字典。
    - method: 'get' 或 'post'
    - data: dict 或 JSON 字符串
    """
    method = method.lower()
    timestamp = int(time.time())

    if method == 'get':
        params = data or {}
        if not isinstance(params, dict):
            raise ValueError("GET 请求参数必须为字典")
        params['s'] = generate_signature(params, timestamp, method)
        params['t'] = timestamp
        return params

    elif method == 'post':
        if data is None:
            data_obj = {}
        elif isinstance(data, str):
            try:
                data_obj = json.loads(data)
            except json.JSONDecodeError:
                raise ValueError("POST 请求中提供了无效的 JSON 字符串")
        elif isinstance(data, dict):
            data_obj = data
        else:
            raise ValueError("POST 请求参数必须为字典或 JSON 字符串")

        data_obj['s'] = generate_signature(data_obj, timestamp, method)
        data_obj['t'] = timestamp
        return data_obj

    else:
        raise ValueError("请求方法必须为 'get' 或 'post'")

def build_headers(
    country: Optional[str] = None,
    # device: Optional[str] = None,
    # user_agent: Optional[str] = None
    # language: Optional[str] = None,
    # country_namespace: Optional[str] = None,
    # did: Optional[str] = None
) -> dict:
    country = country.upper() if country else "DE"
    locale = locales.get(country, {
        "cluster": "eude",
        "namespace": "eurasian",
        "key": "rmdxjisjk7gwykcix"
    })
    client_key = locale.get('key', 'rmdxjisjk7gwykcix')
    timestamp = int(time.time())  # 10位时间戳
    sign_input = f"{timestamp},{client_key}"
    signature = f"{_md5(sign_input)},{timestamp}"
    
    tz = country_to_timezone.get(country, "Europe/Berlin")  # 默认使用德国时区

    return {
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
        'language': 'en',
        'x-project-id': locale['cluster'],
        'x-device-os-type': 'web',
        'td-session-sign': '',
        'grey-secret': 'null',
        'accept': 'application/json, text/plain, */*',
        'td-session-query': '',
        'x-client-country': country,
        'td-session-key': '',
        'tz': tz,
        'td-session-path': '/shop/v1/shop/productDetails',
        'country': country,
        'did':  '',
        'x-sign': signature,
        'clientkey': client_key,
        'x-client-namespace': locale['namespace'],
        'origin': 'https://m.popmart.com',
        'sec-fetch-site': 'same-site',
        'sec-fetch-mode': 'cors',
        'sec-fetch-dest': 'empty',
        'referer': 'https://m.popmart.com/',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'en-US,en;q=0.9',
        'priority': 'u=1, i'
    }
    # return {
    #     "x-doughnuts": "",
    #     "content-type": "application/json;charset=UTF-8",
    #     "accept": "*/*",
    #     "app_version": "4.4.0",
    #     "tz": tz,
    #     "x-device-os-type": device if device else "iOS",
    #     "accept-language": "en-US;q=1",
    #     "accept-encoding": "gzip, deflate, br",
    #     "language": "en",
    #     "user-agent": user_agent if user_agent else  "POPGlobalClient/4.4.0 (iPhone; iOS 15.3.1; Scale/2.00)",
    #     "x-device-id": str(uuid4()).upper(),
    #     "country": country,
    #     "x-client-country": country,
    # }
