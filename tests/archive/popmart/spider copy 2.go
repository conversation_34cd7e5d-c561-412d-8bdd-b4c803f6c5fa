package popmart

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/internal/spiders/shared"

	"github.com/tidwall/gjson"
)

// ========== 常量定义 ==========

// PopMart locales映射 - 对应Python版本的locales
type LocaleConfig struct {
	Cluster   string
	Namespace string
	Key       string
}

var Locales = map[string]LocaleConfig{
	"HK": {
		Cluster:   "hk",
		Namespace: "hk",
		Key:       "xzriem686i2i2dkwo",
	},
	"TH": {
		Cluster:   "thailand",
		Namespace: "thailand",
		Key:       "dbaom9yv13gz80n3j",
	},
	"GB": {
		Cluster:   "uk",
		Namespace: "eurasianuk",
		Key:       "xzriem686i2i2dkwo",
	},
	"US": {
		Cluster:   "naus",
		Namespace: "america",
		Key:       "nw3b089qrgw9m7b7i",
	},
	"EU": {
		Cluster:   "eude",
		Namespace: "eurasian",
		Key:       "rmdxjisjk7gwykcix",
	},
}

const (
	// PopMart 特定常量
	PopMartPlatform      = shared.PlatformPopMart
	PopMartSpiderVersion = shared.SpiderVersion
	PopMartDescription   = "PopMart商品监控爬虫"

	// API 路径
	ProductDetailsPath = "/shop/v1/shop/productDetails"

	// 产品详情参数
	ProductIDParam = "spuId"

	// 默认值
	DefaultSiteURL = shared.PopMartDefaultSiteURL
)

// ========== 类型定义 ==========

// Spider PopMart爬虫实现
type Spider struct {
	*spiders.BaseSpider
}

// ========== 构造函数 ==========

// NewSpider 创建新的PopMart爬虫实例
func NewSpider() spiders.Spider {
	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider(PopMartPlatform, PopMartSpiderVersion, PopMartDescription),
	}
	return spider
}

// GetProductIDs 获取搜索配置集合
func (s *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// ========== 核心方法 ==========

// PrepareRequest 准备产品详情请求
func (s *Spider) PrepareRequest(ctx context.Context, productID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	// 构建API端点URL
	apiBase := config.GetConfigString(spiderConfig, "api", "")
	if apiBase == "" {
		return nil, s.HandleError("获取API配置", fmt.Errorf("缺少API配置"))
	}
	URL := apiBase + ProductDetailsPath

	// 使用HTTP客户端构建器创建GET请求
	builder := shared.CreatePopMartRequest(URL, spiderConfig, productID)

	// 设置GET方法和查询参数
	builder.SetMethod("GET")
	builder.AddParam(ProductIDParam, productID)

	// 设置PopMart特定的headers
	s.setPopMartHeaders(builder, spiderConfig)

	// 构建请求
	httpRequest := builder.Build()

	// 转换为models.Request
	request := &models.Request{
		SpiderType:   "popmart",
		URL:          httpRequest.URL,
		Method:       httpRequest.Method,
		Headers:      httpRequest.Headers,
		Body:         httpRequest.Body,
		Params:       httpRequest.Params,
		ReverseProxy: httpRequest.ReverseProxy,
		Metadata:     httpRequest.Metadata,
	}

	s.LogDebug("准备PopMart产品详情请求，产品ID：%s，URL：%s", productID, request.URL)

	return request, nil
}

// ParseResponse 解析产品详情响应
func (s *Spider) ParseResponse(ctx context.Context, resp *models.Response, spiderConfig *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	// 验证HTTP响应
	if err := shared.ValidateHTTPResponse(resp.StatusCode, resp.Body); err != nil {
		return nil, s.HandleError("验证HTTP响应", err)
	}

	// 获取产品ID
	productID := ""
	if resp.Request != nil {
		productID = resp.Request.GetMetadataString("product_id", "")
	}

	if productID == "" {
		return nil, s.HandleError("获取产品ID", fmt.Errorf("缺少产品ID"))
	}

	// 解析响应内容 - 使用GJSON
	jsonStr := string(resp.Body)
	if !gjson.Valid(jsonStr) {
		return nil, s.HandleError("验证JSON格式", fmt.Errorf(shared.ErrInvalidJSON))
	}

	// 提取产品数据
	productData := s.extractProductData(productID, jsonStr, spiderConfig.Name)
	if productData == nil {
		return nil, nil
	}

	// 处理产品数据
	products := s.processProductData(productID, productData, spiderConfig)

	s.LogDebug("成功解析PopMart产品详情 产品ID：%s，产品数量：%d", productID, len(products))

	return products, nil
}

// ========== 产品数据提取方法 ==========

// extractProductData 从JSON响应中提取产品数据 - 对应Python版本的extract_product_data方法
func (s *Spider) extractProductData(productID string, jsonStr string, monitorName string) map[string]any {
	// 使用GJSON检查响应状态
	if code := gjson.Get(jsonStr, "code").String(); code != "OK" {
		s.LogError("产品详情API响应错误 产品ID：%s，监控名称：%s，代码：%s", productID, monitorName, code)
		return nil
	}

	// 使用GJSON检查data字段存在性
	if !gjson.Get(jsonStr, "data").Exists() {
		s.LogError("产品详情响应中缺少data字段 产品ID：%s，监控名称：%s", productID, monitorName)
		return nil
	}

	// 提取产品数据
	productData := make(map[string]any)
	dataResult := gjson.Get(jsonStr, "data")
	dataResult.ForEach(func(key, value gjson.Result) bool {
		productData[key.String()] = value.Value()
		return true
	})

	s.LogDebug("从产品详情提取数据成功，产品ID：%s", productID)
	return productData
}

// processProductData 处理产品数据 - 对应Python版本的parse_response中的产品处理逻辑
func (s *Spider) processProductData(productID string, productData map[string]any, spiderConfig *spiders.SpiderConfig) []*models.ProductItem {
	// 获取产品基本信息
	title, _ := productData["title"].(string)
	skusData, _ := productData["skus"].([]any)

	// 获取发布状态信息
	upTime, _ := productData["upTime"].(float64)
	downTime, _ := productData["downTime"].(float64)
	isPublish, _ := productData["isPublish"].(bool)

	// 检查发布状态 - 对应Python版本的逻辑
	currentTime := float64(time.Now().Unix())
	releaseDate := ""
	if upTime > currentTime {
		releaseDate = fmt.Sprintf("<t:%.0f:f>", upTime)
	}

	// 没有发布的产品 - 对应Python版本的逻辑
	if downTime < currentTime && !isPublish {
		s.LogDebug("产品未发布或已下架 产品ID：%s，下架时间：%.0f，是否发布：%t，当前时间：%.0f",
			productID, downTime, isPublish, currentTime)
		return nil
	}

	// 处理每个SKU
	var products []*models.ProductItem
	for _, sku := range skusData {
		if skuMap, ok := sku.(map[string]any); ok {
			// 转换为JSON字符串以使用GJSON
			skuBytes, _ := json.Marshal(skuMap)
			skuItem := s.processSKUData(productID, title, string(skuBytes), "", releaseDate, isPublish)
			if skuItem != nil {
				productItem := s.parseProduct(skuItem, spiderConfig)
				if productItem != nil {
					products = append(products, productItem)
				}
			}
		}
	}

	s.LogDebug("处理产品数据 产品ID：%s，标题：%s，是否发布：%t，SKU数量：%d", productID, title, isPublish, len(products))
	return products
}

// processSKUData 处理单个SKU数据 - 对应Python版本的process_sku_data方法
func (s *Spider) processSKUData(productID, title string, skuJson string, productType string, releaseDate string, isPublish bool) map[string]any {
	// 使用GJSON直接提取字段 - 无需类型断言
	skuID := gjson.Get(skuJson, "id").String()
	if skuID == "" {
		return nil
	}

	skuTitle := gjson.Get(skuJson, "title").String()
	discountPrice := gjson.Get(skuJson, "discountPrice").Float()

	// 价格转换逻辑 - 恢复Python版本的除以100处理
	if discountPrice > 0 {
		discountPrice = discountPrice / 100
	}

	// 库存信息提取 - 使用GJSON路径查询替代复杂嵌套
	stock := int(gjson.Get(skuJson, "stock.onlineStock").Float())
	if stock == 0 {
		stock = int(gjson.Get(skuJson, "stock.onlineLockStock").Float())
	}

	// inStock状态判断 - 根据用户记忆，isPublish字段是库存状态的关键因素
	// 只有当产品已发布(isPublish=true)且有库存(stock>0)时，inStock才为true
	inStock := isPublish && stock > 0
	imageURL := gjson.Get(skuJson, "mainImage").String()

	return map[string]any{
		"productId":   productID,
		"skuId":       skuID,
		"addition":    skuTitle,
		"title":       fmt.Sprintf("%s - %s", title, skuTitle),
		"price":       fmt.Sprintf("%.2f", discountPrice),
		"image_url":   imageURL,
		"stock":       stock,
		"inStock":     inStock,
		"releaseDate": releaseDate,
		"type":        productType,
	}
}

// ========== 辅助方法 ==========

// parseProduct 解析单个商品数据 - 对应Python版本的parse_product方法
func (s *Spider) parseProduct(productData map[string]any, spiderConfig *spiders.SpiderConfig) *models.ProductItem {
	productID, ok := productData["productId"].(string)
	if !ok {
		return nil
	}

	skuID, _ := productData["skuId"].(string)
	title, _ := productData["title"].(string)
	stock, _ := productData["stock"].(int)
	inStock, _ := productData["inStock"].(bool)
	priceStr, _ := productData["price"].(string)
	productType, _ := productData["type"].(string)

	// 使用共享工具函数构建产品URL
	siteURL := spiderConfig.SiteURL
	if siteURL == "" {
		siteURL = DefaultSiteURL
	}
	productURL := ""
	if productType == "secret" {
		productURL = fmt.Sprintf("%s/pop-now/set/%s", siteURL, productID)
	} else {
		productURL = fmt.Sprintf("%s/products/%s?skuId=%s", siteURL, productID, skuID)
	}

	// 获取国家信息
	country := spiderConfig.Country
	if country == "" {
		country = shared.DefaultCountry
	}

	// 构建ATC链接 - 对应Python版本的逻辑
	atcLink := ""
	if inStock {
		baseURL := fmt.Sprintf("%s/order-confirmation?spuId=%s&skuId=%s&spuTitle=%s",
			siteURL, productID, skuID, url.QueryEscape(title))
		atcLink = fmt.Sprintf("[x1](%s&count=1) | [x2](%s&count=2)", baseURL, baseURL)
	}

	// 解析价格 - 使用共享工具函数
	priceValue, err := shared.ParsePrice(priceStr)
	if err != nil {
		s.LogWarn("解析价格失败，产品ID：%s，价格字符串：%s，错误：%s", productID, priceStr, err.Error())
		priceValue = 0
	}

	// 获取货币信息
	currency := config.GetConfigString(spiderConfig, "currency", shared.DefaultCurrency)

	// 获取商品详情
	addition, _ := productData["addition"].(string)
	imageURL, _ := productData["image_url"].(string)
	releaseDate, _ := productData["releaseDate"].(string)

	// 使用共享函数创建基础产品
	product := s.CreateBaseProduct(spiderConfig.Name, productID, title, productURL, siteURL, spiderConfig.Notifications)

	// 设置价格和库存信息
	shared.SetProductPriceAndStock(product, priceValue, currency, inStock, shared.GetAvailabilityText(inStock), nil)
	product.Stock = stock

	// 设置PopMart特定信息
	product.Country = country
	product.SkuID = shared.CreateStringPointer(skuID)
	product.AtcLink = shared.CreateStringPointer(atcLink)
	product.ImageURL = shared.CreateStringPointer(imageURL)
	product.Addition = shared.CreateStringPointer(addition)
	product.ReleaseDate = shared.CreateStringPointer(releaseDate)

	// 设置元数据
	shared.SetProductMetadata(product, PopMartPlatform, "api", map[string]any{
		"sku_data":    productData,
		"releaseDate": releaseDate,
	})

	return product
}

// getLocaleByCountry 根据国家代码获取locale配置 - 对应Python版本逻辑
func (s *Spider) getLocaleByCountry(country string) LocaleConfig {
	country = strings.ToUpper(country)

	// 直接查找指定国家（HK, TH, GB, US, EU）
	if locale, exists := Locales[country]; exists {
		return locale
	}

	// 除了列表中的国家，其他都使用EU配置
	return Locales["EU"]
}

// setPopMartHeaders 设置PopMart特定的请求头 - 基于真实POST /shop/v1/search请求
func (s *Spider) setPopMartHeaders(builder *shared.SpiderRequestBuilder, config *spiders.SpiderConfig) {
	// 获取国家代码，优先从config.Country获取
	country := config.Country
	if country == "" {
		country = "DE"
	}
	country = strings.ToUpper(country)

	// 获取locale配置（用于动态headers）
	locale := s.getLocaleByCountry(country)

	// 获取时区
	timezone := s.getTimezoneByCountry(country)

	// 根据国家设置语言
	language := "en"
	if country == "HK" {
		language = "zh-hant"
	}

	// 获取站点URL用于origin和referer
	siteURL := config.SiteURL
	if siteURL == "" {
		siteURL = DefaultSiteURL
	}

	// 设置所有headers（除了x-sign、useragent） - 基于真实POST请求
	builder.AddHeader("cf-turnstile-response", "").
		AddHeader("pragma", "no-cache").
		AddHeader("cache-control", "no-cache").
		AddHeader("language", language).
		AddHeader("x-project-id", locale.Cluster).
		AddHeader("x-device-os-type", "web").
		AddHeader("sec-ch-ua-mobile", "?0").
		AddHeader("grey-secret", "null").
		AddHeader("accept", "application/json, text/plain, */*").
		AddHeader("content-type", "application/json").
		AddHeader("clientkey", locale.Key).
		AddHeader("td-session-query", "").
		AddHeader("x-client-country", country).
		AddHeader("tz", timezone).
		AddHeader("x-doughnuts", "").
		AddHeader("td-session-path", "/shop/v1/search").
		AddHeader("country", country).
		AddHeader("x-client-namespace", locale.Namespace).
		AddHeader("origin", siteURL).
		AddHeader("sec-fetch-site", "same-site").
		AddHeader("sec-fetch-mode", "cors").
		AddHeader("sec-fetch-dest", "empty").
		AddHeader("referer", siteURL+"/").
		AddHeader("accept-encoding", "gzip, deflate, br, zstd").
		AddHeader("accept-language", "en-US;q=1").
		AddHeader("priority", "u=1, i")
}

// getTimezoneByCountry 根据国家代码获取对应的时区
func (s *Spider) getTimezoneByCountry(country string) string {
	// PopMart支持的国家时区映射
	timezoneMap := map[string]string{
		"AD": "Europe/Andorra",
		"AE": "Asia/Dubai",
		"AF": "Asia/Kabul",
		"AG": "America/Puerto_Rico",
		"AI": "America/Puerto_Rico",
		"AL": "Europe/Tirane",
		"AM": "Asia/Yerevan",
		"AO": "Africa/Lagos",
		"AQ": "Antarctica/Casey",
		"AR": "America/Argentina/Buenos_Aires",
		"AS": "Pacific/Pago_Pago",
		"AT": "Europe/Vienna",
		"AU": "Australia/Lord_Howe",
		"AW": "America/Puerto_Rico",
		"AX": "Europe/Helsinki",
		"AZ": "Asia/Baku",
		"BA": "Europe/Belgrade",
		"BB": "America/Barbados",
		"BD": "Asia/Dhaka",
		"BE": "Europe/Brussels",
		"BF": "Africa/Abidjan",
		"BG": "Europe/Sofia",
		"BH": "Asia/Qatar",
		"BI": "Africa/Maputo",
		"BJ": "Africa/Lagos",
		"BL": "America/Puerto_Rico",
		"BM": "Atlantic/Bermuda",
		"BN": "Asia/Kuching",
		"BO": "America/La_Paz",
		"BQ": "America/Puerto_Rico",
		"BR": "America/Noronha",
		"BS": "America/Toronto",
		"BT": "Asia/Thimphu",
		"BW": "Africa/Maputo",
		"BY": "Europe/Minsk",
		"BZ": "America/Belize",
		"CA": "America/St_Johns",
		"CC": "Asia/Yangon",
		"CD": "Africa/Maputo",
		"CF": "Africa/Lagos",
		"CG": "Africa/Lagos",
		"CH": "Europe/Zurich",
		"CI": "Africa/Abidjan",
		"CK": "Pacific/Rarotonga",
		"CL": "America/Santiago",
		"CM": "Africa/Lagos",
		"CN": "Asia/Shanghai",
		"CO": "America/Bogota",
		"CR": "America/Costa_Rica",
		"CU": "America/Havana",
		"CV": "Atlantic/Cape_Verde",
		"CW": "America/Puerto_Rico",
		"CX": "Asia/Bangkok",
		"CY": "Asia/Nicosia",
		"CZ": "Europe/Prague",
		"DE": "Europe/Zurich",
		"DJ": "Africa/Nairobi",
		"DK": "Europe/Berlin",
		"DM": "America/Puerto_Rico",
		"DO": "America/Santo_Domingo",
		"DZ": "Africa/Algiers",
		"EC": "America/Guayaquil",
		"EE": "Europe/Tallinn",
		"EG": "Africa/Cairo",
		"EH": "Africa/El_Aaiun",
		"ER": "Africa/Nairobi",
		"ES": "Europe/Madrid",
		"ET": "Africa/Nairobi",
		"FI": "Europe/Helsinki",
		"FJ": "Pacific/Fiji",
		"FK": "Atlantic/Stanley",
		"FM": "Pacific/Kosrae",
		"FO": "Atlantic/Faroe",
		"FR": "Europe/Paris",
		"GA": "Africa/Lagos",
		"GB": "Europe/London",
		"GD": "America/Puerto_Rico",
		"GE": "Asia/Tbilisi",
		"GF": "America/Cayenne",
		"GG": "Europe/London",
		"GH": "Africa/Abidjan",
		"GI": "Europe/Gibraltar",
		"GL": "America/Nuuk",
		"GM": "Africa/Abidjan",
		"GN": "Africa/Abidjan",
		"GP": "America/Puerto_Rico",
		"GQ": "Africa/Lagos",
		"GR": "Europe/Athens",
		"GS": "Atlantic/South_Georgia",
		"GT": "America/Guatemala",
		"GU": "Pacific/Guam",
		"GW": "Africa/Bissau",
		"GY": "America/Guyana",
		"HK": "Asia/Hong_Kong",
		"HN": "America/Tegucigalpa",
		"HR": "Europe/Belgrade",
		"HT": "America/Port-au-Prince",
		"HU": "Europe/Budapest",
		"ID": "Asia/Jakarta",
		"IE": "Europe/Dublin",
		"IL": "Asia/Jerusalem",
		"IM": "Europe/London",
		"IN": "Asia/Kolkata",
		"IO": "Indian/Chagos",
		"IQ": "Asia/Baghdad",
		"IR": "Asia/Tehran",
		"IS": "Africa/Abidjan",
		"IT": "Europe/Rome",
		"JE": "Europe/London",
		"JM": "America/Jamaica",
		"JO": "Asia/Amman",
		"JP": "Asia/Tokyo",
		"KE": "Africa/Nairobi",
		"KG": "Asia/Bishkek",
		"KH": "Asia/Bangkok",
		"KI": "Pacific/Tarawa",
		"KM": "Africa/Nairobi",
		"KN": "America/Puerto_Rico",
		"KP": "Asia/Pyongyang",
		"KR": "Asia/Seoul",
		"KW": "Asia/Riyadh",
		"KY": "America/Panama",
		"KZ": "Asia/Almaty",
		"LA": "Asia/Bangkok",
		"LB": "Asia/Beirut",
		"LC": "America/Puerto_Rico",
		"LI": "Europe/Zurich",
		"LK": "Asia/Colombo",
		"LR": "Africa/Monrovia",
		"LS": "Africa/Johannesburg",
		"LT": "Europe/Vilnius",
		"LU": "Europe/Brussels",
		"LV": "Europe/Riga",
		"LY": "Africa/Tripoli",
		"MA": "Africa/Casablanca",
		"MC": "Europe/Paris",
		"MD": "Europe/Chisinau",
		"ME": "Europe/Belgrade",
		"MF": "America/Puerto_Rico",
		"MG": "Africa/Nairobi",
		"MH": "Pacific/Tarawa",
		"MK": "Europe/Belgrade",
		"ML": "Africa/Abidjan",
		"MM": "Asia/Yangon",
		"MN": "Asia/Ulaanbaatar",
		"MO": "Asia/Macau",
		"MP": "Pacific/Guam",
		"MQ": "America/Martinique",
		"MR": "Africa/Abidjan",
		"MS": "America/Puerto_Rico",
		"MT": "Europe/Malta",
		"MU": "Indian/Mauritius",
		"MV": "Indian/Maldives",
		"MW": "Africa/Maputo",
		"MX": "America/Mexico_City",
		"MY": "Asia/Kuching",
		"MZ": "Africa/Maputo",
		"NA": "Africa/Windhoek",
		"NC": "Pacific/Noumea",
		"NE": "Africa/Lagos",
		"NF": "Pacific/Norfolk",
		"NG": "Africa/Lagos",
		"NI": "America/Managua",
		"NL": "Europe/Brussels",
		"NO": "Europe/Berlin",
		"NP": "Asia/Kathmandu",
		"NR": "Pacific/Nauru",
		"NU": "Pacific/Niue",
		"NZ": "Pacific/Auckland",
		"OM": "Asia/Dubai",
		"PA": "America/Panama",
		"PE": "America/Lima",
		"PF": "Pacific/Tahiti",
		"PG": "Pacific/Port_Moresby",
		"PH": "Asia/Manila",
		"PK": "Asia/Karachi",
		"PL": "Europe/Warsaw",
		"PM": "America/Miquelon",
		"PN": "Pacific/Pitcairn",
		"PR": "America/Puerto_Rico",
		"PS": "Asia/Gaza",
		"PT": "Europe/Lisbon",
		"PW": "Pacific/Palau",
		"PY": "America/Asuncion",
		"QA": "Asia/Qatar",
		"RE": "Asia/Dubai",
		"RO": "Europe/Bucharest",
		"RS": "Europe/Belgrade",
		"RU": "Europe/Kaliningrad",
		"RW": "Africa/Maputo",
		"SA": "Asia/Riyadh",
		"SB": "Pacific/Guadalcanal",
		"SC": "Asia/Dubai",
		"SD": "Africa/Khartoum",
		"SE": "Europe/Berlin",
		"SG": "Asia/Singapore",
		"SH": "Africa/Abidjan",
		"SI": "Europe/Belgrade",
		"SJ": "Europe/Berlin",
		"SK": "Europe/Prague",
		"SL": "Africa/Abidjan",
		"SM": "Europe/Rome",
		"SN": "Africa/Abidjan",
		"SO": "Africa/Nairobi",
		"SR": "America/Paramaribo",
		"SS": "Africa/Juba",
		"ST": "Africa/Sao_Tome",
		"SV": "America/El_Salvador",
		"SX": "America/Puerto_Rico",
		"SY": "Asia/Damascus",
		"SZ": "Africa/Johannesburg",
		"TC": "America/Grand_Turk",
		"TD": "Africa/Ndjamena",
		"TF": "Asia/Dubai",
		"TG": "Africa/Abidjan",
		"TH": "Asia/Bangkok",
		"TJ": "Asia/Dushanbe",
		"TK": "Pacific/Fakaofo",
		"TL": "Asia/Dili",
		"TM": "Asia/Ashgabat",
		"TN": "Africa/Tunis",
		"TO": "Pacific/Tongatapu",
		"TR": "Europe/Istanbul",
		"TT": "America/Puerto_Rico",
		"TV": "Pacific/Tarawa",
		"TW": "Asia/Taipei",
		"TZ": "Africa/Nairobi",
		"UA": "Europe/Simferopol",
		"UG": "Africa/Nairobi",
		"UM": "Pacific/Pago_Pago",
		"US": "America/New_York",
		"UY": "America/Montevideo",
		"UZ": "Asia/Samarkand",
		"VA": "Europe/Rome",
		"VC": "America/Puerto_Rico",
		"VE": "America/Caracas",
		"VG": "America/Puerto_Rico",
		"VI": "America/Puerto_Rico",
		"VN": "Asia/Bangkok",
		"VU": "Pacific/Efate",
		"WF": "Pacific/Tarawa",
		"WS": "Pacific/Apia",
		"YE": "Asia/Riyadh",
		"YT": "Africa/Nairobi",
		"ZA": "Africa/Johannesburg",
		"ZM": "Africa/Maputo",
		"ZW": "Africa/Maputo",
	}

	// 查找对应的时区，如果没有找到则使用美国东部时区作为默认值
	if timezone, exists := timezoneMap[country]; exists {
		return timezone
	}
	return "Europe/Zurich" // 默认时区
}

// ========== 批量处理能力接口实现 ==========

// SupportsBatchProcessing PopMart爬虫不支持批量处理
func (s *Spider) SupportsBatchProcessing() bool {
	return false
}

// GetMaxBatchSize PopMart爬虫只支持单个处理
func (s *Spider) GetMaxBatchSize() int {
	return 1
}

// PrepareBatchRequest PopMart爬虫不支持批量处理，使用默认实现
// 此方法继承自BaseSpider，会返回错误
