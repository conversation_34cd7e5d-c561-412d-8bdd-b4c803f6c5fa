package request

import (
	"context"
	"fmt"
	"maps"
	"net/url"
	"sync"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/middleware"
	"go-monitor/internal/models"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/logging"
)

// Service 请求服务 - 专注于中间件管理和HTTP请求发送
type Service struct {
	logger            logging.Logger
	baseConfig        *httpclient.Config // 替代原来的client字段
	middlewareManager *middleware.Manager
	mu                sync.RWMutex

	// 统计信息
	stats RequestStats
}

// RequestStats 请求统计信息
type RequestStats struct {
	TotalRequests   int64
	SuccessCount    int64
	FailureCount    int64
	RetryCount      int64
	AverageResponse time.Duration
	ActiveRequests  int64
	LastRequestTime time.Time
	mu              sync.RWMutex
}

// NewService 创建新的请求服务实例
func NewService() *Service {
	return &Service{
		logger:            logging.GetLogger("service.request"),
		baseConfig:        &httpclient.Config{}, // 使用默认配置
		middlewareManager: middleware.NewManager(),
		stats:             RequestStats{},
	}
}

// NewServiceWithConfig 使用指定配置创建请求服务实例
func NewServiceWithConfig(config *httpclient.Config) *Service {
	return &Service{
		logger:            logging.GetLogger("service.request"),
		baseConfig:        config,
		middlewareManager: middleware.NewManager(),
		stats:             RequestStats{},
	}
}

// LoadMiddlewares 加载和注册所有中间件 - 直接使用MiddlewareConfig，无需转换
func (s *Service) LoadMiddlewares(middlewareConfigs map[string]config.MiddlewareConfig) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.logger.Info(fmt.Sprintf("开始加载中间件，数量：%d", len(middlewareConfigs)))

	if err := s.middlewareManager.LoadMiddlewares(middlewareConfigs); err != nil {
		s.logger.Error(fmt.Sprintf("加载中间件失败：%s", err.Error()))
		return fmt.Errorf("failed to load middlewares: %w", err)
	}

	s.logger.Info(fmt.Sprintf("中间件加载完成，数量：%d", s.middlewareManager.Count()))
	return nil
}

// RegisterMiddleware 注册单个中间件
func (s *Service) RegisterMiddleware(name string, middleware interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	return s.middlewareManager.Register(name, middleware)
}

// SendRequest 发送HTTP请求 - 核心请求处理方法
func (s *Service) SendRequest(ctx context.Context, req *models.Request) (*models.Response, error) {
	startTime := time.Now()
	s.updateStats("total_requests", 1)
	s.updateStats("active_requests", 1)
	defer s.updateStats("active_requests", -1)

	s.logger.Debug(fmt.Sprintf("开始处理请求 %s %s，代理：%s",
		req.Method, req.URL, req.Proxy), "platform", req.SpiderType)

	// 1. 预处理请求 - 应用中间件
	processedReq, err := s.preprocessRequest(ctx, req)
	if err != nil {
		s.updateStats("failure_count", 1)
		return nil, fmt.Errorf("request preprocessing failed: %w", err)
	}

	// 请求被中间件跳过
	if processedReq == nil {
		s.logger.Info(fmt.Sprintf("请求被中间件跳过：%s", req.URL), "platform", req.SpiderType)
		return nil, nil
	}

	// 2. 执行HTTP请求
	resp, err := s.executeRequest(ctx, processedReq)
	if err != nil {
		// 处理异常
		if s.shouldRetry(ctx, err, processedReq) {
			s.updateStats("retry_count", 1)
			return s.retryRequest(ctx, processedReq, err)
		}

		s.updateStats("failure_count", 1)
		return nil, err
	}

	// 3. 后处理响应 - 应用中间件
	processedResp, needRetry, err := s.postprocessResponse(ctx, resp)
	if err != nil {
		s.updateStats("failure_count", 1)
		return nil, fmt.Errorf("响应后处理失败: %w", err)
	}

	// 如果中间件请求重试
	if needRetry {
		if processedReq.RetryCount < processedReq.MaxRetries {
			s.updateStats("retry_count", 1)
			s.logger.Debug(fmt.Sprintf("中间件请求重试：%s，重试次数：%d",
				processedReq.URL, processedReq.RetryCount), "platform", processedReq.SpiderType)
			return s.retryRequest(ctx, processedReq, fmt.Errorf("中间件请求重试"))
		} else {
			s.updateStats("failure_count", 1)
			s.logger.Error(fmt.Sprintf("HTTP请求失败：%s，重试：%d/%d",
				processedReq.URL, processedReq.RetryCount, processedReq.MaxRetries))
			return nil, fmt.Errorf("中间件请求重试，但超出最大重试次数")
		}
	}

	// 4. 更新统计
	duration := time.Since(startTime)
	s.updateResponseTime(duration)
	s.updateStats("success_count", 1)

	s.logger.Debug(fmt.Sprintf("请求处理完成：%s，状态码：%d，耗时：%v",
		req.URL, processedResp.StatusCode, duration))

	return processedResp, nil
}

// preprocessRequest 预处理请求 - 应用中间件
func (s *Service) preprocessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 应用中间件处理请求
	processedReq, err := s.middlewareManager.ProcessRequest(ctx, req)
	if err != nil {
		s.logger.Error(fmt.Sprintf("中间件处理请求失败：%s，错误：%s", req.URL, err.Error()))
		return nil, err
	}

	return processedReq, nil
}

// executeRequest 执行实际的HTTP请求 - 重构为独立Client模式
func (s *Service) executeRequest(ctx context.Context, req *models.Request) (*models.Response, error) {
	// 构建请求级配置
	requestConfig := &httpclient.RequestConfig{
		ProxyURL:       req.Proxy,
		Timeout:        req.Timeout,
		UserAgent:      "",   // 可以从请求中获取
		DisableCookies: true, // 爬虫场景下禁用cookie复用
	}

	// 如果请求头中有User-Agent，使用它
	if userAgent, exists := req.Headers["User-Agent"]; exists {
		requestConfig.UserAgent = userAgent
	}

	s.logger.Debug(fmt.Sprintf("创建独立HTTP客户端，代理：%s，超时：%v，URL：%s，Cookie数量：%d",
		req.Proxy, req.Timeout, req.URL, len(req.Cookies)))

	// 创建独立的HTTP Client
	httpClient, err := httpclient.CreateClientForRequest(s.baseConfig, requestConfig)
	if err != nil {
		s.logger.Error(fmt.Sprintf("创建HTTP客户端失败，代理：%s，错误：%s",
			req.Proxy, err.Error()))
		return nil, fmt.Errorf("创建HTTP客户端失败: %w", err)
	}
	defer httpClient.CloseIdleConnections() // 确保资源清理

	// 准备HTTP客户端请求
	// DoWithClient会使用URL和Params重新构建完整URL，所以我们需要确保这些字段正确

	// 确保Host头存在 - 这对API请求非常重要
	headers := make(map[string]string)
	if req.Headers != nil {
		for k, v := range req.Headers {
			headers[k] = v
		}
	}

	// 如果没有Host头，从URL中提取并设置
	if _, hasHost := headers["Host"]; !hasHost {
		if parsedURL, err := url.Parse(req.URL); err == nil && parsedURL.Host != "" {
			headers["Host"] = parsedURL.Host
			s.logger.Debug(fmt.Sprintf("自动设置Host头: %s", parsedURL.Host))
		}
	}

	httpReq := &httpclient.Request{
		URL:          req.URL,
		Method:       req.Method,
		Headers:      headers,          // 使用确保包含Host头的headers
		Params:       req.Params,       // DoWithClient使用Params重新组装查询参数
		Cookies:      req.Cookies,      // 传递cookies
		ReverseProxy: req.ReverseProxy, // 传递反向代理配置
		Body:         req.Body,
		Timeout:      req.Timeout,
		Proxy:        req.Proxy, // 传递代理配置 - 重要！curl实现会优先使用这个字段
	}

	// 执行请求
	httpResp, err := httpclient.DoWithClient(ctx, httpClient, httpReq, nil)
	if err != nil {
		s.logger.Debug(fmt.Sprintf("HTTP请求失败，URL：%s，代理：%s，错误：%s",
			req.URL, req.Proxy, err.Error()))
		return nil, err
	}

	// 转换为内部响应格式
	resp := &models.Response{
		Request:      req,
		StatusCode:   httpResp.StatusCode,
		Headers:      httpResp.Headers,
		Body:         httpResp.Body,
		URL:          httpResp.URL,
		ResponseTime: httpResp.ResponseTime,
		Timestamp:    time.Now(),
		Size:         int64(len(httpResp.Body)),
		SpiderType:   req.SpiderType,
		Metadata:     req.Metadata,
	}

	s.logger.Debug(fmt.Sprintf("HTTP客户端请求完成，URL：%s，代理：%s，状态码：%d，响应时间：%v，响应大小：%d字节，客户端已释放",
		req.URL, req.Proxy, resp.StatusCode, resp.ResponseTime, resp.Size))

	return resp, nil
}

// postprocessResponse 后处理响应 - 应用中间件
func (s *Service) postprocessResponse(ctx context.Context, resp *models.Response) (*models.Response, bool, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 应用中间件处理响应
	needRetry, processedResp, err := s.middlewareManager.ProcessResponse(ctx, resp)
	if err != nil {
		s.logger.Error(fmt.Sprintf("中间件处理响应失败：%s，错误：%s", resp.URL, err.Error()),
			"platform", resp.SpiderType)
		return resp, false, err
	}

	return processedResp, needRetry, nil
}

// shouldRetry 判断是否应该重试
func (s *Service) shouldRetry(ctx context.Context, err error, req *models.Request) bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 应用中间件处理异常
	needRetry, _ := s.middlewareManager.ProcessException(ctx, err, req)
	return needRetry && req.RetryCount < req.MaxRetries
}

// retryRequest 重试请求
func (s *Service) retryRequest(ctx context.Context, req *models.Request, originalErr error) (*models.Response, error) {
	// 增加重试计数
	req.RetryCount++

	// 详细的重试日志
	s.logger.Debug(fmt.Sprintf("开始重试请求：%s，重试次数：%d/%d，原始错误：%s，当前代理：%s",
		req.URL, req.RetryCount, req.MaxRetries, originalErr.Error(), req.Proxy),
		"platform", req.SpiderType)

	// 检查是否超出最大重试次数
	if req.RetryCount > req.MaxRetries {
		s.logger.Error(fmt.Sprintf("HTTP请求失败，已达到最大重试次数：%s，重试：%d/%d，最终错误：%s",
			req.URL, req.RetryCount-1, req.MaxRetries, originalErr.Error()),
			"platform", req.SpiderType)
		return nil, fmt.Errorf("请求失败，已达到最大重试次数 %d: %w", req.MaxRetries, originalErr)
	}

	// 重试时需要清理可能被中间件修改的状态，让中间件重新处理
	// 保存原始URL和参数，以便中间件重新处理
	originalURL := req.URL
	originalParams := make(map[string]string)
	maps.Copy(originalParams, req.Params)

	// 清除可能被签名中间件添加的参数，让其重新生成
	// delete(req.Params, "s")    // POPMART签名参数
	// delete(req.Params, "t")    // POPMART时间戳参数
	// delete(req.Params, "sign") // AliExpress签名参数

	s.logger.Debug(fmt.Sprintf("重试前清理状态，原始URL：%s，清理后参数数量：%d",
		originalURL, len(req.Params)), "platform", req.SpiderType)

	// 等待重试间隔（指数退避）
	retryDelay := time.Duration(req.RetryCount) * time.Second
	s.logger.Debug(fmt.Sprintf("重试延迟：%v", retryDelay), "platform", req.SpiderType)

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-time.After(retryDelay):
		// 继续重试
	}

	// 递归调用发送请求 - 这会重新执行完整的中间件链
	// 包括：代理选择、签名生成、头部设置等
	s.logger.Debug(fmt.Sprintf("开始重新执行中间件链进行重试：%s", req.URL), "platform", req.SpiderType)
	return s.SendRequest(ctx, req)
}

// SendRequestAsync 异步发送请求
func (s *Service) SendRequestAsync(ctx context.Context, req *models.Request, callback func(*models.Response, error)) {
	go func() {
		resp, err := s.SendRequest(ctx, req)
		if callback != nil {
			callback(resp, err)
		}
	}()
}

// BatchSendRequests 批量发送请求
func (s *Service) BatchSendRequests(ctx context.Context, requests []*models.Request) ([]*models.Response, error) {
	results := make([]*models.Response, len(requests))
	errors := make([]error, len(requests))

	var wg sync.WaitGroup
	for i, req := range requests {
		wg.Add(1)
		go func(index int, request *models.Request) {
			defer wg.Done()
			resp, err := s.SendRequest(ctx, request)
			results[index] = resp
			errors[index] = err
		}(i, req)
	}

	wg.Wait()

	// 检查是否有错误
	var firstError error
	for _, err := range errors {
		if err != nil && firstError == nil {
			firstError = err
		}
	}

	return results, firstError
}

// updateStats 更新统计信息
func (s *Service) updateStats(field string, value int64) {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()

	switch field {
	case "total_requests":
		s.stats.TotalRequests += value
		s.stats.LastRequestTime = time.Now()
	case "success_count":
		s.stats.SuccessCount += value
	case "failure_count":
		s.stats.FailureCount += value
	case "retry_count":
		s.stats.RetryCount += value
	case "active_requests":
		s.stats.ActiveRequests += value
	}
}

// updateResponseTime 更新响应时间统计
func (s *Service) updateResponseTime(duration time.Duration) {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()

	if s.stats.TotalRequests > 0 {
		s.stats.AverageResponse = time.Duration(
			(int64(s.stats.AverageResponse)*s.stats.SuccessCount + int64(duration)) / (s.stats.SuccessCount + 1),
		)
	} else {
		s.stats.AverageResponse = duration
	}
}

// GetStats 获取统计信息
func (s *Service) GetStats() map[string]interface{} {
	s.stats.mu.RLock()
	defer s.stats.mu.RUnlock()

	return map[string]interface{}{
		"total_requests":   s.stats.TotalRequests,
		"success_count":    s.stats.SuccessCount,
		"failure_count":    s.stats.FailureCount,
		"retry_count":      s.stats.RetryCount,
		"active_requests":  s.stats.ActiveRequests,
		"average_response": s.stats.AverageResponse,
		"last_request":     s.stats.LastRequestTime,
		"success_rate":     s.getSuccessRate(),
	}
}

// getSuccessRate 计算成功率
func (s *Service) getSuccessRate() float64 {
	if s.stats.TotalRequests == 0 {
		return 0
	}
	return float64(s.stats.SuccessCount) / float64(s.stats.TotalRequests) * 100
}

// Close 关闭服务
func (s *Service) Close() error {
	s.logger.Info("关闭请求服务")

	// 由于现在使用独立的HTTP客户端，无需特殊清理
	return nil
}

// SetBaseConfig 设置基础配置（用于配置更新）
func (s *Service) SetBaseConfig(config *httpclient.Config) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.baseConfig = config
	s.logger.Info(fmt.Sprintf("更新基础HTTP配置，超时：%v，重试次数：%d，用户代理：%s，代理URL：%s，最大空闲连接：%d",
		config.Timeout, config.Retries, config.UserAgent, config.ProxyURL, config.MaxIdleConn))
}

// GetBaseConfig 获取基础配置
func (s *Service) GetBaseConfig() *httpclient.Config {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.baseConfig
}
