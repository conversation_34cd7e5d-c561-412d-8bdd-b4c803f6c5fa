package amazon

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/internal/spiders/shared"
	"go-monitor/pkg/httpclient"

	"github.com/tidwall/gjson"
)

// ========== 常量定义 ==========

// ========== 常量定义 ==========
// 使用shared包中的统一常量，仅保留Amazon特有的常量

// 保留必要的正则表达式（仅用于ASIN验证）
var (
	asinValidationRegex = regexp.MustCompile(`^[A-Z][A-Z0-9]{9}$`)
)

// ========== 工具函数 ==========

// buildProductURL 构建产品URL - 使用shared包函数
func buildProductURL(siteURL, asin string) string {
	return shared.BuildProductURL(siteURL, shared.AmazonProductPagePath, asin)
}

// buildATCLink 构建ATC (Add to Cart) 链接
func buildATCLink(siteURL, offerListingId, asin string) *string {
	if offerListingId == "" || asin == "" {
		return nil
	}

	baseURL := fmt.Sprintf("%s%s?offerListingID=%s&asin=%s&submit.buy-now=1",
		siteURL, shared.AmazonATCBasePath, offerListingId, asin)

	// 验证构建的URL包含当前ASIN
	if !strings.Contains(baseURL, asin) {
		return nil
	}

	link := fmt.Sprintf("[x1](%s&quantity.1=1) | [x2](%s&quantity.1=2)", baseURL, baseURL)
	return &link
}

// extractDebugProductID 提取调试用的产品ID
func extractDebugProductID(productID string, spiderConfig *spiders.SpiderConfig) string {
	if productID != "" {
		return productID
	}

	debugProductID := "unknown"
	if spiderConfig != nil {
		// 尝试从配置中获取产品ID
		productIDs := config.GetProductIDsFromConfig(spiderConfig)
		if len(productIDs) > 0 {
			debugProductID = productIDs[0]
		} else if spiderConfig.Name != "" {
			debugProductID = spiderConfig.Name
		}
	}
	return debugProductID
}

// createOfferIDPointer 创建OfferID指针
func createOfferIDPointer(offerListingId string) *string {
	if offerListingId != "" {
		return &offerListingId
	}
	return nil
}

// 使用shared包中的函数：
// shared.GetStockFromBool()
// shared.GetAvailabilityText()

// ========== 类型定义 ==========

// Spider Amazon商品监控爬虫
type Spider struct {
	*spiders.BaseSpider
	defaultHeaders map[string]string
}

// ========== 构造函数 ==========

// NewSpider 创建Amazon爬虫实例
func NewSpider() spiders.Spider {
	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider(shared.PlatformAmazon, shared.SpiderVersion, "Amazon商品监控爬虫"),
		defaultHeaders: map[string]string{
			"Accept":           "application/json, text/plain, */*",
			"Accept-Language":  "en-US,en;q=0.9",
			"Accept-Encoding":  "gzip, deflate, br, zstd",
			"Cache-Control":    "no-cache",
			"Pragma":           "no-cache",
			"X-Requested-With": "XMLHttpRequest",
			"Sec-Fetch-Dest":   "empty",
			"Sec-Fetch-Mode":   "cors",
			"Sec-Fetch-Site":   "same-origin",
			"Priority":         "u=1, i",
		},
	}
	return spider
}

// ========== 核心方法 ==========

// GetProductIDs 获取产品ID集合 - 对应Python版本的get_product_ids
func (a *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// PrepareRequest 准备请求 - 实现Spider接口
func (s *Spider) PrepareRequest(ctx context.Context, productID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	// 获取站点URL
	siteURL := fmt.Sprintf("%s%s", spiderConfig.SiteURL, shared.AmazonProductAjaxPath)

	params := map[string]string{
		"asin":         productID,
		"filters":      url.QueryEscape(shared.AmazonFilterParam),
		"pc":           shared.AmazonPCParam,
		"experienceId": shared.AmazonExperienceID,
	}

	builder := httpclient.CreateGenericSpiderRequest(shared.PlatformAmazon, siteURL, spiderConfig, productID)

	for key, value := range params {
		builder.AddParam(key, value)
	}
	builder.SetMethod("POST")

	httpRequest := builder.Build()

	return &models.Request{
		SpiderType:  shared.PlatformAmazon,
		URL:         httpRequest.URL,
		Method:      httpRequest.Method,
		Headers:     httpRequest.Headers,
		Body:        httpRequest.Body,
		Params:      httpRequest.Params,
		Metadata:    httpRequest.Meta,
		MonitorName: spiderConfig.Name,
		ProductID:   productID,
	}, nil
}

// buildACPPayload 构建ACP格式的请求payload - 支持批量产品ID
func (a *Spider) buildACPPayload(productIDs []string, _ *spiders.SpiderConfig) (map[string]any, error) {
	// 构建ids数组，每个产品ID需要包装成JSON字符串格式 - 参考init/acp2
	var ids []string
	for _, productID := range productIDs {
		// 根据init/acp2的格式，每个ID需要包装成JSON对象字符串
		idObj := fmt.Sprintf(`{"id":"%s","linkParameters":{"pd_rd_i":"%s"},"contextLinks":[]}`, productID, productID)
		ids = append(ids, idObj)
	}

	// 构建符合Amazon ACP API的请求负载 - 完全按照init/acp2的格式
	payload := map[string]any{
		"aCarouselOptions":    "{}",
		"faceoutspecs":        "{}",
		"faceoutkataname":     "GeneralFaceout",
		"individuals":         "32",
		"linkparameters":      "{}",
		"offset":              "1",
		"aDisplayStrategy":    "swap",
		"aTransitionStrategy": "swap",
		"aAjaxStrategy":       "promise",
		"ids":                 ids,
	}

	return payload, nil
}

// getACPPathFromRedis 从Redis获取ACP路径
func (a *Spider) getACPPathFromRedis(ctx context.Context, redisManager interface {
	GetString(ctx context.Context, key string) (string, error)
}) (string, error) {
	if redisManager == nil {
		return "", fmt.Errorf("Redis管理器不可用")
	}

	// 尝试从版本化存储获取ACP路径
	if redisVersioned, ok := redisManager.(interface {
		GetResourceVersioned(ctx context.Context, resourceType, key string) ([]byte, error)
	}); ok {
		// 使用版本化获取（会自动尝试current -> backup -> default）
		if data, err := redisVersioned.GetResourceVersioned(ctx, "x-amz-acp-path", "default"); err == nil {
			if len(data) > 0 {
				a.LogDebug("Amazon成功获取版本化ACP路径")
				return string(data), nil
			}
		} else {
			a.LogWarn("Amazon获取版本化ACP路径失败：%s", err.Error())
		}
	}

	// 回退到普通Redis存储
	acpPath, err := redisManager.GetString(ctx, "x-amz-acp-path")
	if err != nil {
		return "", fmt.Errorf("从Redis获取ACP路径失败: %w", err)
	}

	if acpPath == "" {
		return "", fmt.Errorf("Redis中的ACP路径为空")
	}

	a.LogDebug("Amazon成功获取ACP路径")
	return acpPath, nil
}

// getACPParamsFromRedis 从Redis获取ACP参数
func (a *Spider) getACPParamsFromRedis(ctx context.Context, redisManager interface {
	GetString(ctx context.Context, key string) (string, error)
}) (string, error) {
	if redisManager == nil {
		return "", fmt.Errorf("Redis管理器不可用")
	}

	// 尝试从版本化存储获取ACP参数
	if redisVersioned, ok := redisManager.(interface {
		GetResourceVersioned(ctx context.Context, resourceType, key string) ([]byte, error)
	}); ok {
		// 使用版本化获取（会自动尝试current -> backup -> default）
		if data, err := redisVersioned.GetResourceVersioned(ctx, "x-amz-acp-params", "default"); err == nil {
			if len(data) > 0 {
				a.LogDebug("Amazon成功获取版本化ACP参数")
				return string(data), nil
			}
		} else {
			a.LogWarn("Amazon获取版本化ACP参数失败：%s", err.Error())
		}
	}

	// 回退到普通获取
	acpParams, err := redisManager.GetString(ctx, "x-amz-acp-params")
	if err != nil {
		return "", fmt.Errorf("从Redis获取ACP参数失败: %w", err)
	}

	if acpParams == "" {
		return "", fmt.Errorf("ACP参数为空")
	}

	a.LogDebug("Amazon成功获取ACP参数（普通存储）")
	return acpParams, nil
}

func (a *Spider) setHeaders(builder *httpclient.SpiderRequestBuilder, _ *spiders.SpiderConfig) {
	// 使用初始化时设置的默认请求头
	for key, value := range a.defaultHeaders {
		// 跳过Content-Type，因为SetJSONBody会自动设置
		if strings.ToLower(key) != "content-type" {
			builder.AddHeader(key, value)
		}
	}
}

// ParseResponse 解析响应 - 实现Spider接口
func (a *Spider) ParseResponse(ctx context.Context, response *models.Response, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	if response.StatusCode != shared.HTTPStatusOK {
		return nil, fmt.Errorf("HTTP状态码错误: %d", response.StatusCode)
	}

	// 检测响应内容类型
	contentType := ""
	if headers := response.Headers["Content-Type"]; len(headers) > 0 {
		contentType = strings.ToLower(headers[0])
	}

	responseStr := string(response.Body)

	// 检查是否为ACP请求
	isACPRequest := false
	if response.Request != nil {
		isACPRequest = response.Request.GetMetadataString("request_type", "") == "batch_acp" ||
			response.Request.GetMetadataString("uses_acp_params", "") == "true"
	}

	// 根据内容类型和请求类型选择解析方法
	if strings.Contains(contentType, shared.ContentTypeJSON) || gjson.Valid(responseStr) {
		if isACPRequest {
			// ACP JSON响应 - 使用ACP解析逻辑
			products, err := a.parseACPResponse(responseStr, config)
			if err != nil {
				return nil, fmt.Errorf("解析ACP响应失败: %w", err)
			}
			a.LogDebug("成功解析Amazon ACP JSON响应，产品数量：%d", len(products))
			return products, nil
		} else {
			// 非ACP的JSON响应 - 现在所有批量请求都应该使用ACP，这里可能是单个请求的JSON响应
			a.LogWarn("收到非ACP的JSON响应，可能是旧格式或单个请求响应")
			return nil, fmt.Errorf("不支持的JSON响应格式，所有批量请求应使用ACP格式")
		}
	} else if strings.Contains(contentType, shared.ContentTypeHTML) || strings.Contains(responseStr, "<html") {
		// HTML响应 - 使用新的HTML解析逻辑
		// 从response.Request中获取productID
		productID := ""
		if response.Request != nil {
			productID = response.Request.GetMetadataString("product_id", "")
			if productID == "" {
				productID = response.Request.ProductID
			}
		}

		products, err := a.parseHTMLResponse(responseStr, config, productID)
		if err != nil {
			return nil, fmt.Errorf("解析HTML响应失败: %w", err)
		}
		a.LogDebug("成功解析Amazon HTML响应，产品数量：%d", len(products))
		return products, nil
	}

	return nil, fmt.Errorf("不支持的响应格式，Content-Type: %s", contentType)
}

// ========== 辅助方法 ==========

// HTMLProductData HTML产品数据结构
type HTMLProductData struct {
	ASIN           string
	Title          string
	Price          float64
	OfferListingID string
	InStock        bool
	ImageURL       string
}

// MerchantValidation 商家验证结果
type MerchantValidation struct {
	IsValid       bool
	MismatchError string
}

// extractHTMLProductData 从HTML中提取产品数据
func (a *Spider) extractHTMLProductData(htmlStr string, productID string) (*HTMLProductData, error) {
	// 提取ASIN，支持productID回退
	asin := a.extractASINFromHTML(htmlStr, productID)
	if asin == "" {
		return nil, fmt.Errorf("无法从HTML中提取ASIN，productID回退也失败")
	}

	// 提取产品标题
	title := a.extractTitleFromHTML(htmlStr)
	if title == "" {
		title = "Amazon Product " + asin
	}

	// 提取价格信息
	priceValue := a.extractPriceFromHTML(htmlStr)

	// 提取offerListingId
	offerListingId := a.extractOfferListingIdFromHTML(htmlStr)

	// 检查库存状态
	inStock := a.extractStockStatusFromHTML(htmlStr)

	// 提取图片URL
	imageURL := a.extractImageURLFromHTML(htmlStr)

	return &HTMLProductData{
		ASIN:           asin,
		Title:          title,
		Price:          priceValue,
		OfferListingID: offerListingId,
		InStock:        inStock,
		ImageURL:       imageURL,
	}, nil
}

// validateMerchantID 验证商家ID匹配
func (a *Spider) validateMerchantID(htmlStr string, spiderConfig *spiders.SpiderConfig, _ string) *MerchantValidation {
	var targetMerchantID string

	if spiderConfig != nil && spiderConfig.SpiderSettings != nil {
		if merchantID, ok := spiderConfig.SpiderSettings["merchantID"].(string); ok {
			targetMerchantID = merchantID
		}
	}

	// 如果没有配置merchantID，则认为验证通过
	if targetMerchantID == "" {
		return &MerchantValidation{IsValid: true}
	}

	pageMerchantID := a.extractMerchantIDFromHTML(htmlStr)
	if pageMerchantID != "" && pageMerchantID != targetMerchantID {
		mismatchReason := fmt.Sprintf("Merchant ID mismatch: expected %s, found %s", targetMerchantID, pageMerchantID)
		// 商家ID不匹配，记录日志并返回验证失败
		return &MerchantValidation{IsValid: false, MismatchError: mismatchReason}
	}

	return &MerchantValidation{IsValid: true}
}

// buildProductFromHTML 从HTML数据构建产品项
func (a *Spider) buildProductFromHTML(data *HTMLProductData, validation *MerchantValidation, htmlStr string, spiderConfig *spiders.SpiderConfig) *models.ProductItem {
	// 应用商家验证结果
	inStock := data.InStock
	if !validation.IsValid {
		inStock = false
	}

	// 计算可用性 - 使用shared包函数
	availability := shared.GetAvailabilityText(inStock)

	// 处理各种无库存情况的可用性描述
	if !inStock {
		if !validation.IsValid {
			availability = validation.MismatchError
		} else if strings.Contains(htmlStr, "No featured offers available") {
			availability = "No featured offers available"
		} else if strings.Contains(htmlStr, "Currently, there are no other sellers") {
			availability = "No other sellers available"
		}
	}

	// 构建产品URL
	siteURL := spiderConfig.SiteURL
	if siteURL == "" {
		siteURL = shared.AmazonDefaultSiteURL
	}
	productURL := buildProductURL(siteURL, data.ASIN)

	// 构建 ATC (Add to Cart) 链接
	var atcLink *string
	if data.OfferListingID != "" && inStock {
		atcLink = buildATCLink(siteURL, data.OfferListingID, data.ASIN)
	}

	// 处理图片URL
	var imageURL *string
	if data.ImageURL != "" {
		imageURL = &data.ImageURL
	}

	// 创建基础产品 - 使用shared包函数
	product := shared.CreateBaseProduct(spiderConfig.Name, data.ASIN, data.Title, productURL, shared.PlatformAmazon, siteURL, spiderConfig.Notifications)

	// 设置价格和库存信息 - 使用shared包函数
	currency := config.GetConfigString(spiderConfig, "currency", "€")
	shared.SetProductPriceAndStock(product, data.Price, currency, inStock, availability, createOfferIDPointer(data.OfferListingID))

	// 设置元数据信息 - 使用shared包函数
	shared.SetProductMetadata(product, shared.PlatformAmazon, "html_aod", map[string]any{
		"image_url": imageURL,
		"atc_link":  atcLink,
	})

	// 设置Amazon特定字段
	product.ImageURL = imageURL
	product.AtcLink = atcLink

	return product
}

// parseACPResponse 解析ACP格式的JSON响应 - 参考init/acp2的真实响应格式
func (a *Spider) parseACPResponse(jsonStr string, spiderConfig *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var products []*models.ProductItem

	// 根据init/acp2的真实响应格式，解析products数组
	if gjson.Get(jsonStr, "products").Exists() {
		gjson.Get(jsonStr, "products").ForEach(func(key, value gjson.Result) bool {
			if product := a.extractProductFromACPResponse(value.Raw, spiderConfig); product != nil {
				products = append(products, product)
			}
			return true // 继续遍历
		})
		return products, nil
	}

	// 如果没有products数组，返回空结果
	a.LogWarn("Amazon ACP响应中未找到products数组")
	return products, nil
}

// extractProductFromACPResponse 从ACP响应中提取单个产品信息 - 参考init/acp2的真实响应格式
func (a *Spider) extractProductFromACPResponse(productJson string, spiderConfig *spiders.SpiderConfig) *models.ProductItem {
	// 从link.url中提取ASIN - 根据init/acp2的响应格式
	linkURL := gjson.Get(productJson, "link.url").String()
	asin := ""
	if linkURL != "" {
		// 从URL中提取ASIN，格式如：/POP-MART-DIMOO-DISNEY-Bag/dp/B0DYJBY6ZV/ref=...
		if matches := regexp.MustCompile(`/dp/([A-Z0-9]{10})`).FindStringSubmatch(linkURL); len(matches) > 1 {
			asin = matches[1]
			a.LogDebug("Amazon ACP从link.url提取ASIN: %s", asin)
		}
	}
	if asin == "" {
		a.LogWarn("Amazon ACP无法从产品数据中提取ASIN")
		return nil
	}

	// 提取产品标题
	title := gjson.Get(productJson, "title.titleText").String()
	if title == "" {
		title = fmt.Sprintf("Product %s", asin)
		a.LogDebug("Amazon ACP使用默认标题: %s", title)
	} else {
		a.LogDebug("Amazon ACP提取产品标题: %s", title)
	}

	// 提取价格信息 - 根据init/acp2的响应格式
	price := 0.0
	currency := "EUR"

	// 尝试从price.singlePrice中获取价格
	if gjson.Get(productJson, "price.singlePrice").Exists() {
		priceStr := gjson.Get(productJson, "price.singlePrice.wholeValue").String()
		fractionalStr := gjson.Get(productJson, "price.singlePrice.fractionalValue").String()
		currency = gjson.Get(productJson, "price.singlePrice.currencyCode").String()

		if priceStr != "" {
			if wholePrice, err := strconv.ParseFloat(priceStr, 64); err == nil {
				price = wholePrice
				if fractionalStr != "" {
					if fractionalPrice, err := strconv.ParseFloat("0."+fractionalStr, 64); err == nil {
						price += fractionalPrice
					}
				}
				a.LogDebug("Amazon ACP从price.singlePrice提取价格: %.2f %s", price, currency)
			}
		}
	} else if gjson.Get(productJson, "marketOffers.auiPrice").Exists() {
		// 备选：从marketOffers.auiPrice中获取价格
		wholeValue := gjson.Get(productJson, "marketOffers.auiPrice.wholeValue").String()
		fractionalValue := gjson.Get(productJson, "marketOffers.auiPrice.fractionalValue").String()
		currencySymbol := gjson.Get(productJson, "marketOffers.auiPrice.currencySymbol").String()

		if wholeValue != "" {
			if wholePrice, err := strconv.ParseFloat(wholeValue, 64); err == nil {
				price = wholePrice
				if fractionalValue != "" {
					if fractionalPrice, err := strconv.ParseFloat("0."+fractionalValue, 64); err == nil {
						price += fractionalPrice
					}
				}
				// 根据货币符号设置currency
				switch currencySymbol {
				case "€":
					currency = "EUR"
				case "$":
					currency = "USD"
				case "£":
					currency = "GBP"
				default:
					currency = "EUR" // 默认
				}
				a.LogDebug("Amazon ACP从marketOffers.auiPrice提取价格: %.2f %s", price, currency)
			}
		}
	}

	// 提取库存状态
	inStock := gjson.Get(productJson, "availability.isInStock").Bool()
	availability := gjson.Get(productJson, "availability.primaryMessage").String()
	if availability == "" {
		if inStock {
			availability = "In Stock"
		} else {
			availability = "Out of Stock"
		}
	}

	// 提取图片URL - 根据init/acp2的真实响应格式
	imageURL := ""
	// 优先从image.image.physicalId构建标准Amazon图片URL
	if physicalId := gjson.Get(productJson, "image.image.physicalId").String(); physicalId != "" {
		// 构建标准Amazon图片URL，使用165x165尺寸
		imageURL = fmt.Sprintf("https://images-eu.ssl-images-amazon.com/images/I/%s._AC_UL165_SR165,165_.jpg", physicalId)
		a.LogDebug("Amazon ACP成功从physicalId构建图片URL: %s", imageURL)
	} else if fallbackURL := gjson.Get(productJson, "image.imageUri").String(); fallbackURL != "" {
		// 备选：直接使用imageUri
		imageURL = fallbackURL
		a.LogDebug("Amazon ACP使用备选图片URL: %s", imageURL)
	}

	// 提取OfferID - 根据init/acp2的真实响应格式
	offerListingId := ""
	// 尝试从atcV2.addToCartViewModel.formItems[0].itemParameters中获取
	if gjson.Get(productJson, "atcV2.addToCartViewModel.formItems").Exists() {
		a.LogDebug("Amazon ACP开始提取offerListingId")
		gjson.Get(productJson, "atcV2.addToCartViewModel.formItems").ForEach(func(key, value gjson.Result) bool {
			if itemParams := gjson.Get(value.Raw, "itemParameters"); itemParams.Exists() {
				if offerID := gjson.Get(itemParams.Raw, "items[0.base][offerListingId]").String(); offerID != "" {
					// URL解码offerListingId
					if decoded, err := url.QueryUnescape(offerID); err == nil {
						offerListingId = decoded
						a.LogDebug("Amazon ACP成功提取并解码offerListingId: %s", offerListingId[:20]+"...")
					} else {
						offerListingId = offerID
						a.LogDebug("Amazon ACP提取offerListingId（未解码）: %s", offerListingId[:20]+"...")
					}
					return false // 找到后停止遍历
				}
			}
			return true
		})
		if offerListingId == "" {
			a.LogDebug("Amazon ACP未能从formItems中提取到offerListingId")
		}
	} else {
		a.LogDebug("Amazon ACP响应中未找到atcV2.addToCartViewModel.formItems")
	}

	// 构建ATC链接（只在有库存且有OfferID时）
	var atcLink *string = nil
	if inStock && offerListingId != "" {
		// 优先使用响应中的原生addToCartUrl
		if nativeURL := gjson.Get(productJson, "atcV2.addToCartViewModel.addToCartUrl").String(); nativeURL != "" {
			// 构建完整URL
			fullURL := fmt.Sprintf("%s%s", spiderConfig.SiteURL, nativeURL)
			link := fmt.Sprintf("[x1](%s&quantity=1) | [x2](%s&quantity=2)", fullURL, fullURL)
			atcLink = &link
			a.LogDebug("Amazon ACP使用原生ATC URL: %s", nativeURL)
		} else {
			// 备选：使用传统构建方式
			atcLink = buildATCLink(spiderConfig.SiteURL, offerListingId, asin)
			if atcLink != nil {
				a.LogDebug("Amazon ACP使用构建的ATC链接")
			}
		}
	}

	// ACP产品解析完成

	// 构建产品项
	product := &models.ProductItem{
		Name:          spiderConfig.Name,
		ProductID:     asin,
		Title:         title,
		URL:           fmt.Sprintf("%s%s", spiderConfig.SiteURL, linkURL),
		Platform:      "amazon",
		Price:         price,
		Currency:      currency,
		InStock:       inStock,
		Stock:         shared.GetStockFromBool(inStock),
		Availability:  availability,
		OfferID:       createOfferIDPointer(offerListingId),
		AtcLink:       atcLink,
		Notifications: spiderConfig.Notifications,
		Country:       spiderConfig.Country,
		SiteURL:       spiderConfig.SiteURL,
		CrawledAt:     time.Now(),
		Metadata: map[string]any{
			"spider_type": "amazon",
			"api_source":  "acp",
		},
	}

	// 添加图片URL（如果存在）
	if imageURL != "" {
		product.ImageURL = &imageURL
	}

	return product
}

// 使用shared包中的函数替换重复代码
// createBaseProduct -> shared.CreateBaseProduct
// setProductPriceAndStock -> shared.SetProductPriceAndStock
// setProductMetadata -> shared.SetProductMetadata

// ========== 批量处理能力接口实现 ==========

// SupportsBatchProcessing Amazon爬虫支持批量处理
func (a *Spider) SupportsBatchProcessing() bool {
	return true
}

// GetMaxBatchSize Amazon爬虫的最大批量大小
func (a *Spider) GetMaxBatchSize() int {
	return shared.DefaultBatchSize
}

// PrepareBatchRequest 准备批量请求 - 使用ACP参数支持多个ASIN
func (a *Spider) PrepareBatchRequest(ctx context.Context, productIDs []string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {

	// 获取站点URL
	siteURL := spiderConfig.SiteURL
	if siteURL == "" {
		siteURL = "https://www.amazon.com"
	}

	// 构建批量ACP格式请求payload
	payload, err := a.buildACPPayload(productIDs, spiderConfig)
	if err != nil {
		return nil, fmt.Errorf("构建批量ACP payload失败: %w", err)
	}

	// 获取动态ACP路径和参数
	var acpEndpoint string
	var acpParams string

	if spiderConfig.RedisManager != nil {
		if redisManager, ok := spiderConfig.RedisManager.(interface {
			GetString(ctx context.Context, key string) (string, error)
		}); ok {
			// 获取ACP路径
			acpPath, err := a.getACPPathFromRedis(ctx, redisManager)
			if err != nil {
				return nil, fmt.Errorf("批量请求获取ACP路径失败: %w", err)
			}

			// 构建完整的ACP端点URL
			acpEndpoint = fmt.Sprintf("%s%sgetCarouselItems", siteURL, acpPath)

			// 获取ACP参数
			acpParams, err = a.getACPParamsFromRedis(ctx, redisManager)
			if err != nil {
				return nil, fmt.Errorf("批量请求获取ACP参数失败: %w", err)
			}

			// 使用动态ACP端点
		} else {
			return nil, fmt.Errorf("Redis管理器不支持GetString方法，无法获取ACP数据")
		}
	} else {
		return nil, fmt.Errorf("Redis管理器不可用，无法获取ACP数据")
	}

	// 使用HTTP客户端构建器创建请求
	builder := httpclient.CreateAmazonRequest(acpEndpoint, spiderConfig, strings.Join(productIDs, ","))
	builder.SetMethod("POST")
	a.setHeaders(builder, spiderConfig)

	// 添加ACP API必需的请求头 - 参考init/acp2
	builder.AddHeader("x-requested-with", "XMLHttpRequest")
	builder.AddHeader("accept", "application/json")
	builder.AddHeader("content-type", "application/json")
	builder.AddHeader("sec-fetch-site", "same-origin")
	builder.AddHeader("sec-fetch-mode", "cors")
	builder.AddHeader("sec-fetch-dest", "empty")

	// 添加ACP参数到请求头（已在上面获取）
	builder.AddHeader("x-amz-acp-params", acpParams)

	builder.SetJSONBody(payload)
	httpRequest := builder.Build()

	// 创建请求并标记为ACP请求
	request := &models.Request{
		SpiderType:  "amazon",
		URL:         httpRequest.URL,
		Method:      httpRequest.Method,
		Headers:     httpRequest.Headers,
		Body:        httpRequest.Body,
		Params:      httpRequest.Params,
		Metadata:    httpRequest.Meta,
		MonitorName: spiderConfig.Name,
		ProductID:   strings.Join(productIDs, ","),
	}

	// 标记此请求使用ACP参数
	request.SetMetadata("uses_acp_params", "true")
	request.SetMetadata("request_type", "batch_acp")

	// 批量ACP请求已准备

	return request, nil
}

// parseHTMLResponse 解析HTML格式的Amazon All Offers Display响应
func (a *Spider) parseHTMLResponse(htmlStr string, spiderConfig *spiders.SpiderConfig, productID string) ([]*models.ProductItem, error) {
	// 提取HTML产品数据
	data, err := a.extractHTMLProductData(htmlStr, productID)
	if err != nil {
		// 保存HTML到调试文件以便分析问题
		debugProductID := extractDebugProductID(productID, spiderConfig)
		a.saveDebugHTML(htmlStr, debugProductID)
		return nil, fmt.Errorf("提取HTML产品数据失败: %w", err)
	}

	// 验证商家ID匹配
	validation := a.validateMerchantID(htmlStr, spiderConfig, data.ASIN)

	// 构建产品项
	product := a.buildProductFromHTML(data, validation, htmlStr, spiderConfig)

	return []*models.ProductItem{product}, nil
}

// extractASINFromHTML 从HTML中提取ASIN - 支持多种提取策略和productID回退
func (a *Spider) extractASINFromHTML(htmlStr string, productID string) string {
	// 策略1: 从隐藏的input字段中提取ASIN（传统方法）
	re := regexp.MustCompile(`name="items\[0\.base\]\[asin\]"\s+value="([^"]+)"`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	// 策略2: 从商家链接的URL参数中提取ASIN
	asin := a.extractASINFromURL(htmlStr)
	if asin != "" {
		return asin
	}

	// 策略3: 从图片URL中提取产品标识（备用方法）
	asin = a.extractASINFromImageURL(htmlStr)
	if asin != "" {
		return asin
	}

	return productID
}

// extractASINFromURL 从商家链接的URL参数中提取ASIN
func (a *Spider) extractASINFromURL(htmlStr string) string {
	// 匹配商家链接中的asin参数，支持HTML编码的&amp;
	// 示例: href="/-/en/gp/aag/main?ie=UTF8&amp;seller=APLYZSTHRMK0Z&amp;isAmazonFulfilled=1&amp;asin=B0DJY3PJ7T&amp;ref_=olp_merch_name_0"
	re := regexp.MustCompile(`href="[^"]*[?&](?:amp;)?asin=([A-Z0-9]{10})[&"]`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	// 备用模式1：处理HTML编码的&amp;
	re = regexp.MustCompile(`[?&](?:amp;)?asin=([A-Z0-9]{10})`)
	matches = re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	// 备用模式2：更宽松的ASIN匹配，不限制前后字符
	re = regexp.MustCompile(`asin=([A-Z0-9]{10})`)
	matches = re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// extractASINFromImageURL 从图片URL中提取产品标识（备用方法）
func (a *Spider) extractASINFromImageURL(_ string) string {
	// Amazon图片URL通常包含产品信息，但这是备用方法，不如ASIN可靠
	// 示例: src="https://m.media-amazon.com/images/I/31JfQqVRJdL.jpg"
	// 这个方法主要用于调试，实际使用中应该依赖其他方法
	// 图片ID不是ASIN，暂不使用此方法

	// 目前不从图片URL提取ASIN，因为图片ID不等于ASIN
	// 这个方法保留用于未来可能的增强
	return ""
}

// extractMerchantIDFromHTML 从HTML中提取商家ID
func (a *Spider) extractMerchantIDFromHTML(htmlStr string) string {
	// 从商家链接中提取seller参数，支持HTML编码的&amp;
	// 示例: href="/-/en/gp/aag/main?ie=UTF8&amp;seller=APLYZSTHRMK0Z&amp;isAmazonFulfilled=1&amp;asin=B0DJY3PJ7T&amp;ref_=olp_merch_name_0"
	re := regexp.MustCompile(`href="[^"]*[?&](?:amp;)?seller=([A-Z0-9]+)[&"]`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	// 备用模式1：处理HTML编码的&amp;
	re = regexp.MustCompile(`[?&](?:amp;)?seller=([A-Z0-9]+)`)
	matches = re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	// 备用模式2：更宽松的seller参数匹配
	re = regexp.MustCompile(`seller=([A-Z0-9]+)`)
	matches = re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// saveDebugHTML 保存HTML到调试文件以便分析ASIN提取问题
func (a *Spider) saveDebugHTML(htmlContent, productID string) {
	// 检查HTML大小，避免保存过大的文件（限制为5MB）
	const maxHTMLSize = 5 * 1024 * 1024
	if len(htmlContent) > maxHTMLSize {
		a.LogWarn("Amazon HTML文件过大，跳过保存，大小：%d bytes，产品ID：%s", len(htmlContent), productID)
		return
	}

	// 创建调试目录
	debugDir := "debug/amazon/html"
	if err := os.MkdirAll(debugDir, 0755); err != nil {
		a.LogError("Amazon创建调试目录失败：%s，错误：%s", debugDir, err.Error())
		return
	}

	// 生成文件名：failed_asin_YYYYMMDD_HHMMSS_[productID].html
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("failed_asin_%s_%s.html", timestamp, productID)
	if productID == "" {
		filename = fmt.Sprintf("failed_asin_%s_unknown.html", timestamp)
	}

	filePath := filepath.Join(debugDir, filename)

	// 保存HTML文件
	if err := os.WriteFile(filePath, []byte(htmlContent), 0644); err != nil {
		a.LogError("Amazon保存调试HTML文件失败：%s，错误：%s", filePath, err.Error())
		return
	}

	// 记录成功保存的信息
	a.LogInfo("Amazon ASIN提取失败，已保存HTML到调试文件：%s，大小：%d bytes，产品ID：%s",
		filePath, len(htmlContent), productID)
}

// extractTitleFromHTML 从HTML中提取产品标题
func (a *Spider) extractTitleFromHTML(htmlStr string) string {
	// 从aod-asin-title-text元素中提取标题
	re := regexp.MustCompile(`id="aod-asin-title-text"[^>]*>\s*([^<]+)\s*</h5>`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}
	return ""
}

// extractPriceFromHTML 从HTML中提取价格和货币
func (a *Spider) extractPriceFromHTML(htmlStr string) float64 {
	// 提取价格符号和数值
	re := regexp.MustCompile(`<span class="a-price-symbol">([^<]+)</span><span class="a-price-whole">([^<]+)<span class="a-price-decimal">\.</span></span><span class="a-price-fraction">([^<]+)</span>`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 3 {
		whole := matches[2]
		fraction := matches[3]

		// 组合价格字符串并转换为浮点数
		priceStr := whole + "." + fraction
		if price, err := strconv.ParseFloat(priceStr, 64); err == nil {
			// 根据符号确定货币
			return price
		}
	}

	// 备用方法：从aok-offscreen元素提取
	re2 := regexp.MustCompile(`<span class="aok-offscreen">\s*([€$£¥]?)([0-9,]+\.?[0-9]*)\s*</span>`)
	matches2 := re2.FindStringSubmatch(htmlStr)
	if len(matches2) > 2 {
		priceStr := strings.ReplaceAll(matches2[2], ",", "")
		if price, err := strconv.ParseFloat(priceStr, 64); err == nil {
			return price
		}
	}

	return 0
}

// extractOfferListingIdFromHTML 从HTML中提取offerListingId
func (a *Spider) extractOfferListingIdFromHTML(htmlStr string) string {
	// 从隐藏的input字段中提取offerListingId
	re := regexp.MustCompile(`name="items\[0\.base\]\[offerListingId\]"\s+value="([^"]+)"`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		// URL解码offerListingId
		if decoded, err := url.QueryUnescape(matches[1]); err == nil {
			return decoded
		}
		return matches[1]
	}
	return ""
}

// extractStockStatusFromHTML 从HTML中提取库存状态
func (a *Spider) extractStockStatusFromHTML(htmlStr string) bool {
	// 检查多种Add to Cart按钮模式（属性顺序无关）
	patterns := []string{
		`<input[^>]*name="submit\.addToCart"[^>]*type="submit"[^>]*>`,   // 原始模式 name->type
		`<input[^>]*type="submit"[^>]*name="submit\.addToCart"[^>]*>`,   // 原始模式 type->name
		`<input[^>]*name="submit\.add-to-cart"[^>]*type="submit"[^>]*>`, // 连字符模式 name->type
		`<input[^>]*type="submit"[^>]*name="submit\.add-to-cart"[^>]*>`, // 连字符模式 type->name
		`<input[^>]*value="Add to Cart"[^>]*type="submit"[^>]*>`,        // 基于value的模式
		`<input[^>]*type="submit"[^>]*value="Add to Cart"[^>]*>`,        // 基于value的模式（type在前）
		`<button[^>]*[^>]*Add to Cart[^>]*</button>`,                    // button标签模式
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		if re.MatchString(htmlStr) {
			// 进一步检查按钮是否被禁用
			disabledRe := regexp.MustCompile(pattern[:len(pattern)-1] + `[^>]*disabled[^>]*>`)
			isDisabled := disabledRe.MatchString(htmlStr)
			if !isDisabled {
				return true
			}
		}
	}

	return false
}

// extractImageURLFromHTML 从HTML中提取产品图片URL
func (a *Spider) extractImageURLFromHTML(htmlStr string) string {
	// 从img标签中提取图片URL
	re := regexp.MustCompile(`<img[^>]*src="([^"]+)"[^>]*id="aod-asin-image-id"`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	// 备用方法：先匹配src再匹配id
	re2 := regexp.MustCompile(`src="([^"]+)"[^>]*id="aod-asin-image-id"`)
	matches2 := re2.FindStringSubmatch(htmlStr)
	if len(matches2) > 1 {
		return matches2[1]
	}

	return ""
}
