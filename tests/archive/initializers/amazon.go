package initializers

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"sync"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/redis"
	"go-monitor/internal/services/request"
	"go-monitor/internal/services/resource"
	"go-monitor/pkg/logging"

	"github.com/tidwall/gjson"
)

// 常量定义
const (
	// 默认配置
	defaultAmazonInitializerName     = "amazon_resources"
	defaultAmazonInitializerPriority = 15
	defaultAmazonTTL                 = 2 * time.Hour
	defaultJuvecTemplateKey          = "amazon_juvec_template"

	// HTTP状态码
	httpStatusOK = 200

	// 配置路径
	configPathTokenPatterns  = "parsing_config.token_patterns"
	configPathTokenMappings  = "parsing_config.token_mappings"
	configPathRequiredTokens = "parsing_config.required_tokens"

	// 默认token模式
	defaultSlateTokenPattern         = `"slateToken":"([^"]+)"`
	defaultFreshCartCSRFTokenPattern = `"freshCartCsrfToken":"([^"]+)"`
	defaultAmazonAPICSRFTokenPattern = `"amazonApiCsrfToken":"([^"]+)"`

	// 默认token映射键
	tokenKeySlate         = "slate_token"
	tokenKeyFreshCartCSRF = "fresh_cart_csrf_token"
	tokenKeyAmazonAPICSRF = "amazon_api_csrf_token"

	// 默认token映射值
	tokenMappingSlate         = "slateToken"
	tokenMappingFreshCartCSRF = "freshCartCsrfToken"
	tokenMappingAmazonAPICSRF = "amazonApiCsrfToken"

	// 其他字段提取模式
	obfuscatedMarketplaceIdPattern = `"obfuscatedMarketplaceId":"([^"]+)"`
	languagePattern                = `"language":"([^"]+)"`
	currencyPattern                = `"currency":"([^"]+)"`
)

// getDefaultTokenPatterns 获取默认token模式
func getDefaultTokenPatterns() map[string]string {
	return map[string]string{
		tokenKeySlate:         defaultSlateTokenPattern,
		tokenKeyFreshCartCSRF: defaultFreshCartCSRFTokenPattern,
		tokenKeyAmazonAPICSRF: defaultAmazonAPICSRFTokenPattern,
	}
}

// getDefaultTokenMappings 获取默认token映射
func getDefaultTokenMappings() map[string]string {
	return map[string]string{
		tokenKeySlate:         tokenMappingSlate,
		tokenKeyFreshCartCSRF: tokenMappingFreshCartCSRF,
		tokenKeyAmazonAPICSRF: tokenMappingAmazonAPICSRF,
	}
}

// getDefaultRequiredTokens 获取默认必需token列表
func getDefaultRequiredTokens() []string {
	return []string{tokenKeySlate, tokenKeyAmazonAPICSRF}
}

// getDefaultHeaders 获取默认HTTP请求头
func getDefaultHeaders() map[string]string {
	return map[string]string{
		"Accept":          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
		"Accept-Language": "en-US,en;q=0.9",
	}
}

// 极简Amazon资源初始化器
type AmazonResourceInitializer struct {
	name           string
	priority       int
	dependencies   []string
	ttl            time.Duration
	redisManager   *redis.RedisManager
	requestService *request.Service
	logger         logging.Logger

	// 保存的配置，用于Refresh时使用
	savedConfig map[string]interface{}

	mu          sync.RWMutex
	initialized bool
	healthy     bool
	lastRefresh time.Time
}

// NewAmazonResourceInitializer 创建Amazon资源初始化器
func NewAmazonResourceInitializer(redisManager *redis.RedisManager, requestService *request.Service) *AmazonResourceInitializer {
	return &AmazonResourceInitializer{
		name:           defaultAmazonInitializerName,
		priority:       defaultAmazonInitializerPriority,
		dependencies:   []string{},
		ttl:            defaultAmazonTTL,
		redisManager:   redisManager,
		requestService: requestService,
		logger:         logging.GetLogger("amazon-resource-initializer"),
	}
}

// 实现ResourceInitializer接口
func (a *AmazonResourceInitializer) Name() string           { return a.name }
func (a *AmazonResourceInitializer) Priority() int          { return a.priority }
func (a *AmazonResourceInitializer) Dependencies() []string { return a.dependencies }
func (a *AmazonResourceInitializer) TTL() time.Duration     { return a.ttl }

func (a *AmazonResourceInitializer) IsInitialized() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.initialized
}

func (a *AmazonResourceInitializer) IsHealthy() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.healthy && a.initialized && !a.IsExpired()
}

func (a *AmazonResourceInitializer) GetStatus() resource.ResourceStatus {
	a.mu.RLock()
	defer a.mu.RUnlock()

	return resource.ResourceStatus{
		Name:        a.name,
		Healthy:     a.healthy,
		LastRefresh: a.lastRefresh,
		Metadata: map[string]string{
			"initialized": "true",
		},
	}
}

// Initialize 初始化Amazon资源
func (a *AmazonResourceInitializer) Initialize(ctx context.Context, config map[string]interface{}) error {
	// 验证配置
	if err := a.validateConfig(config); err != nil {
		return err
	}

	// 解析配置（包括TTL）
	if err := a.parseConfig(config); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	// 保存配置供Refresh时使用
	a.mu.Lock()
	a.savedConfig = config
	a.mu.Unlock()

	// 访问Amazon页面并获取juvec参数
	if err := a.fetchAndStoreJuvecTemplate(ctx, config); err != nil {
		return err
	}

	a.mu.Lock()
	a.initialized = true
	a.healthy = true
	a.lastRefresh = time.Now()
	a.mu.Unlock()

	a.logger.Info("Amazon资源初始化完成")
	return nil
}

// Refresh 刷新Amazon资源
func (a *AmazonResourceInitializer) Refresh(ctx context.Context) error {

	// 获取保存的配置
	a.mu.RLock()
	config := a.savedConfig
	a.mu.RUnlock()

	if config == nil {
		return fmt.Errorf("没有保存的配置，无法刷新资源")
	}

	if err := a.fetchAndStoreJuvecTemplate(ctx, config); err != nil {
		a.mu.Lock()
		a.healthy = false
		a.mu.Unlock()
		return err
	}

	a.mu.Lock()
	a.healthy = true
	a.lastRefresh = time.Now()
	a.mu.Unlock()

	a.logger.Info("Amazon资源刷新完成")
	return nil
}

// parseConfig 解析配置参数
func (a *AmazonResourceInitializer) parseConfig(config map[string]interface{}) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	// 解析TTL配置
	if ttlValue, exists := config["ttl"]; exists {
		switch v := ttlValue.(type) {
		case string:
			if duration, err := time.ParseDuration(v); err == nil {
				a.ttl = duration
			} else {
				a.logger.Warn(fmt.Sprintf("无法解析TTL配置 %s，使用默认值 %s，错误：%s", v, a.ttl.String(), err.Error()))
			}
		case time.Duration:
			a.ttl = v
		default:
			a.logger.Warn(fmt.Sprintf("TTL配置类型 %T 不支持，使用默认值 %s", v, a.ttl.String()))
		}
	}

	// 解析优先级配置
	if priority, exists := config["priority"]; exists {
		if p, ok := priority.(int); ok {
			a.priority = p
		}
	}

	return nil
}

// fetchAndStoreJuvecTemplate 获取并存储juvec模板
func (a *AmazonResourceInitializer) fetchAndStoreJuvecTemplate(ctx context.Context, config map[string]interface{}) error {
	// 构建并发送HTTP请求
	resp, err := a.sendAmazonRequest(ctx, config)
	if err != nil {
		return err
	}

	// 从HTML中提取所有需要的参数
	juvecTemplate, isValid := a.extractJuvecTemplate(string(resp.Body), config)

	// 只有在所有必需token都成功提取时才存储到Redis
	if !isValid {
		a.mu.Lock()
		a.healthy = false
		a.mu.Unlock()
		return fmt.Errorf("关键token提取失败，保持现有Redis数据不变")
	}

	// 存储到Redis
	if err := a.storeJuvecTemplate(ctx, juvecTemplate, config); err != nil {
		a.mu.Lock()
		a.healthy = false
		a.mu.Unlock()
		return err
	}

	return nil
}

// sendAmazonRequest 发送Amazon请求
func (a *AmazonResourceInitializer) sendAmazonRequest(ctx context.Context, config map[string]interface{}) (*models.Response, error) {
	// 从配置中获取请求配置
	requestConfig, ok := config["request_config"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("request_config 配置缺失")
	}

	// 获取URL
	amazonURL, ok := requestConfig["fallback_url"].(string)
	if !ok || amazonURL == "" {
		return nil, fmt.Errorf("fallback_url 配置不能为空")
	}

	// 构建请求
	req := a.buildAmazonRequest(amazonURL, requestConfig)

	// 发送请求
	resp, err := a.requestService.SendRequest(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}

	if resp.StatusCode != httpStatusOK {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	return resp, nil
}

// buildAmazonRequest 构建Amazon请求
func (a *AmazonResourceInitializer) buildAmazonRequest(url string, requestConfig map[string]interface{}) *models.Request {
	req := &models.Request{
		URL:     url,
		Method:  "GET",
		Headers: a.buildRequestHeaders(requestConfig),
		Metadata: map[string]interface{}{
			"spider_type": "amazon",
		},
	}

	// 添加代理配置
	if proxies, ok := requestConfig["proxies"].([]interface{}); ok {
		req.Metadata["proxies"] = proxies
	}

	// 添加cookie配置
	if cookies, ok := requestConfig["cookies"].([]interface{}); ok {
		req.Metadata["cookies"] = cookies
	}

	return req
}

// validateConfig 验证配置
func (a *AmazonResourceInitializer) validateConfig(config map[string]interface{}) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 验证请求配置
	requestConfig, ok := config["request_config"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("request_config 配置缺失")
	}

	fallbackURL, ok := requestConfig["fallback_url"].(string)
	if !ok || fallbackURL == "" {
		return fmt.Errorf("fallback_url 配置不能为空")
	}

	return nil
}

// buildRequestHeaders 构建HTTP请求头
func (a *AmazonResourceInitializer) buildRequestHeaders(requestConfig map[string]interface{}) map[string]string {
	headers := make(map[string]string)

	// 从配置中获取headers
	if defaultHeaders, ok := requestConfig["default_headers"].(map[string]interface{}); ok {
		for k, v := range defaultHeaders {
			if strVal, ok := v.(string); ok {
				headers[k] = strVal
			}
		}
	}

	// 如果没有配置headers，使用默认值
	if len(headers) == 0 {
		headers = getDefaultHeaders()
	}

	return headers
}

// parseTokenPatterns 解析token模式配置
func (a *AmazonResourceInitializer) parseTokenPatterns(config map[string]interface{}) map[string]string {
	tokenPatterns := make(map[string]string)

	// 将config转换为JSON字符串以使用GJSON
	configBytes, _ := json.Marshal(config)
	configJson := string(configBytes)

	// 使用GJSON解析token模式
	gjson.Get(configJson, configPathTokenPatterns).ForEach(func(key, value gjson.Result) bool {
		tokenPatterns[key.String()] = value.String()
		return true
	})

	// 如果没有配置，使用默认值
	if len(tokenPatterns) == 0 {
		tokenPatterns = getDefaultTokenPatterns()
	}

	return tokenPatterns
}

// parseTokenMappings 解析token映射配置
func (a *AmazonResourceInitializer) parseTokenMappings(config map[string]interface{}) map[string]string {
	tokenMappings := make(map[string]string)

	// 将config转换为JSON字符串以使用GJSON
	configBytes, _ := json.Marshal(config)
	configJson := string(configBytes)

	// 使用GJSON解析token映射
	gjson.Get(configJson, configPathTokenMappings).ForEach(func(key, value gjson.Result) bool {
		tokenMappings[key.String()] = value.String()
		return true
	})

	// 如果没有配置，使用默认值
	if len(tokenMappings) == 0 {
		tokenMappings = getDefaultTokenMappings()
	}

	return tokenMappings
}

// parseRequiredTokens 解析必需token列表配置
func (a *AmazonResourceInitializer) parseRequiredTokens(config map[string]interface{}) []string {
	var requiredTokens []string

	// 将config转换为JSON字符串以使用GJSON
	configBytes, _ := json.Marshal(config)
	configJson := string(configBytes)

	// 使用GJSON解析必需token列表
	gjson.Get(configJson, configPathRequiredTokens).ForEach(func(_, token gjson.Result) bool {
		requiredTokens = append(requiredTokens, token.String())
		return true
	})

	// 如果没有配置，使用默认值
	if len(requiredTokens) == 0 {
		requiredTokens = getDefaultRequiredTokens()
	}

	return requiredTokens
}

// extractTokens 从HTML中提取tokens
func (a *AmazonResourceInitializer) extractTokens(html string, tokenPatterns map[string]string) map[string]string {
	tokens := make(map[string]string)

	for tokenName, pattern := range tokenPatterns {
		if value := a.extractWithRegex(html, pattern); value != "" {
			tokens[tokenName] = value
		}
	}

	return tokens
}

// extractJuvecTemplate 从HTML中提取完整的juvec模板
func (a *AmazonResourceInitializer) extractJuvecTemplate(html string, config map[string]interface{}) (map[string]interface{}, bool) {
	// 解析配置并获取token相关配置
	tokenPatterns := a.parseTokenPatterns(config)
	tokenMappings := a.parseTokenMappings(config)
	requiredTokens := a.parseRequiredTokens(config)

	// 提取tokens
	tokens := a.extractTokens(html, tokenPatterns)

	// 验证必需的token是否都已提取
	if !a.validateRequiredTokens(tokens, requiredTokens) {
		return nil, false
	}

	// 提取其他必要字段
	contextFields := a.extractContextFields(html)

	// 构建完整的juvec请求模板
	template := a.buildJuvecTemplate(tokens, tokenMappings, contextFields)

	return template, true
}

// validateRequiredTokens 验证必需的token是否都已提取
func (a *AmazonResourceInitializer) validateRequiredTokens(tokens map[string]string, requiredTokens []string) bool {
	missingTokens := make([]string, 0)

	for _, requiredToken := range requiredTokens {
		if _, exists := tokens[requiredToken]; !exists {
			missingTokens = append(missingTokens, requiredToken)
			a.logger.Warn(fmt.Sprintf("未能提取必需的token：%s", requiredToken))
		}
	}

	if len(missingTokens) > 0 {
		a.logger.Error(fmt.Sprintf("关键token提取失败，跳过Redis更新，缺失 %d/%d 个token：%v",
			len(missingTokens), len(requiredTokens), missingTokens))
		return false
	}

	return true
}

// extractContextFields 提取上下文字段
func (a *AmazonResourceInitializer) extractContextFields(html string) map[string]string {
	return map[string]string{
		"obfuscatedMarketplaceId": a.extractWithRegex(html, obfuscatedMarketplaceIdPattern),
		"language":                a.extractWithRegex(html, languagePattern),
		"currency":                a.extractWithRegex(html, currencyPattern),
	}
}

// buildJuvecTemplate 构建juvec模板
func (a *AmazonResourceInitializer) buildJuvecTemplate(tokens map[string]string, tokenMappings map[string]string, contextFields map[string]string) map[string]interface{} {
	// 构建请求上下文
	requestContext := make(map[string]interface{})

	// 添加上下文字段
	for key, value := range contextFields {
		requestContext[key] = value
	}

	// 添加提取到的tokens，使用配置中的映射
	for tokenKey, tokenValue := range tokens {
		if mappedKey, exists := tokenMappings[tokenKey]; exists {
			requestContext[mappedKey] = tokenValue
		}
	}

	return map[string]interface{}{
		"requestContext": requestContext,
		"endpoint":       "ajax-data",
		"ASINList":       []string{},
	}
}

// extractWithRegex 使用正则表达式提取内容
func (a *AmazonResourceInitializer) extractWithRegex(content, pattern string) string {
	regex := regexp.MustCompile(pattern)
	matches := regex.FindStringSubmatch(content)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// storeJuvecTemplate 存储juvec模板到Redis（使用双缓存策略）
func (a *AmazonResourceInitializer) storeJuvecTemplate(ctx context.Context, template map[string]interface{}, config map[string]interface{}) error {
	if a.redisManager == nil {
		a.logger.Debug("Redis管理器不可用，跳过存储")
		return nil
	}

	// 从配置中获取juvec模板键名
	juvecTemplateKey := a.getJuvecTemplateKey(config)

	// 使用版本化存储（双缓存策略）
	if err := a.redisManager.SetResourceVersioned(ctx, juvecTemplateKey, "default", template, a.ttl); err != nil {
		return err
	}

	a.logger.Info(fmt.Sprintf("TOKEN已存储到Redis（双缓存），键：%s，TTL：%s", juvecTemplateKey, a.ttl.String()))

	return nil
}

// getJuvecTemplateKey 获取juvec模板键名
func (a *AmazonResourceInitializer) getJuvecTemplateKey(config map[string]interface{}) string {
	if key, ok := config["juvec_template_key"].(string); ok && key != "" {
		return key
	}
	return defaultJuvecTemplateKey
}

// 实现剩余的接口方法
func (a *AmazonResourceInitializer) Cleanup() error {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.initialized = false
	a.healthy = false
	return nil
}

func (a *AmazonResourceInitializer) IsExpired() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	if !a.initialized {
		return true
	}
	return time.Since(a.lastRefresh) > a.ttl
}

func (a *AmazonResourceInitializer) LastRefreshTime() time.Time {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.lastRefresh
}

func (a *AmazonResourceInitializer) Close() error {
	return a.Cleanup()
}
