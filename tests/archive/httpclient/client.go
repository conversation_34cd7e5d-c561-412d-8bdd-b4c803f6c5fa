package httpclient

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"go-monitor/pkg/logging"

	"github.com/andybalholm/brotli"
)

// Client HTTP客户端
type Client struct {
	baseConfig *Config
	logger     logging.Logger
}

// Config HTTP客户端配置
type Config struct {
	Timeout     time.Duration
	Retries     int
	UserAgent   string
	ProxyURL    string
	MaxIdleConn int
}

// RequestConfig 请求级配置
type RequestConfig struct {
	ProxyURL       string
	Timeout        time.Duration
	UserAgent      string
	TLSConfig      *tls.Config
	DisableCookies bool
}

// Request HTTP请求
type Request struct {
	URL          string
	Method       string
	Headers      map[string]string
	Body         []byte
	Params       map[string]string
	Cookies      map[string]string
	ReverseProxy string
	Proxy        string
	Timeout      time.Duration
	Meta         map[string]interface{}
}

// Response HTTP响应
type Response struct {
	StatusCode   int
	Headers      map[string][]string
	Body         []byte
	URL          string
	ResponseTime time.Duration
	Request      *Request
}

// executeCurlRequest 使用curl执行HTTP请求的简化实现
func executeCurlRequest(ctx context.Context, request *Request, config *Config, logger logging.Logger) (*Response, error) {
	startTime := time.Now()

	// 构建基本curl命令
	args := []string{"curl", "-s", "-S", "-L", "-i", "--compressed"} // -i 包含响应头, --compressed 支持压缩

	// 设置请求方法
	if request.Method != "GET" {
		args = append(args, "-X", request.Method)
	}

	// 添加请求头
	for k, v := range request.Headers {
		args = append(args, "-H", fmt.Sprintf("%s: %s", k, v))
	}

	// 添加cookies
	if len(request.Cookies) > 0 {
		var cookieStrings []string
		for name, value := range request.Cookies {
			cookieStrings = append(cookieStrings, fmt.Sprintf("%s=%s", name, value))
		}
		args = append(args, "-H", fmt.Sprintf("Cookie: %s", strings.Join(cookieStrings, "; ")))
	}

	// 设置超时
	timeout := 30
	if config != nil && config.Timeout > 0 {
		timeout = int(config.Timeout.Seconds())
	}
	args = append(args, "--max-time", fmt.Sprintf("%d", timeout))

	// 设置代理 - 支持HTTP和SOCKS代理
	proxyURL := ""
	if request.Proxy != "" {
		proxyURL = request.Proxy
	} else if config != nil && config.ProxyURL != "" {
		proxyURL = config.ProxyURL
	}

	if proxyURL != "" {
		// 检测代理类型并设置相应参数
		if strings.HasPrefix(proxyURL, "socks4://") {
			args = append(args, "--socks4", strings.TrimPrefix(proxyURL, "socks4://"))
		} else if strings.HasPrefix(proxyURL, "socks5://") {
			args = append(args, "--socks5", strings.TrimPrefix(proxyURL, "socks5://"))
		} else if strings.HasPrefix(proxyURL, "socks://") {
			// 默认使用socks5
			args = append(args, "--socks5", strings.TrimPrefix(proxyURL, "socks://"))
		} else {
			// HTTP代理（默认）
			args = append(args, "--proxy", proxyURL)
		}
	}

	// 处理请求体
	if len(request.Body) > 0 {
		args = append(args, "--data-binary", "@-") // 从stdin读取
	}

	// 构建URL - 正确处理参数编码
	reqURL := request.URL
	if len(request.Params) > 0 {
		u, err := url.Parse(reqURL)
		if err != nil {
			return nil, fmt.Errorf("解析URL失败: %w", err)
		}

		// 获取现有查询参数
		q := u.Query()
		for k, v := range request.Params {
			q.Add(k, v)
		}
		u.RawQuery = q.Encode()
		reqURL = u.String()
	}
	args = append(args, reqURL)

	// 记录调试信息
	if logger != nil {
		// 隐藏敏感信息的安全日志
		safeArgs := make([]string, len(args))
		copy(safeArgs, args)
		for i, arg := range safeArgs {
			if strings.Contains(arg, "proxy") || strings.Contains(arg, "socks") {
				if i+1 < len(safeArgs) {
					safeArgs[i+1] = "***PROXY***"
				}
			}
			if strings.HasPrefix(arg, "Cookie:") || strings.HasPrefix(arg, "Authorization:") {
				safeArgs[i] = strings.Split(arg, ":")[0] + ": ***"
			}
		}
		logger.Debug(fmt.Sprintf("执行curl命令: %s", strings.Join(safeArgs, " ")))
	}

	// 执行curl命令
	cmd := exec.CommandContext(ctx, args[0], args[1:]...)
	if len(request.Body) > 0 {
		cmd.Stdin = bytes.NewReader(request.Body)
	}

	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	output, err := cmd.Output()
	if err != nil {
		stderrStr := strings.TrimSpace(stderr.String())
		if logger != nil {
			logger.Error(fmt.Sprintf("curl执行失败: %v, stderr: %s", err, stderrStr))
		}
		return nil, fmt.Errorf("curl执行失败: %w, stderr: %s", err, stderrStr)
	}

	// 解析响应
	return parseCurlOutput(output, request, time.Since(startTime))
}

// parseCurlOutput 解析curl输出 - 完全重写以确保100%准确性
func parseCurlOutput(output []byte, request *Request, responseTime time.Duration) (*Response, error) {
	outputStr := string(output)

	// 第一步：找到所有的HTTP响应块（处理重定向）
	// curl -i 的输出格式：HTTP响应1\r\n\r\nHTTP响应2\r\n\r\n响应体

	// 使用更精确的方法：从后往前查找最后一个完整的HTTP响应
	var finalStatusLine string
	var finalHeaders map[string][]string
	var bodyStartIndex int
	var statusCode int = 200

	lines := strings.Split(outputStr, "\n")

	// 从后往前扫描，找到最后一个HTTP状态行
	lastHttpIndex := -1
	for i := len(lines) - 1; i >= 0; i-- {
		line := strings.TrimSpace(lines[i])
		if strings.HasPrefix(line, "HTTP/") {
			lastHttpIndex = i
			break
		}
	}

	if lastHttpIndex == -1 {
		return nil, fmt.Errorf("未找到HTTP状态行")
	}

	// 解析最后一个HTTP响应的状态行
	finalStatusLine = strings.TrimSpace(lines[lastHttpIndex])
	if strings.Contains(finalStatusLine, "HTTP/") {
		parts := strings.Fields(finalStatusLine)
		if len(parts) >= 2 {
			if code, err := strconv.Atoi(parts[1]); err == nil {
				statusCode = code
			}
		}
	}

	// 解析最后一个HTTP响应的响应头
	finalHeaders = make(map[string][]string)
	headerEndIndex := -1

	// 从HTTP状态行开始，向下查找响应头，直到遇到空行
	for i := lastHttpIndex + 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])

		// 遇到空行，说明响应头结束
		if line == "" {
			headerEndIndex = i
			break
		}

		// 解析响应头
		colonIndex := strings.Index(line, ":")
		if colonIndex != -1 {
			key := strings.TrimSpace(line[:colonIndex])
			value := strings.TrimSpace(line[colonIndex+1:])
			finalHeaders[key] = append(finalHeaders[key], value)
		}
	}

	// 确定响应体的开始位置
	if headerEndIndex != -1 && headerEndIndex+1 < len(lines) {
		bodyStartIndex = headerEndIndex + 1
	} else {
		// 如果没有找到空行，说明没有响应体
		bodyStartIndex = len(lines)
	}

	// 提取响应体
	var bodyPart string
	if bodyStartIndex < len(lines) {
		bodyLines := lines[bodyStartIndex:]
		bodyPart = strings.Join(bodyLines, "\n")
	}

	// 处理响应体解压缩
	body := []byte(bodyPart)
	if contentEncoding := finalHeaders["Content-Encoding"]; len(contentEncoding) > 0 {
		encoding := strings.ToLower(contentEncoding[0])
		if decompressed, err := decompressResponse(body, encoding); err == nil {
			body = decompressed
		}
	}

	return &Response{
		StatusCode:   statusCode,
		Headers:      finalHeaders,
		Body:         body,
		URL:          request.URL,
		ResponseTime: responseTime,
		Request:      request,
	}, nil
}

// decompressResponse 解压缩响应体
func decompressResponse(body []byte, encoding string) ([]byte, error) {
	switch encoding {
	case "gzip":
		reader, err := gzip.NewReader(bytes.NewReader(body))
		if err != nil {
			return nil, err
		}
		defer reader.Close()
		return io.ReadAll(reader)
	case "deflate":
		reader := flate.NewReader(bytes.NewReader(body))
		defer reader.Close()
		return io.ReadAll(reader)
	case "br":
		reader := brotli.NewReader(bytes.NewReader(body))
		return io.ReadAll(reader)
	default:
		return body, nil
	}
}

// NewClient 创建HTTP客户端
func NewClient(config *Config) *Client {
	if config == nil {
		config = &Config{
			Timeout:     30 * time.Second,
			Retries:     3,
			UserAgent:   "Go-Monitor/1.0",
			MaxIdleConn: 100,
		}
	}

	return &Client{
		baseConfig: config,
		logger:     logging.GetLogger("service.HTTPClient"),
	}
}

// Get 发起GET请求
func (c *Client) Get(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	req := &Request{
		URL:     url,
		Method:  "GET",
		Headers: headers,
	}
	return c.Do(ctx, req)
}

// Post 发起POST请求
func (c *Client) Post(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error) {
	req := &Request{
		URL:     url,
		Method:  "POST",
		Headers: headers,
		Body:    body,
	}
	return c.Do(ctx, req)
}

// Do 执行HTTP请求
func (c *Client) Do(ctx context.Context, request *Request) (*Response, error) {
	var lastErr error

	for attempt := 0; attempt <= c.baseConfig.Retries; attempt++ {
		if attempt > 0 {
			c.logger.Debug(fmt.Sprintf("重试HTTP请求 URL：%s，尝试：%d", request.URL, attempt))

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Duration(attempt) * time.Second):
			}
		}

		response, err := executeCurlRequest(ctx, request, c.baseConfig, c.logger)
		if err == nil {
			return response, nil
		}

		lastErr = err
		c.logger.Warn(fmt.Sprintf("HTTP请求失败 URL：%s，尝试：%d，错误：%s",
			request.URL, attempt+1, err.Error()))
	}

	return nil, fmt.Errorf("HTTP请求失败，已重试%d次: %w", c.baseConfig.Retries, lastErr)
}

// CreateClientForRequest 创建兼容性HTTP客户端（用于兼容现有代码）
func CreateClientForRequest(baseConfig *Config, requestConfig *RequestConfig) (*http.Client, error) {
	if baseConfig == nil {
		baseConfig = &Config{
			Timeout:     30 * time.Second,
			UserAgent:   "Go-Monitor/1.0",
			MaxIdleConn: 100,
		}
	}

	timeout := baseConfig.Timeout
	if requestConfig != nil && requestConfig.Timeout > 0 {
		timeout = requestConfig.Timeout
	}

	return &http.Client{
		Timeout: timeout,
	}, nil
}

// DoWithClient 兼容性方法（用于兼容现有代码）
func DoWithClient(ctx context.Context, httpClient *http.Client, request *Request, logger logging.Logger) (*Response, error) {
	var config *Config
	if httpClient != nil && httpClient.Timeout > 0 {
		config = &Config{
			Timeout: httpClient.Timeout,
		}
	}

	return executeCurlRequest(ctx, request, config, logger)
}

// SpiderConfig 爬虫配置接口
type SpiderConfig interface {
	GetPlatform() string
	GetName() string
	GetCountry() string
	GetSpiderSettings() map[string]interface{}
}

// SpiderRequestBuilder 爬虫请求构建器
type SpiderRequestBuilder struct {
	platform string
	baseURL  string
	method   string
	headers  map[string]string
	params   map[string]string
	metadata map[string]interface{}
	body     []byte
}

// NewSpiderRequestBuilder 创建爬虫请求构建器
func NewSpiderRequestBuilder(platform, baseURL string) *SpiderRequestBuilder {
	return &SpiderRequestBuilder{
		platform: platform,
		baseURL:  baseURL,
		method:   "GET",
		headers:  make(map[string]string),
		params:   make(map[string]string),
		metadata: make(map[string]interface{}),
	}
}

// SetMethod 设置HTTP方法
func (b *SpiderRequestBuilder) SetMethod(method string) *SpiderRequestBuilder {
	b.method = method
	return b
}

// AddHeader 添加请求头
func (b *SpiderRequestBuilder) AddHeader(key, value string) *SpiderRequestBuilder {
	b.headers[key] = value
	return b
}

// AddHeaders 批量添加请求头
func (b *SpiderRequestBuilder) AddHeaders(headers map[string]string) *SpiderRequestBuilder {
	for k, v := range headers {
		b.headers[k] = v
	}
	return b
}

// AddParam 添加查询参数
func (b *SpiderRequestBuilder) AddParam(key, value string) *SpiderRequestBuilder {
	b.params[key] = value
	return b
}

// AddParams 批量添加查询参数
func (b *SpiderRequestBuilder) AddParams(params map[string]string) *SpiderRequestBuilder {
	for k, v := range params {
		b.params[k] = v
	}
	return b
}

// SetBody 设置请求体
func (b *SpiderRequestBuilder) SetBody(body []byte) *SpiderRequestBuilder {
	b.body = body
	return b
}

// SetJSONBody 设置JSON请求体
func (b *SpiderRequestBuilder) SetJSONBody(data interface{}) *SpiderRequestBuilder {
	if data == nil {
		return b
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		b.metadata["json_marshal_error"] = err.Error()
		return b
	}

	b.body = jsonData
	b.headers["Content-Type"] = "application/json; charset=utf-8"
	return b
}

// SetMetadata 设置元数据
func (b *SpiderRequestBuilder) SetMetadata(key string, value interface{}) *SpiderRequestBuilder {
	b.metadata[key] = value
	return b
}

// SetSpiderMetadata 设置爬虫标准元数据
func (b *SpiderRequestBuilder) SetSpiderMetadata(config SpiderConfig, productID string) *SpiderRequestBuilder {
	if config != nil {
		b.metadata["spider_type"] = config.GetPlatform()
		b.metadata["name"] = config.GetName()
		b.metadata["country"] = config.GetCountry()

		if settings := config.GetSpiderSettings(); settings != nil {
			if proxyGroup, ok := settings["proxies"].([]interface{}); ok {
				b.metadata["proxies"] = proxyGroup
			}
			if cookieGroup, ok := settings["cookies"].([]interface{}); ok {
				b.metadata["cookies"] = cookieGroup
			}
		}
	}

	if productID != "" {
		b.metadata["product_id"] = productID
	}

	return b
}

// SetCommonHeaders 设置常用的爬虫请求头
func (b *SpiderRequestBuilder) SetCommonHeaders() *SpiderRequestBuilder {
	b.headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
	b.headers["Accept-Language"] = "en-US,en;q=0.5"
	b.headers["Accept-Encoding"] = "gzip, deflate"
	b.headers["Connection"] = "keep-alive"
	b.headers["Upgrade-Insecure-Requests"] = "1"
	b.headers["Sec-Fetch-Dest"] = "document"
	b.headers["Sec-Fetch-Mode"] = "navigate"
	b.headers["Sec-Fetch-Site"] = "none"
	b.headers["Sec-Fetch-User"] = "?1"
	return b
}

// SetAPIHeaders 设置API请求头
func (b *SpiderRequestBuilder) SetAPIHeaders() *SpiderRequestBuilder {
	b.headers["Accept"] = "application/json, text/plain, */*"
	b.headers["Content-Type"] = "application/json"
	return b
}

// SetFormHeaders 设置表单请求头
func (b *SpiderRequestBuilder) SetFormHeaders() *SpiderRequestBuilder {
	b.headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8"
	return b
}

// Build 构建请求对象
func (b *SpiderRequestBuilder) Build() *Request {
	return &Request{
		URL:     b.baseURL,
		Method:  b.method,
		Headers: b.headers,
		Params:  b.params,
		Body:    b.body,
		Meta:    b.metadata,
	}
}

// 便捷的爬虫请求创建函数

// CreateAmazonRequest 创建Amazon爬虫请求
func CreateAmazonRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("amazon", baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}

// CreateAliExpressRequest 创建AliExpress爬虫请求
func CreateAliExpressRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("aliexpress", baseURL).
		SetMethod("POST").
		SetFormHeaders().
		SetSpiderMetadata(config, productID)
}

// CreatePopMartRequest 创建PopMart爬虫请求
func CreatePopMartRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("popmart", baseURL).
		SetAPIHeaders().
		SetSpiderMetadata(config, productID)
}

// CreateGenericSpiderRequest 创建通用爬虫请求
func CreateGenericSpiderRequest(platform, baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder(platform, baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}

// BuildURL 构建URL
func BuildURL(baseURL, path string, params map[string]string) string {
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL
	}

	u.Path = strings.TrimSuffix(u.Path, "/") + "/" + strings.TrimPrefix(path, "/")

	if len(params) > 0 {
		q := u.Query()
		for k, v := range params {
			q.Add(k, v)
		}
		u.RawQuery = q.Encode()
	}

	return u.String()
}

// ExtractCookiesFromResponse 从HTTP响应中提取cookies
func ExtractCookiesFromResponse(resp *Response) map[string]string {
	cookies := make(map[string]string)

	if setCookieHeaders, exists := resp.Headers["Set-Cookie"]; exists {
		for _, setCookie := range setCookieHeaders {
			name, value := parseCookieHeader(setCookie)
			if name != "" {
				cookies[name] = value
			}
		}
	}

	return cookies
}

// parseCookieHeader 解析单个Set-Cookie头
func parseCookieHeader(setCookie string) (string, string) {
	parts := strings.Split(setCookie, ";")
	if len(parts) == 0 {
		return "", ""
	}

	nameValue := strings.TrimSpace(parts[0])
	equalIndex := strings.Index(nameValue, "=")
	if equalIndex == -1 {
		return "", ""
	}

	name := strings.TrimSpace(nameValue[:equalIndex])
	value := strings.TrimSpace(nameValue[equalIndex+1:])

	// 移除cookie值首尾的双引号（如果存在）
	if len(value) >= 2 && value[0] == '"' && value[len(value)-1] == '"' {
		value = value[1 : len(value)-1]
	}

	return name, value
}
