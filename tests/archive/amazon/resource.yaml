# 资源初始化配置
resource:
  # 全局设置
  global:
    enabled: true
    refresh_interval: 300s  # 默认刷新间隔
    max_retry_attempts: 3
    retry_delay: 5s

  # 资源初始化器配置
  initializers:
    # Amazon资源初始化器
    amazon_resources:
      enabled: true
      priority: 15
      dependencies: []  # 不依赖其他资源
      ttl: 7200s  # 2小时
      config:
        juvec_template_key: "amazon_juvec_template"
        request_config:
          default_headers:
            Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
            Accept-Language: "en-SG,en-GB;q=0.9,en;q=0.8"
            Accept-Encoding: "gzip, deflate, br"
          fallback_url: "https://www.amazon.it/stores/slot/share?ref_=ast_bln&store_ref=bl_ast_dp_brandLogo_sto&pageId=18FE2457-93E9-494B-ADB1-0FBFA66ED721"
          proxies: ["isp"] # 使用代理组
          # cookies: ["amazon-it"] # 使用cookies组
        parsing_config:
          token_patterns:
            slate_token: '"slateToken":"([^"]+)"'
            fresh_cart_csrf_token: '"freshCartCsrfToken":"([^"]+)"'
            amazon_api_csrf_token: '"amazonApiCsrfToken":"([^"]+)"'
          token_mappings:
            slate_token: "slateToken"
            fresh_cart_csrf_token: "freshCartCsrfToken"
            amazon_api_csrf_token: "amazonApiCsrfToken"
          required_tokens:
            - "slate_token"
            - "amazon_api_csrf_token"

    # Amazon zipCode资源初始化器
    amazon_zipcode_cookies:
      enabled: true
      priority: 16
      dependencies: []  # 不依赖其他资源
      ttl: 1800s  # 30分钟
      config:
        regions:
          it:
            zipCode: "50065"
            url: "https://www.amazon.it"
            cookies: ["amazon-it"]
          de:
            zipCode: "70563"
            url: "https://www.amazon.de"
            cookies: ["amazon-de"]
          es:
            zipCode: "08025"
            url: "https://www.amazon.es"
            cookies: ["amazon-es"]
          fr:
            zipCode: "75011"
            url: "https://www.amazon.fr"
            cookies: ["amazon-fr"]
          gb:
            zipCode: "NW1 5LR"
            url: "https://www.amazon.co.uk"
            cookies: ["amazon-gb"]
        # 请求配置
        request_config:
          timeout: "30s"
          max_retries: 3
          proxies: ["isp"]

    # 远程配置初始化器
    remote_config:
      enabled: false
      priority: 10
      dependencies: []  # 不再依赖 Redis 初始化器
      ttl: 1800s  # 30分钟
      config:
        endpoints:
          - "https://config.example.com/api/v1/config"
          - "https://backup-config.example.com/api/v1/config"
        auth_token: "${CONFIG_AUTH_TOKEN}"
        cache_key: "remote_config"
        timeout: 30s
        
    # 远程 cookies 初始化器
    remote_cookies:
      enabled: false
      priority: 20
      dependencies: []  # 不再依赖 Redis 初始化器
      ttl: 3600s  # 1小时
      config:
        endpoints:
          amazon: "https://cookies.example.com/api/v1/cookies/amazon"
          aliexpress: "https://cookies.example.com/api/v1/cookies/aliexpress"
          popmart: "https://cookies.example.com/api/v1/cookies/popmart"
        auth_token: "${COOKIES_AUTH_TOKEN}"
        cache_prefix: "cookies:"
        timeout: 30s
        
    # 签名信息初始化器
    signature_info:
      enabled: false
      priority: 30
      dependencies: ["remote_config"]  # 只依赖远程配置
      ttl: 7200s  # 2小时
      config:
        endpoints:
          popmart: "https://api.example.com/signature/popmart"
          amazon: "https://api.example.com/signature/amazon"
        auth_token: "${SIGNATURE_AUTH_TOKEN}"
        cache_prefix: "signature:"
        timeout: 30s

# 环境变量说明
# CONFIG_AUTH_TOKEN: 远程配置服务的认证令牌
# COOKIES_AUTH_TOKEN: Cookies 服务的认证令牌  
# SIGNATURE_AUTH_TOKEN: 签名服务的认证令牌

# 配置说明：
# 1. priority: 数字越小优先级越高，Redis 为 1（最高优先级）
# 2. dependencies: 依赖的其他资源名称列表
# 3. ttl: 资源生存时间，0 表示永不过期
# 4. endpoints: 远程端点 URL，支持环境变量展开
# 5. cache_prefix: Redis 缓存键前缀
# 6. timeout: HTTP 请求超时时间

# 依赖关系图：
# Redis 由服务管理器直接管理，不在资源初始化器中
# remote_config (priority: 10, no dependencies)
# remote_cookies (priority: 20, no dependencies)
# signature_info (priority: 30, depends on: remote_config)

# 初始化顺序：
# 1. remote_config, remote_cookies (并行)
# 2. signature_info

# 刷新策略：
# - Redis: 由服务管理器直接管理，不需要刷新
# - remote_config: 每 30 分钟刷新一次
# - remote_cookies: 每 1 小时刷新一次
# - signature_info: 每 2 小时刷新一次

# 错误处理：
# - Redis 由服务管理器管理，如果初始化失败，整个系统无法启动
# - 如果远程资源初始化失败，会记录错误但不阻止系统启动
# - 刷新失败时会自动重试，重试次数由 max_retry_attempts 控制

# 缓存策略：
# - 所有远程资源都会缓存到 Redis 中（通过依赖注入的 Redis 管理器）
# - 缓存键格式：{key_prefix}resource:{resource_type}:{platform}
# - 例如：monitor:resource:cookies:amazon

# 监控和健康检查：
# - 每个资源都提供健康检查接口
# - 支持获取详细的状态信息和统计数据
# - 过期资源会自动触发刷新

# 开发和测试：
# - 可以通过设置 enabled: false 禁用特定的初始化器
# - 支持通过环境变量覆盖配置
# - 提供详细的日志记录用于调试
