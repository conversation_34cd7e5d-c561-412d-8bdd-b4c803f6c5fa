package amazon

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/logging"

	"github.com/tidwall/gjson"
)

// Spider Amazon商品监控爬虫 - 使用juvec API
type Spider struct {
	*spiders.BaseSpider
	logger         logging.Logger
	defaultHeaders map[string]string
}

// NewSpider 创建Amazon爬虫实例
func NewSpider() spiders.Spider {

	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider("amazon", "1.0.0", "Amazon商品监控爬虫"),
		logger:     logging.GetLogger("spider.amazon"),
		defaultHeaders: map[string]string{
			"Accept":           "application/json, text/plain, */*",
			"Accept-Language":  "en-US,en;q=0.9",
			"Accept-Encoding":  "gzip, deflate, br, zstd",
			"Cache-Control":    "no-cache",
			"Pragma":           "no-cache",
			"X-Requested-With": "XMLHttpRequest",
			"Sec-Fetch-Dest":   "empty",
			"Sec-Fetch-Mode":   "cors",
			"Sec-Fetch-Site":   "same-origin",
			"Priority":         "u=1, i",
		},
	}
	return spider
}

// GetProductIDs 获取产品ID集合 - 对应Python版本的get_product_ids
func (a *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// PrepareRequest 准备请求 - 实现Spider接口
func (s *Spider) PrepareRequest(ctx context.Context, productID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	// 获取站点URL
	siteURL := fmt.Sprintf("%s/gp/product/ajax/ref=aod_f_new", spiderConfig.SiteURL)

	params := map[string]string{
		"asin":                        "B0DJY3PJ7T",
		"filters":                     url.QueryEscape(`{"all":true,"new":true}`),
		"m":                           "",
		"qid":                         "",
		"smid":                        "",
		"sourcecustomerorglistid":     "",
		"sourcecustomerorglistitemid": "",
		"sr":                          "",
		"pc":                          "dp",
		"experienceId":                "aodAjaxMain",
	}

	builder := httpclient.CreateGenericSpiderRequest("amazon", siteURL, spiderConfig, productID)

	for key, value := range params {
		builder.AddParam(key, value)
	}

	httpRequest := builder.Build()

	return &models.Request{
		SpiderType:  "amazon",
		URL:         httpRequest.URL,
		Method:      httpRequest.Method,
		Headers:     httpRequest.Headers,
		Body:        httpRequest.Body,
		Params:      httpRequest.Params,
		Metadata:    httpRequest.Meta,
		MonitorName: spiderConfig.Name,
		ProductID:   productID,
	}, nil
}

// buildJuvecPayload 构建juvec API请求payload - 支持单个或批量产品ID
func (a *Spider) buildJuvecPayload(productIDs []string, config *spiders.SpiderConfig) (map[string]any, error) {
	ctx := context.Background()

	// 尝试从Redis获取完整的juvec模板（使用版本化获取）
	if config.RedisManager != nil {
		if redisManager, ok := config.RedisManager.(interface {
			GetResourceVersioned(ctx context.Context, resourceType, key string) ([]byte, error)
			GetResource(ctx context.Context, resourceType, key string) ([]byte, error)
		}); ok {
			// 优先尝试版本化获取
			var templateData map[string]any
			var err error

			if templateData, err = a.getRedisResourceVersioned(ctx, redisManager, "amazon_juvec_template", "default"); err != nil {
				// 回退到普通获取
				templateData, err = a.getRedisResource(ctx, redisManager, "amazon_juvec_template", "default")
			}

			if err == nil {
				// 设置产品ID列表
				templateData["ASINList"] = productIDs

				// 安全地设置requestContext字段
				if len(productIDs) > 1 {
					spiderSettings := config.GetSpiderSettings()
					if requestContext, ok := templateData["requestContext"].(map[string]any); ok {
						requestContext["obfuscatedMarketplaceId"] = spiderSettings["marketplaceID"].(string)
						requestContext["obfuscatedMerchantId"] = spiderSettings["obfuscatedMerchantId"].(string)
						requestContext["currency"] = spiderSettings["currency"].(string)
						requestContext["sessionId"] = spiderSettings["sessionId"].(string)
						requestContext["ubId"] = spiderSettings["ubId"].(string)
						requestContext["amazonApiAjaxEndpoint"] = "data.amazon." + strings.ToLower(config.Country)
					}
				}

				return templateData, nil
			}
		}
	}

	// Redis不可用时的回退方案 - 返回错误而不是无效payload
	a.logger.Warn(fmt.Sprintf("无法获取Amazon juvec模板，Redis资源不可用，产品数量：%d",
		len(productIDs)), "platform", "amazon")

	return nil, fmt.Errorf("Amazon juvec模板不可用，请等待资源刷新完成")
}

func (a *Spider) setHeaders(builder *httpclient.SpiderRequestBuilder, _ *spiders.SpiderConfig) {
	// 使用初始化时设置的默认请求头
	for key, value := range a.defaultHeaders {
		// 跳过Content-Type，因为SetJSONBody会自动设置
		if strings.ToLower(key) != "content-type" {
			builder.AddHeader(key, value)
		}
	}
}

// ParseResponse 解析响应 - 实现Spider接口
func (a *Spider) ParseResponse(ctx context.Context, response *models.Response, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	if response.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP状态码错误: %d", response.StatusCode)
	}

	// 检测响应内容类型
	contentType := ""
	if headers := response.Headers["Content-Type"]; len(headers) > 0 {
		contentType = strings.ToLower(headers[0])
	}

	responseStr := string(response.Body)

	// 根据内容类型选择解析方法
	if strings.Contains(contentType, "application/json") || gjson.Valid(responseStr) {
		// JSON响应 - 使用原有的juvec解析逻辑
		products, err := a.parseJuvecResponse(responseStr, config)
		if err != nil {
			return nil, fmt.Errorf("解析juvec响应失败: %w", err)
		}
		a.logger.Debug(fmt.Sprintf("成功解析Amazon JSON响应，产品数量：%d", len(products)),
			"platform", "amazon")
		return products, nil
	} else if strings.Contains(contentType, "text/html") || strings.Contains(responseStr, "<html") {
		// HTML响应 - 使用新的HTML解析逻辑
		products, err := a.parseHTMLResponse(responseStr, config)
		if err != nil {
			return nil, fmt.Errorf("解析HTML响应失败: %w", err)
		}
		a.logger.Debug(fmt.Sprintf("成功解析Amazon HTML响应，产品数量：%d", len(products)),
			"platform", "amazon")
		return products, nil
	}

	return nil, fmt.Errorf("不支持的响应格式，Content-Type: %s", contentType)
}

// parseJuvecResponse 使用GJSON解析juvec API响应
func (a *Spider) parseJuvecResponse(jsonStr string, spiderConfig *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var products []*models.ProductItem

	// 使用GJSON简洁语法检查products数组
	if gjson.Get(jsonStr, "products").Exists() {
		gjson.Get(jsonStr, "products").ForEach(func(key, value gjson.Result) bool {
			if product := a.extractProductFromJuvecResponse(value.Raw, spiderConfig); product != nil {
				products = append(products, product)
			}
			return true // 继续遍历
		})
		return products, nil
	}

	// 回退：从ASINList创建基本产品信息
	if gjson.Get(jsonStr, "ASINList").Exists() {
		gjson.Get(jsonStr, "ASINList").ForEach(func(key, value gjson.Result) bool {
			asin := value.String()
			if asin != "" {
				product := &models.ProductItem{
					Name:      spiderConfig.Name,
					ProductID: asin,
					Title:     fmt.Sprintf("Product %s", asin),
					URL:       fmt.Sprintf("%s/dp/%s", spiderConfig.SiteURL, asin),
					Platform:  "amazon",
					Currency:  config.GetConfigString(spiderConfig, "currency", "EUR"),
					Country:   spiderConfig.Country,
					SiteURL:   spiderConfig.SiteURL,
					CrawledAt: time.Now(),
					Metadata: map[string]any{
						"spider_type": "amazon",
						"api_source":  "juvec",
						"asin_only":   true,
					},
				}
				products = append(products, product)
			}
			return true
		})
	}

	return products, nil
}

// getRedisResourceGeneric 通用Redis资源获取方法 - 使用GJSON优化，消除重复代码
func (a *Spider) getRedisResourceGeneric(ctx context.Context, getData func() ([]byte, error), resourceDesc string) (map[string]any, error) {
	data, err := getData()
	if err != nil {
		return nil, err
	}

	// 使用GJSON验证和解析
	jsonStr := string(data)
	if !gjson.Valid(jsonStr) {
		return nil, fmt.Errorf("invalid JSON data from %s", resourceDesc)
	}

	// 仍然需要返回map[string]any以保持接口兼容性
	var result map[string]any
	if err := json.Unmarshal(data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal %s data: %w", resourceDesc, err)
	}

	return result, nil
}

// getRedisResource 从Redis获取资源数据 - 重构后使用通用方法
func (a *Spider) getRedisResource(ctx context.Context, redisManager interface {
	GetResource(ctx context.Context, resourceType, key string) ([]byte, error)
}, resourceType, key string) (map[string]any, error) {
	return a.getRedisResourceGeneric(ctx, func() ([]byte, error) {
		return redisManager.GetResource(ctx, resourceType, key)
	}, "redis")
}

// getRedisResourceVersioned 从Redis获取版本化资源数据 - 重构后使用通用方法
func (a *Spider) getRedisResourceVersioned(ctx context.Context, redisManager interface {
	GetResourceVersioned(ctx context.Context, resourceType, key string) ([]byte, error)
}, resourceType, key string) (map[string]any, error) {
	return a.getRedisResourceGeneric(ctx, func() ([]byte, error) {
		return redisManager.GetResourceVersioned(ctx, resourceType, key)
	}, "versioned redis")
}

// extractProductFromJuvecResponse 使用GJSON从juvec响应中提取单个产品信息
func (a *Spider) extractProductFromJuvecResponse(productJson string, spiderConfig *spiders.SpiderConfig) *models.ProductItem {
	// 提取ASIN - 使用GJSON简洁语法
	asin := gjson.Get(productJson, "asin").String()
	if asin == "" {
		return nil
	}

	// 提取标题 - 使用GJSON路径查询
	title := gjson.Get(productJson, "title.displayString").String()
	if title == "" {
		title = "Amazon Product " + asin
	}

	// 使用GJSON简洁语法提取价格和库存信息
	priceValue := 0.0
	currency := config.GetConfigString(spiderConfig, "currency", "EUR")
	inStock := false
	availability := "Out of Stock"
	offerListingId := ""
	var atcLink string

	// 查找匹配的buying option
	targetMerchantID := spiderConfig.SpiderSettings["merchantID"].(string)
	var matchingOptionJson string

	gjson.Get(productJson, "buyingOptions").ForEach(func(key, value gjson.Result) bool {
		if gjson.Get(value.Raw, "merchant.encryptedMerchantId").String() == targetMerchantID {
			matchingOptionJson = value.Raw
			return false // 找到匹配项，停止遍历
		}
		return true
	})

	if matchingOptionJson != "" {
		// 提取库存信息 - GJSON简洁语法
		availabilityType := gjson.Get(matchingOptionJson, "availability.type").String()
		if availabilityType != "" {
			inStock = availabilityType != "OUT_OF_STOCK"
			availability = strings.ReplaceAll(availabilityType, "_", " ")
		}

		// 提取价格信息 - GJSON路径查询，一行搞定！
		priceValue = gjson.Get(matchingOptionJson, "price.priceToPay.moneyValueOrRange.value.amount").Float()
		if currencyCode := gjson.Get(matchingOptionJson, "price.priceToPay.moneyValueOrRange.value.currencyCode").String(); currencyCode != "" {
			currency = currencyCode
		}

		// 提取offerListingId - GJSON路径查询
		if inStock {
			gjson.Get(matchingOptionJson, "callToAction.checkout.data.parameters").ForEach(func(key, value gjson.Result) bool {
				if gjson.Get(value.Raw, "name").String() == "offerListingId" {
					offerListingId = gjson.Get(value.Raw, "value").String()
					return false
				}
				return true
			})
		}
	}

	stock := 0
	if inStock {
		stock = 1
	}

	// 构建产品URL
	siteURL := spiderConfig.SiteURL
	if siteURL == "" {
		siteURL = "https://www.amazon.com"
	}

	productURL := fmt.Sprintf("%s/dp/%s", siteURL, asin)
	// 使用GJSON提取detailPageLinkURL
	if detailPageLinkURL := gjson.Get(productJson, "detailPageLinkURL").String(); detailPageLinkURL != "" {
		// 检测URL是否包含当前ASIN，确保URL正确性
		if strings.Contains(detailPageLinkURL, asin) {
			if strings.HasPrefix(detailPageLinkURL, "/") {
				productURL = siteURL + detailPageLinkURL
			} else {
				productURL = detailPageLinkURL
			}
		} else {
			// 如果URL不包含当前ASIN，记录警告并使用默认URL
			a.logger.Debug("detailPageLinkURL不包含当前ASIN，使用默认URL",
				"asin", asin,
				"detailPageLinkURL", detailPageLinkURL,
				"platform", "amazon",
			)
			// 保持使用默认的productURL
		}
	}

	// 构建 ATC (Add to Cart) 链接 - 使用 offerID 和正确的 siteURL
	if offerListingId != "" && inStock {
		// 构建Amazon购买链接，类似Python版本的逻辑

		baseURL := fmt.Sprintf("%s/gp/product/handle-buy-box/ref=dp_start-bbf_1_glance?offerListingID=%s&asin=%s&submit.buy-now=1",
			siteURL, offerListingId, asin)

		// 验证构建的URL包含当前ASIN
		if strings.Contains(baseURL, asin) {
			atcLink = fmt.Sprintf("[x1](%s&quantity.1=1) | [x2](%s&quantity.1=2)", baseURL, baseURL)
		} else {
			a.logger.Debug("构建的AtcLink不包含当前ASIN",
				"asin", asin,
				"offerListingId", offerListingId,
				"baseURL", baseURL,
				"platform", "amazon",
			)
		}
	}

	// 使用GJSON提取图片URL - 简洁的路径查询
	var imageURL *string
	// 首先尝试高分辨率图片
	if hiResURL := gjson.Get(productJson, "productImages.images.0.hiRes.url").String(); hiResURL != "" {
		imageURL = &hiResURL
	} else if lowResURL := gjson.Get(productJson, "productImages.images.0.lowRes.url").String(); lowResURL != "" {
		// 回退到低分辨率图片
		imageURL = &lowResURL
	}

	return &models.ProductItem{
		Name:      spiderConfig.Name,
		ProductID: asin,
		Title:     title,
		URL:       productURL,
		Platform:  "amazon",
		Price:     priceValue,
		Currency:  currency,
		Stock:     stock,
		InStock:   inStock,
		OfferID: func() *string {
			if offerListingId != "" {
				return &offerListingId
			} else {
				return nil
			}
		}(),
		Availability:  availability,
		SiteURL:       siteURL,
		Notifications: spiderConfig.Notifications,
		ImageURL:      imageURL,
		AtcLink: func() *string {
			if atcLink != "" {
				return &atcLink
			} else {
				return nil
			}
		}(),
		CrawledAt: time.Now(),
		Metadata: map[string]any{
			"spider_type": "amazon",
			"api_source":  "juvec",
		},
	}
}

// ========== 批量处理能力接口实现 ==========

// SupportsBatchProcessing Amazon爬虫支持批量处理
func (a *Spider) SupportsBatchProcessing() bool {
	return true
}

// GetMaxBatchSize Amazon爬虫的最大批量大小
func (a *Spider) GetMaxBatchSize() int {
	return 50 // 根据juvec API的限制设置
}

// PrepareBatchRequest 准备批量请求 - Amazon juvec API支持多个ASIN
func (a *Spider) PrepareBatchRequest(ctx context.Context, productIDs []string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {

	// 获取站点URL
	siteURL := spiderConfig.SiteURL
	if siteURL == "" {
		siteURL = "https://www.amazon.com"
	}

	// 构建批量juvec API请求payload
	payload, err := a.buildJuvecPayload(productIDs, spiderConfig)
	if err != nil {
		return nil, fmt.Errorf("构建批量juvec payload失败: %w", err)
	}

	// 使用HTTP客户端构建器创建请求
	builder := httpclient.CreateAmazonRequest(fmt.Sprintf("%s/juvec", siteURL), spiderConfig, strings.Join(productIDs, ","))
	builder.SetMethod("POST")
	a.setHeaders(builder, spiderConfig)
	builder.SetJSONBody(payload)

	httpRequest := builder.Build()

	return &models.Request{
		SpiderType:  "amazon",
		URL:         httpRequest.URL,
		Method:      httpRequest.Method,
		Headers:     httpRequest.Headers,
		Body:        httpRequest.Body,
		Params:      httpRequest.Params,
		Metadata:    httpRequest.Meta,
		MonitorName: spiderConfig.Name,
		ProductID:   strings.Join(productIDs, ","),
	}, nil
}

// parseHTMLResponse 解析HTML格式的Amazon All Offers Display响应
func (a *Spider) parseHTMLResponse(htmlStr string, spiderConfig *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var products []*models.ProductItem

	// 提取ASIN
	asin := a.extractASINFromHTML(htmlStr)
	if asin == "" {
		return nil, fmt.Errorf("无法从HTML中提取ASIN")
	}

	// 提取产品标题
	title := a.extractTitleFromHTML(htmlStr)
	if title == "" {
		title = "Amazon Product " + asin
	}

	// 提取价格信息
	priceValue, currency := a.extractPriceFromHTML(htmlStr, spiderConfig)

	// 提取offerListingId
	offerListingId := a.extractOfferListingIdFromHTML(htmlStr)

	// 检查库存状态 - 基于Add to Basket按钮是否存在且可用
	inStock := a.extractStockStatusFromHTML(htmlStr)
	stock := 0
	if inStock {
		stock = 1
	}

	availability := "Out of Stock"
	if inStock {
		availability = "In Stock"
	}

	// 构建产品URL
	siteURL := spiderConfig.SiteURL
	if siteURL == "" {
		siteURL = "https://www.amazon.com"
	}
	productURL := fmt.Sprintf("%s/dp/%s", siteURL, asin)

	// 构建 ATC (Add to Cart) 链接
	var atcLink *string
	if offerListingId != "" && inStock {
		baseURL := fmt.Sprintf("%s/gp/product/handle-buy-box/ref=dp_start-bbf_1_glance?offerListingID=%s&asin=%s&submit.buy-now=1",
			siteURL, offerListingId, asin)

		if strings.Contains(baseURL, asin) {
			link := fmt.Sprintf("[x1](%s&quantity.1=1) | [x2](%s&quantity.1=2)", baseURL, baseURL)
			atcLink = &link
		}
	}

	// 提取图片URL
	var imageURL *string
	if imgURL := a.extractImageURLFromHTML(htmlStr); imgURL != "" {
		imageURL = &imgURL
	}

	// 创建产品项 - 按照extractProductFromJuvecResponse的完整格式
	product := &models.ProductItem{
		Name:      spiderConfig.Name,
		ProductID: asin,
		Title:     title,
		URL:       productURL,
		Platform:  "amazon",
		Price:     priceValue,
		Currency:  currency,
		Stock:     stock,
		InStock:   inStock,
		OfferID: func() *string {
			if offerListingId != "" {
				return &offerListingId
			}
			return nil
		}(),
		Availability:  availability,
		SiteURL:       siteURL,
		Notifications: spiderConfig.Notifications,
		ImageURL:      imageURL,
		AtcLink:       atcLink,
		CrawledAt:     time.Now(),
		Metadata: map[string]any{
			"spider_type": "amazon",
			"api_source":  "html_aod", // All Offers Display
		},
	}

	products = append(products, product)
	return products, nil
}

// extractASINFromHTML 从HTML中提取ASIN
func (a *Spider) extractASINFromHTML(htmlStr string) string {
	// 从隐藏的input字段中提取ASIN
	re := regexp.MustCompile(`name="items\[0\.base\]\[asin\]"\s+value="([^"]+)"`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// extractTitleFromHTML 从HTML中提取产品标题
func (a *Spider) extractTitleFromHTML(htmlStr string) string {
	// 从aod-asin-title-text元素中提取标题
	re := regexp.MustCompile(`id="aod-asin-title-text"[^>]*>\s*([^<]+)\s*</h5>`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}
	return ""
}

// extractPriceFromHTML 从HTML中提取价格和货币
func (a *Spider) extractPriceFromHTML(htmlStr string, spiderConfig *spiders.SpiderConfig) (float64, string) {
	// 提取价格符号和数值
	re := regexp.MustCompile(`<span class="a-price-symbol">([^<]+)</span><span class="a-price-whole">([^<]+)<span class="a-price-decimal">\.</span></span><span class="a-price-fraction">([^<]+)</span>`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 3 {
		symbol := matches[1]
		whole := matches[2]
		fraction := matches[3]

		// 组合价格字符串并转换为浮点数
		priceStr := whole + "." + fraction
		if price, err := strconv.ParseFloat(priceStr, 64); err == nil {
			// 根据符号确定货币
			currency := a.getCurrencyFromSymbol(symbol)
			if currency == "" {
				currency = config.GetConfigString(spiderConfig, "currency", "EUR")
			}
			return price, currency
		}
	}

	// 备用方法：从aok-offscreen元素提取
	re2 := regexp.MustCompile(`<span class="aok-offscreen">\s*([€$£¥]?)([0-9,]+\.?[0-9]*)\s*</span>`)
	matches2 := re2.FindStringSubmatch(htmlStr)
	if len(matches2) > 2 {
		symbol := matches2[1]
		priceStr := strings.ReplaceAll(matches2[2], ",", "")
		if price, err := strconv.ParseFloat(priceStr, 64); err == nil {
			currency := a.getCurrencyFromSymbol(symbol)
			if currency == "" {
				currency = config.GetConfigString(spiderConfig, "currency", "EUR")
			}
			return price, currency
		}
	}

	return 0, config.GetConfigString(spiderConfig, "currency", "EUR")
}

// getCurrencyFromSymbol 根据货币符号获取货币代码
func (a *Spider) getCurrencyFromSymbol(symbol string) string {
	switch symbol {
	case "€":
		return "EUR"
	case "$":
		return "USD"
	case "£":
		return "GBP"
	case "¥":
		return "JPY"
	default:
		return ""
	}
}

// extractOfferListingIdFromHTML 从HTML中提取offerListingId
func (a *Spider) extractOfferListingIdFromHTML(htmlStr string) string {
	// 从隐藏的input字段中提取offerListingId
	re := regexp.MustCompile(`name="items\[0\.base\]\[offerListingId\]"\s+value="([^"]+)"`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		// URL解码offerListingId
		if decoded, err := url.QueryUnescape(matches[1]); err == nil {
			return decoded
		}
		return matches[1]
	}
	return ""
}

// extractStockStatusFromHTML 从HTML中提取库存状态
func (a *Spider) extractStockStatusFromHTML(htmlStr string) bool {
	// 检查Add to Basket按钮是否存在且可用
	re := regexp.MustCompile(`<input[^>]*name="submit\.addToCart"[^>]*type="submit"[^>]*/>`)
	if re.MatchString(htmlStr) {
		// 进一步检查按钮是否被禁用
		disabledRe := regexp.MustCompile(`<input[^>]*name="submit\.addToCart"[^>]*disabled[^>]*/>`)
		return !disabledRe.MatchString(htmlStr)
	}
	return false
}

// extractImageURLFromHTML 从HTML中提取产品图片URL
func (a *Spider) extractImageURLFromHTML(htmlStr string) string {
	// 从img标签中提取图片URL
	re := regexp.MustCompile(`<img[^>]*src="([^"]+)"[^>]*id="aod-asin-image-id"`)
	matches := re.FindStringSubmatch(htmlStr)
	if len(matches) > 1 {
		return matches[1]
	}

	// 备用方法：先匹配src再匹配id
	re2 := regexp.MustCompile(`src="([^"]+)"[^>]*id="aod-asin-image-id"`)
	matches2 := re2.FindStringSubmatch(htmlStr)
	if len(matches2) > 1 {
		return matches2[1]
	}

	return ""
}
