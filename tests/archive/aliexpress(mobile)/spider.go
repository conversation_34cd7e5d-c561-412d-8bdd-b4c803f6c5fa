package aliexpress

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/logging"

	"github.com/tidwall/gjson"
)

// Spider AliExpress爬虫实现
type Spider struct {
	*spiders.BaseSpider
	logger logging.Logger

	// 默认请求头 - 对应Python版本的default_headers
	defaultHeaders map[string]string
}

// NewSpider 创建新的AliExpress爬虫实例
func NewSpider() spiders.Spider {
	// 获取全局日志工厂

	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider("aliexpress", "1.0.0", "AliExpress商品监控爬虫"),
		logger:     logging.GetLogger("spider.aliexpress"),
		defaultHeaders: map[string]string{
			"pragma":          "no-cache",
			"cache-control":   "no-cache",
			"accept":          "*/*",
			"sec-fetch-site":  "same-origin",
			"sec-fetch-mode":  "cors",
			"sec-fetch-dest":  "empty",
			"accept-encoding": "gzip, deflate, br, zstd",
			"accept-language": "en-US,en;q=0.9",
			"priority":        "u=1, i",
		},
	}
	return spider
}

// GetProductIDs 获取产品ID集合 - 对应Python版本的get_product_ids
func (s *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// PrepareRequest 准备请求 - 对应Python版本的prepare_request
func (s *Spider) PrepareRequest(ctx context.Context, productID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	// 使用传入的productID参数
	if productID == "" {
		return nil, fmt.Errorf("缺少产品ID")
	}

	// 构建查询参数
	params := map[string]string{
		"productId": productID,
	}

	// API端点URL
	baseURL := spiderConfig.SiteURL + "/aeglodetailweb/api/msite/item"

	// 获取配置参数 - 从SpiderConfig中获取
	country := spiderConfig.Country

	// 使用HTTP客户端构建器创建请求
	builder := httpclient.CreateAliExpressRequest(baseURL, spiderConfig, productID)

	// 添加查询参数
	for key, value := range params {
		builder.AddParam(key, value)
	}

	// 添加默认请求头
	builder.AddHeaders(s.defaultHeaders)
	builder.AddHeaders(map[string]string{
		"referer": spiderConfig.SiteURL + fmt.Sprintf("/item/%s.html", productID),
	})

	// 构建请求
	httpRequest := builder.Build()

	// 转换为models.Request
	request := &models.Request{
		SpiderType:   "aliexpress",
		URL:          httpRequest.URL,
		Method:       httpRequest.Method,
		Headers:      httpRequest.Headers,
		Body:         httpRequest.Body,
		Params:       httpRequest.Params,
		ReverseProxy: httpRequest.ReverseProxy,
		Metadata:     httpRequest.Meta,
	}

	s.logger.Debug(fmt.Sprintf("准备AliExpress请求，产品ID：%s，国家：%s",
		productID, country), "platform", "aliexpress")

	return request, nil
}

// ParseResponse 解析响应 - 支持新旧两种API格式
func (s *Spider) ParseResponse(ctx context.Context, resp *models.Response, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	// 获取产品ID
	productID := ""
	if resp.Request != nil {
		productID = resp.Request.GetMetadataString("product_id", "")
	}

	if productID == "" {
		return nil, fmt.Errorf("响应中缺少产品ID")
	}

	// 使用GJSON解析响应内容
	jsonStr := string(resp.Body)
	if !gjson.Valid(jsonStr) {
		return nil, fmt.Errorf("无效的JSON响应")
	}

	// 为了兼容性，仍然创建content map
	var content map[string]interface{}
	if err := json.Unmarshal(resp.Body, &content); err != nil {
		return nil, fmt.Errorf("解析JSON响应失败: %w", err)
	}

	// 检测数据格式 - 使用GJSON优化的检测
	if s.isComponentBasedFormat(content) {
		s.logger.Debug(fmt.Sprintf("检测到组件化API格式，产品ID：%s", productID), "platform", "aliexpress")
		return s.parseComponentBasedResponse(productID, content, config)
	}

	// 处理旧格式 - 保持向后兼容
	s.logger.Debug(fmt.Sprintf("使用传统API格式解析，产品ID：%s", productID), "platform", "aliexpress")

	// 检查API返回的嵌套结构 - 恢复Python版本的逻辑
	if data, ok := content["data"].(map[string]interface{}); ok {
		// 检查data中是否包含data字段和success状态
		innerData := data
		if success, ok := innerData["success"].(bool); ok && !success {
			errorMsg := "未知错误"
			if msg, ok := innerData["errorMsg"].(string); ok {
				errorMsg = msg
			}
			s.logger.Error(fmt.Sprintf("API返回错误，产品ID：%s，错误：%s", productID, errorMsg),
				"platform", "aliexpress")
			return nil, fmt.Errorf("API返回错误: %s", errorMsg)
		}

		// 获取真正的产品数据
		if innerProductData, ok := innerData["data"].(map[string]interface{}); ok {
			// 提取商品信息
			productItems, err := s.extractProductInfo(productID, innerProductData, config)
			if err != nil {
				return nil, err
			}
			// 如果有多个SKU，返回所有SKU
			return productItems, nil
		} else {
			s.logger.Error(fmt.Sprintf("响应中缺少产品数据，产品ID：%s", productID),
				"platform", "aliexpress")
			return nil, fmt.Errorf("响应中缺少产品数据")
		}
	} else {
		// 保持原有逻辑，直接使用content作为产品数据
		productItems, err := s.extractProductInfo(productID, content, config)
		if err != nil {
			return nil, err
		}
		// 如果有多个SKU，返回所有SKU
		return productItems, nil
	}
}

// isComponentBasedFormat 检测是否为组件化API格式
func (s *Spider) isComponentBasedFormat(content map[string]interface{}) bool {
	// 检查是否包含组件化格式的关键组件
	componentKeys := []string{"SKU", "PRICE", "PRODUCT_TITLE", "HEADER_IMAGE_PC"}
	foundComponents := 0

	for _, key := range componentKeys {
		if _, exists := content[key]; exists {
			foundComponents++
		}
	}

	// 如果找到至少3个关键组件，认为是组件化格式
	return foundComponents >= 3
}

// extractProductInfo 提取商品信息 - 对应Python版本的_extract_product_info方法
func (s *Spider) extractProductInfo(productID string, content map[string]interface{}, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var result []*models.ProductItem

	// 提取标题
	title := s.extractProductTitle(content)

	// 检查组件化数据结构 - 恢复Python版本的逻辑
	skuComponent, hasSkuComponent := content["skuComponent"].(map[string]interface{})
	var skuData map[string]interface{}
	var skuPaths []map[string]interface{}

	if hasSkuComponent {
		// 使用新的组件化结构
		skuData = skuComponent

		// 从priceComponent中获取skuPriceList
		if priceComponent, ok := content["priceComponent"].(map[string]interface{}); ok {
			if skuPriceList, ok := priceComponent["skuPriceList"].([]interface{}); ok {
				// 转换skuPriceList为map切片
				for _, item := range skuPriceList {
					if skuMap, ok := item.(map[string]interface{}); ok {
						skuPaths = append(skuPaths, skuMap)
					}
				}
			}
		}
	} else {
		// 使用旧结构
		if sku, ok := content["SKU"].(map[string]interface{}); ok {
			skuData = sku
		}

		if skuPathsInterface, ok := skuData["skuPaths"].([]interface{}); ok {
			for _, item := range skuPathsInterface {
				if skuMap, ok := item.(map[string]interface{}); ok {
					skuPaths = append(skuPaths, skuMap)
				}
			}
		}
	}

	if skuData == nil {
		s.logger.Warn(fmt.Sprintf("产品没有SKU数据，产品ID：%s", productID), "platform", "aliexpress")
		return result, nil
	}

	if len(skuPaths) == 0 {
		s.logger.Warn(fmt.Sprintf("产品没有SKU路径，产品ID：%s", productID), "platform", "aliexpress")
		return result, nil
	}

	// 提取价格数据
	priceData := s.getPriceData(content)

	// 提取SKU图片
	skuImages := s.getSKUImages(skuData, skuPaths)

	// 构建SKU项
	for _, path := range skuPaths {
		skuItem := s.buildSKUItem(productID, title, path, skuImages, priceData, config)
		if skuItem != nil {
			result = append(result, skuItem)
		}
	}

	return result, nil
}

// extractProductTitle 提取产品标题
func (s *Spider) extractProductTitle(content map[string]interface{}) string {
	// 尝试多个可能的标题字段
	titleFields := []string{"title", "productTitle", "subject"}

	for _, field := range titleFields {
		if title, ok := content[field].(string); ok && title != "" {
			return title
		}
	}

	// 如果直接字段没有，尝试从嵌套对象中获取
	if actionModule, ok := content["actionModule"].(map[string]interface{}); ok {
		for _, field := range titleFields {
			if title, ok := actionModule[field].(string); ok && title != "" {
				return title
			}
		}
	}

	return ""
}

// getPriceData 提取SKU价格信息 - 恢复Python版本的完整逻辑
func (s *Spider) getPriceData(content map[string]interface{}) map[string]map[string]interface{} {
	priceData := make(map[string]map[string]interface{})

	// 检查新的组件化结构
	if priceComponent, ok := content["priceComponent"].(map[string]interface{}); ok {
		// 从priceComponent中获取skuPriceList
		if skuPriceList, ok := priceComponent["skuPriceList"].([]interface{}); ok {
			for _, priceItem := range skuPriceList {
				if priceMap, ok := priceItem.(map[string]interface{}); ok {
					skuID := ""
					if id, ok := priceMap["skuId"].(string); ok {
						skuID = id
					} else if id, ok := priceMap["skuIdStr"].(string); ok {
						skuID = id
					}

					if skuID == "" {
						continue
					}

					if skuVal, ok := priceMap["skuVal"].(map[string]interface{}); ok {
						if skuAmount, ok := skuVal["skuAmount"].(map[string]interface{}); ok {
							if value, ok := skuAmount["value"].(float64); ok {
								priceData[skuID] = map[string]interface{}{
									"price_value": value,
									"price":       value,
								}
							}
						} else {
							// 尝试从skuCalPrice获取价格
							if priceValue, ok := skuVal["skuCalPrice"].(string); ok {
								// 尝试转换为float64
								if priceFloat, err := strconv.ParseFloat(priceValue, 64); err == nil {
									priceData[skuID] = map[string]interface{}{
										"price_value": priceValue,
										"price":       priceFloat,
									}
								} else {
									priceData[skuID] = map[string]interface{}{
										"price_value": priceValue,
										"price":       0.0,
									}
								}
							}
						}
					}
				}
			}
		}
	} else {
		// 使用旧结构
		if priceInfo, ok := content["PRICE"].(map[string]interface{}); ok {
			if skuPriceMap, ok := priceInfo["skuPriceInfoMap"].(map[string]interface{}); ok {
				for skuID, priceInfo := range skuPriceMap {
					if priceMap, ok := priceInfo.(map[string]interface{}); ok {
						if salePriceString, ok := priceMap["salePriceString"].(string); ok {
							// 清理价格字符串，移除非数字字符
							priceValue := strings.Map(func(r rune) rune {
								if (r >= '0' && r <= '9') || r == '.' || r == ',' {
									return r
								}
								return -1
							}, salePriceString)

							priceData[skuID] = map[string]interface{}{
								"price_value": priceValue,
							}
						}
					}
				}
			}
		}
	}

	return priceData
}

// getSKUImages 获取SKU图片 - 恢复Python版本的完整逻辑
func (s *Spider) getSKUImages(skuData map[string]interface{}, skuPaths []map[string]interface{}) map[string]string {
	skuImages := make(map[string]string)

	// 检查新的组件化结构
	var skuProperties []interface{}
	if productSKUPropertyList, ok := skuData["productSKUPropertyList"].([]interface{}); ok {
		skuProperties = productSKUPropertyList
	} else if skuPropertiesOld, ok := skuData["skuProperties"].([]interface{}); ok {
		// 使用旧结构
		skuProperties = skuPropertiesOld
	}

	if len(skuProperties) == 0 {
		// 如果没有找到任何图片，尝试从imageComponent中获取
		if len(skuPaths) > 0 {
			// 选择第一个SKU的ID
			firstPath := skuPaths[0]
			if firstSkuID, ok := firstPath["skuId"].(string); ok {
				skuImages[firstSkuID] = "default_image_placeholder"
			} else if firstSkuID, ok := firstPath["skuIdStr"].(string); ok {
				skuImages[firstSkuID] = "default_image_placeholder"
			}
		}
		return skuImages
	}

	for _, prop := range skuProperties {
		if propMap, ok := prop.(map[string]interface{}); ok {
			if skuPropertyValues, ok := propMap["skuPropertyValues"].([]interface{}); ok {
				for _, value := range skuPropertyValues {
					if valueMap, ok := value.(map[string]interface{}); ok {
						propertyID := ""
						if id, ok := valueMap["propertyValueIdLong"].(float64); ok {
							propertyID = fmt.Sprintf("%.0f", id)
						} else if id, ok := valueMap["propertyValueIdLong"].(string); ok {
							propertyID = id
						}

						// 在新结构中，图片URL可能在skuPropertyImagePath或者其他字段
						imageURL := ""
						if url, ok := valueMap["skuPropertyImagePath"].(string); ok {
							imageURL = url
						}

						if propertyID == "" || imageURL == "" {
							continue
						}

						// 处理路径
						for _, path := range skuPaths {
							if skuAttr, ok := path["skuAttr"].(string); ok {
								if strings.Contains(skuAttr, propertyID) {
									skuID := ""
									if id, ok := path["skuId"].(string); ok {
										skuID = id
									} else if id, ok := path["skuIdStr"].(string); ok {
										skuID = id
									}
									if skuID != "" {
										skuImages[skuID] = imageURL
									}
								}
							} else if skuPropIds, ok := path["skuPropIds"].(string); ok {
								// 如果没有skuAttr字段，检查可能的替代字段
								if strings.Contains(skuPropIds, propertyID) {
									skuID := ""
									if id, ok := path["skuId"].(string); ok {
										skuID = id
									} else if id, ok := path["skuIdStr"].(string); ok {
										skuID = id
									}
									if skuID != "" {
										skuImages[skuID] = imageURL
									}
								}
							}
						}
					}
				}
			}
		}
	}

	return skuImages
}

// parseComponentBasedResponse 解析组件化API响应格式
func (s *Spider) parseComponentBasedResponse(productID string, content map[string]interface{}, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var result []*models.ProductItem

	// 将content转换为JSON字符串以便使用GJSON
	jsonBytes, _ := json.Marshal(content)
	jsonStr := string(jsonBytes)

	// 提取产品标题
	title := s.extractProductTitleFromComponents(content)
	if title == "" {
		s.logger.Warn(fmt.Sprintf("无法提取产品标题，产品ID：%s", productID), "platform", "aliexpress")
	}

	// 提取SKU数据
	_, skuPaths, err := s.extractSKUDataFromComponents(content)
	if err != nil {
		return nil, fmt.Errorf("提取SKU数据失败: %w", err)
	}

	if len(skuPaths) == 0 {
		s.logger.Warn(fmt.Sprintf("产品没有SKU路径，产品ID：%s", productID), "platform", "aliexpress")
		return result, nil
	}

	// 提取价格数据
	priceData := s.extractPriceDataFromComponents(content)

	// 提取图片数据
	skuImages := s.extractImageDataFromComponents(content)

	// 提取库存数据
	stockData := s.extractStockDataFromComponents(content)

	// 构建SKU项
	for _, path := range skuPaths {
		skuItem := s.buildSKUItemFromComponents(productID, title, path, skuImages, priceData, stockData, config)
		if skuItem != nil {
			result = append(result, skuItem)
		}
	}

	return result, nil
}

// extractProductTitleFromComponents 从组件中提取产品标题 - 使用GJSON简化
func (s *Spider) extractProductTitleFromComponents(content map[string]interface{}) string {
	// 从PRODUCT_TITLE组件提取标题 - 一行GJSON查询替代复杂嵌套
	if title := gjson.Get(jsonStr, "PRODUCT_TITLE.text").String(); title != "" {
		return title
	}

	// 从GLOBAL_DATA组件提取标题作为备选 - 一行GJSON查询替代复杂嵌套
	if subject := gjson.Get(jsonStr, "GLOBAL_DATA.globalData.subject").String(); subject != "" {
		return subject
	}

	return ""
}

// extractSKUDataFromComponents 从组件中提取SKU数据 - 使用GJSON大幅简化
func (s *Spider) extractSKUDataFromComponents(jsonStr string) (map[string]interface{}, []map[string]interface{}, error) {
	var skuPaths []map[string]interface{}

	// 使用GJSON检查SKU组件存在性
	if !gjson.Get(jsonStr, "SKU").Exists() {
		return nil, nil, fmt.Errorf("SKU组件不存在")
	}

	// 使用GJSON提取skuPaths - 替代复杂的类型断言循环
	gjson.Get(jsonStr, "SKU.skuPaths").ForEach(func(_, item gjson.Result) bool {
		var skuMap map[string]interface{}
		if err := json.Unmarshal([]byte(item.Raw), &skuMap); err == nil {
			skuPaths = append(skuPaths, skuMap)
		}
		return true
	})

	if len(skuPaths) == 0 {
		return nil, skuPaths, fmt.Errorf("SKU组件中没有找到skuPaths")
	}

	// 返回SKU组件数据以保持接口兼容性
	var skuComponent map[string]interface{}
	json.Unmarshal([]byte(gjson.Get(jsonStr, "SKU").Raw), &skuComponent)

	return skuComponent, skuPaths, nil
}

// extractPriceDataFromComponents 从组件中提取价格数据 - 使用GJSON大幅简化
func (s *Spider) extractPriceDataFromComponents(jsonStr string) map[string]map[string]interface{} {
	priceData := make(map[string]map[string]interface{})

	// 使用GJSON一行查询替代47行复杂嵌套解析 - 从skuPriceInfoMap提取
	gjson.Get(jsonStr, "PRICE.skuPriceInfoMap").ForEach(func(skuID, priceInfo gjson.Result) bool {
		if salePriceString := gjson.Get(priceInfo.Raw, "salePriceString").String(); salePriceString != "" {
			priceValue := s.parsePriceString(salePriceString)
			priceData[skuID.String()] = map[string]interface{}{
				"price_value": salePriceString,
				"price":       priceValue,
			}
		}
		return true
	})

	// 备选路径 - 同样用GJSON一行查询替代复杂循环
	gjson.Get(jsonStr, "PRICE.skuIdStrPriceInfoMap").ForEach(func(skuID, priceInfo gjson.Result) bool {
		if _, exists := priceData[skuID.String()]; !exists {
			if salePriceString := gjson.Get(priceInfo.Raw, "salePriceString").String(); salePriceString != "" {
				priceValue := s.parsePriceString(salePriceString)
				priceData[skuID.String()] = map[string]interface{}{
					"price_value": salePriceString,
					"price":       priceValue,
				}
			}
		}
		return true
	})

	return priceData
}

// parsePriceString 解析价格字符串，提取数值
func (s *Spider) parsePriceString(priceString string) float64 {
	// 移除货币符号和其他非数字字符，保留数字、点和逗号
	cleanPrice := strings.Map(func(r rune) rune {
		if (r >= '0' && r <= '9') || r == '.' || r == ',' {
			return r
		}
		return -1
	}, priceString)

	// 处理欧洲格式的数字（逗号作为小数点）
	if strings.Contains(cleanPrice, ",") && strings.Contains(cleanPrice, ".") {
		// 如果同时包含逗号和点，假设点是千位分隔符，逗号是小数点
		cleanPrice = strings.ReplaceAll(cleanPrice, ".", "")
		cleanPrice = strings.ReplaceAll(cleanPrice, ",", ".")
	} else if strings.Contains(cleanPrice, ",") {
		// 只有逗号，替换为点
		cleanPrice = strings.ReplaceAll(cleanPrice, ",", ".")
	}

	if price, err := strconv.ParseFloat(cleanPrice, 64); err == nil {
		return price
	}

	return 0.0
}

// extractImageDataFromComponents 从组件中提取图片数据
func (s *Spider) extractImageDataFromComponents(jsonStr string) map[string]string {
	skuImages := make(map[string]string)

	// 从HEADER_IMAGE_PC组件提取图片信息
	if headerImage, ok := content["HEADER_IMAGE_PC"].(map[string]interface{}); ok {
		// 从skuImagesMap提取SKU对应的图片
		if skuImagesMap, ok := headerImage["skuImagesMap"].(map[string]interface{}); ok {
			for skuID, images := range skuImagesMap {
				if imageList, ok := images.([]interface{}); ok && len(imageList) > 0 {
					// 取第一张图片
					if imageURL, ok := imageList[0].(string); ok {
						skuImages[skuID] = imageURL
					}
				}
			}
		}

		// 如果没有找到SKU特定图片，使用默认图片
		if len(skuImages) == 0 {
			if imagePathList, ok := headerImage["imagePathList"].([]interface{}); ok && len(imagePathList) > 0 {
				if defaultImage, ok := imagePathList[0].(string); ok {
					// 为所有SKU设置默认图片（稍后会被具体SKU覆盖）
					skuImages["default"] = defaultImage
				}
			}
		}
	}

	return skuImages
}

// extractStockDataFromComponents 从组件中提取库存数据 - 使用GJSON大幅简化
func (s *Spider) extractStockDataFromComponents(jsonStr string) map[string]map[string]interface{} {
	stockData := make(map[string]map[string]interface{})

	// 使用GJSON一行查询替代48行复杂嵌套 - 提取SKU库存
	gjson.Get(jsonStr, "QUANTITY_PC.allSkuQuantityView").ForEach(func(skuID, quantityInfo gjson.Result) bool {
		maxBuyCount := gjson.Get(quantityInfo.Raw, "maxBuyCount").Float()
		maxBuyCountStr := gjson.Get(quantityInfo.Raw, "maxBuyCountStr").String()

		// 直接构建库存信息，无需复杂的条件判断
		stockData[skuID.String()] = map[string]interface{}{
			"stock":        int(maxBuyCount),
			"in_stock":     maxBuyCount > 0 && maxBuyCountStr != "Sold out",
			"stock_status": maxBuyCountStr,
		}
		return true
	})

	// 检查总库存 - 同样用GJSON简化
	if totalInventory := gjson.Get(jsonStr, "QUANTITY_PC.totalAvailableInventory").Float(); totalInventory == 0 {
		// 如果总库存为0，所有SKU都设为无库存
		for skuID := range stockData {
			stockData[skuID]["stock"] = 0
			stockData[skuID]["in_stock"] = false
		}
	}

	return stockData
}

// buildSKUItemFromComponents 从组件数据构建SKU项
func (s *Spider) buildSKUItemFromComponents(productID, title string, skuPath map[string]interface{},
	skuImages map[string]string, priceData map[string]map[string]interface{},
	stockData map[string]map[string]interface{}, spiderConfig *spiders.SpiderConfig) *models.ProductItem {

	// 提取SKU ID
	skuIDStr := ""
	if skuID, ok := skuPath["skuIdStr"].(string); ok {
		skuIDStr = skuID
	} else if skuID, ok := skuPath["skuId"].(float64); ok {
		skuIDStr = fmt.Sprintf("%.0f", skuID)
	}

	if skuIDStr == "" {
		return nil
	}

	// 提取价格信息
	priceInfo, exists := priceData[skuIDStr]
	if !exists {
		priceInfo = map[string]interface{}{"price": 0.0, "price_value": "0"}
	}

	priceValue := 0.0
	if price, ok := priceInfo["price"].(float64); ok {
		priceValue = price
	}

	// 获取货币配置
	currency := config.GetConfigString(spiderConfig, "currency", "€")

	// 提取库存信息
	stockInfo, stockExists := stockData[skuIDStr]
	inStock := false
	stock := 0

	if stockExists {
		if stockVal, ok := stockInfo["stock"].(int); ok {
			stock = stockVal
		}
		if inStockVal, ok := stockInfo["in_stock"].(bool); ok {
			inStock = inStockVal
		}
	}

	// 构建ATC链接
	var atcLink *string
	availability := "Out of Stock"
	if inStock {
		siteURL := spiderConfig.SiteURL
		baseURL := fmt.Sprintf("%s/p/trade/confirm.html?productId=%s&skuId=%s", siteURL, productID, skuIDStr)
		atcLinkStr := fmt.Sprintf("[x1](%s&quantity=1) | [x2](%s&quantity=2)", baseURL, baseURL)
		atcLink = &atcLinkStr
		availability = "In Stock"
	}

	// 提取图片URL
	imageURL := skuImages[skuIDStr]
	if imageURL == "" {
		// 使用默认图片
		imageURL = skuImages["default"]
	}

	// 提取商品详情
	addition := ""
	if skuAttr, ok := skuPath["skuAttr"].(string); ok {
		// 从skuAttr中提取商品规格信息
		parts := strings.Split(skuAttr, "#")
		if len(parts) > 1 {
			addition = parts[1]
		}
	}

	// 获取国家信息
	country := spiderConfig.Country
	if country == "" {
		country = "US"
	}

	// 构建产品URL
	siteURL := spiderConfig.SiteURL
	productURL := fmt.Sprintf("%s/item/%s.html?pdp_ext_f={\"sku_id\":\"%s\"}", siteURL, productID, skuIDStr)

	// 创建产品数据项
	item := &models.ProductItem{
		// 基础信息
		Name:      spiderConfig.Name,
		ProductID: productID,
		Title:     title,
		URL:       productURL,
		Platform:  "aliexpress",

		// 价格信息
		Price:    priceValue,
		Currency: currency,

		// 库存信息
		Stock:         stock,
		InStock:       inStock,
		Availability:  availability,
		Country:       country,
		SiteURL:       siteURL,
		Notifications: spiderConfig.Notifications,

		// 可选参数
		SkuID:   &skuIDStr,
		AtcLink: atcLink,

		// 商品详情
		Addition: &addition,

		// 系统元数据
		CrawledAt: time.Now(),
		Metadata: map[string]interface{}{
			"spider_type": "aliexpress",
			"sku_path":    skuPath,
			"price_info":  priceInfo,
			"stock_info":  stockInfo,
		},
	}

	// 设置图片URL
	if imageURL != "" {
		item.ImageURL = &imageURL
	}

	return item
}

// buildSKUItem 构建SKU项
func (s *Spider) buildSKUItem(productID, title string, path map[string]interface{},
	skuImages map[string]string, priceData map[string]map[string]interface{}, spiderConfig *spiders.SpiderConfig) *models.ProductItem {

	// 提取SKU ID - 兼容新旧结构，对应Python版本的逻辑
	skuIDStr := ""
	if skuID, ok := path["skuId"].(string); ok {
		skuIDStr = skuID
	} else if skuID, ok := path["skuIdStr"].(string); ok {
		skuIDStr = skuID
	} else if skuID, ok := path["skuId"].(float64); ok {
		skuIDStr = fmt.Sprintf("%.0f", skuID)
	}

	if skuIDStr == "" {
		return nil
	}

	// 提取价格信息 - 对应Python版本的价格提取逻辑
	priceInfo, exists := priceData[skuIDStr]
	if !exists {
		priceInfo = map[string]interface{}{"price": 0.0, "price_value": "0"}
	}

	priceValue := 0.0
	if price, ok := priceInfo["price"].(float64); ok {
		priceValue = price
	} else if priceVal, ok := priceInfo["price_value"].(string); ok {
		if price, err := strconv.ParseFloat(priceVal, 64); err == nil {
			priceValue = price
		}
	}

	// 获取货币配置 - 对应Python版本的currency配置
	currency := config.GetConfigString(spiderConfig, "currency", "€")

	// 提取库存信息 - 对应Python版本的库存逻辑
	inStock := false
	stock := 0
	if skuVal, ok := path["skuVal"].(map[string]interface{}); ok {
		if availQuantity, ok := skuVal["availQuantity"].(float64); ok {
			stock = int(availQuantity)
			if isActivity, ok := skuVal["isActivity"].(bool); ok {
				inStock = stock > 0 && isActivity
			} else {
				inStock = stock > 0
			}
		} else if inventory, ok := skuVal["inventory"].(float64); ok {
			stock = int(inventory)
			inStock = stock > 0
		}
	} else if quantity, ok := path["quantity"].(float64); ok {
		stock = int(quantity)
		inStock = stock > 0
	}

	// 构建ATC链接 - 对应Python版本的atcLink构建
	var atcLink *string
	availability := "Out of Stock"
	if inStock {
		siteURL := spiderConfig.SiteURL
		baseURL := fmt.Sprintf("%s/p/trade/confirm.html?productId=%s&skuId=%s", siteURL, productID, skuIDStr)
		atcLinkStr := fmt.Sprintf("[x1](%s&quantity=1) | [x2](%s&quantity=2)", baseURL, baseURL)
		atcLink = &atcLinkStr
		availability = "In Stock"
	}

	// 提取图片URL
	imageURL := skuImages[skuIDStr]

	// 提取商品详情 - 对应Python版本的addition字段
	skuAttr := ""
	if attr, ok := path["skuAttr"].(string); ok {
		skuAttr = attr
	}

	addition := ""
	if skuAttr != "" {
		parts := strings.Split(skuAttr, "#")
		if len(parts) > 1 {
			addition = parts[1]
		}
	}

	// 获取国家信息 - 对应Python版本的country配置
	country := spiderConfig.Country
	if country == "" {
		country = "US"
	}

	// 构建产品URL - 对应Python版本的product_url
	siteURL := spiderConfig.SiteURL
	productURL := fmt.Sprintf("%s/item/%s.html?pdp_ext_f={\"sku_id\":\"%s\"}", siteURL, productID, skuIDStr)

	// 创建产品数据项 - 对应Python版本的ProductItem创建
	item := &models.ProductItem{
		// 基础信息
		Name:      spiderConfig.Name,
		ProductID: productID,
		Title:     title,
		URL:       productURL,
		Platform:  "aliexpress",

		// 价格信息
		Price:    priceValue,
		Currency: currency,

		// 库存信息
		Stock:         stock,
		InStock:       inStock,
		Availability:  availability,
		Country:       country,
		SiteURL:       siteURL,
		Notifications: spiderConfig.Notifications,

		// 可选参数
		SkuID:   &skuIDStr,
		AtcLink: atcLink,

		// 商品详情
		Addition: &addition,

		// 系统元数据
		CrawledAt: time.Now(),
		Metadata: map[string]interface{}{
			"spider_type": "aliexpress",
			"sku_path":    path,
			"price_info":  priceInfo,
		},
	}

	// 设置图片URL
	if imageURL != "" {
		item.ImageURL = &imageURL
	}

	return item
}

// ========== 批量处理能力接口实现 ==========

// SupportsBatchProcessing AliExpress爬虫不支持批量处理
func (s *Spider) SupportsBatchProcessing() bool {
	return false
}

// GetMaxBatchSize AliExpress爬虫只支持单个处理
func (s *Spider) GetMaxBatchSize() int {
	return 1
}
