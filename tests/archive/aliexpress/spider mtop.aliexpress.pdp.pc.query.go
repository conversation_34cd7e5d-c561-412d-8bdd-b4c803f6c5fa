package aliexpress

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/internal/spiders/shared"
	"go-monitor/pkg/httpclient"

	"github.com/tidwall/gjson"
)

// ========== 常量定义 ==========
// 使用shared包中的统一常量，移除重复定义

// ========== 类型定义 ==========

// Spider AliExpress爬虫实现
type Spider struct {
	*spiders.BaseSpider
	// 默认请求头 - 对应Python版本的default_headers
	defaultHeaders map[string]string
}

// ========== 构造函数 ==========

// NewSpider 创建新的AliExpress爬虫实例
func NewSpider() spiders.Spider {
	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider(shared.PlatformAliExpress, shared.SpiderVersion, "AliExpress商品监控爬虫"),
		defaultHeaders: map[string]string{
			"pragma":          "no-cache",
			"cache-control":   "no-cache",
			"accept":          "*/*",
			"sec-fetch-site":  "same-origin",
			"sec-fetch-mode":  "cors",
			"sec-fetch-dest":  "empty",
			"accept-encoding": "gzip, deflate, br, zstd",
			"accept-language": "en-US,en;q=0.9",
			"priority":        "u=1, i",
		},
	}
	return spider
}

// GetProductIDs 获取产品ID集合 - 对应Python版本的get_product_ids
func (s *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// PrepareRequest 准备请求 - 对应Python版本的prepare_request
func (s *Spider) PrepareRequest(ctx context.Context, productID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	// 生成动态callback参数
	callbackNum := time.Now().UnixNano() % 10
	callback := fmt.Sprintf("mtopjsonp%d", callbackNum)
	// 构建查询参数 - 使用shared包常量
	params := map[string]string{
		"jsv":      "2.5.1",
		"appKey":   "12574478",
		"api":      "mtop.aliexpress.pdp.pc.query",
		"v":        "1.0",
		"type":     "originaljson",
		"dataType": "originaljson",
		"callback": callback,
	}

	// API端点URL
	baseURL := "https://acs.aliexpress.com/h5/mtop.aliexpress.pdp.pc.query/1.0/"

	// 获取配置参数 - 从SpiderConfig中获取
	country := spiderConfig.Country
	currency := config.GetConfigString(spiderConfig, "currency", "USD")
	province := config.GetConfigString(spiderConfig, "province", "")
	city := config.GetConfigString(spiderConfig, "city", "")

	// 构建数据负载
	data := map[string]any{
		"productId":  productID,
		"_lang":      fmt.Sprintf("en_%s", country),
		"_currency":  currency,
		"country":    country,
		"province":   province,
		"city":       city,
		"clientType": "pc",
		"ext":        "{\"minidetail\":true}",
	}

	// 序列化数据
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return nil, s.HandleError("序列化数据", err)
	}
	params["data"] = string(dataJSON)

	// 使用HTTP客户端构建器创建请求
	builder := httpclient.CreateAliExpressRequest(baseURL, spiderConfig, productID)

	// 添加查询参数
	for key, value := range params {
		builder.AddParam(key, value)
	}

	// 添加默认请求头
	builder.AddHeaders(s.defaultHeaders)

	// 构建表单数据并设置请求体
	// formData := url.Values{}
	// formData.Set("data", string(dataJSON))
	// builder.SetBody([]byte(formData.Encode()))

	// 构建请求
	httpRequest := builder.Build()

	// 转换为models.Request
	request := &models.Request{
		SpiderType:   "aliexpress",
		URL:          httpRequest.URL,
		Method:       httpRequest.Method,
		Headers:      httpRequest.Headers,
		Body:         httpRequest.Body,
		Params:       httpRequest.Params,
		ReverseProxy: httpRequest.ReverseProxy,
		Metadata:     httpRequest.Meta,
	}

	s.LogDebug("准备AliExpress请求，产品ID：%s，国家：%s，货币：%s",
		productID, country, currency)

	return request, nil
}

// extractJSONFromJSONP 从JSONP响应中提取JSON内容
func (s *Spider) extractJSONFromJSONP(responseBody string) (string, error) {
	// 去除首尾空白字符
	responseBody = strings.TrimSpace(responseBody)

	// 检查是否为JSONP格式：以callback函数名开头，以({...})结构包装
	// JSONP格式示例：mtopjsonp1({...JSON内容...})
	if !strings.Contains(responseBody, "(") || !strings.HasSuffix(responseBody, ")") {
		// 不是JSONP格式，返回原始内容
		return responseBody, nil
	}

	// 查找第一个左括号的位置
	startIndex := strings.Index(responseBody, "(")
	if startIndex == -1 {
		return responseBody, nil
	}

	// 检查左括号前是否有callback函数名
	callbackName := responseBody[:startIndex]
	if callbackName == "" || strings.Contains(callbackName, " ") {
		// 没有callback函数名或包含空格，不是有效的JSONP
		return responseBody, nil
	}

	// 提取括号内的JSON内容
	jsonContent := responseBody[startIndex+1 : len(responseBody)-1]

	// 验证提取的内容是否为有效JSON
	if !gjson.Valid(jsonContent) {
		return "", fmt.Errorf("提取的JSONP内容不是有效的JSON格式")
	}

	s.LogDebug("检测到JSONP响应，callback函数：%s，成功提取JSON内容", callbackName)
	return jsonContent, nil
}

// ParseResponse 解析响应 - 对应Python版本的parse_response
func (s *Spider) ParseResponse(ctx context.Context, resp *models.Response, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	// 获取产品ID
	productID := ""
	if resp.Request != nil {
		productID = resp.Request.GetMetadataString("product_id", "")
	}

	if productID == "" {
		return nil, fmt.Errorf("响应中缺少产品ID")
	}

	// 解析响应内容 - 先处理JSONP格式，再使用纯GJSON处理
	rawResponse := string(resp.Body)

	// 从JSONP响应中提取JSON内容（如果是JSONP格式）
	jsonStr, err := s.extractJSONFromJSONP(rawResponse)
	if err != nil {
		return nil, s.HandleError("提取JSONP内容", err)
	}

	// 验证提取后的JSON内容
	if !gjson.Valid(jsonStr) {
		return nil, s.HandleError("验证JSON响应", fmt.Errorf("无效的JSON响应"))
	}

	// 使用GJSON直接检查API返回状态 - 适配JSONP响应结构
	if !gjson.Get(jsonStr, "data").Exists() {
		// 如果没有data字段，直接使用整个响应作为产品数据
		productItems, err := s.extractProductInfo(productID, jsonStr, config)
		if err != nil {
			return nil, err
		}
		return productItems, nil
	}

	// 检查API调用是否成功 - JSONP响应通常在ret字段中包含状态信息
	if ret := gjson.Get(jsonStr, "ret"); ret.Exists() {
		retArray := ret.Array()
		if len(retArray) > 0 && !strings.Contains(retArray[0].String(), "SUCCESS") {
			errorMsg := retArray[0].String()
			s.LogError("API返回错误，产品ID：%s，错误：%s", productID, errorMsg)
			return nil, fmt.Errorf("API返回错误: %s", errorMsg)
		}
	}

	// 获取真正的产品数据 - 使用GJSON直接访问data.result
	if !gjson.Get(jsonStr, "data.result").Exists() {
		s.LogError("响应中缺少产品数据，产品ID：%s", productID)
		return nil, fmt.Errorf("响应中缺少产品数据")
	}

	// 提取商品信息 - 传递data.result部分的JSON字符串
	productItems, err := s.extractProductInfo(productID, gjson.Get(jsonStr, "data.result").Raw, config)
	if err != nil {
		return nil, err
	}
	return productItems, nil
}

// extractProductInfo 提取商品信息 - 使用GJSON简化
func (s *Spider) extractProductInfo(productID string, jsonStr string, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var result []*models.ProductItem

	// 提取标题
	title := s.extractProductTitle(jsonStr)

	// 使用纯GJSON处理SKU数据 - 适配JSONP响应结构
	var skuItems []gjson.Result
	gjson.Get(jsonStr, "SKU.skuPaths").ForEach(func(_, item gjson.Result) bool {
		skuItems = append(skuItems, item)
		return true
	})

	// 检查是否有SKU数据
	if len(skuItems) == 0 {
		s.LogWarn("产品没有SKU数据，产品ID：%s", productID)
		return result, nil
	}

	// 提取价格数据
	priceData := s.getPriceData(jsonStr)

	// 提取SKU图片 - 使用纯GJSON处理
	skuImages := s.getSKUImages(jsonStr)

	// 构建SKU项 - 直接使用GJSON结果的Raw字符串
	for _, skuItem := range skuItems {
		productItem := s.buildSKUItem(productID, title, skuItem.Raw, skuImages, priceData, config)
		if productItem != nil {
			result = append(result, productItem)
		}
	}

	return result, nil
}

// extractProductTitle 提取产品标题 - 使用shared包清理标题
func (s *Spider) extractProductTitle(jsonStr string) string {
	// 使用GJSON提取产品标题 - 适配JSONP响应结构
	var title string
	if productTitle := gjson.Get(jsonStr, "PRODUCT_TITLE.text").String(); productTitle != "" {
		title = productTitle
	} else if globalTitle := gjson.Get(jsonStr, "GLOBAL_DATA.globalData.subject").String(); globalTitle != "" {
		title = globalTitle
	} else {
		title = "Unknown product"
	}

	// 使用shared包清理标题
	return shared.CleanTitle(title)
}

// getPriceData 提取SKU价格信息 - 适配JSONP响应结构
func (s *Spider) getPriceData(jsonStr string) map[string]map[string]any {
	priceData := make(map[string]map[string]any)

	// 使用GJSON从PRICE.skuIdStrPriceInfoMap中提取价格信息
	priceInfoMap := gjson.Get(jsonStr, "PRICE.skuIdStrPriceInfoMap")
	if priceInfoMap.Exists() {
		priceInfoMap.ForEach(func(skuId, priceInfo gjson.Result) bool {
			// 提取价格字符串，格式如"27,39€"
			salePriceString := gjson.Get(priceInfo.Raw, "salePriceString").String()

			// 解析价格值 - 从salePriceLocal字段提取数值部分
			salePriceLocal := gjson.Get(priceInfo.Raw, "salePriceLocal").String()
			var priceValue float64
			if salePriceLocal != "" {
				// salePriceLocal格式如"27,39€|27|39"，提取中间的数值部分
				parts := strings.Split(salePriceLocal, "|")
				if len(parts) >= 2 {
					if parsedPrice, err := shared.ParsePrice(parts[1] + "." + parts[2]); err == nil {
						priceValue = parsedPrice
					}
				}
			}

			priceData[skuId.String()] = map[string]any{
				"price":       priceValue,
				"price_value": salePriceString,
			}
			return true
		})
	}

	return priceData
}

// getSKUImages 获取SKU图片 - 适配JSONP响应结构
func (s *Spider) getSKUImages(jsonStr string) map[string]string {
	skuImages := make(map[string]string)

	// 首先尝试从HEADER_IMAGE_PC.skuImagesMap中获取SKU图片映射
	skuImagesMap := gjson.Get(jsonStr, "HEADER_IMAGE_PC.skuImagesMap")
	if skuImagesMap.Exists() {
		skuImagesMap.ForEach(func(skuId, imageArray gjson.Result) bool {
			// 取第一张图片
			if imageArray.IsArray() && len(imageArray.Array()) > 0 {
				skuImages[skuId.String()] = imageArray.Array()[0].String()
			}
			return true
		})
	}

	// 如果没有直接的SKU图片映射，从SKU属性中提取
	if len(skuImages) == 0 {
		gjson.Get(jsonStr, "SKU.skuProperties").ForEach(func(_, prop gjson.Result) bool {
			gjson.Get(prop.Raw, "skuPropertyValues").ForEach(func(_, value gjson.Result) bool {
				// 提取属性ID
				propertyID := gjson.Get(value.Raw, "propertyValueIdLong").String()
				if propertyID == "" {
					if id := gjson.Get(value.Raw, "propertyValueIdLong").Float(); id > 0 {
						propertyID = fmt.Sprintf("%.0f", id)
					}
				}

				imageURL := gjson.Get(value.Raw, "skuPropertyImagePath").String()
				if propertyID == "" || imageURL == "" {
					return true
				}

				// 为所有包含此属性的SKU设置图片
				gjson.Get(jsonStr, "SKU.skuPaths").ForEach(func(_, skuItem gjson.Result) bool {
					skuAttr := gjson.Get(skuItem.Raw, "skuAttr").String()
					path := gjson.Get(skuItem.Raw, "path").String()

					if strings.Contains(skuAttr, propertyID) || strings.Contains(path, propertyID) {
						skuID := gjson.Get(skuItem.Raw, "skuIdStr").String()
						if skuID == "" {
							if id := gjson.Get(skuItem.Raw, "skuId").Float(); id > 0 {
								skuID = fmt.Sprintf("%.0f", id)
							}
						}
						if skuID != "" {
							skuImages[skuID] = imageURL
						}
					}
					return true
				})
				return true
			})
			return true
		})
	}

	return skuImages
}

// buildSKUItem 构建SKU项 - 使用GJSON简化
func (s *Spider) buildSKUItem(productID, title string, pathJson string,
	skuImages map[string]string, priceData map[string]map[string]any, spiderConfig *spiders.SpiderConfig) *models.ProductItem {

	// 使用GJSON提取SKU ID - 适配JSONP响应结构
	skuIDStr := gjson.Get(pathJson, "skuIdStr").String()
	if skuIDStr == "" {
		// 处理数字类型的skuId
		if skuIDFloat := gjson.Get(pathJson, "skuId").Float(); skuIDFloat > 0 {
			skuIDStr = fmt.Sprintf("%.0f", skuIDFloat)
		}
	}

	if skuIDStr == "" {
		return nil
	}

	// 提取价格信息 - 对应Python版本的价格提取逻辑
	priceInfo, exists := priceData[skuIDStr]
	if !exists {
		priceInfo = map[string]any{"price": 0.0, "price_value": "0"}
	}

	// 使用shared包解析价格
	priceValue := 0.0
	if price, ok := priceInfo["price"].(float64); ok {
		priceValue = price
	} else if priceVal, ok := priceInfo["price_value"].(string); ok {
		if parsedPrice, err := shared.ParsePrice(priceVal); err == nil {
			priceValue = parsedPrice
		}
	}

	// 获取货币配置 - 对应Python版本的currency配置
	currency := config.GetConfigString(spiderConfig, "currency", "€")

	// 使用GJSON提取库存信息 - 适配JSONP响应结构
	stock := int(gjson.Get(pathJson, "skuStock").Int())
	salable := gjson.Get(pathJson, "salable").Bool()

	inStock := stock > 0 && salable

	// 构建ATC链接 - 对应Python版本的atcLink构建
	var atcLink *string = nil
	availability := "Out of Stock"
	if inStock {
		siteURL := spiderConfig.SiteURL
		baseURL := fmt.Sprintf("%s/p/trade/confirm.html?productId=%s&skuId=%s", siteURL, productID, skuIDStr)
		atcLinkStr := fmt.Sprintf("[x1](%s&quantity=1) | [x2](%s&quantity=2)", baseURL, baseURL)
		atcLink = &atcLinkStr
		availability = "In Stock"
	}

	// 提取图片URL
	imageURL := skuImages[skuIDStr]

	// 使用GJSON提取商品详情 - 适配JSONP响应结构
	skuAttr := gjson.Get(pathJson, "skuAttr").String()

	addition := ""
	if skuAttr != "" {
		// 从skuAttr中提取商品规格信息，格式如："14:691#1PC Single Box;200007763:201336100"
		parts := strings.Split(skuAttr, "#")
		if len(parts) > 1 {
			// 提取中间部分，如"1PC Single Box"
			specParts := strings.Split(parts[1], ";")
			addition = specParts[0]
		}
	}

	// 构建产品URL - 对应Python版本的product_url
	siteURL := spiderConfig.SiteURL
	productURL := fmt.Sprintf("%s/item/%s.html?pdp_ext_f={\"sku_id\":\"%s\"}", siteURL, productID, skuIDStr)

	// 创建产品数据项 - 对应Python版本的ProductItem创建
	item := &models.ProductItem{
		// 基础信息
		Name:      spiderConfig.Name,
		ProductID: productID,
		Title:     fmt.Sprintf("%s - %s", title, addition),
		URL:       productURL,
		Platform:  "aliexpress",

		// 价格信息
		Price:    priceValue,
		Currency: currency,

		// 库存信息
		Stock:         stock,
		InStock:       inStock,
		Availability:  availability,
		Country:       spiderConfig.Country,
		SiteURL:       siteURL,
		Notifications: spiderConfig.Notifications,
		ImageURL:      &imageURL,

		// 可选参数
		SkuID:   &skuIDStr,
		AtcLink: atcLink,

		// 商品详情
		Addition: &addition,

		// 系统元数据
		CrawledAt: time.Now(),
		Metadata: map[string]any{
			"spider_type": "aliexpress",
			"sku_path":    pathJson,
		},
	}

	return item
}

// ========== 批量处理能力接口实现 ==========

// SupportsBatchProcessing AliExpress爬虫不支持批量处理
func (s *Spider) SupportsBatchProcessing() bool {
	return false
}

// GetMaxBatchSize AliExpress爬虫只支持单个处理
func (s *Spider) GetMaxBatchSize() int {
	return 1
}

// PrepareBatchRequest AliExpress爬虫不支持批量处理，使用默认实现
// 此方法继承自BaseSpider，会返回错误
