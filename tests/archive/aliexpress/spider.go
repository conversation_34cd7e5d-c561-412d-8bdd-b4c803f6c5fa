package aliexpress

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/internal/spiders/shared"
	"go-monitor/pkg/httpclient"

	"github.com/tidwall/gjson"
)

// ========== 常量定义 ==========
// 使用shared包中的统一常量，移除重复定义

// ========== 类型定义 ==========

// Spider AliExpress爬虫实现
type Spider struct {
	*spiders.BaseSpider
	// 默认请求头 - 对应Python版本的default_headers
	defaultHeaders map[string]string
}

// ========== 构造函数 ==========

// NewSpider 创建新的AliExpress爬虫实例
func NewSpider() spiders.Spider {
	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider(shared.PlatformAliExpress, shared.SpiderVersion, "AliExpress商品监控爬虫"),
		defaultHeaders: map[string]string{
			"pragma":          "no-cache",
			"cache-control":   "no-cache",
			"accept":          "*/*",
			"sec-fetch-site":  "same-origin",
			"sec-fetch-mode":  "cors",
			"sec-fetch-dest":  "empty",
			"accept-encoding": "gzip, deflate, br, zstd",
			"accept-language": "en-US,en;q=0.9",
			"priority":        "u=1, i",
		},
	}
	return spider
}

// GetProductIDs 获取产品ID集合 - 对应Python版本的get_product_ids
func (s *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// PrepareRequest 准备请求 - 对应Python版本的prepare_request
func (s *Spider) PrepareRequest(ctx context.Context, productID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	// 构建查询参数 - 使用shared包常量
	params := map[string]string{
		"jsv":      "2.5.1",
		"appKey":   "12574478",
		"api":      "mtop.aliexpress.itemdetail.querySkuPanelDetail",
		"v":        "1.0",
		"type":     "originaljson",
		"dataType": "jsonp",
	}

	// API端点URL
	baseURL := "https://acs.aliexpress.com/h5/mtop.aliexpress.itemdetail.queryskupaneldetail/1.0/"

	// 获取配置参数 - 从SpiderConfig中获取
	country := spiderConfig.Country
	currency := config.GetConfigString(spiderConfig, "currency", "USD")

	// 构建数据负载
	data := map[string]any{
		"productId":  productID,
		"lang":       fmt.Sprintf("en_%s", country),
		"country":    country,
		"currency":   currency,
		"_currency":  currency,
		"clientType": "pc",
		"sourceType": 1,
	}

	// 序列化数据
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return nil, s.HandleError("序列化数据", err)
	}

	// 使用HTTP客户端构建器创建请求
	builder := httpclient.CreateAliExpressRequest(baseURL, spiderConfig, productID)

	// 添加查询参数
	for key, value := range params {
		builder.AddParam(key, value)
	}

	// 添加默认请求头
	builder.AddHeaders(s.defaultHeaders)

	// 构建表单数据并设置请求体
	formData := url.Values{}
	formData.Set("data", string(dataJSON))
	builder.SetBody([]byte(formData.Encode()))

	// 构建请求
	httpRequest := builder.Build()

	// 转换为models.Request
	request := &models.Request{
		SpiderType:   "aliexpress",
		URL:          httpRequest.URL,
		Method:       httpRequest.Method,
		Headers:      httpRequest.Headers,
		Body:         httpRequest.Body,
		Params:       httpRequest.Params,
		ReverseProxy: httpRequest.ReverseProxy,
		Metadata:     httpRequest.Meta,
	}

	s.LogDebug("准备AliExpress请求，产品ID：%s，国家：%s，货币：%s",
		productID, country, currency)

	return request, nil
}

// ParseResponse 解析响应 - 对应Python版本的parse_response
func (s *Spider) ParseResponse(ctx context.Context, resp *models.Response, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	// 获取产品ID
	productID := ""
	if resp.Request != nil {
		productID = resp.Request.GetMetadataString("product_id", "")
	}

	if productID == "" {
		return nil, fmt.Errorf("响应中缺少产品ID")
	}

	// 解析响应内容 - 使用纯GJSON处理
	jsonStr := string(resp.Body)
	if !gjson.Valid(jsonStr) {
		return nil, s.HandleError("验证JSON响应", fmt.Errorf("无效的JSON响应"))
	}

	// 使用GJSON直接检查API返回状态 - 简化嵌套检查
	if !gjson.Get(jsonStr, "data").Exists() {
		// 如果没有data字段，直接使用整个响应作为产品数据
		productItems, err := s.extractProductInfo(productID, jsonStr, config)
		if err != nil {
			return nil, err
		}
		return productItems, nil
	}

	// 检查success状态 - 使用GJSON零值特性
	if success := gjson.Get(jsonStr, "data.success"); success.Exists() && !success.Bool() {
		errorMsg := gjson.Get(jsonStr, "data.errorMsg").String()
		if errorMsg == "" {
			errorMsg = "未知错误"
		}
		s.LogError("API返回错误，产品ID：%s，错误：%s", productID, errorMsg)
		return nil, fmt.Errorf("API返回错误: %s", errorMsg)
	}

	// 获取真正的产品数据 - 使用GJSON直接访问
	if !gjson.Get(jsonStr, "data.data").Exists() {
		s.LogError("响应中缺少产品数据，产品ID：%s", productID)
		return nil, fmt.Errorf("响应中缺少产品数据")
	}

	// 提取商品信息 - 传递JSON字符串
	productItems, err := s.extractProductInfo(productID, gjson.Get(jsonStr, "data.data").Raw, config)
	if err != nil {
		return nil, err
	}
	return productItems, nil
}

// extractProductInfo 提取商品信息 - 使用GJSON简化
func (s *Spider) extractProductInfo(productID string, jsonStr string, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var result []*models.ProductItem

	// 提取标题
	title := s.extractProductTitle(jsonStr)

	// 使用纯GJSON处理SKU数据 - 完全移除json.Unmarshal
	var skuItems []gjson.Result
	gjson.Get(jsonStr, "priceComponent.skuPriceList").ForEach(func(_, item gjson.Result) bool {
		skuItems = append(skuItems, item)
		return true
	})

	// 检查是否有SKU数据
	if len(skuItems) == 0 {
		s.LogWarn("产品没有SKU数据，产品ID：%s", productID)
		return result, nil
	}

	// 提取价格数据
	priceData := s.getPriceData(jsonStr)

	// 提取SKU图片 - 使用纯GJSON处理
	skuImages := s.getSKUImages(jsonStr)

	// 构建SKU项 - 直接使用GJSON结果的Raw字符串
	for _, skuItem := range skuItems {
		productItem := s.buildSKUItem(productID, title, skuItem.Raw, skuImages, priceData, config)
		if productItem != nil {
			result = append(result, productItem)
		}
	}

	return result, nil
}

// extractProductTitle 提取产品标题 - 使用shared包清理标题
func (s *Spider) extractProductTitle(jsonStr string) string {
	// 使用GJSON提取产品标题
	var title string
	if subject := gjson.Get(jsonStr, "productInfoComponent.subject").String(); subject != "" {
		title = subject
	} else if seoTitle := gjson.Get(jsonStr, "extraComponent.seoTitle").String(); seoTitle != "" {
		title = seoTitle
	} else {
		title = "Unknown product"
	}

	// 使用shared包清理标题
	return shared.CleanTitle(title)
}

// getPriceData 提取SKU价格信息 - 使用GJSON简化
func (s *Spider) getPriceData(jsonStr string) map[string]map[string]any {
	priceData := make(map[string]map[string]any)

	// 使用GJSON检查新的组件化结构 - 大幅简化
	gjson.Get(jsonStr, "priceComponent.skuPriceList").ForEach(func(_, priceItem gjson.Result) bool {
		// 直接提取SKU ID
		skuIdStr := gjson.Get(priceItem.Raw, "skuIdStr").String()

		// 直接提取价格值
		priceData[skuIdStr] = map[string]any{
			"price":     gjson.Get(priceItem.Raw, "skuVal.skuAmount.value").Float(),
			"usd_price": gjson.Get(priceItem.Raw, "skuVal.skuCalPrice").String(),
		}
		return true
	})

	return priceData
}

// getSKUImages 获取SKU图片 - 使用纯GJSON处理
func (s *Spider) getSKUImages(jsonStr string) map[string]string {
	skuImages := make(map[string]string)

	// 使用GJSON检查组件化结构
	hasSkuProperties := gjson.Get(jsonStr, "skuComponent.productSKUPropertyList").Exists() ||
		gjson.Get(jsonStr, "SKU.skuProperties").Exists()

	if !hasSkuProperties {
		// 如果没有SKU属性，尝试从价格组件中获取第一个SKU作为默认
		firstSKU := gjson.Get(jsonStr, "priceComponent.skuPriceList.0.skuIdStr").String()
		if firstSKU != "" {
			skuImages[firstSKU] = "default_image_placeholder"
		}
		return skuImages
	}

	// 使用GJSON处理SKU属性 - 替代56行复杂的嵌套循环
	skuPropertiesPath := "skuComponent.productSKUPropertyList"
	if !gjson.Get(jsonStr, skuPropertiesPath).Exists() {
		skuPropertiesPath = "SKU.skuProperties"
	}

	gjson.Get(jsonStr, skuPropertiesPath).ForEach(func(_, prop gjson.Result) bool {
		gjson.Get(prop.Raw, "skuPropertyValues").ForEach(func(_, value gjson.Result) bool {
			// 直接提取属性ID，无需复杂类型断言
			propertyID := gjson.Get(value.Raw, "propertyValueIdLong").String()
			if propertyID == "" {
				if id := gjson.Get(value.Raw, "propertyValueIdLong").Float(); id > 0 {
					propertyID = fmt.Sprintf("%.0f", id)
				}
			}

			imageURL := gjson.Get(value.Raw, "skuPropertyImagePath").String()
			if propertyID == "" || imageURL == "" {
				return true
			}

			// 为所有包含此属性的SKU设置图片 - 使用GJSON直接查询SKU列表
			gjson.Get(jsonStr, "priceComponent.skuPriceList").ForEach(func(_, skuItem gjson.Result) bool {
				skuAttr := gjson.Get(skuItem.Raw, "skuAttr").String()
				skuPropIds := gjson.Get(skuItem.Raw, "skuPropIds").String()

				if strings.Contains(skuAttr, propertyID) || strings.Contains(skuPropIds, propertyID) {
					skuID := gjson.Get(skuItem.Raw, "skuIdStr").String()
					if skuID == "" {
						skuID = gjson.Get(skuItem.Raw, "skuId").String()
					}
					if skuID != "" {
						skuImages[skuID] = imageURL
					}
				}
				return true
			})
			return true
		})
		return true
	})

	return skuImages
}

// buildSKUItem 构建SKU项 - 使用GJSON简化
func (s *Spider) buildSKUItem(productID, title string, pathJson string,
	skuImages map[string]string, priceData map[string]map[string]any, spiderConfig *spiders.SpiderConfig) *models.ProductItem {

	// 使用GJSON提取SKU ID - 兼容新旧结构，无需复杂类型断言
	skuIDStr := gjson.Get(pathJson, "skuIdStr").String()
	if skuIDStr == "" {
		// 处理数字类型的skuId
		if skuIDFloat := gjson.Get(pathJson, "skuId").Float(); skuIDFloat > 0 {
			skuIDStr = fmt.Sprintf("%.0f", skuIDFloat)
		}
	}

	if skuIDStr == "" {
		return nil
	}

	// 提取价格信息 - 对应Python版本的价格提取逻辑
	priceInfo, exists := priceData[skuIDStr]
	if !exists {
		priceInfo = map[string]any{"price": 0.0, "price_value": "0"}
	}

	// 使用shared包解析价格
	priceValue := 0.0
	if price, ok := priceInfo["price"].(float64); ok {
		priceValue = price
	} else if priceVal, ok := priceInfo["price_value"].(string); ok {
		if parsedPrice, err := shared.ParsePrice(priceVal); err == nil {
			priceValue = parsedPrice
		}
	}

	// 获取货币配置 - 对应Python版本的currency配置
	currency := config.GetConfigString(spiderConfig, "currency", "€")

	// 使用GJSON提取库存信息 - 替代复杂的嵌套类型断言
	stock := int(gjson.Get(pathJson, "skuVal.availQuantity").Int())

	inStock := stock > 0

	// 构建ATC链接 - 对应Python版本的atcLink构建
	var atcLink *string = nil
	availability := "Out of Stock"
	if inStock {
		siteURL := spiderConfig.SiteURL
		baseURL := fmt.Sprintf("%s/p/trade/confirm.html?productId=%s&skuId=%s", siteURL, productID, skuIDStr)
		atcLinkStr := fmt.Sprintf("[x1](%s&quantity=1) | [x2](%s&quantity=2)", baseURL, baseURL)
		atcLink = &atcLinkStr
		availability = "In Stock"
	}

	// 提取图片URL
	imageURL := skuImages[skuIDStr]

	// 使用GJSON提取商品详情 - 无需类型断言
	skuAttr := gjson.Get(pathJson, "skuAttr").String()

	addition := ""
	if skuAttr != "" {
		// 从skuAttr中提取商品规格信息，格式如："14:691#1PC Single Box;200007763:201336100"
		parts := strings.Split(skuAttr, "#")
		if len(parts) > 1 {
			// 提取中间部分，如"1PC Single Box"
			specParts := strings.Split(parts[1], ";")
			addition = specParts[0]
		}
	}

	// 构建产品URL - 对应Python版本的product_url
	siteURL := spiderConfig.SiteURL
	productURL := fmt.Sprintf("%s/item/%s.html?pdp_ext_f={\"sku_id\":\"%s\"}", siteURL, productID, skuIDStr)

	// 创建产品数据项 - 对应Python版本的ProductItem创建
	item := &models.ProductItem{
		// 基础信息
		Name:      spiderConfig.Name,
		ProductID: productID,
		Title:     fmt.Sprintf("%s - %s", title, addition),
		URL:       productURL,
		Platform:  "aliexpress",

		// 价格信息
		Price:    priceValue,
		Currency: currency,

		// 库存信息
		Stock:         stock,
		InStock:       inStock,
		Availability:  availability,
		Country:       spiderConfig.Country,
		SiteURL:       siteURL,
		Notifications: spiderConfig.Notifications,
		ImageURL:      &imageURL,

		// 可选参数
		SkuID:   &skuIDStr,
		AtcLink: atcLink,

		// 商品详情
		Addition: &addition,

		// 系统元数据
		CrawledAt: time.Now(),
		Metadata: map[string]any{
			"spider_type": "aliexpress",
			"sku_path":    pathJson,
		},
	}

	return item
}

// ========== 批量处理能力接口实现 ==========

// SupportsBatchProcessing AliExpress爬虫不支持批量处理
func (s *Spider) SupportsBatchProcessing() bool {
	return false
}

// GetMaxBatchSize AliExpress爬虫只支持单个处理
func (s *Spider) GetMaxBatchSize() int {
	return 1
}

// PrepareBatchRequest AliExpress爬虫不支持批量处理，使用默认实现
// 此方法继承自BaseSpider，会返回错误
