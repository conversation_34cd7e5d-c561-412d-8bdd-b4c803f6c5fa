package core

import (
	"context"
	"fmt"
	"time"

	"go-monitor/pkg/httpclient/config"
	"go-monitor/pkg/httpclient/transport"
	"go-monitor/pkg/logging"
)

// Client 核心HTTP客户端
type Client struct {
	config *config.Config
	logger logging.Logger
}

// NewClient 创建新的HTTP客户端
func NewClient(cfg *config.Config) *Client {
	if cfg == nil {
		cfg = config.Default()
	}

	return &Client{
		config: cfg,
		logger: logging.GetLogger("httpclient.core"),
	}
}

// Do 执行HTTP请求
func (c *Client) Do(ctx context.Context, request *Request) (*Response, error) {
	var lastErr error

	for attempt := 0; attempt <= c.config.Retries; attempt++ {
		if attempt > 0 {
			c.logger.Debug("重试HTTP请求 URL：%s，尝试：%d", request.URL, attempt)

			// 重试延迟
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Duration(attempt) * time.Second):
			}
		}

		response, err := c.doSingleRequest(ctx, request)
		if err == nil {
			return response, nil
		}

		lastErr = err
		c.logger.Warn("HTTP请求失败 URL：%s，尝试：%d，错误：%s",
			request.URL, attempt+1, err.Error())
	}

	return nil, fmt.Errorf("HTTP请求失败，已重试%d次: %w", c.config.Retries, lastErr)
}

// doSingleRequest 执行单次HTTP请求
func (c *Client) doSingleRequest(ctx context.Context, request *Request) (*Response, error) {
	// 转换为transport请求
	transportReq := c.convertToTransportRequest(request)

	// 创建HTTP客户端
	httpClient, err := transport.CreateHTTPClient(c.config, transportReq)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP客户端失败: %w", err)
	}
	defer httpClient.CloseIdleConnections()

	// 执行请求
	transportResp, err := transport.DoHTTPRequest(ctx, httpClient, transportReq, c.logger)
	if err != nil {
		return nil, err
	}

	// 转换响应
	return c.convertFromTransportResponse(transportResp), nil
}

// convertToTransportRequest 转换为transport请求
func (c *Client) convertToTransportRequest(req *Request) *transport.Request {
	return &transport.Request{
		URL:          req.URL,
		Method:       req.Method,
		Headers:      req.Headers,
		Body:         req.Body,
		Params:       req.Params,
		Cookies:      req.Cookies,
		ReverseProxy: req.ReverseProxy,
		Proxy:        req.Proxy,
		Timeout:      req.Timeout,
		Meta:         req.Meta,
	}
}

// convertFromTransportResponse 转换transport响应
func (c *Client) convertFromTransportResponse(resp *transport.Response) *Response {
	var originalReq *Request
	if resp.Request != nil {
		originalReq = &Request{
			URL:          resp.Request.URL,
			Method:       resp.Request.Method,
			Headers:      resp.Request.Headers,
			Body:         resp.Request.Body,
			Params:       resp.Request.Params,
			Cookies:      resp.Request.Cookies,
			ReverseProxy: resp.Request.ReverseProxy,
			Proxy:        resp.Request.Proxy,
			Timeout:      resp.Request.Timeout,
			Meta:         resp.Request.Meta,
		}
	}

	return &Response{
		StatusCode:   resp.StatusCode,
		Headers:      resp.Headers,
		Body:         resp.Body,
		URL:          resp.URL,
		ResponseTime: resp.ResponseTime,
		Request:      originalReq,
	}
}

// Get 发起GET请求
func (c *Client) Get(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	req := &Request{
		URL:     url,
		Method:  "GET",
		Headers: headers,
	}
	return c.Do(ctx, req)
}

// Post 发起POST请求
func (c *Client) Post(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error) {
	req := &Request{
		URL:     url,
		Method:  "POST",
		Headers: headers,
		Body:    body,
	}
	return c.Do(ctx, req)
}

// PostJSON 发起JSON POST请求
func (c *Client) PostJSON(ctx context.Context, url string, data interface{}, headers map[string]string) (*Response, error) {
	req, err := NewJSONRequest("POST", url, data)
	if err != nil {
		return nil, fmt.Errorf("创建JSON请求失败: %w", err)
	}

	// 添加额外的headers
	for k, v := range headers {
		req.Headers[k] = v
	}

	return c.Do(ctx, req)
}
