package core

import (
	"encoding/json"
	"fmt"
	"time"
)

// Request HTTP请求
type Request struct {
	URL          string                 // 基础请求URL（不含查询参数）
	Method       string                 // HTTP方法
	Headers      map[string]string      // 请求头
	Body         []byte                 // 请求体
	Params       map[string]string      // 查询参数
	Cookies      map[string]string      // Cookies
	ReverseProxy string                 // 反向代理URL
	Proxy        string                 // 代理地址
	Timeout      time.Duration          // 超时时间
	Meta         map[string]interface{} // 元数据
}

// Response HTTP响应
type Response struct {
	StatusCode   int                 // 状态码
	Headers      map[string][]string // 响应头
	Body         []byte              // 响应体
	URL          string              // 最终请求URL
	ResponseTime time.Duration       // 响应时间
	Request      *Request            // 原始请求
}

// NewRequest 创建新的HTTP请求
func NewRequest(method, url string) *Request {
	return &Request{
		Method:  method,
		URL:     url,
		Headers: make(map[string]string),
		Params:  make(map[string]string),
		Cookies: make(map[string]string),
		Meta:    make(map[string]interface{}),
	}
}

// NewJSONRequest 创建JSON请求
func NewJSONRequest(method, url string, data interface{}) (*Request, error) {
	req := NewRequest(method, url)

	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("序列化JSON数据失败: %w", err)
		}
		req.Body = jsonData
		req.Headers["Content-Type"] = "application/json; charset=utf-8"
	}

	return req, nil
}

// SetHeader 设置请求头
func (r *Request) SetHeader(key, value string) *Request {
	r.Headers[key] = value
	return r
}

// SetParam 设置查询参数
func (r *Request) SetParam(key, value string) *Request {
	r.Params[key] = value
	return r
}

// SetCookie 设置Cookie
func (r *Request) SetCookie(key, value string) *Request {
	r.Cookies[key] = value
	return r
}

// SetMeta 设置元数据
func (r *Request) SetMeta(key string, value interface{}) *Request {
	r.Meta[key] = value
	return r
}

// Clone 克隆请求
func (r *Request) Clone() *Request {
	clone := &Request{
		URL:          r.URL,
		Method:       r.Method,
		Headers:      make(map[string]string),
		Params:       make(map[string]string),
		Cookies:      make(map[string]string),
		ReverseProxy: r.ReverseProxy,
		Proxy:        r.Proxy,
		Timeout:      r.Timeout,
		Meta:         make(map[string]interface{}),
	}

	// 复制Body
	if r.Body != nil {
		clone.Body = make([]byte, len(r.Body))
		copy(clone.Body, r.Body)
	}

	// 复制映射
	for k, v := range r.Headers {
		clone.Headers[k] = v
	}
	for k, v := range r.Params {
		clone.Params[k] = v
	}
	for k, v := range r.Cookies {
		clone.Cookies[k] = v
	}
	for k, v := range r.Meta {
		clone.Meta[k] = v
	}

	return clone
}

// GetMetadataString 获取字符串元数据
func (r *Request) GetMetadataString(key, defaultValue string) string {
	if value, exists := r.Meta[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return defaultValue
}

// IsJSONContent 检查是否为JSON内容
func (r *Response) IsJSONContent() bool {
	contentType := ""
	if values, exists := r.Headers["Content-Type"]; exists && len(values) > 0 {
		contentType = values[0]
	}
	return len(contentType) > 0 &&
		(contentType == "application/json" ||
			contentType == "application/json; charset=utf-8")
}

// IsSuccess 检查响应是否成功
func (r *Response) IsSuccess() bool {
	return r.StatusCode >= 200 && r.StatusCode < 300
}

// BodyString 获取响应体字符串
func (r *Response) BodyString() string {
	return string(r.Body)
}
