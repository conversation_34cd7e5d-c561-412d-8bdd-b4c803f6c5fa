# HTTP Client Framework

一个功能强大、模块化的 Go HTTP 客户端框架，专为爬虫和 API 调用设计，集成了 TLS 指纹管理和风控检测功能。

## 功能特性

### 🚀 核心功能
- **模块化架构**：清晰的包结构，易于扩展和维护
- **链式构建器**：流畅的 API 设计，易于使用
- **配置灵活**：支持多种配置选项和自定义
- **重试机制**：内置重试逻辑，提高请求成功率
- **压缩支持**：自动处理 gzip、deflate、brotli、zstd 压缩

### 🔒 TLS 指纹管理
- **智能轮换**：基于健康评分的自动指纹切换
- **地区适配**：针对不同地区（PopMart INTL、ASIA、NA）的专用配置
- **风控检测**：自动识别被风控的响应并切换指纹
- **健康监控**：实时跟踪指纹使用效果和成功率
- **动态配置**：支持运行时调整轮换策略

### 🕷️ 爬虫优化
- **预设模板**：Amazon、AliExpress、PopMart 等常用爬虫配置
- **请求伪装**：模拟真实浏览器行为
- **代理支持**：HTTP/SOCKS 代理配置
- **会话管理**：Cookie 和会话状态维护

## 快速开始

### 基础使用

```go
package main

import (
    "context"
    "fmt"
    "log"
    
    "go-monitor/pkg/httpclient"
    "go-monitor/pkg/httpclient/builder"
)

func main() {
    // 创建默认客户端
    client := httpclient.DefaultClient()
    
    // 简单 GET 请求
    response, err := client.Get(context.Background(), "https://httpbin.org/get", nil)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("Status: %d, Body: %s\n", response.StatusCode, string(response.Body))
}
```

### 使用请求构建器

```go
// 构建复杂请求
req := builder.Post("https://api.example.com/data").
    Header("Authorization", "Bearer token").
    JSONBody(map[string]string{"key": "value"}).
    Param("format", "json").
    Build()

response, err := client.Do(context.Background(), req)
```

### 爬虫专用构建器

```go
// Amazon 爬虫请求
req := builder.AmazonRequest("https://www.amazon.com/dp/B08N5WRWNW").
    Param("ref", "sr_1_1").
    Build()

// PopMart API 请求
req := builder.PopMartRequest("https://prod-intl-api.popmart.com/shop/v1/search").
    JSONBody(map[string]interface{}{
        "term": "SKULLPANDA",
        "pageSize": 100,
    }).
    Build()
```

## 高级配置

### 自定义客户端配置

```go
import "go-monitor/pkg/httpclient/config"

cfg := config.Default().
    WithTimeout(30*time.Second).
    WithRetries(3).
    WithUserAgent("Custom-Agent/1.0").
    WithTLSFingerprint(true).
    WithProxy("http://proxy.example.com:8080")

client := httpclient.NewClient(cfg)
```

### TLS 指纹管理

```go
// 初始化指纹管理配置
fingerprintConfig := map[string]interface{}{
    "enabled": true,
    "rotation": map[string]interface{}{
        "max_requests_per_profile":    100,
        "max_profile_duration":        "30m",
        "random_rotation_probability": 0.1,
    },
    "risk_control": map[string]interface{}{
        "blocked_status_codes": []int{418, 470, 471, 403, 429},
        "blocked_keywords":     []string{"blocked", "banned", "captcha"},
    },
}

// 初始化指纹管理
err := httpclient.InitializeFingerprint(fingerprintConfig)
if err != nil {
    log.Fatal(err)
}

// 创建支持 TLS 指纹的客户端
client := httpclient.NewConfiguredClient(cfg)

// 获取指纹统计
stats := httpclient.GetFingerprintStats()
fmt.Printf("指纹统计: %+v\n", stats)
```

## 架构设计

### 包结构

```
pkg/httpclient/
├── httpclient.go          # 主入口和统一接口
├── config/               # 配置管理
│   └── config.go
├── core/                # 核心HTTP客户端
│   ├── client.go
│   └── types.go
├── transport/           # 传输层实现
│   ├── transport.go
│   └── types.go
├── fingerprint/        # TLS指纹管理
│   ├── manager.go
│   ├── health.go
│   ├── types.go
│   └── profiles.go
├── builder/            # 请求构建器
│   └── builder.go
└── README.md
```

### 设计原则

1. **模块分离**：每个包职责单一，接口清晰
2. **依赖注入**：通过接口避免循环依赖
3. **配置驱动**：所有行为都可通过配置控制
4. **可扩展性**：易于添加新的指纹配置和爬虫模板

## TLS 指纹系统详解

### 指纹池管理

框架维护多个指纹池：
- **通用指纹池**：Chrome、Safari、Firefox等浏览器指纹
- **专用指纹池**：PopMart等特定站点的优化指纹
- **健康指纹池**：当前可用的健康指纹
- **黑名单池**：被风控的指纹，定期清理

### 自动轮换策略

指纹轮换基于以下条件：
- **请求数限制**：单个指纹达到最大请求数
- **时间限制**：指纹使用时间超过阈值
- **健康评分**：指纹健康评分低于阈值
- **随机轮换**：按配置概率随机切换
- **风控检测**：检测到风控后立即切换

### 风控检测机制

多层次风控检测：
1. **状态码检测**：418、470、471、403、429等
2. **关键词检测**：响应内容包含"blocked"、"banned"等
3. **响应时间异常**：异常长或异常短的响应时间
4. **连续失败**：同一指纹连续失败次数

## 性能优化

### 连接池管理
- 智能连接复用
- 自动空闲连接清理
- 可配置的连接池大小

### 压缩处理
- 自动检测和处理多种压缩格式
- 透明的解压缩，无需手动处理

### 重试策略
- 指数退避重试
- 可配置重试次数和间隔
- 智能错误分类

## 监控和调试

### 日志系统
框架集成了完整的日志系统：
```go
// 启用调试日志
cfg := config.Default().WithDebugLog(true)
```

### 指纹统计
实时监控指纹使用情况：
```go
stats := httpclient.GetFingerprintStats()
// 查看各地区指纹状态、成功率、使用次数等
```

### 请求追踪
每个请求都包含详细的元数据：
- TLS指纹名称
- 响应时间
- 重试次数
- 错误信息

## 最佳实践

### 爬虫开发建议

1. **使用专用构建器**：为不同站点使用对应的构建器
2. **启用指纹管理**：对于反爬严格的站点启用TLS指纹
3. **合理设置重试**：根据站点特性调整重试策略
4. **监控指纹健康**：定期检查指纹统计信息

### 配置优化

1. **超时设置**：根据网络环境和目标站点特性设置合理超时
2. **并发控制**：通过连接池大小控制并发请求数
3. **代理轮换**：结合代理池使用以分散请求来源

### 错误处理

```go
response, err := client.Do(ctx, req)
if err != nil {
    // 检查是否为风控错误
    if transport.IsBlockedResponse(0, err) {
        // 处理风控情况
        log.Warn("检测到风控，等待指纹轮换")
        time.Sleep(time.Minute)
        // 重试请求
    }
    return err
}
```

## 扩展开发

### 添加新的指纹配置

在 `fingerprint/profiles.go` 中添加新的指纹：

```go
{
    Name:       "NewSite_Profile",
    MinVersion: tls.VersionTLS12,
    MaxVersion: tls.VersionTLS13,
    CipherSuites: []uint16{
        // 自定义密码套件
    },
    // ... 其他配置
}
```

### 自定义构建器

在 `builder/builder.go` 中添加新的构建器：

```go
func NewSiteRequest(url string) *RequestBuilder {
    return Post(url).
        ContentType("application/json").
        UserAgent("Custom-Agent").
        Header("Custom-Header", "value")
}
```

## 故障排除

### 常见问题

1. **TLS握手失败**
   - 检查指纹配置是否正确
   - 确认目标站点支持的TLS版本

2. **请求被拒绝**
   - 检查User-Agent和其他headers
   - 确认代理设置是否正确

3. **性能问题**
   - 调整连接池大小
   - 检查超时设置
   - 监控指纹轮换频率

### 调试模式

```go
// 启用详细日志
cfg := config.Default().WithDebugLog(true)

// 查看详细的请求/响应信息
client := httpclient.NewClient(cfg)
```

## 贡献指南

欢迎提交 Issue 和 Pull Request 来完善这个框架。请确保：

1. 代码符合 Go 规范
2. 添加适当的测试
3. 更新相关文档
4. 保持向后兼容性

## 许可证

本项目采用 MIT 许可证。 