package httpclient

import (
	"context"
	"testing"
	"time"

	"go-monitor/pkg/httpclient/builder"
	"go-monitor/pkg/httpclient/config"
)

// TestBasicHTTPClient 测试基础HTTP客户端功能
func TestBasicHTTPClient(t *testing.T) {
	client := DefaultClient()

	t.Run("SimpleGET", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		response, err := client.Get(ctx, "https://httpbin.org/get", nil)
		if err != nil {
			t.Fatalf("GET请求失败: %v", err)
		}

		if response.StatusCode != 200 {
			t.Errorf("期望状态码200，实际：%d", response.StatusCode)
		}

		if len(response.Body) == 0 {
			t.Error("响应体为空")
		}

		t.Logf("GET请求成功，状态码：%d，响应大小：%d，响应时间：%v",
			response.StatusCode, len(response.Body), response.ResponseTime)
	})

	t.Run("JSONPost", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		data := map[string]string{"test": "data"}
		response, err := client.PostJSON(ctx, "https://httpbin.org/post", data, nil)
		if err != nil {
			t.Fatalf("POST请求失败: %v", err)
		}

		if response.StatusCode != 200 {
			t.Errorf("期望状态码200，实际：%d", response.StatusCode)
		}

		t.Logf("POST请求成功，状态码：%d，响应大小：%d",
			response.StatusCode, len(response.Body))
	})
}

// TestRequestBuilder 测试请求构建器
func TestRequestBuilder(t *testing.T) {
	t.Run("BuildGETRequest", func(t *testing.T) {
		req := builder.Get("https://example.com").
			Header("User-Agent", "Test-Agent").
			Param("q", "test").
			Cookie("session", "12345").
			Build()

		if req.Method != "GET" {
			t.Errorf("期望方法GET，实际：%s", req.Method)
		}

		if req.URL != "https://example.com" {
			t.Errorf("期望URL https://example.com，实际：%s", req.URL)
		}

		if req.Headers["User-Agent"] != "Test-Agent" {
			t.Errorf("User-Agent设置失败")
		}

		if req.Params["q"] != "test" {
			t.Errorf("查询参数设置失败")
		}

		if req.Cookies["session"] != "12345" {
			t.Errorf("Cookie设置失败")
		}
	})

	t.Run("BuildJSONRequest", func(t *testing.T) {
		data := map[string]string{"key": "value"}
		req := builder.Post("https://api.example.com").
			JSONBody(data).
			Build()

		if req.Method != "POST" {
			t.Errorf("期望方法POST，实际：%s", req.Method)
		}

		if req.Headers["Content-Type"] != "application/json; charset=utf-8" {
			t.Errorf("Content-Type设置失败")
		}

		if len(req.Body) == 0 {
			t.Error("JSON请求体为空")
		}
	})
}

// TestConfigurableClient 测试可配置客户端
func TestConfigurableClient(t *testing.T) {
	cfg := config.Default().
		WithTimeout(30 * time.Second).
		WithRetries(2).
		WithUserAgent("Test-Client/1.0").
		WithTLSFingerprint(true)

	client := NewClient(cfg)

	t.Run("WithCustomConfig", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// 这里使用一个简单的请求测试配置是否生效
		req := NewRequest("GET", "https://httpbin.org/user-agent")
		req.SetHeader("User-Agent", "Test-Client/1.0")

		response, err := client.Do(ctx, req)
		if err != nil {
			t.Fatalf("配置客户端请求失败: %v", err)
		}

		if response.StatusCode != 200 {
			t.Errorf("期望状态码200，实际：%d", response.StatusCode)
		}

		t.Logf("配置客户端请求成功，响应时间：%v", response.ResponseTime)
	})
}

// TestTLSFingerprintIntegration 测试TLS指纹集成
func TestTLSFingerprintIntegration(t *testing.T) {
	// 初始化指纹管理配置
	fingerprintConfig := map[string]interface{}{
		"enabled": true,
		"rotation": map[string]interface{}{
			"max_requests_per_profile":    50,
			"max_profile_duration":        "15m",
			"random_rotation_probability": 0.2,
		},
		"risk_control": map[string]interface{}{
			"blocked_status_codes": []int{418, 470, 471, 403, 429},
			"blocked_keywords":     []string{"blocked", "banned", "captcha"},
		},
	}

	// 初始化指纹管理
	err := InitializeFingerprint(fingerprintConfig)
	if err != nil {
		t.Fatalf("初始化指纹管理失败: %v", err)
	}

	// 创建启用TLS指纹的客户端
	cfg := config.Default().WithTLSFingerprint(true)
	client := NewConfiguredClient(cfg)

	t.Run("WithTLSFingerprint", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 使用PopMart域名测试（会触发TLS指纹管理）
		// 注意：这个测试可能会失败，因为域名可能不存在或不可访问
		req := builder.PopMartRequest("https://prod-intl-api.popmart.com/test").
			JSONBody(map[string]string{"test": "data"}).
			Build()

		response, err := client.Do(ctx, req)
		// 由于这是一个测试域名，我们主要关心是否触发了TLS指纹管理
		// 而不是请求是否成功
		if err != nil {
			t.Logf("PopMart测试请求失败（预期）: %v", err)
		} else {
			t.Logf("PopMart测试请求成功，状态码：%d", response.StatusCode)
		}

		// 获取指纹统计信息
		stats := GetFingerprintStats()
		if stats != nil {
			t.Logf("指纹统计信息：%+v", stats)
		}
	})
}

// BenchmarkHTTPClient 性能基准测试
func BenchmarkHTTPClient(b *testing.B) {
	client := DefaultClient()
	ctx := context.Background()

	b.Run("SimpleGET", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			response, err := client.Get(ctx, "https://httpbin.org/get", nil)
			if err != nil {
				b.Fatalf("GET请求失败: %v", err)
			}
			if response.StatusCode != 200 {
				b.Fatalf("状态码异常: %d", response.StatusCode)
			}
		}
	})

	b.Run("RequestBuilder", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			req := builder.Get("https://httpbin.org/get").
				Header("User-Agent", "Benchmark-Client").
				Param("test", "value").
				Build()

			response, err := client.Do(ctx, req)
			if err != nil {
				b.Fatalf("请求失败: %v", err)
			}
			if response.StatusCode != 200 {
				b.Fatalf("状态码异常: %d", response.StatusCode)
			}
		}
	})
}
