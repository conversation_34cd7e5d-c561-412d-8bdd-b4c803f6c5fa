package httpclient

import (
	"context"
	"encoding/json"
	"net/url"
	"strings"

	"go-monitor/pkg/httpclient/config"
	"go-monitor/pkg/httpclient/core"
	"go-monitor/pkg/logging"
)

// SpiderConfig 爬虫配置接口 - 完全按照原始设计
type SpiderConfig interface {
	GetPlatform() string
	GetName() string
	GetCountry() string
	GetSpiderSettings() map[string]interface{}
}

// SpiderRequestBuilder 爬虫请求构建器 - 完全按照原始设计
type SpiderRequestBuilder struct {
	platform string
	baseURL  string
	method   string
	headers  map[string]string
	params   map[string]string
	metadata map[string]interface{}
	body     []byte
}

// NewSpiderRequestBuilder 创建爬虫请求构建器 - 完全按照原始设计
func NewSpiderRequestBuilder(platform, baseURL string) *SpiderRequestBuilder {
	return &SpiderRequestBuilder{
		platform: platform,
		baseURL:  baseURL,
		method:   "GET",
		headers:  make(map[string]string),
		params:   make(map[string]string),
		metadata: make(map[string]interface{}),
	}
}

// SetMethod 设置HTTP方法
func (b *SpiderRequestBuilder) SetMethod(method string) *SpiderRequestBuilder {
	b.method = method
	return b
}

// AddHeader 添加请求头
func (b *SpiderRequestBuilder) AddHeader(key, value string) *SpiderRequestBuilder {
	b.headers[key] = value
	return b
}

// AddHeaders 批量添加请求头
func (b *SpiderRequestBuilder) AddHeaders(headers map[string]string) *SpiderRequestBuilder {
	for k, v := range headers {
		b.headers[k] = v
	}
	return b
}

// AddParam 添加查询参数
func (b *SpiderRequestBuilder) AddParam(key, value string) *SpiderRequestBuilder {
	b.params[key] = value
	return b
}

// AddParams 批量添加查询参数
func (b *SpiderRequestBuilder) AddParams(params map[string]string) *SpiderRequestBuilder {
	for k, v := range params {
		b.params[k] = v
	}
	return b
}

// SetBody 设置请求体
func (b *SpiderRequestBuilder) SetBody(body []byte) *SpiderRequestBuilder {
	b.body = body
	return b
}

// SetJSONBody 设置JSON请求体
func (b *SpiderRequestBuilder) SetJSONBody(data interface{}) *SpiderRequestBuilder {
	if data == nil {
		return b
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		b.metadata["json_marshal_error"] = err.Error()
		return b
	}

	b.body = jsonData
	b.headers["Content-Type"] = "application/json; charset=utf-8"
	return b
}

// SetMetadata 设置元数据
func (b *SpiderRequestBuilder) SetMetadata(key string, value interface{}) *SpiderRequestBuilder {
	b.metadata[key] = value
	return b
}

// SetSpiderMetadata 设置爬虫标准元数据 - 完全按照原始设计
func (b *SpiderRequestBuilder) SetSpiderMetadata(config SpiderConfig, productID string) *SpiderRequestBuilder {
	if config != nil {
		b.metadata["spider_type"] = config.GetPlatform()
		b.metadata["name"] = config.GetName()
		b.metadata["country"] = config.GetCountry()

		if settings := config.GetSpiderSettings(); settings != nil {
			if proxyGroup, ok := settings["proxies"].([]interface{}); ok {
				b.metadata["proxies"] = proxyGroup
			}
			if cookieGroup, ok := settings["cookies"].([]interface{}); ok {
				b.metadata["cookies"] = cookieGroup
			}
		}
	}

	if productID != "" {
		b.metadata["product_id"] = productID
	}

	return b
}

// SetCommonHeaders 设置常用的爬虫请求头
func (b *SpiderRequestBuilder) SetCommonHeaders() *SpiderRequestBuilder {
	b.headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
	b.headers["Accept-Language"] = "en-US,en;q=0.5"
	b.headers["Accept-Encoding"] = "gzip, deflate"
	b.headers["Connection"] = "keep-alive"
	b.headers["Upgrade-Insecure-Requests"] = "1"
	b.headers["Sec-Fetch-Dest"] = "document"
	b.headers["Sec-Fetch-Mode"] = "navigate"
	b.headers["Sec-Fetch-Site"] = "none"
	b.headers["Sec-Fetch-User"] = "?1"
	return b
}

// SetAPIHeaders 设置API请求头
func (b *SpiderRequestBuilder) SetAPIHeaders() *SpiderRequestBuilder {
	b.headers["Accept"] = "application/json, text/plain, */*"
	b.headers["Content-Type"] = "application/json"
	return b
}

// SetFormHeaders 设置表单请求头
func (b *SpiderRequestBuilder) SetFormHeaders() *SpiderRequestBuilder {
	b.headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8"
	return b
}

// Build 构建请求对象 - 完全按照原始设计
func (b *SpiderRequestBuilder) Build() *core.Request {
	return &core.Request{
		URL:     b.baseURL,
		Method:  b.method,
		Headers: b.headers,
		Params:  b.params,
		Body:    b.body,
		Meta:    b.metadata,
	}
}

// ============ 便捷的爬虫请求创建函数 ============

// CreateAmazonRequest 创建Amazon爬虫请求 - 完全按照原始设计
func CreateAmazonRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("amazon", baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}

// CreateAliExpressRequest 创建AliExpress爬虫请求 - 完全按照原始设计
func CreateAliExpressRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("aliexpress", baseURL).
		SetMethod("POST").
		SetFormHeaders().
		SetSpiderMetadata(config, productID)
}

// CreatePopMartRequest 创建PopMart爬虫请求 - 完全按照原始设计
func CreatePopMartRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("popmart", baseURL).
		SetAPIHeaders().
		SetSpiderMetadata(config, productID)
}

// CreateGenericSpiderRequest 创建通用爬虫请求 - 完全按照原始设计
func CreateGenericSpiderRequest(platform, baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder(platform, baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}

// ============ HTTP客户端兼容性函数 ============

// RequestConfig 兼容性类型别名
type RequestConfig = config.Config

// LegacyHTTPClient 包装器，用于向后兼容
type LegacyHTTPClient struct {
	client *core.Client
}

// CloseIdleConnections 兼容*http.Client的方法
func (c *LegacyHTTPClient) CloseIdleConnections() {
	// 新架构中的Client不直接暴露这个方法
	// 这里我们可以添加适当的清理逻辑，或者简单地忽略
}

// Get 实现HTTPClient接口
func (c *LegacyHTTPClient) Get(ctx context.Context, url string, headers map[string]string) (*core.Response, error) {
	return c.client.Get(ctx, url, headers)
}

// Post 实现HTTPClient接口
func (c *LegacyHTTPClient) Post(ctx context.Context, url string, body []byte, headers map[string]string) (*core.Response, error) {
	return c.client.Post(ctx, url, body, headers)
}

// PostJSON 实现HTTPClient接口
func (c *LegacyHTTPClient) PostJSON(ctx context.Context, url string, data interface{}, headers map[string]string) (*core.Response, error) {
	return c.client.PostJSON(ctx, url, data, headers)
}

// Do 实现HTTPClient接口
func (c *LegacyHTTPClient) Do(ctx context.Context, request *core.Request) (*core.Response, error) {
	return c.client.Do(ctx, request)
}

// NewLegacyHTTPClient 创建兼容的HTTP客户端
func NewLegacyHTTPClient(cfg *config.Config) *LegacyHTTPClient {
	// 使用新的核心客户端
	coreClient := core.NewClient(cfg)
	return &LegacyHTTPClient{client: coreClient}
}

// CreateStrictBrowserLikeClient 创建严格的浏览器样式客户端 - service.go需要的函数
func CreateStrictBrowserLikeClient(globalConfig interface{}, requestConfig *config.Config) (*LegacyHTTPClient, error) {
	cfg := config.Default()
	if requestConfig != nil {
		cfg = requestConfig
	}

	// 启用TLS指纹管理，模拟真实浏览器
	cfg = cfg.WithTLSFingerprint(true).
		WithUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

	// 使用core.NewClient创建客户端
	coreClient := core.NewClient(cfg)
	return &LegacyHTTPClient{client: coreClient}, nil
}

// CreateBrowserLikeClient 创建浏览器样式客户端 - service.go需要的函数
func CreateBrowserLikeClient(globalConfig interface{}, requestConfig *config.Config) (*LegacyHTTPClient, error) {
	cfg := config.Default()
	if requestConfig != nil {
		cfg = requestConfig
	}

	cfg = cfg.WithUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 使用core.NewClient创建客户端
	coreClient := core.NewClient(cfg)
	return &LegacyHTTPClient{client: coreClient}, nil
}

// CreateClientForRequest 创建用于特定请求的客户端 - 兼容service.go的期望
func CreateClientForRequest(globalConfig interface{}, requestConfig *config.Config) (*LegacyHTTPClient, error) {
	cfg := config.Default()
	if requestConfig != nil {
		cfg = requestConfig
	}
	// 使用core.NewClient创建客户端
	coreClient := core.NewClient(cfg)
	return &LegacyHTTPClient{client: coreClient}, nil
}

// DoWithClient 使用指定的HTTP Client执行请求 - 完全按照原始设计
func DoWithClient(ctx context.Context, httpClient interface{}, request *core.Request, logger logging.Logger) (*core.Response, error) {
	// 兼容处理：支持LegacyHTTPClient或直接使用默认客户端
	if legacyClient, ok := httpClient.(*LegacyHTTPClient); ok {
		return legacyClient.Do(ctx, request)
	} else {
		// 对于其他类型的client，使用默认客户端
		defaultClient := core.NewClient(nil)
		return defaultClient.Do(ctx, request)
	}
}

// ============ 辅助函数 ============

// BuildURL 构建URL
func BuildURL(baseURL, path string, params map[string]string) string {
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL
	}

	u.Path = strings.TrimSuffix(u.Path, "/") + "/" + strings.TrimPrefix(path, "/")

	if len(params) > 0 {
		q := u.Query()
		for k, v := range params {
			q.Add(k, v)
		}
		u.RawQuery = q.Encode()
	}

	return u.String()
}

// ExtractCookiesFromResponse 从HTTP响应中提取cookies
func ExtractCookiesFromResponse(resp *core.Response) map[string]string {
	cookies := make(map[string]string)

	if setCookieHeaders, exists := resp.Headers["Set-Cookie"]; exists {
		for _, setCookie := range setCookieHeaders {
			name, value := parseCookieHeader(setCookie)
			if name != "" {
				cookies[name] = value
			}
		}
	}

	return cookies
}

// parseCookieHeader 解析单个Set-Cookie头
func parseCookieHeader(setCookie string) (string, string) {
	parts := strings.Split(setCookie, ";")
	if len(parts) == 0 {
		return "", ""
	}

	nameValue := strings.TrimSpace(parts[0])
	equalIndex := strings.Index(nameValue, "=")
	if equalIndex == -1 {
		return "", ""
	}

	name := strings.TrimSpace(nameValue[:equalIndex])
	value := strings.TrimSpace(nameValue[equalIndex+1:])

	// 移除cookie值首尾的双引号（如果存在）
	if len(value) >= 2 && value[0] == '"' && value[len(value)-1] == '"' {
		value = value[1 : len(value)-1]
	}

	return name, value
}
