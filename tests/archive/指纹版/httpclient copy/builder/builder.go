package builder

import (
	"encoding/json"
	"fmt"

	"go-monitor/pkg/httpclient/core"
)

// RequestBuilder 请求构建器
type RequestBuilder struct {
	request *core.Request
}

// NewBuilder 创建新的请求构建器
func NewBuilder(method, url string) *RequestBuilder {
	return &RequestBuilder{
		request: core.NewRequest(method, url),
	}
}

// Get 创建GET请求构建器
func Get(url string) *RequestBuilder {
	return NewBuilder("GET", url)
}

// Post 创建POST请求构建器
func Post(url string) *RequestBuilder {
	return NewBuilder("POST", url)
}

// Put 创建PUT请求构建器
func Put(url string) *RequestBuilder {
	return NewBuilder("PUT", url)
}

// Delete 创建DELETE请求构建器
func Delete(url string) *RequestBuilder {
	return NewBuilder("DELETE", url)
}

// Header 设置请求头
func (b *RequestBuilder) Header(key, value string) *RequestBuilder {
	b.request.SetHeader(key, value)
	return b
}

// Headers 批量设置请求头
func (b *RequestBuilder) Headers(headers map[string]string) *RequestBuilder {
	for k, v := range headers {
		b.request.SetHeader(k, v)
	}
	return b
}

// Param 设置查询参数
func (b *RequestBuilder) Param(key, value string) *RequestBuilder {
	b.request.SetParam(key, value)
	return b
}

// Params 批量设置查询参数
func (b *RequestBuilder) Params(params map[string]string) *RequestBuilder {
	for k, v := range params {
		b.request.SetParam(k, v)
	}
	return b
}

// Cookie 设置Cookie
func (b *RequestBuilder) Cookie(key, value string) *RequestBuilder {
	b.request.SetCookie(key, value)
	return b
}

// Cookies 批量设置Cookies
func (b *RequestBuilder) Cookies(cookies map[string]string) *RequestBuilder {
	for k, v := range cookies {
		b.request.SetCookie(k, v)
	}
	return b
}

// Body 设置请求体
func (b *RequestBuilder) Body(body []byte) *RequestBuilder {
	b.request.Body = body
	return b
}

// JSONBody 设置JSON请求体
func (b *RequestBuilder) JSONBody(data interface{}) *RequestBuilder {
	if data != nil {
		if jsonData, err := json.Marshal(data); err == nil {
			b.request.Body = jsonData
			b.request.SetHeader("Content-Type", "application/json; charset=utf-8")
		}
	}
	return b
}

// TextBody 设置文本请求体
func (b *RequestBuilder) TextBody(text string) *RequestBuilder {
	b.request.Body = []byte(text)
	b.request.SetHeader("Content-Type", "text/plain; charset=utf-8")
	return b
}

// FormBody 设置表单请求体
func (b *RequestBuilder) FormBody(data map[string]string) *RequestBuilder {
	var parts []string
	for k, v := range data {
		parts = append(parts, fmt.Sprintf("%s=%s", k, v))
	}
	formData := ""
	for i, part := range parts {
		if i > 0 {
			formData += "&"
		}
		formData += part
	}
	b.request.Body = []byte(formData)
	b.request.SetHeader("Content-Type", "application/x-www-form-urlencoded")
	return b
}

// Proxy 设置代理
func (b *RequestBuilder) Proxy(proxyURL string) *RequestBuilder {
	b.request.Proxy = proxyURL
	return b
}

// ReverseProxy 设置反向代理
func (b *RequestBuilder) ReverseProxy(reverseProxyURL string) *RequestBuilder {
	b.request.ReverseProxy = reverseProxyURL
	return b
}

// Meta 设置元数据
func (b *RequestBuilder) Meta(key string, value interface{}) *RequestBuilder {
	b.request.SetMeta(key, value)
	return b
}

// UserAgent 设置User-Agent
func (b *RequestBuilder) UserAgent(userAgent string) *RequestBuilder {
	b.request.SetHeader("User-Agent", userAgent)
	return b
}

// Accept 设置Accept头
func (b *RequestBuilder) Accept(accept string) *RequestBuilder {
	b.request.SetHeader("Accept", accept)
	return b
}

// ContentType 设置Content-Type头
func (b *RequestBuilder) ContentType(contentType string) *RequestBuilder {
	b.request.SetHeader("Content-Type", contentType)
	return b
}

// Authorization 设置Authorization头
func (b *RequestBuilder) Authorization(auth string) *RequestBuilder {
	b.request.SetHeader("Authorization", auth)
	return b
}

// BearerToken 设置Bearer Token
func (b *RequestBuilder) BearerToken(token string) *RequestBuilder {
	b.request.SetHeader("Authorization", "Bearer "+token)
	return b
}

// BasicAuth 设置基础认证
func (b *RequestBuilder) BasicAuth(username, password string) *RequestBuilder {
	// 这里应该进行base64编码，简化实现
	auth := fmt.Sprintf("%s:%s", username, password)
	b.request.SetHeader("Authorization", "Basic "+auth)
	return b
}

// Build 构建请求
func (b *RequestBuilder) Build() *core.Request {
	return b.request
}

// 便捷的爬虫请求构建器

// AmazonRequest 创建Amazon爬虫请求构建器
func AmazonRequest(url string) *RequestBuilder {
	return Get(url).
		UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36").
		Accept("text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8").
		Header("Accept-Language", "en-US,en;q=0.5").
		Header("Accept-Encoding", "gzip, deflate").
		Header("Connection", "keep-alive").
		Header("Upgrade-Insecure-Requests", "1")
}

// AliExpressRequest 创建AliExpress爬虫请求构建器
func AliExpressRequest(url string) *RequestBuilder {
	return Post(url).
		ContentType("application/x-www-form-urlencoded; charset=UTF-8").
		UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
}

// PopMartRequest 创建PopMart爬虫请求构建器
func PopMartRequest(url string) *RequestBuilder {
	return Post(url).
		ContentType("application/json; charset=utf-8").
		Accept("application/json, text/plain, */*").
		UserAgent("POPGlobalClient/4.4.2 (iPhone; iOS 18.5; Scale/3.00)")
}
