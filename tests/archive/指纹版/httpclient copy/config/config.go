package config

import (
	"time"
)

// Config HTTP客户端配置
type Config struct {
	// 基础配置
	Timeout        time.Duration // 请求超时时间
	Retries        int           // 重试次数
	UserAgent      string        // 用户代理
	ProxyURL       string        // 默认代理URL
	MaxIdleConn    int           // 最大空闲连接数
	DisableCookies bool          // 是否禁用Cookie（爬虫场景）

	// TLS配置
	EnableTLSFingerprint bool // 是否启用TLS指纹管理

	// 日志配置
	EnableDebugLog bool // 是否启用调试日志
}

// Default 创建默认配置
func Default() *Config {
	return &Config{
		Timeout:              30 * time.Second,
		Retries:              3,
		UserAgent:            "Go-Monitor/1.0",
		MaxIdleConn:          100,
		DisableCookies:       false,
		EnableTLSFingerprint: true,
		EnableDebugLog:       false,
	}
}

// WithTimeout 设置超时时间
func (c *Config) WithTimeout(timeout time.Duration) *Config {
	c.Timeout = timeout
	return c
}

// WithRetries 设置重试次数
func (c *Config) WithRetries(retries int) *Config {
	c.Retries = retries
	return c
}

// WithUserAgent 设置用户代理
func (c *Config) WithUserAgent(userAgent string) *Config {
	c.UserAgent = userAgent
	return c
}

// WithProxy 设置代理
func (c *Config) WithProxy(proxyURL string) *Config {
	c.ProxyURL = proxyURL
	return c
}

// WithTLSFingerprint 设置是否启用TLS指纹
func (c *Config) WithTLSFingerprint(enabled bool) *Config {
	c.EnableTLSFingerprint = enabled
	return c
}

// WithDebugLog 设置是否启用调试日志
func (c *Config) WithDebugLog(enabled bool) *Config {
	c.EnableDebugLog = enabled
	return c
}

// WithDisableCookies 设置是否禁用Cookie
func (c *Config) WithDisableCookies(disabled bool) *Config {
	c.DisableCookies = disabled
	return c
}

// Clone 克隆配置
func (c *Config) Clone() *Config {
	return &Config{
		Timeout:              c.Timeout,
		Retries:              c.Retries,
		UserAgent:            c.UserAgent,
		ProxyURL:             c.ProxyURL,
		MaxIdleConn:          c.MaxIdleConn,
		DisableCookies:       c.DisableCookies,
		EnableTLSFingerprint: c.EnableTLSFingerprint,
		EnableDebugLog:       c.EnableDebugLog,
	}
}
