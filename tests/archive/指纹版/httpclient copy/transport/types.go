package transport

import (
	"time"
)

// Request HTTP请求（transport包内部使用）
type Request struct {
	URL          string                 // 基础请求URL（不含查询参数）
	Method       string                 // HTTP方法
	Headers      map[string]string      // 请求头
	Body         []byte                 // 请求体
	Params       map[string]string      // 查询参数
	Cookies      map[string]string      // Cookies
	ReverseProxy string                 // 反向代理URL
	Proxy        string                 // 代理地址
	Timeout      time.Duration          // 超时时间
	Meta         map[string]interface{} // 元数据
}

// Response HTTP响应（transport包内部使用）
type Response struct {
	StatusCode   int                 // 状态码
	Headers      map[string][]string // 响应头
	Body         []byte              // 响应体
	URL          string              // 最终请求URL
	ResponseTime time.Duration       // 响应时间
	Request      *Request            // 原始请求
}

// GetMetadataString 获取字符串元数据
func (r *Request) GetMetadataString(key, defaultValue string) string {
	if r.Meta == nil {
		return defaultValue
	}
	if value, exists := r.Meta[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return defaultValue
}
