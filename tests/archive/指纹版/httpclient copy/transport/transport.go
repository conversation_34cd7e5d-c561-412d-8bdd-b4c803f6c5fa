package transport

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go-monitor/pkg/httpclient/config"
	"go-monitor/pkg/logging"

	"github.com/andybalholm/brotli"
	"github.com/klauspost/compress/zstd"
	"golang.org/x/net/http2"
)

// FingerprintManager 指纹管理器接口 - 避免循环导入
type FingerprintManager interface {
	CreateManagedTLSConfig(serverName string) (*tls.Config, string, error)
	CreateManagedTLSConfigWithExclusions(serverName string, excludeProfiles []string) (*tls.Config, string, error)
	RecordRequestResult(profileName string, success bool, responseTime time.Duration, statusCode int, err error)
}

// fingerprintManager 全局指纹管理器实例
var fingerprintManager FingerprintManager

// SetFingerprintManager 设置指纹管理器
func SetFingerprintManager(manager FingerprintManager) {
	fingerprintManager = manager
}

// CreateHTTPClient 创建HTTP客户端
func CreateHTTPClient(cfg *config.Config, request *Request) (*http.Client, error) {
	// 创建基础传输配置
	transport := &http.Transport{
		MaxIdleConns:          cfg.MaxIdleConn,
		MaxIdleConnsPerHost:   20,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		ResponseHeaderTimeout: 30 * time.Second,
		DisableKeepAlives:     false,
		DisableCompression:    false,
		ForceAttemptHTTP2:     true,
	}

	// 设置代理
	if proxyURL := getEffectiveProxy(cfg, request); proxyURL != "" {
		if parsed, err := url.Parse(proxyURL); err == nil {
			transport.Proxy = http.ProxyURL(parsed)
		}
	}

	// 配置TLS
	if err := configureTLS(transport, cfg, request); err != nil {
		return nil, fmt.Errorf("配置TLS失败: %w", err)
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Transport: transport,
		Timeout:   cfg.Timeout,
	}

	return httpClient, nil
}

// DoHTTPRequest 执行HTTP请求
func DoHTTPRequest(ctx context.Context, httpClient *http.Client, request *Request, logger logging.Logger) (*Response, error) {
	startTime := time.Now()

	// 构建完整URL
	reqURL := buildRequestURL(request)

	// 创建HTTP请求
	var bodyReader io.Reader
	if request.Body != nil {
		bodyReader = bytes.NewReader(request.Body)
	}

	httpReq, err := http.NewRequestWithContext(ctx, request.Method, reqURL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	setRequestHeaders(httpReq, request)

	// 执行请求
	httpResp, err := httpClient.Do(httpReq)
	responseTime := time.Since(startTime)

	// 记录TLS指纹使用结果
	recordTLSFingerprintResult(request, err, httpResp, responseTime, logger)

	if err != nil {
		return nil, fmt.Errorf("执行HTTP请求失败: %w", err)
	}
	defer httpResp.Body.Close()

	// 读取响应体
	body, err := readResponseBody(httpResp, logger)
	if err != nil {
		return nil, err
	}

	// 额外的风控检测（基于响应内容）
	checkContentBasedRiskControl(request, body, responseTime, logger)

	// 记录请求日志
	logRequest(request, reqURL, httpResp, responseTime, len(body), logger)

	// 构建响应
	response := &Response{
		StatusCode:   httpResp.StatusCode,
		Headers:      httpResp.Header,
		Body:         body,
		URL:          reqURL,
		ResponseTime: responseTime,
		Request:      request,
	}

	return response, nil
}

// configureTLS 配置TLS设置
func configureTLS(transport *http.Transport, cfg *config.Config, request *Request) error {
	// 解析请求URL获取主机名
	u, err := url.Parse(request.URL)
	if err != nil {
		return fmt.Errorf("解析URL失败: %w", err)
	}

	// 检查是否启用TLS指纹管理
	if cfg.EnableTLSFingerprint && shouldUseManagedTLS(u.Host) && fingerprintManager != nil {
		return configureManagedTLS(transport, u.Host, request)
	}

	// 使用默认TLS配置
	return configureDefaultTLS(transport, u.Host)
}

// shouldUseManagedTLS 判断是否应该使用管理的TLS配置
func shouldUseManagedTLS(host string) bool {
	// 检测是否为需要特殊处理的域名（如PopMart）
	return detectPopMartRegion(host) != "UNKNOWN" || strings.Contains(host, "popmart")
}

// detectPopMartRegion 检测PopMart API地区组
func detectPopMartRegion(serverName string) string {
	switch {
	case strings.Contains(serverName, "prod-intl-api.popmart.com"):
		return "INTL"
	case strings.Contains(serverName, "prod-intl-app.popmart.com"):
		return "INTL"
	case strings.Contains(serverName, "prod-asia-api.popmart.com"):
		return "ASIA"
	case strings.Contains(serverName, "prod-na-api.popmart.com"):
		return "NA"
	case strings.Contains(serverName, "prod-na-app.popmart.com"):
		return "NA"
	default:
		return "UNKNOWN"
	}
}

// isBlockedResponse 判断响应是否表示被风控
func isBlockedResponse(statusCode int, err error) bool {
	// 检查特定的风控状态码
	blockedStatusCodes := []int{418, 470, 471, 403, 429}
	for _, code := range blockedStatusCodes {
		if statusCode == code {
			return true
		}
	}

	// 检查错误信息中的风控关键词
	if err != nil {
		errStr := strings.ToLower(err.Error())
		blockedKeywords := []string{"blocked", "banned", "captcha", "forbidden", "rate limit"}
		for _, keyword := range blockedKeywords {
			if strings.Contains(errStr, keyword) {
				return true
			}
		}
	}

	return false
}

// configureManagedTLS 配置管理的TLS
func configureManagedTLS(transport *http.Transport, host string, request *Request) error {
	// 从请求元数据中获取排除的指纹列表
	var excludeProfiles []string
	if request.Meta != nil {
		if excludeList, ok := request.Meta["exclude_tls_profiles"]; ok {
			if profiles, ok := excludeList.([]string); ok {
				excludeProfiles = profiles
			}
		}
	}

	// 选择合适的指纹配置，排除指定指纹
	var tlsConfig *tls.Config
	var profileName string
	var err error

	if len(excludeProfiles) > 0 {
		tlsConfig, profileName, err = fingerprintManager.CreateManagedTLSConfigWithExclusions(host, excludeProfiles)
	} else {
		tlsConfig, profileName, err = fingerprintManager.CreateManagedTLSConfig(host)
	}

	if err != nil {
		// 降级到默认TLS配置
		return configureDefaultTLS(transport, host)
	}

	transport.TLSClientConfig = tlsConfig

	// 记录选择的指纹名称到请求元数据
	if request.Meta == nil {
		request.Meta = make(map[string]interface{})
	}
	request.Meta["tls_profile_name"] = profileName

	// 为PopMart更新HTTP/2设置
	updateHTTP2ConfigForProfile(transport, profileName)

	return nil
}

// configureDefaultTLS 配置默认TLS
func configureDefaultTLS(transport *http.Transport, host string) error {
	// 创建基本的TLS配置
	transport.TLSClientConfig = &tls.Config{
		ServerName:         host,
		InsecureSkipVerify: false,
	}

	return nil
}

// updateHTTP2ConfigForProfile 为指纹配置更新HTTP/2设置
func updateHTTP2ConfigForProfile(transport *http.Transport, profileName string) {
	// 这是一个简化版本，实际应该从manager获取profile信息
	// 暂时使用通用的HTTP/2配置
	if err := http2.ConfigureTransport(transport); err != nil {
		// HTTP/2配置失败时记录错误，但不中断
		if logger := logging.GetLogger("httpclient.transport"); logger != nil {
			logger.Debug("HTTP/2配置失败，降级到HTTP/1.1: %v", err)
		}
	}
}

// getEffectiveProxy 获取有效的代理配置
func getEffectiveProxy(cfg *config.Config, request *Request) string {
	// 优先使用请求级的代理配置
	if request.Proxy != "" {
		return request.Proxy
	}

	// 使用全局代理配置
	return cfg.ProxyURL
}

// buildRequestURL 构建完整的请求URL
func buildRequestURL(request *Request) string {
	// 确定基础URL（考虑反向代理）
	baseURL := request.URL
	if request.ReverseProxy != "" {
		// 如果有反向代理，将原始URL进行URL编码后添加到反向代理URL
		encodedURL := url.QueryEscape(request.URL)
		baseURL = request.ReverseProxy + "/" + encodedURL
	}

	// 如果没有参数，直接返回基础URL
	if len(request.Params) == 0 {
		return baseURL
	}

	// 组装查询参数
	params := url.Values{}
	for k, v := range request.Params {
		params.Add(k, v)
	}

	// 添加参数到URL
	if strings.Contains(baseURL, "?") {
		return baseURL + "&" + params.Encode()
	} else {
		return baseURL + "?" + params.Encode()
	}
}

// setRequestHeaders 设置请求头
func setRequestHeaders(httpReq *http.Request, request *Request) {
	// 设置自定义请求头
	if request.Headers != nil {
		for k, v := range request.Headers {
			httpReq.Header.Set(k, v)
		}
	}

	// 设置cookies
	if len(request.Cookies) > 0 {
		var cookieStrings []string
		for name, value := range request.Cookies {
			cookieStrings = append(cookieStrings, fmt.Sprintf("%s=%s", name, value))
		}
		if len(cookieStrings) > 0 {
			httpReq.Header.Set("Cookie", strings.Join(cookieStrings, "; "))
		}
	}

	// 设置默认User-Agent（如果没有指定）
	if httpReq.Header.Get("User-Agent") == "" {
		httpReq.Header.Set("User-Agent", "Go-Monitor/1.0")
	}
}

// readResponseBody 读取响应体
func readResponseBody(httpResp *http.Response, logger logging.Logger) ([]byte, error) {
	var body []byte
	var readErr error

	// 检查响应压缩格式
	contentEncoding := strings.ToLower(httpResp.Header.Get("Content-Encoding"))

	switch {
	case strings.Contains(contentEncoding, "gzip"):
		// 处理gzip压缩
		gzipReader, gzipErr := gzip.NewReader(httpResp.Body)
		if gzipErr != nil {
			return nil, fmt.Errorf("创建gzip reader失败: %w", gzipErr)
		}
		defer gzipReader.Close()

		body, readErr = io.ReadAll(gzipReader)
		if readErr != nil {
			return nil, fmt.Errorf("读取gzip解压缩数据失败: %w", readErr)
		}

		if logger != nil {
			logger.Debug("成功解压缩gzip响应 原始大小：%d，解压后大小：%d",
				httpResp.ContentLength, len(body))
		}

	case strings.Contains(contentEncoding, "deflate"):
		// 处理deflate压缩
		deflateReader := flate.NewReader(httpResp.Body)
		defer deflateReader.Close()

		body, readErr = io.ReadAll(deflateReader)
		if readErr != nil {
			return nil, fmt.Errorf("读取deflate解压缩数据失败: %w", readErr)
		}

		if logger != nil {
			logger.Debug("成功解压缩deflate响应 原始大小：%d，解压后大小：%d",
				httpResp.ContentLength, len(body))
		}

	case strings.Contains(contentEncoding, "br"):
		// 处理Brotli压缩
		brotliReader := brotli.NewReader(httpResp.Body)

		body, readErr = io.ReadAll(brotliReader)
		if readErr != nil {
			return nil, fmt.Errorf("读取Brotli解压缩数据失败: %w", readErr)
		}

		if logger != nil {
			logger.Debug("成功解压缩Brotli响应 原始大小：%d，解压后大小：%d",
				httpResp.ContentLength, len(body))
		}

	case strings.Contains(contentEncoding, "zstd"):
		// 处理Zstandard压缩
		zstdDecoder, zstdErr := zstd.NewReader(httpResp.Body)
		if zstdErr != nil {
			return nil, fmt.Errorf("创建zstd decoder失败: %w", zstdErr)
		}
		defer zstdDecoder.Close()

		body, readErr = io.ReadAll(zstdDecoder)
		if readErr != nil {
			return nil, fmt.Errorf("读取Zstandard解压缩数据失败: %w", readErr)
		}

		if logger != nil {
			logger.Debug("成功解压缩Zstandard响应 原始大小：%d，解压后大小：%d",
				httpResp.ContentLength, len(body))
		}

	default:
		// 直接读取响应体
		body, readErr = io.ReadAll(httpResp.Body)
		if readErr != nil {
			return nil, fmt.Errorf("读取响应体失败: %w", readErr)
		}
	}

	return body, nil
}

// recordTLSFingerprintResult 记录TLS指纹使用结果
func recordTLSFingerprintResult(request *Request, err error, httpResp *http.Response, responseTime time.Duration, logger logging.Logger) {
	// 检查是否使用了TLS指纹管理
	profileName := request.GetMetadataString("tls_profile_name", "")
	if profileName == "" || fingerprintManager == nil {
		return
	}

	if err != nil {
		// 请求失败
		fingerprintManager.RecordRequestResult(profileName, false, responseTime, 0, err)
	} else {
		// 根据状态码判断是否成功
		success := httpResp.StatusCode >= 200 && httpResp.StatusCode < 400
		fingerprintManager.RecordRequestResult(profileName, success, responseTime, httpResp.StatusCode, nil)

		// 检测风控响应
		if isBlockedResponse(httpResp.StatusCode, nil) {
			if logger != nil {
				logger.Warn("检测到风控响应，状态码：%d，TLS指纹：%s，URL：%s",
					httpResp.StatusCode, profileName, request.URL)
			}
		}
	}
}

// checkContentBasedRiskControl 基于响应内容检测风控
func checkContentBasedRiskControl(request *Request, body []byte, responseTime time.Duration, logger logging.Logger) {
	profileName := request.GetMetadataString("tls_profile_name", "")
	if profileName == "" || len(body) == 0 || fingerprintManager == nil {
		return
	}

	bodyStr := strings.ToLower(string(body))
	blockedKeywords := []string{"blocked", "banned", "captcha", "forbidden", "access denied"}

	for _, keyword := range blockedKeywords {
		if strings.Contains(bodyStr, keyword) {
			fingerprintManager.RecordRequestResult(profileName, false, responseTime, 0,
				fmt.Errorf("检测到风控关键词: %s", keyword))

			if logger != nil {
				logger.Warn("响应内容包含风控关键词：%s，TLS指纹：%s", keyword, profileName)
			}
			break
		}
	}
}

// logRequest 记录请求日志
func logRequest(request *Request, reqURL string, httpResp *http.Response, responseTime time.Duration, bodySize int, logger logging.Logger) {
	if logger == nil {
		return
	}

	logMsg := fmt.Sprintf("HTTP请求完成 方法：%s，URL：%s，状态码：%d，响应时间：%s，响应大小：%d",
		request.Method, reqURL, httpResp.StatusCode, responseTime.String(), bodySize)

	// 添加TLS指纹信息
	if profileName := request.GetMetadataString("tls_profile_name", ""); profileName != "" {
		logMsg += fmt.Sprintf("，TLS指纹：%s", profileName)
	}

	logger.Debug(logMsg)
}
