package fingerprint

import (
	"crypto/tls"
	"strings"
)

// SessionCacheStrategy 会话缓存策略
type SessionCacheStrategy struct {
	Enabled          bool    // 是否启用会话缓存
	Size             int     // 缓存大小
	DisableOnBurst   bool    // 在突发请求时禁用缓存
	MaxCacheAge      int     // 最大缓存时间（秒）
	ClearProbability float32 // 随机清理缓存的概率
}

// Profile TLS指纹配置模板
type Profile struct {
	Name             string
	MinVersion       uint16
	MaxVersion       uint16
	CipherSuites     []uint16
	CurvePreferences []tls.CurveID
	SessionCache     SessionCacheStrategy
	NextProtos       []string // ALPN协议列表
	ForceHTTP2       bool     // 是否强制使用HTTP/2
}

// DetectPopMartRegion 检测PopMart API地区组
func DetectPopMartRegion(serverName string) string {
	switch {
	case strings.Contains(serverName, "prod-intl-api.popmart.com"):
		return "INTL" // EU, GB, AU, SG, HK, KR, JP
	case strings.Contains(serverName, "prod-intl-app.popmart.com"):
		return "INTL" // 旧域名兼容
	case strings.Contains(serverName, "prod-asia-api.popmart.com"):
		return "ASIA" // TH
	case strings.Contains(serverName, "prod-na-api.popmart.com"):
		return "NA" // US
	case strings.Contains(serverName, "prod-na-app.popmart.com"):
		return "NA" // 旧域名兼容
	default:
		return "UNKNOWN"
	}
}

// IsBlockedResponse 判断响应是否表示被风控
func IsBlockedResponse(statusCode int, err error) bool {
	// 检查特定的风控状态码
	blockedStatusCodes := []int{418, 470, 471, 403, 429}
	for _, code := range blockedStatusCodes {
		if statusCode == code {
			return true
		}
	}

	// 检查错误信息中的风控关键词
	if err != nil {
		errStr := strings.ToLower(err.Error())
		blockedKeywords := []string{"blocked", "banned", "captcha", "forbidden", "rate limit"}
		for _, keyword := range blockedKeywords {
			if strings.Contains(errStr, keyword) {
				return true
			}
		}
	}

	return false
}

// CreateSmartSessionCache 创建智能会话缓存
func CreateSmartSessionCache(strategy SessionCacheStrategy) tls.ClientSessionCache {
	if !strategy.Enabled {
		return nil
	}

	// 检查是否应该随机禁用缓存
	if strategy.ClearProbability > 0 {
		// 简化实现，实际应该使用rand
		return tls.NewLRUClientSessionCache(strategy.Size)
	}

	return tls.NewLRUClientSessionCache(strategy.Size)
}
