package fingerprint

import (
	"crypto/tls"
)

// GetMinimalProfiles 获取基于真实抓包的极简TLS指纹配置
// 这些指纹基于Charles代理简化后的真实指纹特征
func GetMinimalProfiles() []Profile {
	return []Profile{
		{
			Name:       "Minimal_TLS13_v1",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13, // 纯TLS 1.3，匹配抓包
			// 极简cipher suites - 精确匹配抓包，仅AES-256
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384, // 唯一选择，强制匹配抓包
			},
			// 匹配抓包的曲线偏好：x25519, secp256r1, secp384r1...
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             32, // 较小的缓存
				DisableOnBurst:   false,
				MaxCacheAge:      180,
				ClearProbability: 0.05,
			},
			NextProtos: nil, // ALPN为空，匹配抓包
			ForceHTTP2: true,
		},
		{
			Name:       "Minimal_TLS13_v2",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 精确匹配抓包 - 仅AES-256
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384, // 强制使用抓包中的套件
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             48,
				DisableOnBurst:   false,
				MaxCacheAge:      240,
				ClearProbability: 0.08,
			},
			NextProtos: nil, // ALPN为空
			ForceHTTP2: true,
		},
		{
			Name:       "Minimal_TLS13_v3",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 精确匹配抓包 - 仅AES-256
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384, // 强制使用抓包中的套件
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             16, // 更小的缓存，模拟代理
				DisableOnBurst:   true,
				MaxCacheAge:      120,
				ClearProbability: 0.1,
			},
			NextProtos: nil, // ALPN为空
			ForceHTTP2: true,
		},
		{
			Name:       "Proxy_Like_v1",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 模拟代理环境的极简配置
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384, // 匹配您抓包的选择
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: SessionCacheStrategy{
				Enabled:          false, // 代理可能禁用会话缓存
				Size:             0,
				DisableOnBurst:   true,
				MaxCacheAge:      0,
				ClearProbability: 0.0,
			},
			NextProtos: nil, // ALPN为空
			ForceHTTP2: true,
		},
		{
			Name:       "Proxy_Like_v2",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 精确匹配抓包 - 仅AES-256
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384, // 强制使用抓包中的套件
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             8, // 极小缓存
				DisableOnBurst:   true,
				MaxCacheAge:      60,
				ClearProbability: 0.2,
			},
			NextProtos: nil, // ALPN为空
			ForceHTTP2: true,
		},
	}
}
