package fingerprint

import (
	"crypto/tls"
	"math/rand"
)

// GetPredefinedProfiles 获取预定义的TLS指纹配置
func GetPredefinedProfiles() []Profile {
	return []Profile{
		{
			Name:       "Chrome_Latest",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             64,
				DisableOnBurst:   false,
				MaxCacheAge:      300,
				ClearProbability: 0.05,
			},
			NextProtos: nil, // 匹配真实Chrome：ALPN为空
			ForceHTTP2: false,
		},
		{
			Name:       "Safari_iOS",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             32,
				DisableOnBurst:   true,
				MaxCacheAge:      180,
				ClearProbability: 0.08,
			},
			NextProtos: []string{"h2", "http/1.1"},
			ForceHTTP2: false,
		},
		{
			Name:       "Firefox_Latest",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             48,
				DisableOnBurst:   false,
				MaxCacheAge:      240,
				ClearProbability: 0.06,
			},
			NextProtos: []string{"h2", "http/1.1"},
			ForceHTTP2: false,
		},
		{
			Name:       "Charles_Proxy",
			MinVersion: tls.VersionTLS10,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,
				tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_RSA_WITH_AES_128_CBC_SHA,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             64,
				DisableOnBurst:   false,
				MaxCacheAge:      600,
				ClearProbability: 0.03,
			},
			NextProtos: nil,
			ForceHTTP2: false,
		},
		{
			Name:       "PopMart_INTL",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             48,
				DisableOnBurst:   false,
				MaxCacheAge:      240,
				ClearProbability: 0.05,
			},
			NextProtos: []string{"h2", "http/1.1"},
			ForceHTTP2: false,
		},
		{
			Name:       "PopMart_ASIA",
			MinVersion: tls.VersionTLS10,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,
				tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_RSA_WITH_AES_128_CBC_SHA,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             64,
				DisableOnBurst:   false,
				MaxCacheAge:      300,
				ClearProbability: 0.04,
			},
			NextProtos: nil,
			ForceHTTP2: false,
		},
		{
			Name:       "PopMart_NA",
			MinVersion: tls.VersionTLS10,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,
				tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_RSA_WITH_AES_128_CBC_SHA,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: SessionCacheStrategy{
				Enabled:          false,
				Size:             0,
				DisableOnBurst:   true,
				MaxCacheAge:      0,
				ClearProbability: 0.0,
			},
			NextProtos: []string{"h2", "http/1.1"},
			ForceHTTP2: true,
		},
	}
}

// RandomizeCipherSuites 随机化密码套件顺序（保持兼容性）
func RandomizeCipherSuites(original []uint16) []uint16 {
	if len(original) <= 1 {
		return original
	}

	// 复制原始切片
	result := make([]uint16, len(original))
	copy(result, original)

	// 对TLS 1.3套件（前3个）进行微调随机化
	tls13Count := 0
	for i, suite := range result {
		if suite == tls.TLS_AES_128_GCM_SHA256 ||
			suite == tls.TLS_AES_256_GCM_SHA384 ||
			suite == tls.TLS_CHACHA20_POLY1305_SHA256 {
			tls13Count = i + 1
		} else {
			break
		}
	}

	// 随机调整TLS 1.3套件顺序
	if tls13Count > 1 {
		for i := 0; i < tls13Count-1; i++ {
			if rand.Intn(2) == 0 { // 50%概率交换
				j := i + 1
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	// 对TLS 1.2套件进行轻微随机化（保持ECDHE优先）
	if len(result) > tls13Count+2 {
		start := tls13Count
		end := len(result)

		// 在相邻的套件间进行小范围随机交换
		for i := start; i < end-1; i += 2 {
			if rand.Intn(3) == 0 { // 33%概率交换相邻套件
				if i+1 < end {
					result[i], result[i+1] = result[i+1], result[i]
				}
			}
		}
	}

	return result
}

// RandomizeCurvePreferences 随机化椭圆曲线偏好
func RandomizeCurvePreferences(original []tls.CurveID) []tls.CurveID {
	if len(original) <= 1 {
		return original
	}

	result := make([]tls.CurveID, len(original))
	copy(result, original)

	// X25519通常保持首选，但偶尔可以调整
	if len(result) >= 2 && rand.Intn(4) == 0 { // 25%概率调整X25519位置
		result[0], result[1] = result[1], result[0]
	}

	// 对其余曲线进行轻微随机化
	if len(result) > 2 {
		for i := 1; i < len(result)-1; i++ {
			if rand.Intn(3) == 0 { // 33%概率交换
				j := i + 1
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	return result
}
