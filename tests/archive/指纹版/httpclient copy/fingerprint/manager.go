package fingerprint

import (
	"crypto/tls"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"

	"go-monitor/pkg/logging"
)

// Manager TLS指纹管理器
type Manager struct {
	// 指纹池
	profiles       map[string]*Profile // 所有可用的指纹配置
	healthTrackers map[string]*Health  // 指纹健康追踪器

	// 配置选项
	maxRequestsPerProfile int64         // 每个指纹最大请求数
	maxProfileDuration    time.Duration // 每个指纹最大使用时长
	rotationProbability   float32       // 随机轮换概率

	// 地区指纹映射
	regionProfiles map[string][]string // 地区 -> 适用的指纹名称列表

	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger
}

// NewManager 创建新的TLS指纹管理器
func NewManager() *Manager {
	m := &Manager{
		profiles:              make(map[string]*Profile),
		healthTrackers:        make(map[string]*Health),
		maxRequestsPerProfile: 100,
		maxProfileDuration:    30 * time.Minute,
		rotationProbability:   0.1, // 10%概率随机轮换
		regionProfiles:        make(map[string][]string),
		logger:                logging.GetLogger("httpclient.fingerprint"),
	}

	// 初始化所有预定义的指纹配置
	m.initializeProfiles()

	// 启动定期清理任务
	go m.periodicCleanup()

	return m
}

// initializeProfiles 初始化所有指纹配置
func (m *Manager) initializeProfiles() {
	// 基础浏览器指纹
	baseProfiles := []string{"Chrome_Latest", "Safari_iOS", "Firefox_Latest", "Charles_Proxy"}

	// 为每个基础指纹创建多个变体
	for _, baseName := range baseProfiles {
		for i := 0; i < 3; i++ { // 每个基础指纹创建3个变体
			variantName := fmt.Sprintf("%s_v%d", baseName, i+1)
			profile := m.createProfileVariant(baseName, i)

			m.profiles[variantName] = &profile
			m.healthTrackers[variantName] = NewHealth(variantName, "GENERAL")

			// 添加到通用地区
			m.addProfileToRegion("GENERAL", variantName)
		}
	}

	// 优先加载基于真实抓包的极简指纹（最高优先级）
	minimalProfiles := GetMinimalProfiles()
	for i, minimalProfile := range minimalProfiles {
		// 为每个极简指纹创建地区变体
		regions := []string{"INTL", "ASIA", "NA"}
		for _, region := range regions {
			variantName := fmt.Sprintf("%s_%s", minimalProfile.Name, region)
			profile := minimalProfile
			profile.Name = variantName

			// 为不同地区创建微调差异
			profile = m.createMinimalVariant(profile, region, i)

			m.profiles[variantName] = &profile
			m.healthTrackers[variantName] = NewHealth(variantName, region)
			m.addProfileToRegion(region, variantName)
		}
	}

	// 加载基于Chrome的指纹作为备用（次优先级）
	chromeProfiles := GetChromeBasedProfiles()
	for i, chromeProfile := range chromeProfiles {
		// 为每个真实Chrome指纹创建变体用于不同地区
		regions := []string{"INTL", "ASIA", "NA"}
		for _, region := range regions {
			variantName := fmt.Sprintf("%s_%s_Backup", chromeProfile.Name, region)
			profile := chromeProfile
			profile.Name = variantName

			// 为不同地区创建微调差异
			profile = m.createRegionalVariant(profile, region, i)

			m.profiles[variantName] = &profile
			m.healthTrackers[variantName] = NewHealth(variantName, region)
			m.addProfileToRegion(region, variantName)
		}
	}

	// PopMart 专用指纹 - 保留原有但降低优先级
	popmartProfiles := []struct {
		name   string
		region string
		base   int // predefinedProfiles数组中的索引
	}{
		// INTL地区保留一些原版指纹作为后备
		{"PopMart_INTL_Original_v1", "INTL", 4},
		{"PopMart_INTL_Original_v2", "INTL", 4},
		// ASIA地区
		{"PopMart_ASIA_v1", "ASIA", 5},
		{"PopMart_ASIA_v2", "ASIA", 5},
		// NA地区
		{"PopMart_NA_v1", "NA", 6},
		{"PopMart_NA_v2", "NA", 6},
	}

	predefinedProfiles := GetPredefinedProfiles()
	for i, pm := range popmartProfiles {
		if pm.base < len(predefinedProfiles) {
			profile := predefinedProfiles[pm.base]
			profile.Name = pm.name

			// 为PopMart指纹创建更大的变体差异
			profile = m.createPopMartVariant(profile, i)

			m.profiles[pm.name] = &profile
			m.healthTrackers[pm.name] = NewHealth(pm.name, pm.region)
			m.addProfileToRegion(pm.region, pm.name)
		}
	}

	m.logger.Info("初始化TLS指纹管理器，共加载 %d 个指纹配置", len(m.profiles))
}

// createProfileVariant 创建指纹变体
func (m *Manager) createProfileVariant(baseName string, variant int) Profile {
	predefinedProfiles := GetPredefinedProfiles()
	var baseProfile Profile

	switch baseName {
	case "Chrome_Latest":
		baseProfile = predefinedProfiles[0]
	case "Safari_iOS":
		baseProfile = predefinedProfiles[1]
	case "Firefox_Latest":
		baseProfile = predefinedProfiles[2]
	case "Charles_Proxy":
		baseProfile = predefinedProfiles[3]
	default:
		// 默认使用Chrome配置
		baseProfile = predefinedProfiles[0]
	}

	// 创建变体时进行微调
	profile := baseProfile
	profile.Name = fmt.Sprintf("%s_v%d", baseName, variant+1)

	// 根据变体编号进行不同的随机化
	switch variant {
	case 0:
		// 变体1：轻微调整密码套件顺序
		profile.CipherSuites = RandomizeCipherSuites(profile.CipherSuites)
	case 1:
		// 变体2：调整曲线偏好和会话缓存
		profile.CurvePreferences = RandomizeCurvePreferences(profile.CurvePreferences)
		profile.SessionCache.Size += rand.Intn(16) - 8
	case 2:
		// 变体3：综合调整
		profile.CipherSuites = RandomizeCipherSuites(profile.CipherSuites)
		profile.CurvePreferences = RandomizeCurvePreferences(profile.CurvePreferences)
		profile.SessionCache.ClearProbability = float32(rand.Intn(10)) / 100
	}

	return profile
}

// createPopMartVariant 为PopMart创建高度差异化的指纹变体
func (m *Manager) createPopMartVariant(baseProfile Profile, variantIndex int) Profile {
	profile := baseProfile

	// 基于变体索引创建更大的差异
	switch variantIndex % 8 {
	case 0, 1:
		// 保持原始配置，轻微随机化
		profile.CipherSuites = RandomizeCipherSuites(profile.CipherSuites)
		profile.CurvePreferences = RandomizeCurvePreferences(profile.CurvePreferences)
	case 2:
		// 强制使用TLS 1.2，模拟旧版浏览器
		profile.MinVersion = tls.VersionTLS12
		profile.MaxVersion = tls.VersionTLS12
		profile.ForceHTTP2 = false
		profile.NextProtos = []string{"http/1.1"}
		profile.SessionCache.Size = 32
	case 3:
		// 纯TLS 1.3，模拟最新浏览器
		profile.MinVersion = tls.VersionTLS13
		profile.MaxVersion = tls.VersionTLS13
		profile.CipherSuites = []uint16{tls.TLS_AES_128_GCM_SHA256, tls.TLS_AES_256_GCM_SHA384, tls.TLS_CHACHA20_POLY1305_SHA256}
		profile.ForceHTTP2 = true
		profile.NextProtos = []string{"h2"}
	case 4:
		// 禁用会话缓存，模拟隐私浏览模式
		profile.SessionCache.Enabled = false
		profile.SessionCache.Size = 0
		profile.CurvePreferences = []tls.CurveID{tls.CurveP256, tls.X25519} // 不同的曲线偏好
	case 5:
		// 启用突发禁用缓存，模拟移动端
		profile.SessionCache.DisableOnBurst = true
		profile.SessionCache.Size = 16
		profile.SessionCache.MaxCacheAge = 60
		profile.CurvePreferences = []tls.CurveID{tls.X25519, tls.CurveP384}
	case 6:
		// 高随机化配置
		profile.SessionCache.ClearProbability = 0.2                                               // 20%概率清理
		profile.CipherSuites = RandomizeCipherSuites(RandomizeCipherSuites(profile.CipherSuites)) // 双重随机化
	case 7:
		// 模拟代理环境
		profile.SessionCache.Size = 128
		profile.SessionCache.MaxCacheAge = 600
		profile.NextProtos = nil // 不声明协议偏好
		profile.ForceHTTP2 = false
	}

	// 为所有变体增加额外的随机化
	if variantIndex > 0 {
		// 随机调整缓存大小
		profile.SessionCache.Size += rand.Intn(32) - 16
		if profile.SessionCache.Size < 0 {
			profile.SessionCache.Size = 16
		}

		// 随机调整清理概率
		profile.SessionCache.ClearProbability += float32(rand.Intn(10)-5) / 100.0
		if profile.SessionCache.ClearProbability < 0 {
			profile.SessionCache.ClearProbability = 0.01
		}
		if profile.SessionCache.ClearProbability > 0.5 {
			profile.SessionCache.ClearProbability = 0.5
		}
	}

	return profile
}

// createRegionalVariant 为不同地区创建指纹变体
func (m *Manager) createRegionalVariant(baseProfile Profile, region string, variantIndex int) Profile {
	profile := baseProfile

	// 为不同地区创建微调差异，提高成功率
	switch region {
	case "INTL":
		// 国际版：保持接近真实Chrome
		// 基于变体索引进行微调
		if variantIndex%2 == 0 {
			// 偶数变体：偏好AES-256
			if len(profile.CipherSuites) >= 2 {
				profile.CipherSuites[0], profile.CipherSuites[1] = profile.CipherSuites[1], profile.CipherSuites[0]
			}
		}

		// 调整会话缓存大小
		profile.SessionCache.Size += rand.Intn(16) - 8
		if profile.SessionCache.Size < 16 {
			profile.SessionCache.Size = 16
		}

	case "ASIA":
		// 亚洲版：使用更保守的配置
		profile.SessionCache.MaxCacheAge = 180
		profile.SessionCache.ClearProbability = 0.12

		// 调整曲线偏好
		if len(profile.CurvePreferences) >= 2 {
			profile.CurvePreferences = []tls.CurveID{tls.X25519, tls.CurveP256}
		}

	case "NA":
		// 北美版：更现代的配置
		profile.SessionCache.MaxCacheAge = 360
		profile.SessionCache.ClearProbability = 0.03

		// 偏好CHACHA20
		if len(profile.CipherSuites) >= 3 {
			// 将CHACHA20提前
			for i, suite := range profile.CipherSuites {
				if suite == tls.TLS_CHACHA20_POLY1305_SHA256 && i > 0 {
					profile.CipherSuites[0], profile.CipherSuites[i] = profile.CipherSuites[i], profile.CipherSuites[0]
					break
				}
			}
		}
	}

	// 为所有地区添加额外的随机化
	profile.SessionCache.ClearProbability += float32(rand.Intn(5)-2) / 100.0
	if profile.SessionCache.ClearProbability < 0 {
		profile.SessionCache.ClearProbability = 0.01
	}
	if profile.SessionCache.ClearProbability > 0.3 {
		profile.SessionCache.ClearProbability = 0.3
	}

	return profile
}

// createMinimalVariant 为极简指纹创建地区变体
func (m *Manager) createMinimalVariant(baseProfile Profile, region string, variantIndex int) Profile {
	profile := baseProfile

	// 为极简指纹创建细微差异，保持简洁性
	switch region {
	case "INTL":
		// 国际版：保持极简，微调cipher suites
		if len(profile.CipherSuites) > 1 && variantIndex%2 == 0 {
			// 交换前两个cipher suites
			profile.CipherSuites[0], profile.CipherSuites[1] = profile.CipherSuites[1], profile.CipherSuites[0]
		}

		// 微调会话缓存
		if profile.SessionCache.Enabled {
			profile.SessionCache.Size += rand.Intn(8) - 4
			if profile.SessionCache.Size < 8 {
				profile.SessionCache.Size = 8
			}
		}

	case "ASIA":
		// 亚洲版：稍微保守的配置
		if profile.SessionCache.Enabled {
			profile.SessionCache.MaxCacheAge = 120
			profile.SessionCache.ClearProbability = 0.15
		}

		// 调整曲线偏好，去掉P521
		if len(profile.CurvePreferences) > 3 {
			profile.CurvePreferences = profile.CurvePreferences[:3]
		}

	case "NA":
		// 北美版：更现代的配置
		if profile.SessionCache.Enabled {
			profile.SessionCache.MaxCacheAge = 300
			profile.SessionCache.ClearProbability = 0.02
		}

		// 偏好CHACHA20（如果存在）
		for i, suite := range profile.CipherSuites {
			if suite == tls.TLS_CHACHA20_POLY1305_SHA256 && i > 0 {
				// 将CHACHA20移到前面
				profile.CipherSuites[0], profile.CipherSuites[i] = profile.CipherSuites[i], profile.CipherSuites[0]
				break
			}
		}
	}

	// 为所有地区添加轻微的随机化（保持极简特性）
	if profile.SessionCache.Enabled {
		profile.SessionCache.ClearProbability += float32(rand.Intn(3)-1) / 100.0
		if profile.SessionCache.ClearProbability < 0 {
			profile.SessionCache.ClearProbability = 0.01
		}
		if profile.SessionCache.ClearProbability > 0.2 {
			profile.SessionCache.ClearProbability = 0.2
		}
	}

	return profile
}

// SelectProfile 选择合适的TLS指纹配置
func (m *Manager) SelectProfile(serverName string) (*Profile, error) {
	return m.SelectProfileWithExclusions(serverName, nil)
}

// SelectProfileWithExclusions 选择合适的TLS指纹配置，支持排除指定指纹
func (m *Manager) SelectProfileWithExclusions(serverName string, excludeProfiles []string) (*Profile, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 检测目标地区
	region := DetectPopMartRegion(serverName)
	if region == "UNKNOWN" {
		region = "GENERAL"
	}

	// 获取该地区可用的指纹列表
	profileNames, exists := m.regionProfiles[region]
	if !exists || len(profileNames) == 0 {
		// 如果没有地区专用指纹，使用通用指纹
		profileNames = m.regionProfiles["GENERAL"]
	}

	// 过滤掉被排除的指纹
	if len(excludeProfiles) > 0 {
		excludeMap := make(map[string]bool)
		for _, exclude := range excludeProfiles {
			excludeMap[exclude] = true
		}

		var filteredNames []string
		for _, name := range profileNames {
			if !excludeMap[name] {
				filteredNames = append(filteredNames, name)
			}
		}
		profileNames = filteredNames

		if len(profileNames) == 0 {
			m.logger.Warn("所有可用指纹都被排除，使用备用策略，服务器：%s，排除数量：%d",
				serverName, len(excludeProfiles))
			// 如果所有指纹都被排除，使用其他地区的指纹作为备用
			for fallbackRegion, fallbackProfiles := range m.regionProfiles {
				if fallbackRegion != region && len(fallbackProfiles) > 0 {
					profileNames = fallbackProfiles
					m.logger.Debug("使用备用地区指纹：%s -> %s", region, fallbackRegion)
					break
				}
			}
		}
	}

	// 根据健康状态和评分选择最佳指纹
	var bestProfile string
	var bestScore float64 = -1

	for _, name := range profileNames {
		tracker := m.healthTrackers[name]
		if tracker == nil {
			continue
		}

		// 检查指纹是否健康
		if !tracker.IsHealthy() {
			continue
		}

		// 检查是否需要轮换
		if tracker.ShouldRotate(m.maxRequestsPerProfile, m.maxProfileDuration) {
			continue
		}

		// 计算综合评分
		score := m.calculateProfileScore(tracker, region)
		if score > bestScore {
			bestScore = score
			bestProfile = name
		}
	}

	// 如果没有找到合适的指纹，选择一个随机的健康指纹
	if bestProfile == "" {
		bestProfile = m.selectRandomHealthyProfile(profileNames)
	}

	// 如果还是没有，返回错误
	if bestProfile == "" {
		return nil, fmt.Errorf("没有可用的健康TLS指纹配置，服务器：%s，可用指纹：%d，排除指纹：%d",
			serverName, len(profileNames), len(excludeProfiles))
	}

	// 应用随机轮换策略
	if rand.Float32() < m.rotationProbability {
		alternativeProfile := m.selectRandomHealthyProfile(profileNames)
		if alternativeProfile != "" && alternativeProfile != bestProfile {
			bestProfile = alternativeProfile
			m.logger.Debug("触发随机轮换，从 %s 切换到 %s", bestProfile, alternativeProfile)
		}
	}

	profile := m.profiles[bestProfile]
	if len(excludeProfiles) > 0 {
		m.logger.Debug("选择TLS指纹：%s，地区：%s，健康评分：%.2f，排除数量：%d",
			bestProfile, region, m.healthTrackers[bestProfile].GetHealthScore(), len(excludeProfiles))
	} else {
		m.logger.Debug("选择TLS指纹：%s，地区：%s，健康评分：%.2f",
			bestProfile, region, m.healthTrackers[bestProfile].GetHealthScore())
	}

	return profile, nil
}

// RecordRequestResult 记录请求结果
func (m *Manager) RecordRequestResult(profileName string, success bool,
	responseTime time.Duration, statusCode int, err error) {
	m.mu.RLock()
	tracker, exists := m.healthTrackers[profileName]
	m.mu.RUnlock()

	if !exists {
		return
	}

	if success {
		tracker.RecordSuccess(responseTime)
	} else {
		// 判断是否被风控
		isBlocked := IsBlockedResponse(statusCode, err)
		errMsg := "unknown error"
		if err != nil {
			errMsg = err.Error()
		}
		tracker.RecordFailure(errMsg, isBlocked)

		if isBlocked {
			m.logger.Warn("检测到风控响应，指纹：%s，状态码：%d", profileName, statusCode)
		}
	}
}

// calculateProfileScore 计算指纹的综合评分
func (m *Manager) calculateProfileScore(tracker *Health, region string) float64 {
	baseScore := tracker.GetHealthScore()

	// 极简指纹优先（最高加分）
	if strings.Contains(tracker.ProfileName, "Minimal_") || strings.Contains(tracker.ProfileName, "Proxy_Like_") {
		baseScore += 50 // 极简指纹大幅加分
	}

	// 地区匹配加分
	if tracker.Region == region {
		baseScore += 20
	}

	// 新鲜度加分
	lastUsed := tracker.LastUsedAt.Load().(time.Time)
	if time.Since(lastUsed) > 5*time.Minute {
		baseScore += 10 // 超过5分钟未使用的指纹优先
	}

	// 请求数惩罚（避免过度使用单一指纹）
	totalRequests := tracker.TotalRequests.Load()
	if totalRequests > 50 {
		baseScore -= float64(totalRequests-50) * 0.5
	}

	// 备用指纹惩罚
	if strings.Contains(tracker.ProfileName, "_Backup") {
		baseScore -= 30 // 备用指纹降低优先级
	}

	return baseScore
}

// selectRandomHealthyProfile 随机选择一个健康的指纹
func (m *Manager) selectRandomHealthyProfile(profileNames []string) string {
	var healthyProfiles []string

	for _, name := range profileNames {
		if tracker := m.healthTrackers[name]; tracker != nil && tracker.IsHealthy() {
			healthyProfiles = append(healthyProfiles, name)
		}
	}

	if len(healthyProfiles) == 0 {
		return ""
	}

	return healthyProfiles[rand.Intn(len(healthyProfiles))]
}

// selectBestAlternativeProfile 选择最佳替代指纹（避开问题指纹）
func (m *Manager) selectBestAlternativeProfile(profileNames []string, avoidProfile string) string {
	var candidates []string
	var scores []float64

	for _, name := range profileNames {
		if name == avoidProfile {
			continue // 避开问题指纹
		}

		tracker := m.healthTrackers[name]
		if tracker == nil || !tracker.IsHealthy() {
			continue
		}

		// 优先选择连续错误少、最近未使用的指纹
		score := tracker.GetHealthScore()

		// 连续错误越少越好
		consecutiveErrors := tracker.ConsecutiveErrors.Load()
		score += float64(10 - consecutiveErrors*2)

		// 最近未使用的指纹优先
		lastUsed := tracker.LastUsedAt.Load().(time.Time)
		timeSinceLastUse := time.Since(lastUsed)
		if timeSinceLastUse > 10*time.Minute {
			score += 30 // 长时间未使用的指纹大加分
		} else if timeSinceLastUse > 2*time.Minute {
			score += 15
		}

		candidates = append(candidates, name)
		scores = append(scores, score)
	}

	if len(candidates) == 0 {
		return ""
	}

	// 选择评分最高的指纹
	bestIndex := 0
	bestScore := scores[0]
	for i, score := range scores {
		if score > bestScore {
			bestScore = score
			bestIndex = i
		}
	}

	return candidates[bestIndex]
}

// addProfileToRegion 添加指纹到地区映射
func (m *Manager) addProfileToRegion(region, profileName string) {
	m.regionProfiles[region] = append(m.regionProfiles[region], profileName)
}

// GetStats 获取所有指纹的统计信息
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := make(map[string]interface{})

	// 按地区统计
	regionStats := make(map[string]interface{})
	for region, profiles := range m.regionProfiles {
		var healthyCount, blockedCount int
		var totalRequests int64

		for _, profileName := range profiles {
			if tracker := m.healthTrackers[profileName]; tracker != nil {
				if tracker.IsHealthy() {
					healthyCount++
				} else if tracker.Status.Load().(string) == "blocked" {
					blockedCount++
				}
				totalRequests += tracker.TotalRequests.Load()
			}
		}

		regionStats[region] = map[string]interface{}{
			"total_profiles": len(profiles),
			"healthy_count":  healthyCount,
			"blocked_count":  blockedCount,
			"total_requests": totalRequests,
		}
	}

	stats["regions"] = regionStats

	// 详细的指纹统计
	profileStats := make(map[string]interface{})
	for name, tracker := range m.healthTrackers {
		profileStats[name] = tracker.GetStats()
	}
	stats["profiles"] = profileStats

	return stats
}

// periodicCleanup 定期清理任务
func (m *Manager) periodicCleanup() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		m.mu.Lock()

		// 重置长时间未使用的指纹
		for name, tracker := range m.healthTrackers {
			lastUsed := tracker.LastUsedAt.Load().(time.Time)
			if time.Since(lastUsed) > 24*time.Hour {
				tracker.Reset()
				m.logger.Debug("重置长时间未使用的指纹：%s", name)
			}
		}

		m.mu.Unlock()
	}
}

// SetRotationConfig 设置轮换配置
func (m *Manager) SetRotationConfig(maxRequests int64, maxDuration time.Duration, probability float32) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.maxRequestsPerProfile = maxRequests
	m.maxProfileDuration = maxDuration
	m.rotationProbability = probability

	m.logger.Info("更新指纹轮换配置：最大请求数=%d，最大时长=%v，随机概率=%.2f",
		maxRequests, maxDuration, probability)
}

// LoadConfigFromMiddleware 从中间件配置加载TLS指纹管理设置
func (m *Manager) LoadConfigFromMiddleware(config map[string]interface{}) error {
	// 检查是否启用
	if enabled, ok := config["enabled"].(bool); ok && !enabled {
		m.logger.Info("TLS指纹管理器已禁用")
		return nil
	}

	// 加载轮换策略配置
	if rotation, ok := config["rotation"].(map[string]interface{}); ok {
		if maxRequests, ok := rotation["max_requests_per_profile"].(int); ok {
			m.maxRequestsPerProfile = int64(maxRequests)
		}

		if durationStr, ok := rotation["max_profile_duration"].(string); ok {
			if duration, err := time.ParseDuration(durationStr); err == nil {
				m.maxProfileDuration = duration
			}
		}

		if probability, ok := rotation["random_rotation_probability"].(float64); ok {
			m.rotationProbability = float32(probability)
		}
	}

	// 加载风控检测配置
	if riskControl, ok := config["risk_control"].(map[string]interface{}); ok {
		// 这里可以扩展加载更多风控相关配置
		if blockedCodes, ok := riskControl["blocked_status_codes"].([]interface{}); ok {
			m.logger.Debug("加载风控状态码：%v", blockedCodes)
		}

		if blockedKeywords, ok := riskControl["blocked_keywords"].([]interface{}); ok {
			m.logger.Debug("加载风控关键词：%v", blockedKeywords)
		}
	}

	// 加载健康检查配置
	if healthCheck, ok := config["health_check"].(map[string]interface{}); ok {
		if resetDurationStr, ok := healthCheck["reset_idle_duration"].(string); ok {
			if duration, err := time.ParseDuration(resetDurationStr); err == nil {
				// 可以存储这个配置供定期清理任务使用
				m.logger.Debug("设置空闲重置时间：%v", duration)
			}
		}
	}

	m.logger.Info("TLS指纹管理器配置加载完成：最大请求数=%d，最大时长=%v，随机概率=%.2f",
		m.maxRequestsPerProfile, m.maxProfileDuration, m.rotationProbability)

	return nil
}

// CreateManagedTLSConfig 创建由指纹管理器管理的TLS配置
func (m *Manager) CreateManagedTLSConfig(serverName string) (*tls.Config, string, error) {
	return m.CreateManagedTLSConfigWithExclusions(serverName, nil)
}

// CreateManagedTLSConfigWithExclusions 创建TLS配置，支持排除指定指纹
func (m *Manager) CreateManagedTLSConfigWithExclusions(serverName string, excludeProfiles []string) (*tls.Config, string, error) {
	// 选择合适的指纹配置，排除指定指纹
	profile, err := m.SelectProfileWithExclusions(serverName, excludeProfiles)
	if err != nil {
		return nil, "", err
	}

	// 创建TLS配置
	tlsConfig := &tls.Config{
		MinVersion:         profile.MinVersion,
		MaxVersion:         profile.MaxVersion,
		CipherSuites:       profile.CipherSuites,
		CurvePreferences:   profile.CurvePreferences,
		NextProtos:         profile.NextProtos,
		ServerName:         serverName,
		InsecureSkipVerify: false,
		ClientSessionCache: CreateSmartSessionCache(profile.SessionCache),
	}

	return tlsConfig, profile.Name, nil
}

// 全局指纹管理器实例
var globalManager *Manager
var managerOnce sync.Once

// GetGlobalManager 获取全局指纹管理器实例
func GetGlobalManager() *Manager {
	managerOnce.Do(func() {
		globalManager = NewManager()
	})
	return globalManager
}

// InitializeFromConfig 使用配置初始化全局指纹管理器
func InitializeFromConfig(config map[string]interface{}) error {
	manager := GetGlobalManager()
	return manager.LoadConfigFromMiddleware(config)
}
