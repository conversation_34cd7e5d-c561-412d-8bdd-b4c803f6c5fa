package fingerprint

import (
	"sync"
	"sync/atomic"
	"time"
)

// Health TLS指纹健康状态
type Health struct {
	// 基础信息
	ProfileName string    // 指纹配置名称
	Region      string    // 适用地区（INTL、ASIA、NA等）
	CreatedAt   time.Time // 创建时间

	// 使用统计
	TotalRequests     atomic.Int64 // 总请求数
	SuccessRequests   atomic.Int64 // 成功请求数
	FailedRequests    atomic.Int64 // 失败请求数
	BlockedRequests   atomic.Int64 // 被风控的请求数
	LastUsedAt        atomic.Value // 最后使用时间 (time.Time)
	ConsecutiveErrors atomic.Int32 // 连续错误次数

	// 性能指标
	AvgResponseTime atomic.Value // 平均响应时间 (time.Duration)
	LastError       atomic.Value // 最后一次错误信息 (string)

	// 状态标记
	Status       atomic.Value // 状态: "active", "limited", "blocked"
	BlockedUntil atomic.Value // 封禁截止时间 (time.Time)
	HealthScore  atomic.Value // 健康评分 0-100 (float64)

	// 并发控制
	mu sync.RWMutex
}

// NewHealth 创建新的指纹健康追踪器
func NewHealth(profileName, region string) *Health {
	h := &Health{
		ProfileName: profileName,
		Region:      region,
		CreatedAt:   time.Now(),
	}

	// 初始化原子值
	h.LastUsedAt.Store(time.Now())
	h.Status.Store("active")
	h.HealthScore.Store(float64(100))
	h.AvgResponseTime.Store(time.Duration(0))
	h.LastError.Store("")
	h.BlockedUntil.Store(time.Time{})

	return h
}

// RecordSuccess 记录成功请求
func (h *Health) RecordSuccess(responseTime time.Duration) {
	h.TotalRequests.Add(1)
	h.SuccessRequests.Add(1)
	h.ConsecutiveErrors.Store(0)
	h.LastUsedAt.Store(time.Now())

	// 更新平均响应时间
	h.updateAvgResponseTime(responseTime)

	// 更新健康评分
	h.updateHealthScore()
}

// RecordFailure 记录失败请求
func (h *Health) RecordFailure(err string, isBlocked bool) {
	h.TotalRequests.Add(1)
	h.FailedRequests.Add(1)
	h.ConsecutiveErrors.Add(1)
	h.LastUsedAt.Store(time.Now())
	h.LastError.Store(err)

	if isBlocked {
		h.BlockedRequests.Add(1)
		// 如果连续被风控，增加封禁时间
		if h.ConsecutiveErrors.Load() >= 3 {
			h.setBlocked(time.Hour) // 封禁1小时
		}
	}

	// 更新健康评分
	h.updateHealthScore()
}

// IsHealthy 检查指纹是否健康可用
func (h *Health) IsHealthy() bool {
	status := h.Status.Load().(string)
	if status == "blocked" {
		// 检查封禁是否已过期
		blockedUntil := h.BlockedUntil.Load().(time.Time)
		if !blockedUntil.IsZero() && time.Now().After(blockedUntil) {
			h.Status.Store("active")
			h.BlockedUntil.Store(time.Time{})
			return true
		}
		return false
	}

	// 检查健康评分
	score := h.HealthScore.Load().(float64)
	return score > 30 && status == "active"
}

// GetSuccessRate 获取成功率
func (h *Health) GetSuccessRate() float64 {
	total := h.TotalRequests.Load()
	if total == 0 {
		return 1.0 // 新指纹默认100%成功率
	}

	success := h.SuccessRequests.Load()
	return float64(success) / float64(total)
}

// GetHealthScore 获取健康评分
func (h *Health) GetHealthScore() float64 {
	return h.HealthScore.Load().(float64)
}

// ShouldRotate 判断是否应该轮换
func (h *Health) ShouldRotate(maxRequests int64, maxDuration time.Duration) bool {
	// 检查请求数限制
	if h.TotalRequests.Load() >= maxRequests {
		return true
	}

	// 检查使用时长限制
	lastUsed := h.LastUsedAt.Load().(time.Time)
	if time.Since(lastUsed) > maxDuration {
		return true
	}

	// 检查连续错误
	if h.ConsecutiveErrors.Load() >= 5 {
		return true
	}

	// 检查健康评分
	if h.GetHealthScore() < 50 {
		return true
	}

	return false
}

// updateAvgResponseTime 更新平均响应时间
func (h *Health) updateAvgResponseTime(newTime time.Duration) {
	h.mu.Lock()
	defer h.mu.Unlock()

	current := h.AvgResponseTime.Load().(time.Duration)
	total := h.TotalRequests.Load()

	if total == 1 {
		h.AvgResponseTime.Store(newTime)
	} else {
		// 计算新的平均值
		avg := (current*time.Duration(total-1) + newTime) / time.Duration(total)
		h.AvgResponseTime.Store(avg)
	}
}

// updateHealthScore 更新健康评分
func (h *Health) updateHealthScore() {
	h.mu.Lock()
	defer h.mu.Unlock()

	// 基础分数从成功率开始
	successRate := h.GetSuccessRate()
	score := successRate * 70 // 成功率占70%权重

	// 连续错误扣分
	consecutiveErrors := h.ConsecutiveErrors.Load()
	if consecutiveErrors > 0 {
		score -= float64(consecutiveErrors) * 5
	}

	// 被风控次数扣分
	blockedRate := float64(h.BlockedRequests.Load()) / float64(max(h.TotalRequests.Load(), 1))
	score -= blockedRate * 20

	// 使用新鲜度加分（最近使用的指纹加分）
	lastUsed := h.LastUsedAt.Load().(time.Time)
	freshness := time.Since(lastUsed)
	if freshness < 5*time.Minute {
		score += 10
	} else if freshness > 30*time.Minute {
		score -= 10
	}

	// 限制分数范围
	if score > 100 {
		score = 100
	} else if score < 0 {
		score = 0
	}

	h.HealthScore.Store(score)

	// 根据分数更新状态
	if score < 30 {
		h.Status.Store("limited")
	} else if score < 10 {
		h.Status.Store("blocked")
	}
}

// setBlocked 设置封禁状态
func (h *Health) setBlocked(duration time.Duration) {
	h.Status.Store("blocked")
	h.BlockedUntil.Store(time.Now().Add(duration))
	h.HealthScore.Store(float64(0))
}

// Reset 重置健康状态（用于定期清理）
func (h *Health) Reset() {
	h.TotalRequests.Store(0)
	h.SuccessRequests.Store(0)
	h.FailedRequests.Store(0)
	h.BlockedRequests.Store(0)
	h.ConsecutiveErrors.Store(0)
	h.LastUsedAt.Store(time.Now())
	h.Status.Store("active")
	h.HealthScore.Store(float64(100))
	h.AvgResponseTime.Store(time.Duration(0))
	h.LastError.Store("")
	h.BlockedUntil.Store(time.Time{})
}

// GetStats 获取统计信息
func (h *Health) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"profile_name":       h.ProfileName,
		"region":             h.Region,
		"total_requests":     h.TotalRequests.Load(),
		"success_requests":   h.SuccessRequests.Load(),
		"failed_requests":    h.FailedRequests.Load(),
		"blocked_requests":   h.BlockedRequests.Load(),
		"success_rate":       h.GetSuccessRate(),
		"health_score":       h.GetHealthScore(),
		"status":             h.Status.Load().(string),
		"last_used_at":       h.LastUsedAt.Load().(time.Time),
		"avg_response_time":  h.AvgResponseTime.Load().(time.Duration),
		"consecutive_errors": h.ConsecutiveErrors.Load(),
		"last_error":         h.LastError.Load().(string),
	}
}

// 辅助函数
func max(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}
