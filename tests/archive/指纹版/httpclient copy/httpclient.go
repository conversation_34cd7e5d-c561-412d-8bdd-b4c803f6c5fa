package httpclient

import (
	"context"

	"go-monitor/pkg/httpclient/config"
	"go-monitor/pkg/httpclient/core"
	"go-monitor/pkg/httpclient/fingerprint"
	"go-monitor/pkg/httpclient/transport"
)

// HTTPClient 统一的HTTP客户端接口
type HTTPClient interface {
	Get(ctx context.Context, url string, headers map[string]string) (*core.Response, error)
	Post(ctx context.Context, url string, body []byte, headers map[string]string) (*core.Response, error)
	PostJSON(ctx context.Context, url string, data interface{}, headers map[string]string) (*core.Response, error)
	Do(ctx context.Context, request *core.Request) (*core.Response, error)
}

// 类型别名，便于使用
type (
	Config   = config.Config
	Request  = core.Request
	Response = core.Response
)

// 构造函数别名
var (
	NewRequest     = core.NewRequest
	NewJSONRequest = core.NewJSONRequest
)

// NewClient 创建新的HTTP客户端
func NewClient(cfg *config.Config) HTTPClient {
	return core.NewClient(cfg)
}

// DefaultClient 创建默认配置的HTTP客户端
func DefaultClient() HTTPClient {
	return core.NewClient(config.Default())
}

// NewConfiguredClient 创建配置好TLS指纹管理的HTTP客户端
func NewConfiguredClient(cfg *config.Config) HTTPClient {
	// 初始化指纹管理器
	manager := fingerprint.GetGlobalManager()

	// 设置transport的指纹管理器
	transport.SetFingerprintManager(manager)

	return core.NewClient(cfg)
}

// InitializeFingerprint 初始化TLS指纹管理
func InitializeFingerprint(config map[string]interface{}) error {
	return fingerprint.InitializeFromConfig(config)
}

// GetFingerprintStats 获取指纹统计信息
func GetFingerprintStats() map[string]interface{} {
	manager := fingerprint.GetGlobalManager()
	return manager.GetStats()
}
