// Package pool 指纹选择策略
package pool

import (
	"math/rand"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/core"
)

// RandomStrategy 随机选择策略
type RandomStrategy struct{}

// NewRandomStrategy 创建随机选择策略
func NewRandomStrategy() *RandomStrategy {
	return &RandomStrategy{}
}

// Select 随机选择指纹
func (s *RandomStrategy) Select(profiles map[string]*core.PoolProfile, excludeNames []string) (*core.PoolProfile, error) {
	candidates := s.filterCandidates(profiles, excludeNames)
	if len(candidates) == 0 {
		return nil, core.ErrNoHealthyProfiles
	}

	// 随机选择
	index := rand.Intn(len(candidates))
	return candidates[index], nil
}

// GetName 获取策略名称
func (s *RandomStrategy) GetName() string {
	return "random"
}

// filterCandidates 过滤候选指纹
func (s *RandomStrategy) filterCandidates(profiles map[string]*core.PoolProfile, excludeNames []string) []*core.PoolProfile {
	var candidates []*core.PoolProfile
	
	for _, profile := range profiles {
		// 检查是否在排除列表中
		excluded := false
		for _, excludeName := range excludeNames {
			if profile.Profile.Name == excludeName {
				excluded = true
				break
			}
		}
		
		if !excluded && s.isHealthy(profile) {
			candidates = append(candidates, profile)
		}
	}
	
	return candidates
}

// isHealthy 检查指纹是否健康
func (s *RandomStrategy) isHealthy(profile *core.PoolProfile) bool {
	if profile.Health == nil {
		return true
	}
	return profile.Health.Status == core.HealthStatusHealthy
}

// LeastRecentlyUsedStrategy 最少最近使用策略
type LeastRecentlyUsedStrategy struct{}

// NewLeastRecentlyUsedStrategy 创建LRU策略
func NewLeastRecentlyUsedStrategy() *LeastRecentlyUsedStrategy {
	return &LeastRecentlyUsedStrategy{}
}

// Select 选择最少最近使用的指纹
func (s *LeastRecentlyUsedStrategy) Select(profiles map[string]*core.PoolProfile, excludeNames []string) (*core.PoolProfile, error) {
	candidates := s.filterCandidates(profiles, excludeNames)
	if len(candidates) == 0 {
		return nil, core.ErrNoHealthyProfiles
	}

	// 选择最少最近使用的
	var selected *core.PoolProfile
	for _, candidate := range candidates {
		if selected == nil || candidate.LastUsed.Before(selected.LastUsed) {
			selected = candidate
		}
	}

	return selected, nil
}

// GetName 获取策略名称
func (s *LeastRecentlyUsedStrategy) GetName() string {
	return "least_recently_used"
}

// filterCandidates 过滤候选指纹
func (s *LeastRecentlyUsedStrategy) filterCandidates(profiles map[string]*core.PoolProfile, excludeNames []string) []*core.PoolProfile {
	var candidates []*core.PoolProfile
	
	for _, profile := range profiles {
		excluded := false
		for _, excludeName := range excludeNames {
			if profile.Profile.Name == excludeName {
				excluded = true
				break
			}
		}
		
		if !excluded && s.isHealthy(profile) {
			candidates = append(candidates, profile)
		}
	}
	
	return candidates
}

// isHealthy 检查指纹是否健康
func (s *LeastRecentlyUsedStrategy) isHealthy(profile *core.PoolProfile) bool {
	if profile.Health == nil {
		return true
	}
	return profile.Health.Status == core.HealthStatusHealthy
}

// SuccessRateStrategy 成功率优先策略
type SuccessRateStrategy struct{}

// NewSuccessRateStrategy 创建成功率策略
func NewSuccessRateStrategy() *SuccessRateStrategy {
	return &SuccessRateStrategy{}
}

// Select 选择成功率最高的指纹
func (s *SuccessRateStrategy) Select(profiles map[string]*core.PoolProfile, excludeNames []string) (*core.PoolProfile, error) {
	candidates := s.filterCandidates(profiles, excludeNames)
	if len(candidates) == 0 {
		return nil, core.ErrNoHealthyProfiles
	}

	// 选择成功率最高的
	var selected *core.PoolProfile
	for _, candidate := range candidates {
		if selected == nil || candidate.SuccessRate > selected.SuccessRate {
			selected = candidate
		}
	}

	return selected, nil
}

// GetName 获取策略名称
func (s *SuccessRateStrategy) GetName() string {
	return "success_rate"
}

// filterCandidates 过滤候选指纹
func (s *SuccessRateStrategy) filterCandidates(profiles map[string]*core.PoolProfile, excludeNames []string) []*core.PoolProfile {
	var candidates []*core.PoolProfile
	
	for _, profile := range profiles {
		excluded := false
		for _, excludeName := range excludeNames {
			if profile.Profile.Name == excludeName {
				excluded = true
				break
			}
		}
		
		if !excluded && s.isHealthy(profile) {
			candidates = append(candidates, profile)
		}
	}
	
	return candidates
}

// isHealthy 检查指纹是否健康
func (s *SuccessRateStrategy) isHealthy(profile *core.PoolProfile) bool {
	if profile.Health == nil {
		return true
	}
	return profile.Health.Status == core.HealthStatusHealthy
}

// RoundRobinStrategy 轮询策略
type RoundRobinStrategy struct {
	lastIndex int
}

// NewRoundRobinStrategy 创建轮询策略
func NewRoundRobinStrategy() *RoundRobinStrategy {
	return &RoundRobinStrategy{
		lastIndex: -1,
	}
}

// Select 轮询选择指纹
func (s *RoundRobinStrategy) Select(profiles map[string]*core.PoolProfile, excludeNames []string) (*core.PoolProfile, error) {
	candidates := s.filterCandidates(profiles, excludeNames)
	if len(candidates) == 0 {
		return nil, core.ErrNoHealthyProfiles
	}

	// 轮询选择
	s.lastIndex = (s.lastIndex + 1) % len(candidates)
	return candidates[s.lastIndex], nil
}

// GetName 获取策略名称
func (s *RoundRobinStrategy) GetName() string {
	return "round_robin"
}

// filterCandidates 过滤候选指纹
func (s *RoundRobinStrategy) filterCandidates(profiles map[string]*core.PoolProfile, excludeNames []string) []*core.PoolProfile {
	var candidates []*core.PoolProfile
	
	for _, profile := range profiles {
		excluded := false
		for _, excludeName := range excludeNames {
			if profile.Profile.Name == excludeName {
				excluded = true
				break
			}
		}
		
		if !excluded && s.isHealthy(profile) {
			candidates = append(candidates, profile)
		}
	}
	
	return candidates
}

// isHealthy 检查指纹是否健康
func (s *RoundRobinStrategy) isHealthy(profile *core.PoolProfile) bool {
	if profile.Health == nil {
		return true
	}
	return profile.Health.Status == core.HealthStatusHealthy
}

// WeightedStrategy 加权选择策略
type WeightedStrategy struct{}

// NewWeightedStrategy 创建加权策略
func NewWeightedStrategy() *WeightedStrategy {
	return &WeightedStrategy{}
}

// Select 加权选择指纹
func (s *WeightedStrategy) Select(profiles map[string]*core.PoolProfile, excludeNames []string) (*core.PoolProfile, error) {
	candidates := s.filterCandidates(profiles, excludeNames)
	if len(candidates) == 0 {
		return nil, core.ErrNoHealthyProfiles
	}

	// 计算权重并选择
	totalWeight := 0.0
	weights := make([]float64, len(candidates))
	
	for i, candidate := range candidates {
		weight := s.calculateWeight(candidate)
		weights[i] = weight
		totalWeight += weight
	}

	// 随机选择
	r := rand.Float64() * totalWeight
	cumulative := 0.0
	
	for i, weight := range weights {
		cumulative += weight
		if r <= cumulative {
			return candidates[i], nil
		}
	}

	// fallback
	return candidates[0], nil
}

// GetName 获取策略名称
func (s *WeightedStrategy) GetName() string {
	return "weighted"
}

// calculateWeight 计算权重
func (s *WeightedStrategy) calculateWeight(profile *core.PoolProfile) float64 {
	weight := profile.SuccessRate
	
	// 考虑使用频率（使用越少权重越高）
	if profile.UseCount > 0 {
		weight *= (1.0 / float64(profile.UseCount))
	}
	
	// 考虑最后使用时间（越久未使用权重越高）
	if !profile.LastUsed.IsZero() {
		timeSinceUse := time.Since(profile.LastUsed).Hours()
		weight *= (1.0 + timeSinceUse/24.0) // 每天增加权重
	}
	
	return weight
}

// filterCandidates 过滤候选指纹
func (s *WeightedStrategy) filterCandidates(profiles map[string]*core.PoolProfile, excludeNames []string) []*core.PoolProfile {
	var candidates []*core.PoolProfile
	
	for _, profile := range profiles {
		excluded := false
		for _, excludeName := range excludeNames {
			if profile.Profile.Name == excludeName {
				excluded = true
				break
			}
		}
		
		if !excluded && s.isHealthy(profile) {
			candidates = append(candidates, profile)
		}
	}
	
	return candidates
}

// isHealthy 检查指纹是否健康
func (s *WeightedStrategy) isHealthy(profile *core.PoolProfile) bool {
	if profile.Health == nil {
		return true
	}
	return profile.Health.Status == core.HealthStatusHealthy
}
