// Package core 定义指纹管理的错误类型
package core

import (
	"errors"
	"fmt"
)

// 预定义错误
var (
	// 通用错误
	ErrProfileNotFound     = errors.New("指纹配置未找到")
	ErrPoolNotFound        = errors.New("指纹池未找到")
	ErrInvalidConfig       = errors.New("无效的配置")
	ErrManagerNotInitialized = errors.New("管理器未初始化")

	// 指纹相关错误
	ErrProfileInvalid      = errors.New("指纹配置无效")
	ErrProfileBlocked      = errors.New("指纹被风控封禁")
	ErrProfileExpired      = errors.New("指纹已过期")
	ErrNoHealthyProfiles   = errors.New("没有健康的指纹可用")

	// 池管理错误
	ErrPoolFull            = errors.New("指纹池已满")
	ErrPoolEmpty           = errors.New("指纹池为空")
	ErrInvalidPoolConfig   = errors.New("无效的池配置")

	// 生成器错误
	ErrGenerationFailed    = errors.New("指纹生成失败")
	ErrNoTemplateAvailable = errors.New("没有可用的模板")
	ErrVariationFailed     = errors.New("指纹变异失败")

	// 配置错误
	ErrConfigNotFound      = errors.New("配置文件未找到")
	ErrConfigParseError    = errors.New("配置解析错误")
	ErrConfigValidationError = errors.New("配置验证错误")
)

// FingerprintError 指纹相关错误
type FingerprintError struct {
	Type    string // 错误类型
	Message string // 错误消息
	Profile string // 相关指纹名称
	Cause   error  // 原始错误
}

// Error 实现 error 接口
func (e *FingerprintError) Error() string {
	if e.Profile != "" {
		return fmt.Sprintf("指纹错误 [%s] %s: %s", e.Profile, e.Type, e.Message)
	}
	return fmt.Sprintf("指纹错误 [%s]: %s", e.Type, e.Message)
}

// Unwrap 支持错误链
func (e *FingerprintError) Unwrap() error {
	return e.Cause
}

// NewFingerprintError 创建指纹错误
func NewFingerprintError(errorType, message, profile string, cause error) *FingerprintError {
	return &FingerprintError{
		Type:    errorType,
		Message: message,
		Profile: profile,
		Cause:   cause,
	}
}

// PoolError 指纹池相关错误
type PoolError struct {
	Type     string // 错误类型
	Message  string // 错误消息
	PoolName string // 池名称
	Cause    error  // 原始错误
}

// Error 实现 error 接口
func (e *PoolError) Error() string {
	if e.PoolName != "" {
		return fmt.Sprintf("指纹池错误 [%s] %s: %s", e.PoolName, e.Type, e.Message)
	}
	return fmt.Sprintf("指纹池错误 [%s]: %s", e.Type, e.Message)
}

// Unwrap 支持错误链
func (e *PoolError) Unwrap() error {
	return e.Cause
}

// NewPoolError 创建指纹池错误
func NewPoolError(errorType, message, poolName string, cause error) *PoolError {
	return &PoolError{
		Type:     errorType,
		Message:  message,
		PoolName: poolName,
		Cause:    cause,
	}
}

// ConfigError 配置相关错误
type ConfigError struct {
	Type     string // 错误类型
	Message  string // 错误消息
	FilePath string // 配置文件路径
	Cause    error  // 原始错误
}

// Error 实现 error 接口
func (e *ConfigError) Error() string {
	if e.FilePath != "" {
		return fmt.Sprintf("配置错误 [%s] %s: %s", e.FilePath, e.Type, e.Message)
	}
	return fmt.Sprintf("配置错误 [%s]: %s", e.Type, e.Message)
}

// Unwrap 支持错误链
func (e *ConfigError) Unwrap() error {
	return e.Cause
}

// NewConfigError 创建配置错误
func NewConfigError(errorType, message, filePath string, cause error) *ConfigError {
	return &ConfigError{
		Type:     errorType,
		Message:  message,
		FilePath: filePath,
		Cause:    cause,
	}
}

// 错误类型常量
const (
	// 指纹错误类型
	ErrorTypeProfileNotFound   = "profile_not_found"
	ErrorTypeProfileBlocked    = "profile_blocked"
	ErrorTypeProfileExpired    = "profile_expired"
	ErrorTypeProfileInvalid    = "profile_invalid"

	// 池错误类型
	ErrorTypePoolNotFound      = "pool_not_found"
	ErrorTypePoolFull          = "pool_full"
	ErrorTypePoolEmpty         = "pool_empty"
	ErrorTypePoolConfigInvalid = "pool_config_invalid"

	// 生成错误类型
	ErrorTypeGenerationFailed  = "generation_failed"
	ErrorTypeNoTemplate        = "no_template"
	ErrorTypeVariationFailed   = "variation_failed"

	// 配置错误类型
	ErrorTypeConfigNotFound    = "config_not_found"
	ErrorTypeConfigParse       = "config_parse"
	ErrorTypeConfigValidation  = "config_validation"

	// 管理器错误类型
	ErrorTypeManagerNotInit    = "manager_not_initialized"
	ErrorTypeManagerClosed     = "manager_closed"
)

// IsProfileError 检查是否为指纹相关错误
func IsProfileError(err error) bool {
	var fpErr *FingerprintError
	return errors.As(err, &fpErr)
}

// IsPoolError 检查是否为池相关错误
func IsPoolError(err error) bool {
	var poolErr *PoolError
	return errors.As(err, &poolErr)
}

// IsConfigError 检查是否为配置相关错误
func IsConfigError(err error) bool {
	var configErr *ConfigError
	return errors.As(err, &configErr)
}

// WrapError 包装错误，添加上下文信息
func WrapError(err error, context string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", context, err)
}

// IsTemporaryError 检查是否为临时错误（可重试）
func IsTemporaryError(err error) bool {
	// 检查是否为风控相关的临时错误
	if fpErr, ok := err.(*FingerprintError); ok {
		return fpErr.Type == ErrorTypeProfileBlocked
	}

	// 检查是否为池相关的临时错误
	if poolErr, ok := err.(*PoolError); ok {
		return poolErr.Type == ErrorTypePoolEmpty
	}

	return false
}

// IsPermanentError 检查是否为永久错误（不可重试）
func IsPermanentError(err error) bool {
	// 检查是否为配置错误（通常是永久的）
	if IsConfigError(err) {
		return true
	}

	// 检查是否为指纹无效错误
	if fpErr, ok := err.(*FingerprintError); ok {
		return fpErr.Type == ErrorTypeProfileInvalid
	}

	return false
}
