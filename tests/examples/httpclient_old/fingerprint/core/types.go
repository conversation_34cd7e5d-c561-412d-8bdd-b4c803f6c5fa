// Package core 定义指纹管理的核心类型
package core

import (
	"crypto/tls"
	"strings"
	"time"
)

// RiskControlType 风控类型
type RiskControlType int

const (
	RiskControlNone     RiskControlType = iota // 无风控
	RiskControlLight                           // 轻度风控（403, 429等）
	RiskControlModerate                        // 中度风控（418等）
	RiskControlSevere                          // 重度风控（470, 471等）
)

// String 返回风控类型名称
func (r RiskControlType) String() string {
	switch r {
	case RiskControlNone:
		return "none"
	case RiskControlLight:
		return "light"
	case RiskControlModerate:
		return "moderate"
	case RiskControlSevere:
		return "severe"
	default:
		return "unknown"
	}
}

// RiskControlInfo 风控信息
type RiskControlInfo struct {
	Type         RiskControlType // 风控类型
	StatusCode   int             // HTTP状态码
	Reason       string          // 风控原因
	BlockTime    time.Duration   // 建议阻塞时间
	Severity     int             // 严重程度 (1-10)
	NeedRefresh  bool            // 是否需要刷新指纹
	NeedGenerate bool            // 是否需要生成新指纹
}

// ProfileTier 指纹层级
type ProfileTier int

const (
	TierVerified  ProfileTier = iota // 已验证层级
	TierCandidate                    // 候选层级
	TierBackup                       // 备用层级
)

// String 返回层级名称
func (t ProfileTier) String() string {
	switch t {
	case TierVerified:
		return "verified"
	case TierCandidate:
		return "candidate"
	case TierBackup:
		return "backup"
	default:
		return "unknown"
	}
}

// PoolProfile 池中的指纹配置
type PoolProfile struct {
	Profile     *Profile      // 指纹配置
	Tier        ProfileTier   // 层级
	Health      *HealthStatus // 健康状态
	AddedAt     time.Time     // 添加时间
	LastUsed    time.Time     // 最后使用时间
	UseCount    int64         // 使用次数
	SuccessRate float64       // 成功率
}

// PoolStats 池统计信息
type PoolStats struct {
	TotalProfiles    int                    // 总指纹数
	VerifiedCount    int                    // 已验证指纹数
	CandidateCount   int                    // 候选指纹数
	BackupCount      int                    // 备用指纹数
	HealthyCount     int                    // 健康指纹数
	BlockedCount     int                    // 被封指纹数
	TierDistribution map[ProfileTier]int    // 层级分布
	AvgSuccessRate   float64                // 平均成功率
	LastCleanup      time.Time              // 最后清理时间
	GenerationCount  int                    // 生成次数
	PromotionCount   int                    // 晋升次数
	DemotionCount    int                    // 降级次数
}

// PoolConfig 池配置
type PoolConfig struct {
	// 基础配置
	MaxProfiles          int           // 最大指纹数
	SelectionStrategy    string        // 选择策略
	EnableSmartGeneration bool          // 启用智能生成
	
	// 质量阈值
	PromotionSuccessRate float64       // 晋升成功率阈值
	PromotionMinUses     int           // 晋升最小使用次数
	DemotionSuccessRate  float64       // 降级成功率阈值
	CleanupSuccessRate   float64       // 清理成功率阈值
	
	// 时间配置
	ObservationPeriod    time.Duration // 观察期
	PromotionCooldown    time.Duration // 晋升冷却时间
	GenerationCooldown   time.Duration // 生成冷却时间
	CleanupInterval      time.Duration // 清理间隔
	
	// 生成配置
	VariationStrength    float64       // 变异强度
	MaxFailedAttempts    int           // 最大失败尝试次数
	BaseOnSuccessful     bool          // 基于成功指纹生成
}

// DetectionPattern 风控检测模式
type DetectionPattern struct {
	ServerName         string    // 服务器名称
	BlockedStatusCodes []int     // 风控状态码
	BlockedKeywords    []string  // 风控关键词
	DetectionMethods   []string  // 检测方法
	Sensitivity        float64   // 敏感度 (0-1)
	LastUpdated        time.Time // 最后更新时间
	SampleCount        int       // 样本数量
}

// IsBlockedResponse 判断响应是否表示被风控
func IsBlockedResponse(statusCode int, err error) bool {
	info := AnalyzeRiskControl(statusCode, err)
	return info.Type != RiskControlNone
}

// AnalyzeRiskControl 分析风控情况，返回详细信息
func AnalyzeRiskControl(statusCode int, err error) *RiskControlInfo {
	info := &RiskControlInfo{
		Type:         RiskControlNone,
		StatusCode:   statusCode,
		Reason:       "",
		BlockTime:    0,
		Severity:     0,
		NeedRefresh:  false,
		NeedGenerate: false,
	}

	// 检查特定的风控状态码
	switch statusCode {
	case 470, 471:
		// 重度风控 - PopMart等平台的自定义风控状态码
		info.Type = RiskControlSevere
		info.Reason = "自定义风控状态码"
		info.BlockTime = 30 * time.Minute
		info.Severity = 9
		info.NeedRefresh = true
		info.NeedGenerate = true
		return info

	case 418:
		// 中度风控 - I'm a teapot，通常用于反爬虫
		info.Type = RiskControlModerate
		info.Reason = "反爬虫检测"
		info.BlockTime = 20 * time.Minute
		info.Severity = 6
		info.NeedRefresh = true
		info.NeedGenerate = true
		return info

	case 403:
		// 轻度风控 - 禁止访问
		info.Type = RiskControlLight
		info.Reason = "访问被禁止"
		info.BlockTime = 10 * time.Minute
		info.Severity = 4
		info.NeedRefresh = true
		info.NeedGenerate = false
		return info

	case 429:
		// 轻度风控 - 请求过多
		info.Type = RiskControlLight
		info.Reason = "请求频率限制"
		info.BlockTime = 5 * time.Minute
		info.Severity = 3
		info.NeedRefresh = false
		info.NeedGenerate = false
		return info
	}

	// 检查错误信息中的风控关键词
	if err != nil {
		errStr := strings.ToLower(err.Error())

		// 重度风控关键词
		severeKeywords := []string{"blocked", "banned", "suspended", "封禁", "暂停"}
		for _, keyword := range severeKeywords {
			if strings.Contains(errStr, keyword) {
				info.Type = RiskControlSevere
				info.Reason = "账户被封禁"
				info.BlockTime = 60 * time.Minute
				info.Severity = 8
				info.NeedRefresh = true
				info.NeedGenerate = true
				return info
			}
		}

		// 中度风控关键词
		moderateKeywords := []string{"captcha", "challenge", "verification", "验证码", "人机验证"}
		for _, keyword := range moderateKeywords {
			if strings.Contains(errStr, keyword) {
				info.Type = RiskControlModerate
				info.Reason = "需要人机验证"
				info.BlockTime = 15 * time.Minute
				info.Severity = 5
				info.NeedRefresh = true
				info.NeedGenerate = false
				return info
			}
		}

		// 轻度风控关键词
		lightKeywords := []string{"rate limit", "forbidden", "access denied", "访问被拒绝", "请稍后再试"}
		for _, keyword := range lightKeywords {
			if strings.Contains(errStr, keyword) {
				info.Type = RiskControlLight
				info.Reason = "访问限制"
				info.BlockTime = 8 * time.Minute
				info.Severity = 2
				info.NeedRefresh = false
				info.NeedGenerate = false
				return info
			}
		}
	}

	return info
}

// CreateSmartSessionCache 创建智能会话缓存
func CreateSmartSessionCache(strategy SessionCacheStrategy) tls.ClientSessionCache {
	if !strategy.Enabled {
		return nil
	}

	// 检查是否应该随机禁用缓存
	if strategy.ClearProbability > 0 {
		// 简化实现，实际应该使用rand
		return tls.NewLRUClientSessionCache(strategy.Size)
	}

	return tls.NewLRUClientSessionCache(strategy.Size)
}
