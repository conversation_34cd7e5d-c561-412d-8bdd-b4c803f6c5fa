// Package profiles 预定义指纹配置
package profiles

import (
	"crypto/tls"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/core"
)

// GetPredefinedProfiles 获取所有预定义的指纹配置
func GetPredefinedProfiles() []*core.Profile {
	now := time.Now()
	
	return []*core.Profile{
		// Chrome 浏览器指纹
		{
			Name:       "Chrome_Windows_Standard",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             128,
				DisableOnBurst:   false,
				MaxCacheAge:      300,
				ClearProbability: 0.1,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeChrome,
			OSType:         core.OSTypeWindows,
			Version:        "120.0.6099.109",
			IsDynamic:      false,
			BaseTemplate:   "chrome_standard",
			SuccessRate:    0.95,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		// Firefox 浏览器指纹
		{
			Name:       "Firefox_Windows_Standard",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             64,
				DisableOnBurst:   false,
				MaxCacheAge:      240,
				ClearProbability: 0.05,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeFirefox,
			OSType:         core.OSTypeWindows,
			Version:        "121.0",
			IsDynamic:      false,
			BaseTemplate:   "firefox_standard",
			SuccessRate:    0.92,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		// Safari 浏览器指纹
		{
			Name:       "Safari_macOS_Standard",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             32,
				DisableOnBurst:   false,
				MaxCacheAge:      180,
				ClearProbability: 0.02,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeSafari,
			OSType:         core.OSTypeMacOS,
			Version:        "17.2",
			IsDynamic:      false,
			BaseTemplate:   "safari_standard",
			SuccessRate:    0.90,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		// Edge 浏览器指纹
		{
			Name:       "Edge_Windows_Standard",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             96,
				DisableOnBurst:   false,
				MaxCacheAge:      270,
				ClearProbability: 0.08,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeEdge,
			OSType:         core.OSTypeWindows,
			Version:        "120.0.2210.91",
			IsDynamic:      false,
			BaseTemplate:   "edge_standard",
			SuccessRate:    0.93,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		// 移动端 Chrome 指纹
		{
			Name:       "Chrome_Android_Mobile",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             32,
				DisableOnBurst:   true,
				MaxCacheAge:      120,
				ClearProbability: 0.15,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeChrome,
			OSType:         core.OSTypeAndroid,
			Version:        "120.0.6099.43",
			IsDynamic:      false,
			BaseTemplate:   "chrome_mobile",
			SuccessRate:    0.88,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		// iOS Safari 指纹
		{
			Name:       "Safari_iOS_Mobile",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             16,
				DisableOnBurst:   false,
				MaxCacheAge:      90,
				ClearProbability: 0.1,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeSafari,
			OSType:         core.OSTypeIOS,
			Version:        "17.2",
			IsDynamic:      false,
			BaseTemplate:   "safari_mobile",
			SuccessRate:    0.85,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},
	}
}

// GetRandomProfile 随机选择一个预定义指纹
func GetRandomProfile() *core.Profile {
	profiles := GetPredefinedProfiles()
	if len(profiles) == 0 {
		return nil
	}
	
	// 简单的伪随机选择
	index := int(time.Now().UnixNano()) % len(profiles)
	return profiles[index]
}

// GetProfilesByBrowser 按浏览器类型获取指纹
func GetProfilesByBrowser(browserType string) []*core.Profile {
	profiles := GetPredefinedProfiles()
	var result []*core.Profile
	
	for _, profile := range profiles {
		if profile.BrowserType == browserType {
			result = append(result, profile)
		}
	}
	
	return result
}

// GetProfilesByOS 按操作系统获取指纹
func GetProfilesByOS(osType string) []*core.Profile {
	profiles := GetPredefinedProfiles()
	var result []*core.Profile
	
	for _, profile := range profiles {
		if profile.OSType == osType {
			result = append(result, profile)
		}
	}
	
	return result
}

// GetDesktopProfiles 获取桌面端指纹
func GetDesktopProfiles() []*core.Profile {
	profiles := GetPredefinedProfiles()
	var result []*core.Profile
	
	for _, profile := range profiles {
		if profile.OSType == core.OSTypeWindows || 
		   profile.OSType == core.OSTypeMacOS || 
		   profile.OSType == core.OSTypeLinux {
			result = append(result, profile)
		}
	}
	
	return result
}

// GetMobileProfiles 获取移动端指纹
func GetMobileProfiles() []*core.Profile {
	profiles := GetPredefinedProfiles()
	var result []*core.Profile
	
	for _, profile := range profiles {
		if profile.OSType == core.OSTypeAndroid || 
		   profile.OSType == core.OSTypeIOS {
			result = append(result, profile)
		}
	}
	
	return result
}
