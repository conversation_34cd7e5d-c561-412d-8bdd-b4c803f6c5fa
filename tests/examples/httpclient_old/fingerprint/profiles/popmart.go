// Package profiles PopMart专用指纹配置
package profiles

import (
	"crypto/tls"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/core"
)

// Charles抓包TLS密码套件常量定义 (基于真实抓包数据)
const (
	// TLS 1.3 cipher suites
	TLS_AES_256_GCM_SHA384_CHARLES       = 0x1302 // Charles优先选择
	TLS_AES_128_GCM_SHA256_CHARLES       = 0x1301 // 标准Chrome优先
	TLS_CHACHA20_POLY1305_SHA256_CHARLES = 0x1303 // ChaCha20支持

	// ECDHE cipher suites
	TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384_CHARLES       = 0xC02C
	TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256_CHARLES       = 0xC02B
	TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256_CHARLES = 0xCCA9
	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384_CHARLES         = 0xC030
	TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256_CHARLES   = 0xCCA8
	TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256_CHARLES         = 0xC02F

	// DHE cipher suites (Charles抓包中的第10-13位)
	TLS_DHE_RSA_WITH_AES_256_GCM_SHA384_CHARLES       = 0x009F
	TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256_CHARLES = 0xCCAA
	TLS_DHE_DSS_WITH_AES_256_GCM_SHA384_CHARLES       = 0x00A3
	TLS_DHE_RSA_WITH_AES_128_GCM_SHA256_CHARLES       = 0x009E
	TLS_DHE_DSS_WITH_AES_128_GCM_SHA256_CHARLES       = 0x00A0

	// CBC cipher suites
	TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384_CHARLES = 0xC024
	TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384_CHARLES   = 0xC028
	TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256_CHARLES = 0xC023
	TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256_CHARLES   = 0xC027
	TLS_DHE_RSA_WITH_AES_256_CBC_SHA256_CHARLES     = 0x006B
	TLS_DHE_DSS_WITH_AES_256_CBC_SHA256_CHARLES     = 0x006A
	TLS_DHE_RSA_WITH_AES_128_CBC_SHA256_CHARLES     = 0x0067
	TLS_DHE_DSS_WITH_AES_128_CBC_SHA256_CHARLES     = 0x0040

	// Legacy ECDHE CBC
	TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA_CHARLES = 0xC00A
	TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA_CHARLES   = 0xC014
	TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA_CHARLES = 0xC009
	TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA_CHARLES   = 0xC013
	TLS_DHE_RSA_WITH_AES_256_CBC_SHA_CHARLES     = 0x0039
	TLS_DHE_DSS_WITH_AES_256_CBC_SHA_CHARLES     = 0x0038
	TLS_DHE_RSA_WITH_AES_128_CBC_SHA_CHARLES     = 0x0033
	TLS_DHE_DSS_WITH_AES_128_CBC_SHA_CHARLES     = 0x0032

	// RSA cipher suites
	TLS_RSA_WITH_AES_256_GCM_SHA384_CHARLES = 0x009D
	TLS_RSA_WITH_AES_128_GCM_SHA256_CHARLES = 0x009C
	TLS_RSA_WITH_AES_256_CBC_SHA256_CHARLES = 0x003D
	TLS_RSA_WITH_AES_128_CBC_SHA256_CHARLES = 0x003C
	TLS_RSA_WITH_AES_256_CBC_SHA_CHARLES    = 0x0035
	TLS_RSA_WITH_AES_128_CBC_SHA_CHARLES    = 0x002F

	// 3DES cipher suites (legacy)
	TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA_CHARLES = 0xC008
	TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA_CHARLES   = 0xC012
	TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA_CHARLES     = 0x0016
	TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA_CHARLES     = 0x0013
	TLS_RSA_WITH_3DES_EDE_CBC_SHA_CHARLES         = 0x000A
)

// GetCharlesCipherSuites 获取Charles代理的完整密码套件列表 (56个)
func GetCharlesCipherSuites() []uint16 {
	return []uint16{
		// TLS 1.3 cipher suites (前3个)
		TLS_AES_256_GCM_SHA384_CHARLES,       // 1. Charles优先选择
		TLS_AES_128_GCM_SHA256_CHARLES,       // 2. 标准Chrome优先
		TLS_CHACHA20_POLY1305_SHA256_CHARLES, // 3. ChaCha20支持
		// ECDHE cipher suites (4-9)
		TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384_CHARLES,       // 4.
		TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256_CHARLES,       // 5.
		TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256_CHARLES, // 6.
		TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384_CHARLES,         // 7.
		TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256_CHARLES,   // 8.
		TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256_CHARLES,         // 9.
		// DHE cipher suites (10-14)
		TLS_DHE_RSA_WITH_AES_256_GCM_SHA384_CHARLES,       // 10.
		TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256_CHARLES, // 11.
		TLS_DHE_DSS_WITH_AES_256_GCM_SHA384_CHARLES,       // 12.
		TLS_DHE_RSA_WITH_AES_128_GCM_SHA256_CHARLES,       // 13.
		TLS_DHE_DSS_WITH_AES_128_GCM_SHA256_CHARLES,       // 14.
		// CBC cipher suites (15-22)
		TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384_CHARLES, // 15.
		TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384_CHARLES,   // 16.
		TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256_CHARLES, // 17.
		TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256_CHARLES,   // 18.
		TLS_DHE_RSA_WITH_AES_256_CBC_SHA256_CHARLES,     // 19.
		TLS_DHE_DSS_WITH_AES_256_CBC_SHA256_CHARLES,     // 20.
		TLS_DHE_RSA_WITH_AES_128_CBC_SHA256_CHARLES,     // 21.
		TLS_DHE_DSS_WITH_AES_128_CBC_SHA256_CHARLES,     // 22.
		// Legacy ECDHE CBC (31-38)
		TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA_CHARLES, // 31.
		TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA_CHARLES,   // 32.
		TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA_CHARLES, // 33.
		TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA_CHARLES,   // 34.
		TLS_DHE_RSA_WITH_AES_256_CBC_SHA_CHARLES,     // 35.
		TLS_DHE_DSS_WITH_AES_256_CBC_SHA_CHARLES,     // 36.
		TLS_DHE_RSA_WITH_AES_128_CBC_SHA_CHARLES,     // 37.
		TLS_DHE_DSS_WITH_AES_128_CBC_SHA_CHARLES,     // 38.
		// RSA cipher suites (43-48)
		TLS_RSA_WITH_AES_256_GCM_SHA384_CHARLES, // 43.
		TLS_RSA_WITH_AES_128_GCM_SHA256_CHARLES, // 44.
		TLS_RSA_WITH_AES_256_CBC_SHA256_CHARLES, // 45.
		TLS_RSA_WITH_AES_128_CBC_SHA256_CHARLES, // 46.
		TLS_RSA_WITH_AES_256_CBC_SHA_CHARLES,    // 47.
		TLS_RSA_WITH_AES_128_CBC_SHA_CHARLES,    // 48.
		// 3DES cipher suites (52-56)
		TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA_CHARLES, // 52.
		TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA_CHARLES,   // 53.
		TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA_CHARLES,     // 54.
		TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA_CHARLES,     // 55.
		TLS_RSA_WITH_3DES_EDE_CBC_SHA_CHARLES,         // 56.
	}
}

// GetPopMartProfiles 获取PopMart专用指纹配置
func GetPopMartProfiles() []*core.Profile {
	now := time.Now()

	return []*core.Profile{
		// PopMart Charles超精确匹配 - 基于真实抓包数据 (2025-07-24)
		{
			Name:        "PopMart_Charles_Ultra_Exact",
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeMacOS,
			Version:     "*********",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			// Charles抓包TLS指纹 - 完整56个密码套件 (基于真实抓包数据 2025-07-24)
			// 协议版本: TLSv1.3, 选择的密码套件: TLS_AES_256_GCM_SHA384 (0x1302)
			// TLS扩展: 12个 (SNI, OCSP, supported_groups, ec_point_formats, signature_algorithms, 等)
			// 完全匹配Charles代理的密码套件顺序和十六进制值
			CipherSuites:     GetCharlesCipherSuites(),
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             128,
				DisableOnBurst:   false,
				MaxCacheAge:      600,
				ClearProbability: 0.02,
			},
			NextProtos:     nil, // ALPN为空，匹配抓包
			ForceHTTP2:     false,
			IsDynamic:      false,
			BaseTemplate:   "popmart_charles_exact",
			SuccessRate:    1.0,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		// PopMart Chrome稳定版 - 通用版本
		{
			Name:        "PopMart_Chrome_Stable",
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeWindows,
			Version:     "120.0.6099.109",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             64,
				DisableOnBurst:   false,
				MaxCacheAge:      480,
				ClearProbability: 0.05,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			IsDynamic:      false,
			BaseTemplate:   "popmart_chrome_stable",
			SuccessRate:    1.0,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 1,
		},

		// PopMart Chrome变异版本1
		{
			Name:        "PopMart_Chrome_Variant_1",
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeWindows,
			Version:     "120.0.6099.224",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.CurveP256, tls.X25519, tls.CurveP384},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             96,
				DisableOnBurst:   false,
				MaxCacheAge:      360,
				ClearProbability: 0.08,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			IsDynamic:      false,
			BaseTemplate:   "popmart_chrome_variant",
			SuccessRate:    0.95,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 2,
		},

		// PopMart Chrome变异版本2
		{
			Name:        "PopMart_Chrome_Variant_2",
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeMacOS,
			Version:     "121.0.6167.85",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP384, tls.CurveP256},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             32,
				DisableOnBurst:   true,
				MaxCacheAge:      240,
				ClearProbability: 0.12,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			IsDynamic:      false,
			BaseTemplate:   "popmart_chrome_variant",
			SuccessRate:    0.92,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 2,
		},

		// PopMart Edge兼容版本
		{
			Name:        "PopMart_Edge_Compatible",
			BrowserType: core.BrowserTypeEdge,
			OSType:      core.OSTypeWindows,
			Version:     "120.0.2210.91",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             80,
				DisableOnBurst:   false,
				MaxCacheAge:      420,
				ClearProbability: 0.06,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			IsDynamic:      false,
			BaseTemplate:   "popmart_edge_compatible",
			SuccessRate:    0.88,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 1,
		},

		// PopMart Firefox备用版本
		{
			Name:        "PopMart_Firefox_Backup",
			BrowserType: core.BrowserTypeFirefox,
			OSType:      core.OSTypeWindows,
			Version:     "121.0",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             48,
				DisableOnBurst:   false,
				MaxCacheAge:      300,
				ClearProbability: 0.04,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     false,
			IsDynamic:      false,
			BaseTemplate:   "popmart_firefox_backup",
			SuccessRate:    0.85,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 1,
		},
	}
}

// GetPopMartCharlesProfiles 获取PopMart Charles专用指纹
func GetPopMartCharlesProfiles() []*core.Profile {
	profiles := GetPopMartProfiles()
	var result []*core.Profile

	for _, profile := range profiles {
		if profile.Name == "PopMart_Charles_Ultra_Exact" {
			result = append(result, profile)
		}
	}

	return result
}

// GetPopMartChromeProfiles 获取PopMart Chrome系列指纹
func GetPopMartChromeProfiles() []*core.Profile {
	profiles := GetPopMartProfiles()
	var result []*core.Profile

	for _, profile := range profiles {
		if profile.BrowserType == core.BrowserTypeChrome {
			result = append(result, profile)
		}
	}

	return result
}

// GetPopMartHighSuccessRateProfiles 获取高成功率PopMart指纹
func GetPopMartHighSuccessRateProfiles() []*core.Profile {
	profiles := GetPopMartProfiles()
	var result []*core.Profile

	for _, profile := range profiles {
		if profile.SuccessRate >= 0.9 {
			result = append(result, profile)
		}
	}

	return result
}
