// Package profiles 极简指纹配置
package profiles

import (
	"crypto/tls"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/core"
)

// GetMinimalProfiles 获取基于真实抓包的极简TLS指纹配置
// 这些指纹基于Charles代理简化后的真实指纹特征
func GetMinimalProfiles() []*core.Profile {
	now := time.Now()
	
	return []*core.Profile{
		{
			Name:       "Minimal_TLS13_v1",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13, // 纯TLS 1.3，匹配抓包
			// 极简cipher suites - 精确匹配抓包，仅AES-256
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384, // 唯一选择，强制匹配抓包
			},
			// 匹配抓包的曲线偏好：x25519, secp256r1, secp384r1...
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             32, // 较小的缓存
				DisableOnBurst:   false,
				MaxCacheAge:      180,
				ClearProbability: 0.05,
			},
			NextProtos:     nil, // ALPN为空，匹配抓包
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeChrome,
			OSType:         core.OSTypeMacOS,
			Version:        "minimal_v1",
			IsDynamic:      false,
			BaseTemplate:   "minimal_tls13",
			SuccessRate:    0.98,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		{
			Name:       "Minimal_TLS13_v2",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 双cipher suite版本
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             16, // 更小的缓存
				DisableOnBurst:   true,
				MaxCacheAge:      120,
				ClearProbability: 0.1,
			},
			NextProtos:     nil, // ALPN为空
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeChrome,
			OSType:         core.OSTypeMacOS,
			Version:        "minimal_v2",
			IsDynamic:      false,
			BaseTemplate:   "minimal_tls13",
			SuccessRate:    0.96,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		{
			Name:       "Proxy_Like_v1",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 代理风格 - 仅AES-256
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384, // 强制使用抓包中的套件
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             8, // 极小缓存
				DisableOnBurst:   true,
				MaxCacheAge:      60,
				ClearProbability: 0.15,
			},
			NextProtos:     nil, // ALPN为空
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeChrome,
			OSType:         core.OSTypeMacOS,
			Version:        "proxy_v1",
			IsDynamic:      false,
			BaseTemplate:   "proxy_like",
			SuccessRate:    0.94,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		{
			Name:       "Proxy_Like_v2",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 精确匹配抓包 - 仅AES-256
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384, // 强制使用抓包中的套件
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             8, // 极小缓存
				DisableOnBurst:   true,
				MaxCacheAge:      60,
				ClearProbability: 0.15,
			},
			NextProtos:     nil, // ALPN为空
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeChrome,
			OSType:         core.OSTypeMacOS,
			Version:        "proxy_v2",
			IsDynamic:      false,
			BaseTemplate:   "proxy_like",
			SuccessRate:    0.92,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		{
			Name:       "Simple_TLS13_Basic",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 基础TLS 1.3配置
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             24,
				DisableOnBurst:   false,
				MaxCacheAge:      150,
				ClearProbability: 0.08,
			},
			NextProtos:     nil, // ALPN为空
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeChrome,
			OSType:         core.OSTypeWindows,
			Version:        "simple_basic",
			IsDynamic:      false,
			BaseTemplate:   "simple_tls13",
			SuccessRate:    0.90,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		{
			Name:       "Ultra_Minimal_TLS13",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 超极简配置 - 单一cipher suite
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256, // 仅一个cipher suite
			},
			CurvePreferences: []tls.CurveID{tls.X25519}, // 仅一个曲线
			SessionCache: core.SessionCacheStrategy{
				Enabled:          false, // 禁用会话缓存
				Size:             0,
				DisableOnBurst:   false,
				MaxCacheAge:      0,
				ClearProbability: 0,
			},
			NextProtos:     nil, // ALPN为空
			ForceHTTP2:     false,
			BrowserType:    core.BrowserTypeChrome,
			OSType:         core.OSTypeMacOS,
			Version:        "ultra_minimal",
			IsDynamic:      false,
			BaseTemplate:   "ultra_minimal",
			SuccessRate:    0.88,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},
	}
}

// GetCharlesLikeProfiles 获取Charles代理风格的指纹
func GetCharlesLikeProfiles() []*core.Profile {
	profiles := GetMinimalProfiles()
	var result []*core.Profile
	
	for _, profile := range profiles {
		if profile.Name == "Proxy_Like_v1" || profile.Name == "Proxy_Like_v2" {
			result = append(result, profile)
		}
	}
	
	return result
}

// GetUltraMinimalProfiles 获取超极简指纹
func GetUltraMinimalProfiles() []*core.Profile {
	profiles := GetMinimalProfiles()
	var result []*core.Profile
	
	for _, profile := range profiles {
		if profile.Name == "Ultra_Minimal_TLS13" {
			result = append(result, profile)
		}
	}
	
	return result
}

// GetTLS13OnlyProfiles 获取纯TLS 1.3指纹
func GetTLS13OnlyProfiles() []*core.Profile {
	// 所有极简指纹都是TLS 1.3
	return GetMinimalProfiles()
}

// GetSingleCipherProfiles 获取单cipher suite指纹
func GetSingleCipherProfiles() []*core.Profile {
	profiles := GetMinimalProfiles()
	var result []*core.Profile
	
	for _, profile := range profiles {
		if len(profile.CipherSuites) == 1 {
			result = append(result, profile)
		}
	}
	
	return result
}

// GetNoCacheProfiles 获取无缓存指纹
func GetNoCacheProfiles() []*core.Profile {
	profiles := GetMinimalProfiles()
	var result []*core.Profile
	
	for _, profile := range profiles {
		if !profile.SessionCache.Enabled {
			result = append(result, profile)
		}
	}
	
	return result
}
