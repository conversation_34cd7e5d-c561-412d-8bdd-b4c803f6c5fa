// Package profiles 指纹配置注册表
package profiles

import (
	"sync"

	"go-monitor/pkg/httpclient/fingerprint/core"
	"go-monitor/pkg/logging"
)

// Registry 指纹注册表
type Registry struct {
	profiles map[string]*core.Profile // 所有注册的指纹
	mu       sync.RWMutex             // 读写锁
	logger   logging.Logger           // 日志记录器
}

// NewRegistry 创建新的指纹注册表
func NewRegistry() *Registry {
	return &Registry{
		profiles: make(map[string]*core.Profile),
		logger:   logging.GetLogger("fingerprint.profiles.registry"),
	}
}

// Register 注册指纹配置
func (r *Registry) Register(profile *core.Profile) error {
	if profile == nil {
		return core.NewFingerprintError(core.ErrorTypeProfileInvalid, "指纹配置不能为空", "", nil)
	}

	if profile.Name == "" {
		return core.NewFingerprintError(core.ErrorTypeProfileInvalid, "指纹名称不能为空", "", nil)
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	// 检查是否已存在
	if _, exists := r.profiles[profile.Name]; exists {
		r.logger.Warn("指纹配置已存在，将被覆盖", "name", profile.Name)
	}

	r.profiles[profile.Name] = profile
	r.logger.Debug("注册指纹配置", "name", profile.Name, "browser", profile.BrowserType, "os", profile.OSType)

	return nil
}

// Get 获取指纹配置
func (r *Registry) Get(name string) (*core.Profile, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	profile, exists := r.profiles[name]
	return profile, exists
}

// GetAll 获取所有指纹配置
func (r *Registry) GetAll() map[string]*core.Profile {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 创建副本避免并发问题
	result := make(map[string]*core.Profile, len(r.profiles))
	for name, profile := range r.profiles {
		result[name] = profile
	}

	return result
}

// Find 按条件查找指纹
func (r *Registry) Find(filter core.ProfileFilter) []*core.Profile {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var result []*core.Profile

	for _, profile := range r.profiles {
		if r.matchesFilter(profile, filter) {
			result = append(result, profile)
		}
	}

	return result
}

// matchesFilter 检查指纹是否匹配过滤条件
func (r *Registry) matchesFilter(profile *core.Profile, filter core.ProfileFilter) bool {
	// 检查浏览器类型
	if filter.BrowserType != "" && profile.BrowserType != filter.BrowserType {
		return false
	}

	// 检查操作系统类型
	if filter.OSType != "" && profile.OSType != filter.OSType {
		return false
	}

	// 检查最小成功率
	if filter.MinSuccessRate > 0 && profile.SuccessRate < filter.MinSuccessRate {
		return false
	}

	// 检查排除名称
	for _, excludeName := range filter.ExcludeNames {
		if profile.Name == excludeName {
			return false
		}
	}

	// 检查是否为动态指纹
	if filter.IsDynamic != nil && profile.IsDynamic != *filter.IsDynamic {
		return false
	}

	return true
}

// Remove 移除指纹配置
func (r *Registry) Remove(name string) bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.profiles[name]; exists {
		delete(r.profiles, name)
		r.logger.Debug("移除指纹配置", "name", name)
		return true
	}

	return false
}

// Count 获取指纹数量
func (r *Registry) Count() int {
	r.mu.RLock()
	defer r.mu.RUnlock()

	return len(r.profiles)
}

// GetByBrowserType 按浏览器类型获取指纹
func (r *Registry) GetByBrowserType(browserType string) []*core.Profile {
	return r.Find(core.ProfileFilter{
		BrowserType: browserType,
	})
}

// GetByOSType 按操作系统类型获取指纹
func (r *Registry) GetByOSType(osType string) []*core.Profile {
	return r.Find(core.ProfileFilter{
		OSType: osType,
	})
}

// GetCharlesProfiles 获取Charles相关指纹
func (r *Registry) GetCharlesProfiles() []*core.Profile {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var result []*core.Profile

	for _, profile := range r.profiles {
		// 检查名称中是否包含Charles关键词
		if containsCharlesKeywords(profile.Name) {
			result = append(result, profile)
		}
	}

	return result
}

// GetPopMartProfiles 获取PopMart专用指纹
func (r *Registry) GetPopMartProfiles() []*core.Profile {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var result []*core.Profile

	for _, profile := range r.profiles {
		// 检查名称中是否包含PopMart关键词
		if containsPopMartKeywords(profile.Name) {
			result = append(result, profile)
		}
	}

	return result
}

// GetMinimalProfiles 获取极简指纹
func (r *Registry) GetMinimalProfiles() []*core.Profile {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var result []*core.Profile

	for _, profile := range r.profiles {
		// 检查名称中是否包含Minimal关键词
		if containsMinimalKeywords(profile.Name) {
			result = append(result, profile)
		}
	}

	return result
}

// GetHealthyProfiles 获取健康的指纹（成功率高）
func (r *Registry) GetHealthyProfiles(minSuccessRate float64) []*core.Profile {
	return r.Find(core.ProfileFilter{
		MinSuccessRate: minSuccessRate,
	})
}

// GetStats 获取注册表统计信息
func (r *Registry) GetStats() map[string]interface{} {
	r.mu.RLock()
	defer r.mu.RUnlock()

	stats := make(map[string]interface{})

	// 基本统计
	stats["total_profiles"] = len(r.profiles)

	// 按浏览器类型统计
	browserStats := make(map[string]int)
	osStats := make(map[string]int)
	dynamicCount := 0
	charlesCount := 0
	popMartCount := 0
	minimalCount := 0

	for _, profile := range r.profiles {
		// 浏览器类型统计
		if profile.BrowserType != "" {
			browserStats[profile.BrowserType]++
		}

		// 操作系统统计
		if profile.OSType != "" {
			osStats[profile.OSType]++
		}

		// 动态指纹统计
		if profile.IsDynamic {
			dynamicCount++
		}

		// 特殊类型统计
		if containsCharlesKeywords(profile.Name) {
			charlesCount++
		}
		if containsPopMartKeywords(profile.Name) {
			popMartCount++
		}
		if containsMinimalKeywords(profile.Name) {
			minimalCount++
		}
	}

	stats["browser_distribution"] = browserStats
	stats["os_distribution"] = osStats
	stats["dynamic_profiles"] = dynamicCount
	stats["charles_profiles"] = charlesCount
	stats["popmart_profiles"] = popMartCount
	stats["minimal_profiles"] = minimalCount

	return stats
}

// Clear 清空所有指纹配置
func (r *Registry) Clear() {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.profiles = make(map[string]*core.Profile)
	r.logger.Info("清空所有指纹配置")
}

// 辅助函数

// containsCharlesKeywords 检查是否包含Charles关键词
func containsCharlesKeywords(name string) bool {
	keywords := []string{"Charles", "Proxy", "Minimal"}
	for _, keyword := range keywords {
		if contains(name, keyword) {
			return true
		}
	}
	return false
}

// containsPopMartKeywords 检查是否包含PopMart关键词
func containsPopMartKeywords(name string) bool {
	keywords := []string{"PopMart", "INTL", "NA", "ASIA"}
	for _, keyword := range keywords {
		if contains(name, keyword) {
			return true
		}
	}
	return false
}

// containsMinimalKeywords 检查是否包含Minimal关键词
func containsMinimalKeywords(name string) bool {
	keywords := []string{"Minimal", "Simple", "Basic"}
	for _, keyword := range keywords {
		if contains(name, keyword) {
			return true
		}
	}
	return false
}

// contains 简单的字符串包含检查
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				containsSubstring(s, substr))))
}

// containsSubstring 检查字符串是否包含子字符串
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
