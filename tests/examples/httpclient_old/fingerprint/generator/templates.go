// Package generator 模板管理器
package generator

import (
	"fmt"
	"sync"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/core"
	"go-monitor/pkg/logging"
)

// TemplateManager 模板管理器
type TemplateManager struct {
	// 模板存储
	templates map[string]*Template
	
	// 模板评分
	scores map[string]*TemplateScore
	
	// 配置
	config TemplateConfig
	
	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger
}

// Template 指纹模板
type Template struct {
	Name           string                  // 模板名称
	Description    string                  // 模板描述
	BaseProfile    *core.Profile          // 基础指纹配置
	VariationRules map[string]float64     // 变异规则
	Category       string                  // 模板分类
	Tags           []string               // 标签
	CreatedAt      time.Time              // 创建时间
	UpdatedAt      time.Time              // 更新时间
	Version        string                 // 版本号
	Author         string                 // 作者
	Enabled        bool                   // 是否启用
}

// TemplateScore 模板评分
type TemplateScore struct {
	TemplateName    string    // 模板名称
	SuccessRate     float64   // 成功率
	UsageCount      int64     // 使用次数
	GeneratedCount  int64     // 生成次数
	AvgResponseTime time.Duration // 平均响应时间
	LastUsed        time.Time // 最后使用时间
	Score           float64   // 综合评分
	Rank            int       // 排名
}

// TemplateConfig 模板配置
type TemplateConfig struct {
	MaxTemplates     int           // 最大模板数
	ScoreUpdateInterval time.Duration // 评分更新间隔
	MinUsageForScore int64         // 最小使用次数才参与评分
	EnableAutoCleanup bool         // 启用自动清理
}

// NewTemplateManager 创建模板管理器
func NewTemplateManager(config TemplateConfig) *TemplateManager {
	return &TemplateManager{
		templates: make(map[string]*Template),
		scores:    make(map[string]*TemplateScore),
		config:    config,
		logger:    logging.GetLogger("fingerprint.generator.templates"),
	}
}

// Initialize 初始化模板管理器
func (tm *TemplateManager) Initialize() error {
	// 加载预定义模板
	tm.loadPredefinedTemplates()
	
	tm.logger.Info("模板管理器初始化完成", "templates", len(tm.templates))
	return nil
}

// RegisterTemplate 注册模板
func (tm *TemplateManager) RegisterTemplate(template *Template) error {
	if template == nil {
		return fmt.Errorf("模板不能为空")
	}
	
	if template.Name == "" {
		return fmt.Errorf("模板名称不能为空")
	}
	
	tm.mu.Lock()
	defer tm.mu.Unlock()
	
	// 检查是否已存在
	if _, exists := tm.templates[template.Name]; exists {
		tm.logger.Warn("模板已存在，将被覆盖", "name", template.Name)
	}
	
	// 设置时间戳
	now := time.Now()
	if template.CreatedAt.IsZero() {
		template.CreatedAt = now
	}
	template.UpdatedAt = now
	
	// 注册模板
	tm.templates[template.Name] = template
	
	// 初始化评分
	if _, exists := tm.scores[template.Name]; !exists {
		tm.scores[template.Name] = &TemplateScore{
			TemplateName: template.Name,
			Score:        0.5, // 默认评分
		}
	}
	
	tm.logger.Debug("注册模板", "name", template.Name, "category", template.Category)
	return nil
}

// GetTemplate 获取模板
func (tm *TemplateManager) GetTemplate(name string) (*Template, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	template, exists := tm.templates[name]
	if !exists {
		return nil, false
	}
	
	return tm.copyTemplate(template), true
}

// GetAllTemplates 获取所有模板
func (tm *TemplateManager) GetAllTemplates() map[string]*Template {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	result := make(map[string]*Template)
	for name, template := range tm.templates {
		if template.Enabled {
			result[name] = tm.copyTemplate(template)
		}
	}
	
	return result
}

// GetTemplatesByCategory 按分类获取模板
func (tm *TemplateManager) GetTemplatesByCategory(category string) []*Template {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	var result []*Template
	for _, template := range tm.templates {
		if template.Enabled && template.Category == category {
			result = append(result, tm.copyTemplate(template))
		}
	}
	
	return result
}

// GetBestTemplates 获取最佳模板
func (tm *TemplateManager) GetBestTemplates(limit int) []*Template {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	// 获取所有评分
	var scores []*TemplateScore
	for _, score := range tm.scores {
		if score.UsageCount >= tm.config.MinUsageForScore {
			scores = append(scores, score)
		}
	}
	
	// 按评分排序
	tm.sortScoresByRank(scores)
	
	// 获取对应的模板
	var result []*Template
	for i, score := range scores {
		if i >= limit {
			break
		}
		
		if template, exists := tm.templates[score.TemplateName]; exists && template.Enabled {
			result = append(result, tm.copyTemplate(template))
		}
	}
	
	return result
}

// UpdateTemplateScore 更新模板评分
func (tm *TemplateManager) UpdateTemplateScore(templateName string, success bool, responseTime time.Duration) {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	
	score, exists := tm.scores[templateName]
	if !exists {
		score = &TemplateScore{
			TemplateName: templateName,
			Score:        0.5,
		}
		tm.scores[templateName] = score
	}
	
	// 更新统计
	score.UsageCount++
	score.LastUsed = time.Now()
	
	if success {
		score.GeneratedCount++
		
		// 更新平均响应时间
		if score.AvgResponseTime == 0 {
			score.AvgResponseTime = responseTime
		} else {
			// 使用指数移动平均
			alpha := 0.1
			score.AvgResponseTime = time.Duration(float64(score.AvgResponseTime)*(1-alpha) + float64(responseTime)*alpha)
		}
	}
	
	// 更新成功率
	if score.UsageCount > 0 {
		score.SuccessRate = float64(score.GeneratedCount) / float64(score.UsageCount)
	}
	
	// 重新计算评分
	tm.calculateScore(score)
	
	tm.logger.Debug("更新模板评分", "template", templateName, "success", success, "score", score.Score)
}

// GetTemplateScore 获取模板评分
func (tm *TemplateManager) GetTemplateScore(templateName string) (*TemplateScore, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	score, exists := tm.scores[templateName]
	if !exists {
		return nil, false
	}
	
	return tm.copyTemplateScore(score), true
}

// GetAllScores 获取所有评分
func (tm *TemplateManager) GetAllScores() map[string]*TemplateScore {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	result := make(map[string]*TemplateScore)
	for name, score := range tm.scores {
		result[name] = tm.copyTemplateScore(score)
	}
	
	return result
}

// RemoveTemplate 移除模板
func (tm *TemplateManager) RemoveTemplate(templateName string) bool {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	
	if _, exists := tm.templates[templateName]; exists {
		delete(tm.templates, templateName)
		delete(tm.scores, templateName)
		tm.logger.Debug("移除模板", "name", templateName)
		return true
	}
	
	return false
}

// EnableTemplate 启用模板
func (tm *TemplateManager) EnableTemplate(templateName string) bool {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	
	if template, exists := tm.templates[templateName]; exists {
		template.Enabled = true
		template.UpdatedAt = time.Now()
		tm.logger.Debug("启用模板", "name", templateName)
		return true
	}
	
	return false
}

// DisableTemplate 禁用模板
func (tm *TemplateManager) DisableTemplate(templateName string) bool {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	
	if template, exists := tm.templates[templateName]; exists {
		template.Enabled = false
		template.UpdatedAt = time.Now()
		tm.logger.Debug("禁用模板", "name", templateName)
		return true
	}
	
	return false
}

// 内部方法

// loadPredefinedTemplates 加载预定义模板
func (tm *TemplateManager) loadPredefinedTemplates() {
	now := time.Now()
	
	// Chrome标准模板
	chromeTemplate := &Template{
		Name:        "chrome_standard",
		Description: "Chrome浏览器标准指纹模板",
		BaseProfile: &core.Profile{
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeWindows,
			Version:     "120.0.6099.109",
		},
		VariationRules: map[string]float64{
			"cipher_shuffle": 0.3,
			"curve_shuffle":  0.2,
			"version_vary":   0.1,
		},
		Category:  "browser",
		Tags:      []string{"chrome", "standard", "desktop"},
		CreatedAt: now,
		UpdatedAt: now,
		Version:   "1.0",
		Author:    "system",
		Enabled:   true,
	}
	tm.RegisterTemplate(chromeTemplate)
	
	// PopMart专用模板
	popmartTemplate := &Template{
		Name:        "popmart_charles",
		Description: "PopMart Charles专用指纹模板",
		BaseProfile: &core.Profile{
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeMacOS,
			Version:     "*********",
		},
		VariationRules: map[string]float64{
			"cipher_order": 0.2,
			"curve_order":  0.1,
			"session_vary": 0.3,
		},
		Category:  "specialized",
		Tags:      []string{"popmart", "charles", "ecommerce"},
		CreatedAt: now,
		UpdatedAt: now,
		Version:   "1.0",
		Author:    "system",
		Enabled:   true,
	}
	tm.RegisterTemplate(popmartTemplate)
	
	// 极简模板
	minimalTemplate := &Template{
		Name:        "minimal_tls13",
		Description: "极简TLS 1.3指纹模板",
		BaseProfile: &core.Profile{
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeMacOS,
			Version:     "minimal",
		},
		VariationRules: map[string]float64{
			"cipher_minimal": 0.1,
			"curve_minimal":  0.1,
		},
		Category:  "minimal",
		Tags:      []string{"minimal", "tls13", "simple"},
		CreatedAt: now,
		UpdatedAt: now,
		Version:   "1.0",
		Author:    "system",
		Enabled:   true,
	}
	tm.RegisterTemplate(minimalTemplate)
}

// calculateScore 计算评分
func (tm *TemplateManager) calculateScore(score *TemplateScore) {
	// 基础评分基于成功率
	baseScore := score.SuccessRate
	
	// 使用频率加成
	usageBonus := 0.0
	if score.UsageCount > 0 {
		// 使用次数越多，加成越大，但有上限
		usageBonus = float64(score.UsageCount) / 1000.0
		if usageBonus > 0.2 {
			usageBonus = 0.2
		}
	}
	
	// 响应时间惩罚
	responseTimePenalty := 0.0
	if score.AvgResponseTime > 2*time.Second {
		responseTimePenalty = 0.1
	} else if score.AvgResponseTime > 5*time.Second {
		responseTimePenalty = 0.2
	}
	
	// 最近使用加成
	recentUsageBonus := 0.0
	if !score.LastUsed.IsZero() {
		hoursSinceUse := time.Since(score.LastUsed).Hours()
		if hoursSinceUse < 24 {
			recentUsageBonus = 0.1 * (1.0 - hoursSinceUse/24.0)
		}
	}
	
	// 计算最终评分
	score.Score = baseScore + usageBonus - responseTimePenalty + recentUsageBonus
	
	// 确保评分在0-1范围内
	if score.Score > 1.0 {
		score.Score = 1.0
	}
	if score.Score < 0.0 {
		score.Score = 0.0
	}
}

// sortScoresByRank 按排名排序评分
func (tm *TemplateManager) sortScoresByRank(scores []*TemplateScore) {
	// 简单的冒泡排序，按评分降序
	for i := 0; i < len(scores)-1; i++ {
		for j := i + 1; j < len(scores); j++ {
			if scores[i].Score < scores[j].Score {
				scores[i], scores[j] = scores[j], scores[i]
			}
		}
	}
	
	// 设置排名
	for i, score := range scores {
		score.Rank = i + 1
	}
}

// copyTemplate 复制模板
func (tm *TemplateManager) copyTemplate(template *Template) *Template {
	copied := &Template{
		Name:           template.Name,
		Description:    template.Description,
		BaseProfile:    template.BaseProfile, // 注意：这里是浅拷贝
		VariationRules: make(map[string]float64),
		Category:       template.Category,
		Tags:           make([]string, len(template.Tags)),
		CreatedAt:      template.CreatedAt,
		UpdatedAt:      template.UpdatedAt,
		Version:        template.Version,
		Author:         template.Author,
		Enabled:        template.Enabled,
	}
	
	// 复制变异规则
	for k, v := range template.VariationRules {
		copied.VariationRules[k] = v
	}
	
	// 复制标签
	copy(copied.Tags, template.Tags)
	
	return copied
}

// copyTemplateScore 复制模板评分
func (tm *TemplateManager) copyTemplateScore(score *TemplateScore) *TemplateScore {
	return &TemplateScore{
		TemplateName:    score.TemplateName,
		SuccessRate:     score.SuccessRate,
		UsageCount:      score.UsageCount,
		GeneratedCount:  score.GeneratedCount,
		AvgResponseTime: score.AvgResponseTime,
		LastUsed:        score.LastUsed,
		Score:           score.Score,
		Rank:            score.Rank,
	}
}
