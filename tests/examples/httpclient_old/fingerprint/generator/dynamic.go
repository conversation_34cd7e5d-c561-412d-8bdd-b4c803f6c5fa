// Package generator 动态指纹生成器
package generator

import (
	"context"
	"crypto/tls"
	"fmt"
	"math/rand"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/core"
	"go-monitor/pkg/logging"
)

// DynamicGenerator 动态指纹生成器
type DynamicGenerator struct {
	// 模板管理
	templates map[string]*Template
	
	// 配置
	config GeneratorConfig
	
	// 统计信息
	stats *GeneratorStats
	
	// 日志记录
	logger logging.Logger
}

// GeneratorConfig 生成器配置
type GeneratorConfig struct {
	MaxVariations     int     // 最大变异数量
	VariationStrength float64 // 变异强度 (0.0-1.0)
	BaseOnSuccessful  bool    // 基于成功指纹生成
	EnableCaching     bool    // 启用缓存
	CacheSize         int     // 缓存大小
}

// GeneratorStats 生成器统计
type GeneratorStats struct {
	TotalGenerated    int64
	SuccessfulGenerated int64
	FailedGenerated   int64
	CacheHits         int64
	CacheMisses       int64
	LastGenerated     time.Time
}

// NewDynamicGenerator 创建动态生成器
func NewDynamicGenerator(config GeneratorConfig) *DynamicGenerator {
	return &DynamicGenerator{
		templates: make(map[string]*Template),
		config:    config,
		stats: &GeneratorStats{
			LastGenerated: time.Now(),
		},
		logger: logging.GetLogger("fingerprint.generator.dynamic"),
	}
}

// Initialize 初始化生成器
func (g *DynamicGenerator) Initialize() error {
	// 加载内置模板
	g.loadBuiltinTemplates()
	
	g.logger.Info("动态指纹生成器初始化完成", "templates", len(g.templates))
	return nil
}

// Generate 生成指纹
func (g *DynamicGenerator) Generate(ctx context.Context, req *core.GenerationRequest) (*core.Profile, error) {
	// 选择基础模板
	template, err := g.selectTemplate(req)
	if err != nil {
		g.stats.FailedGenerated++
		return nil, fmt.Errorf("选择模板失败: %w", err)
	}
	
	// 生成指纹
	profile, err := g.generateFromTemplate(template, req)
	if err != nil {
		g.stats.FailedGenerated++
		return nil, fmt.Errorf("从模板生成指纹失败: %w", err)
	}
	
	// 更新统计
	g.stats.TotalGenerated++
	g.stats.SuccessfulGenerated++
	g.stats.LastGenerated = time.Now()
	
	g.logger.Debug("生成动态指纹", "name", profile.Name, "template", template.Name)
	return profile, nil
}

// GenerateVariation 生成变异指纹
func (g *DynamicGenerator) GenerateVariation(base *core.Profile, strength float64) (*core.Profile, error) {
	if base == nil {
		return nil, fmt.Errorf("基础指纹不能为空")
	}
	
	// 创建变异指纹
	variation := &core.Profile{
		Name:             g.generateVariationName(base.Name),
		MinVersion:       base.MinVersion,
		MaxVersion:       base.MaxVersion,
		CipherSuites:     g.varyCipherSuites(base.CipherSuites, strength),
		CurvePreferences: g.varyCurvePreferences(base.CurvePreferences, strength),
		SessionCache:     g.varySessionCache(base.SessionCache, strength),
		NextProtos:       g.varyNextProtos(base.NextProtos, strength),
		ForceHTTP2:       base.ForceHTTP2,
		BrowserType:      base.BrowserType,
		OSType:           base.OSType,
		Version:          g.varyVersion(base.Version, strength),
		IsDynamic:        true,
		BaseTemplate:     base.BaseTemplate,
		SuccessRate:      0.0, // 新生成的指纹成功率未知
		LastUsed:         time.Time{},
		CreatedAt:        time.Now(),
		VariationLevel:   base.VariationLevel + 1,
	}
	
	g.stats.TotalGenerated++
	g.stats.SuccessfulGenerated++
	
	g.logger.Debug("生成变异指纹", "base", base.Name, "variation", variation.Name, "strength", strength)
	return variation, nil
}

// UpdateSuccessRate 更新成功率
func (g *DynamicGenerator) UpdateSuccessRate(profileName string, rate float64) {
	// 这里可以根据成功率调整生成策略
	g.logger.Debug("更新指纹成功率", "profile", profileName, "rate", rate)
}

// GetStats 获取统计信息
func (g *DynamicGenerator) GetStats() *GeneratorStats {
	return &GeneratorStats{
		TotalGenerated:      g.stats.TotalGenerated,
		SuccessfulGenerated: g.stats.SuccessfulGenerated,
		FailedGenerated:     g.stats.FailedGenerated,
		CacheHits:           g.stats.CacheHits,
		CacheMisses:         g.stats.CacheMisses,
		LastGenerated:       g.stats.LastGenerated,
	}
}

// 内部方法

// loadBuiltinTemplates 加载内置模板
func (g *DynamicGenerator) loadBuiltinTemplates() {
	// Chrome模板
	g.templates["chrome_standard"] = &Template{
		Name:        "chrome_standard",
		Description: "标准Chrome指纹模板",
		BaseProfile: &core.Profile{
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeWindows,
			MinVersion:  tls.VersionTLS12,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			NextProtos:       []string{"h2", "http/1.1"},
		},
		VariationRules: map[string]float64{
			"cipher_shuffle":  0.3,
			"curve_shuffle":   0.2,
			"version_vary":    0.1,
			"session_vary":    0.4,
		},
	}
	
	// PopMart模板
	g.templates["popmart_charles"] = &Template{
		Name:        "popmart_charles",
		Description: "PopMart Charles专用模板",
		BaseProfile: &core.Profile{
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeMacOS,
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			NextProtos:       nil, // ALPN为空
		},
		VariationRules: map[string]float64{
			"cipher_order":    0.2,
			"curve_order":     0.1,
			"session_vary":    0.3,
		},
	}
	
	// 极简模板
	g.templates["minimal_tls13"] = &Template{
		Name:        "minimal_tls13",
		Description: "极简TLS 1.3模板",
		BaseProfile: &core.Profile{
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeMacOS,
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			NextProtos:       nil,
		},
		VariationRules: map[string]float64{
			"cipher_minimal": 0.1,
			"curve_minimal":  0.1,
			"session_vary":   0.2,
		},
	}
}

// selectTemplate 选择模板
func (g *DynamicGenerator) selectTemplate(req *core.GenerationRequest) (*Template, error) {
	// 优先使用指定的模板
	if req.BaseTemplate != "" {
		if template, exists := g.templates[req.BaseTemplate]; exists {
			return template, nil
		}
	}
	
	// 根据浏览器类型选择
	for _, template := range g.templates {
		if template.BaseProfile.BrowserType == req.BrowserType {
			return template, nil
		}
	}
	
	// 使用默认模板
	if template, exists := g.templates["chrome_standard"]; exists {
		return template, nil
	}
	
	return nil, fmt.Errorf("没有可用的模板")
}

// generateFromTemplate 从模板生成指纹
func (g *DynamicGenerator) generateFromTemplate(template *Template, req *core.GenerationRequest) (*core.Profile, error) {
	base := template.BaseProfile
	
	profile := &core.Profile{
		Name:             g.generateProfileName(template.Name, req),
		MinVersion:       base.MinVersion,
		MaxVersion:       base.MaxVersion,
		CipherSuites:     make([]uint16, len(base.CipherSuites)),
		CurvePreferences: make([]tls.CurveID, len(base.CurvePreferences)),
		SessionCache:     base.SessionCache,
		NextProtos:       make([]string, len(base.NextProtos)),
		ForceHTTP2:       base.ForceHTTP2,
		BrowserType:      base.BrowserType,
		OSType:           base.OSType,
		Version:          base.Version,
		IsDynamic:        true,
		BaseTemplate:     template.Name,
		SuccessRate:      0.0,
		LastUsed:         time.Time{},
		CreatedAt:        time.Now(),
		VariationLevel:   req.VariationLevel,
	}
	
	// 复制数组
	copy(profile.CipherSuites, base.CipherSuites)
	copy(profile.CurvePreferences, base.CurvePreferences)
	copy(profile.NextProtos, base.NextProtos)
	
	// 应用变异规则
	strength := g.config.VariationStrength
	if req.VariationLevel > 0 {
		strength = float64(req.VariationLevel) * 0.1
	}
	
	profile.CipherSuites = g.varyCipherSuites(profile.CipherSuites, strength)
	profile.CurvePreferences = g.varyCurvePreferences(profile.CurvePreferences, strength)
	profile.SessionCache = g.varySessionCache(profile.SessionCache, strength)
	
	return profile, nil
}

// generateProfileName 生成指纹名称
func (g *DynamicGenerator) generateProfileName(templateName string, req *core.GenerationRequest) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("Dynamic_%s_%d", templateName, timestamp)
}

// generateVariationName 生成变异名称
func (g *DynamicGenerator) generateVariationName(baseName string) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s_Var_%d", baseName, timestamp)
}

// varyCipherSuites 变异密码套件
func (g *DynamicGenerator) varyCipherSuites(original []uint16, strength float64) []uint16 {
	if strength <= 0 || len(original) <= 1 {
		return original
	}
	
	result := make([]uint16, len(original))
	copy(result, original)
	
	// 根据强度决定是否打乱顺序
	if rand.Float64() < strength {
		// 简单的打乱算法
		for i := len(result) - 1; i > 0; i-- {
			j := rand.Intn(i + 1)
			result[i], result[j] = result[j], result[i]
		}
	}
	
	return result
}

// varyCurvePreferences 变异曲线偏好
func (g *DynamicGenerator) varyCurvePreferences(original []tls.CurveID, strength float64) []tls.CurveID {
	if strength <= 0 || len(original) <= 1 {
		return original
	}
	
	result := make([]tls.CurveID, len(original))
	copy(result, original)
	
	// 根据强度决定是否打乱顺序
	if rand.Float64() < strength {
		for i := len(result) - 1; i > 0; i-- {
			j := rand.Intn(i + 1)
			result[i], result[j] = result[j], result[i]
		}
	}
	
	return result
}

// varySessionCache 变异会话缓存
func (g *DynamicGenerator) varySessionCache(original core.SessionCacheStrategy, strength float64) core.SessionCacheStrategy {
	result := original
	
	if rand.Float64() < strength {
		// 变异缓存大小
		if result.Size > 0 {
			variation := int(float64(result.Size) * 0.2) // 20%变异
			if variation == 0 {
				variation = 1
			}
			result.Size += rand.Intn(variation*2) - variation
			if result.Size < 8 {
				result.Size = 8
			}
			if result.Size > 256 {
				result.Size = 256
			}
		}
		
		// 变异清理概率
		if rand.Float64() < 0.3 {
			result.ClearProbability = rand.Float64() * 0.2
		}
	}
	
	return result
}

// varyNextProtos 变异ALPN协议
func (g *DynamicGenerator) varyNextProtos(original []string, strength float64) []string {
	if strength <= 0 || len(original) <= 1 {
		return original
	}
	
	result := make([]string, len(original))
	copy(result, original)
	
	// 根据强度决定是否打乱顺序
	if rand.Float64() < strength {
		for i := len(result) - 1; i > 0; i-- {
			j := rand.Intn(i + 1)
			result[i], result[j] = result[j], result[i]
		}
	}
	
	return result
}

// varyVersion 变异版本号
func (g *DynamicGenerator) varyVersion(original string, strength float64) string {
	if strength <= 0 {
		return original
	}
	
	// 简单的版本变异
	if rand.Float64() < strength {
		timestamp := time.Now().Unix() % 1000
		return fmt.Sprintf("%s_v%d", original, timestamp)
	}
	
	return original
}
