// Package config 默认配置
package config

import (
	"time"
)

// GetDefaultGlobalConfig 获取默认全局配置
func GetDefaultGlobalConfig() *GlobalConfig {
	return &GlobalConfig{
		FingerprintPools:       GetDefaultFingerprintPoolsConfig(),
		DefaultFingerprintPool: GetDefaultDefaultPoolConfig(),
		HealthyFingerprintPool: GetDefaultHealthyPoolConfig(),
		Performance:            GetDefaultPerformanceConfig(),
		Monitoring:             GetDefaultMonitoringConfig(),
	}
}

// GetDefaultFingerprintPoolsConfig 获取默认指纹池配置
func GetDefaultFingerprintPoolsConfig() FingerprintPoolsConfig {
	return FingerprintPoolsConfig{
		GlobalSettings: FingerprintPoolGlobalSettings{
			Enabled:                   true,
			UseMiddlewareDomainConfig: true,
			DefaultPoolName:           "default",
			AutoCreatePools:           true,
			MaxPools:                  50,
		},
		PoolDefinitions: map[string]FingerprintPoolDefinition{
			"default": {
				Name:              "默认指纹池",
				Description:       "未指定fingerprint字段时使用的默认指纹池",
				MaxProfiles:       15,
				SelectionStrategy: "least_recently_used",
				QualityRequirements: QualityRequirements{
					RequireVerified: true,
					MinSuccessRate:  0.9,
					ExcludeHighRisk: true,
				},
				ForceCharlesOnly: true,
			},
			"popmart-us": {
				Name:              "PopMart美国指纹池",
				Description:       "专用于PopMart美国地区的指纹池",
				MaxProfiles:       20,
				SelectionStrategy: "success_rate",
				QualityRequirements: QualityRequirements{
					RequireVerified: true,
					MinSuccessRate:  0.85,
					ExcludeHighRisk: true,
				},
				ForceCharlesOnly:      true,
				PreferredProfileTypes: []string{"PopMart_Charles_Ultra_Exact", "PopMart_NA_Chrome_Stable"},
			},
			"popmart-eu": {
				Name:              "PopMart欧洲指纹池",
				Description:       "专用于PopMart欧洲地区的指纹池",
				MaxProfiles:       20,
				SelectionStrategy: "success_rate",
				QualityRequirements: QualityRequirements{
					RequireVerified: true,
					MinSuccessRate:  0.85,
					ExcludeHighRisk: true,
				},
				ForceCharlesOnly:      true,
				PreferredProfileTypes: []string{"PopMart_Charles_Ultra_Exact", "PopMart_INTL_Chrome_Stable"},
			},
			"popmart-asia": {
				Name:              "PopMart亚洲指纹池",
				Description:       "专用于PopMart亚洲地区的指纹池",
				MaxProfiles:       20,
				SelectionStrategy: "success_rate",
				QualityRequirements: QualityRequirements{
					RequireVerified: true,
					MinSuccessRate:  0.85,
					ExcludeHighRisk: true,
				},
				ForceCharlesOnly:      true,
				PreferredProfileTypes: []string{"PopMart_Charles_Ultra_Exact", "PopMart_ASIA_Chrome_Stable"},
			},
		},
	}
}

// GetDefaultDefaultPoolConfig 获取默认指纹池的默认配置
func GetDefaultDefaultPoolConfig() DefaultPoolConfig {
	return DefaultPoolConfig{
		Enabled: true,
		SelectionStrategy: DefaultSelectionStrategy{
			ForceCharlesOnly: true,
			MaxProfiles:      15,
			RotationStrategy: "least_recently_used",
		},
		QualityRequirements: QualityRequirements{
			RequireVerified: true,
			MinSuccessRate:  0.9,
			ExcludeHighRisk: true,
		},
	}
}

// GetDefaultHealthyPoolConfig 获取默认健康指纹池配置
func GetDefaultHealthyPoolConfig() HealthyPoolConfig {
	return HealthyPoolConfig{
		Enabled: true,
		PoolSize: PoolSizeConfig{
			MinVerifiedCount:  20,
			MinCandidateCount: 15,
			MinBackupCount:    10,
			MaxTotalCount:     200,
		},
		Timing: TimingConfig{
			ObservationPeriod:  6 * time.Hour,
			PromotionCooldown:  1 * time.Hour,
			GenerationCooldown: 30 * time.Minute,
			CleanupInterval:    1 * time.Hour,
		},
		QualityThresholds: QualityThresholdsConfig{
			PromotionSuccessRate: 0.85,
			PromotionMinUses:     20,
			DemotionSuccessRate:  0.70,
			CleanupSuccessRate:   0.30,
		},
		Generation: GenerationConfig{
			EnableSmartGeneration: true,
			VariationStrength:     0.3,
			MaxFailedAttempts:     3,
			BaseOnSuccessful:      true,
		},
		Rotation: RotationConfig{
			Enabled:             true,
			MaxConsecutiveUses:  50,
			RotationInterval:    2 * time.Hour,
			RotationProbability: 0.1,
		},
		Maintenance: MaintenanceConfig{
			AutoCleanup:      true,
			CleanupInterval:  2 * time.Hour,
			MaxIdleTime:      24 * time.Hour,
			CompactThreshold: 100,
		},
	}
}

// GetDefaultPerformanceConfig 获取默认性能配置
func GetDefaultPerformanceConfig() PerformanceConfig {
	return PerformanceConfig{
		Caching: CachingConfig{
			EnableProfileCache: true,
			MaxCachedConfigs:   1000,
			CacheExpiration:    30 * time.Minute,
			CleanupInterval:    10 * time.Minute,
		},
		Concurrency: ConcurrencyConfig{
			MaxConcurrentRequests:   100,
			MaxConcurrentGeneration: 10,
			WorkerPoolSize:          20,
		},
		Timeouts: TimeoutsConfig{
			SelectionTimeout:   5 * time.Second,
			GenerationTimeout:  30 * time.Second,
			HealthCheckTimeout: 10 * time.Second,
		},
	}
}

// GetDefaultMonitoringConfig 获取默认监控配置
func GetDefaultMonitoringConfig() MonitoringConfig {
	return MonitoringConfig{
		Enabled:         true,
		MetricsInterval: 1 * time.Minute,
		HealthCheck: HealthCheckConfig{
			Enabled:           true,
			CheckInterval:     30 * time.Second,
			FailureThreshold:  5,
			RecoveryThreshold: 3,
		},
		Alerting: AlertingConfig{
			Enabled:            true,
			LowHealthThreshold: 0.7,
			HighFailureRate:    0.3,
			PoolEmptyAlert:     true,
		},
	}
}

// GetDefaultManagerConfig 获取默认管理器配置
func GetDefaultManagerConfig() *ManagerConfig {
	return &ManagerConfig{
		MaxRequestsPerProfile: 100,
		MaxProfileDuration:    2 * time.Hour,
		RotationProbability:   0.1,
		EnableDynamic:         true,
		EnableHealthCheck:     true,
		CleanupInterval:       1 * time.Hour,
	}
}

// GetDefaultDomainConfig 获取默认域名配置
func GetDefaultDomainConfig(domain string) *DomainConfig {
	return &DomainConfig{
		Domain:                domain,
		PreferredProfiles:     []string{},
		ExcludedProfiles:      []string{},
		CustomPoolName:        "",
		RotationProbability:   0.1,
		MaxRequestsPerProfile: 100,
	}
}

// GetPopMartDomainConfigs 获取PopMart域名配置
func GetPopMartDomainConfigs() map[string]*DomainConfig {
	return map[string]*DomainConfig{
		"prod-intl-api.popmart.com": {
			Domain:                "prod-intl-api.popmart.com",
			PreferredProfiles:     []string{"PopMart_Charles_Ultra_Exact", "PopMart_INTL_Chrome_Stable"},
			ExcludedProfiles:      []string{},
			CustomPoolName:        "popmart-eu",
			RotationProbability:   0.05,
			MaxRequestsPerProfile: 150,
		},
		"prod-na-api.popmart.com": {
			Domain:                "prod-na-api.popmart.com",
			PreferredProfiles:     []string{"PopMart_Charles_Ultra_Exact", "PopMart_NA_Chrome_Stable"},
			ExcludedProfiles:      []string{},
			CustomPoolName:        "popmart-us",
			RotationProbability:   0.05,
			MaxRequestsPerProfile: 150,
		},
		"prod-asia-api.popmart.com": {
			Domain:                "prod-asia-api.popmart.com",
			PreferredProfiles:     []string{"PopMart_Charles_Ultra_Exact", "PopMart_ASIA_Chrome_Stable"},
			ExcludedProfiles:      []string{},
			CustomPoolName:        "popmart-asia",
			RotationProbability:   0.05,
			MaxRequestsPerProfile: 150,
		},
	}
}

// ConvertToPoolConfig 将配置转换为池配置
func ConvertToPoolConfig(healthyConfig HealthyPoolConfig) PoolConfig {
	return PoolConfig{
		MaxProfiles:           healthyConfig.PoolSize.MaxTotalCount,
		SelectionStrategy:     "success_rate",
		EnableSmartGeneration: healthyConfig.Generation.EnableSmartGeneration,
		PromotionSuccessRate:  healthyConfig.QualityThresholds.PromotionSuccessRate,
		PromotionMinUses:      healthyConfig.QualityThresholds.PromotionMinUses,
		DemotionSuccessRate:   healthyConfig.QualityThresholds.DemotionSuccessRate,
		CleanupSuccessRate:    healthyConfig.QualityThresholds.CleanupSuccessRate,
		ObservationPeriod:     healthyConfig.Timing.ObservationPeriod,
		PromotionCooldown:     healthyConfig.Timing.PromotionCooldown,
		GenerationCooldown:    healthyConfig.Timing.GenerationCooldown,
		CleanupInterval:       healthyConfig.Timing.CleanupInterval,
		VariationStrength:     healthyConfig.Generation.VariationStrength,
		MaxFailedAttempts:     healthyConfig.Generation.MaxFailedAttempts,
		BaseOnSuccessful:      healthyConfig.Generation.BaseOnSuccessful,
	}
}
