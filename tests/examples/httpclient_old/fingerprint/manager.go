// Package fingerprint 指纹管理器 - 重构后的简化版本
package fingerprint

import (
	"context"
	"crypto/tls"
	"fmt"
	"sync"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/config"
	"go-monitor/pkg/httpclient/fingerprint/core"
	"go-monitor/pkg/httpclient/fingerprint/detection"
	"go-monitor/pkg/httpclient/fingerprint/generator"
	"go-monitor/pkg/httpclient/fingerprint/health"
	"go-monitor/pkg/httpclient/fingerprint/pool"
	"go-monitor/pkg/httpclient/fingerprint/profiles"
	"go-monitor/pkg/logging"
)

// Manager 指纹管理器 - 简化的协调器
type Manager struct {
	// 核心组件
	configLoader     *config.Loader
	profileRegistry  *profiles.Registry
	poolManager      *pool.Manager
	healthTracker    *health.Tracker
	metricsCollector *health.MetricsCollector
	healthAnalyzer   *health.Analyzer
	dynamicGenerator *generator.DynamicGenerator
	detectionEngine  *detection.Engine

	// 状态管理
	initialized bool
	mu          sync.RWMutex
	logger      logging.Logger
}

// NewManager 创建指纹管理器
func NewManager() *Manager {
	return &Manager{
		logger: logging.GetLogger("fingerprint.manager"),
	}
}

// Initialize 初始化管理器
func (m *Manager) Initialize(configPath string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.initialized {
		return fmt.Errorf("管理器已经初始化")
	}

	// 1. 初始化配置加载器
	m.configLoader = config.NewLoader()
	if err := m.configLoader.Load(configPath); err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 2. 初始化指纹注册表
	m.profileRegistry = profiles.NewRegistry()
	if err := m.loadProfiles(); err != nil {
		return fmt.Errorf("加载指纹配置失败: %w", err)
	}

	// 3. 初始化健康追踪器
	trackerConfig := health.TrackerConfig{
		MaxConsecutiveErrors: 5,
		HealthCheckInterval:  30 * time.Minute,
		CleanupInterval:      60 * time.Minute,
		MaxIdleTime:          24 * time.Hour,
	}
	m.healthTracker = health.NewTracker(trackerConfig)
	if err := m.healthTracker.Initialize(); err != nil {
		return fmt.Errorf("初始化健康追踪器失败: %w", err)
	}

	// 4. 初始化指标收集器
	metricsConfig := health.MetricsConfig{
		CollectionInterval: 1 * time.Minute,
		WindowDuration:     1 * time.Hour,
		RetentionPeriod:    24 * time.Hour,
		MaxProfiles:        1000,
	}
	m.metricsCollector = health.NewMetricsCollector(metricsConfig)
	if err := m.metricsCollector.Initialize(); err != nil {
		return fmt.Errorf("初始化指标收集器失败: %w", err)
	}

	// 5. 初始化健康分析器
	analyzerConfig := health.AnalyzerConfig{
		AnalysisInterval:      5 * time.Minute,
		CacheExpiry:           10 * time.Minute,
		HealthThreshold:       0.8,
		DegradedThreshold:     0.6,
		MinSampleSize:         10,
		ConsecutiveErrorLimit: 3,
	}
	m.healthAnalyzer = health.NewAnalyzer(m.healthTracker, m.metricsCollector, analyzerConfig)
	if err := m.healthAnalyzer.Initialize(); err != nil {
		return fmt.Errorf("初始化健康分析器失败: %w", err)
	}

	// 6. 初始化动态生成器
	generatorConfig := generator.GeneratorConfig{
		MaxVariations:     100,
		VariationStrength: 0.3,
		BaseOnSuccessful:  true,
		EnableCaching:     true,
		CacheSize:         50,
	}
	m.dynamicGenerator = generator.NewDynamicGenerator(generatorConfig)
	if err := m.dynamicGenerator.Initialize(); err != nil {
		return fmt.Errorf("初始化动态生成器失败: %w", err)
	}

	// 7. 初始化反风控引擎
	detectionConfig := detection.EngineConfig{
		MaxRecentBlocks:  10,
		BlockWindowSize:  1 * time.Hour,
		AnalysisInterval: 5 * time.Minute,
		DefaultCooldown:  10 * time.Minute,
		MaxCooldown:      1 * time.Hour,
	}
	m.detectionEngine = detection.NewEngine(detectionConfig)
	if err := m.detectionEngine.Initialize(); err != nil {
		return fmt.Errorf("初始化反风控引擎失败: %w", err)
	}

	// 8. 初始化池管理器
	m.poolManager = pool.NewManager(m.configLoader, m.profileRegistry, m.healthTracker)
	if err := m.poolManager.Initialize(); err != nil {
		return fmt.Errorf("初始化池管理器失败: %w", err)
	}

	m.initialized = true
	m.logger.Info("指纹管理器初始化完成")
	return nil
}

// SelectFingerprint 选择指纹 - 主要入口点
func (m *Manager) SelectFingerprint(ctx context.Context, req *core.FingerprintRequest) (*core.Profile, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.initialized {
		return nil, core.ErrManagerNotInitialized
	}

	// 委托给池管理器
	profile, err := m.poolManager.GetFingerprint(ctx, req)
	if err != nil {
		m.logger.Error("选择指纹失败", "error", err, "pool", req.PoolName)
		return nil, err
	}

	m.logger.Debug("选择指纹成功", "profile", profile.Name, "pool", req.PoolName)
	return profile, nil
}

// CreateTLSConfig 创建TLS配置
func (m *Manager) CreateTLSConfig(profile *core.Profile, serverName string) (*tls.Config, error) {
	if profile == nil {
		return nil, fmt.Errorf("指纹配置不能为空")
	}

	config := &tls.Config{
		ServerName:         serverName,
		MinVersion:         profile.MinVersion,
		MaxVersion:         profile.MaxVersion,
		CipherSuites:       profile.CipherSuites,
		CurvePreferences:   profile.CurvePreferences,
		NextProtos:         profile.NextProtos,
		InsecureSkipVerify: false,
	}

	// 设置会话缓存
	if profile.SessionCache.Enabled {
		config.ClientSessionCache = core.CreateSmartSessionCache(profile.SessionCache)
	}

	return config, nil
}

// UpdateHealth 更新健康状态
func (m *Manager) UpdateHealth(profileName string, success bool, responseTime time.Duration) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.initialized {
		return
	}

	// 更新健康追踪器
	if success {
		m.healthTracker.RecordSuccess(profileName, responseTime)
	} else {
		m.healthTracker.RecordFailure(profileName, fmt.Errorf("请求失败"))
	}

	// 更新指标收集器
	m.metricsCollector.RecordRequest(profileName, success, responseTime, false)
}

// AnalyzeResponse 分析响应（风控检测）
func (m *Manager) AnalyzeResponse(serverName string, statusCode int, body string, responseTime time.Duration, profileName string) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.initialized {
		return
	}

	// 委托给反风控引擎
	m.detectionEngine.AnalyzeResponse(serverName, statusCode, body, responseTime, profileName)

	// 如果检测到风控，更新健康状态
	if core.IsBlockedResponse(statusCode, nil) {
		m.healthTracker.RecordBlocked(profileName, statusCode)
		m.metricsCollector.RecordRequest(profileName, false, responseTime, true)
	}
}

// GetStats 获取统计信息
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.initialized {
		return map[string]interface{}{"error": "管理器未初始化"}
	}

	stats := make(map[string]interface{})

	// 池统计
	stats["pool_stats"] = m.poolManager.GetPoolStats()

	// 健康统计
	if globalMetrics := m.metricsCollector.GetGlobalMetrics(); globalMetrics != nil {
		stats["health_stats"] = map[string]interface{}{
			"total_profiles":    globalMetrics.TotalProfiles,
			"healthy_profiles":  globalMetrics.HealthyProfiles,
			"degraded_profiles": globalMetrics.DegradedProfiles,
			"blocked_profiles":  globalMetrics.BlockedProfiles,
			"success_rate":      globalMetrics.OverallSuccessRate,
		}
	}

	// 生成器统计
	if genStats := m.dynamicGenerator.GetStats(); genStats != nil {
		stats["generator_stats"] = map[string]interface{}{
			"total_generated":      genStats.TotalGenerated,
			"successful_generated": genStats.SuccessfulGenerated,
			"failed_generated":     genStats.FailedGenerated,
		}
	}

	// 注册表统计
	stats["registry_stats"] = m.profileRegistry.GetStats()

	return stats
}

// Close 关闭管理器
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.initialized {
		return nil
	}

	// 按依赖顺序关闭组件
	var errors []error

	if err := m.poolManager.Close(); err != nil {
		errors = append(errors, fmt.Errorf("关闭池管理器失败: %w", err))
	}

	if err := m.detectionEngine.Close(); err != nil {
		errors = append(errors, fmt.Errorf("关闭反风控引擎失败: %w", err))
	}

	if err := m.healthAnalyzer.Close(); err != nil {
		errors = append(errors, fmt.Errorf("关闭健康分析器失败: %w", err))
	}

	if err := m.metricsCollector.Close(); err != nil {
		errors = append(errors, fmt.Errorf("关闭指标收集器失败: %w", err))
	}

	if err := m.healthTracker.Close(); err != nil {
		errors = append(errors, fmt.Errorf("关闭健康追踪器失败: %w", err))
	}

	m.initialized = false

	if len(errors) > 0 {
		return fmt.Errorf("关闭过程中发生错误: %v", errors)
	}

	m.logger.Info("指纹管理器已关闭")
	return nil
}

// 内部方法

// loadProfiles 加载指纹配置
func (m *Manager) loadProfiles() error {
	// 加载预定义指纹
	predefinedProfiles := profiles.GetPredefinedProfiles()
	for _, profile := range predefinedProfiles {
		if err := m.profileRegistry.Register(profile); err != nil {
			m.logger.Warn("注册预定义指纹失败", "profile", profile.Name, "error", err)
		}
	}

	// 加载PopMart专用指纹
	popmartProfiles := profiles.GetPopMartProfiles()
	for _, profile := range popmartProfiles {
		if err := m.profileRegistry.Register(profile); err != nil {
			m.logger.Warn("注册PopMart指纹失败", "profile", profile.Name, "error", err)
		}
	}

	// 加载极简指纹
	minimalProfiles := profiles.GetMinimalProfiles()
	for _, profile := range minimalProfiles {
		if err := m.profileRegistry.Register(profile); err != nil {
			m.logger.Warn("注册极简指纹失败", "profile", profile.Name, "error", err)
		}
	}

	m.logger.Info("加载指纹配置完成", "total", m.profileRegistry.Count())
	return nil
}

// 全局管理器实例
var globalManager *Manager
var globalOnce sync.Once

// GetGlobalManager 获取全局管理器实例
func GetGlobalManager() *Manager {
	globalOnce.Do(func() {
		globalManager = NewManager()
	})
	return globalManager
}
