// Package health 健康指标收集
package health

import (
	"sync"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/core"
	"go-monitor/pkg/logging"
)

// MetricsCollector 指标收集器
type MetricsCollector struct {
	// 指标数据
	metrics map[string]*ProfileMetrics
	
	// 全局指标
	globalMetrics *GlobalMetrics
	
	// 配置
	config MetricsConfig
	
	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger
	
	// 生命周期管理
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// ProfileMetrics 指纹指标
type ProfileMetrics struct {
	ProfileName string
	
	// 请求统计
	TotalRequests     int64
	SuccessRequests   int64
	FailedRequests    int64
	BlockedRequests   int64
	
	// 时间统计
	TotalResponseTime time.Duration
	MinResponseTime   time.Duration
	MaxResponseTime   time.Duration
	AvgResponseTime   time.Duration
	
	// 成功率统计
	SuccessRate       float64
	RecentSuccessRate float64 // 最近的成功率
	
	// 使用统计
	UseCount          int64
	LastUsed          time.Time
	FirstUsed         time.Time
	
	// 错误统计
	ConsecutiveErrors int64
	MaxConsecutiveErrors int64
	ErrorRate         float64
	
	// 状态变化统计
	StatusChanges     int64
	LastStatusChange  time.Time
	CurrentStatus     string
	
	// 时间窗口统计（最近1小时）
	RecentWindow      *TimeWindowMetrics
}

// TimeWindowMetrics 时间窗口指标
type TimeWindowMetrics struct {
	WindowStart     time.Time
	WindowDuration  time.Duration
	Requests        int64
	Successes       int64
	Failures        int64
	Blocks          int64
	AvgResponseTime time.Duration
}

// GlobalMetrics 全局指标
type GlobalMetrics struct {
	// 总体统计
	TotalProfiles     int
	ActiveProfiles    int
	HealthyProfiles   int
	DegradedProfiles  int
	BlockedProfiles   int
	
	// 请求统计
	TotalRequests     int64
	TotalSuccesses    int64
	TotalFailures     int64
	TotalBlocks       int64
	
	// 成功率统计
	OverallSuccessRate float64
	AvgProfileSuccessRate float64
	
	// 响应时间统计
	AvgResponseTime   time.Duration
	MinResponseTime   time.Duration
	MaxResponseTime   time.Duration
	
	// 更新时间
	LastUpdated       time.Time
}

// MetricsConfig 指标配置
type MetricsConfig struct {
	CollectionInterval time.Duration // 收集间隔
	WindowDuration     time.Duration // 时间窗口大小
	RetentionPeriod    time.Duration // 保留期
	MaxProfiles        int           // 最大指纹数
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(config MetricsConfig) *MetricsCollector {
	return &MetricsCollector{
		metrics: make(map[string]*ProfileMetrics),
		globalMetrics: &GlobalMetrics{
			LastUpdated: time.Now(),
		},
		config: config,
		logger: logging.GetLogger("fingerprint.health.metrics"),
		stopCh: make(chan struct{}),
	}
}

// Initialize 初始化指标收集器
func (c *MetricsCollector) Initialize() error {
	// 启动收集任务
	c.wg.Add(1)
	go c.collectionLoop()
	
	c.logger.Info("指标收集器初始化完成")
	return nil
}

// RecordRequest 记录请求
func (c *MetricsCollector) RecordRequest(profileName string, success bool, responseTime time.Duration, blocked bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	metrics := c.getOrCreateMetrics(profileName)
	now := time.Now()
	
	// 更新基础统计
	metrics.TotalRequests++
	metrics.UseCount++
	metrics.LastUsed = now
	
	if metrics.FirstUsed.IsZero() {
		metrics.FirstUsed = now
	}
	
	// 更新响应时间统计
	c.updateResponseTimeMetrics(metrics, responseTime)
	
	// 更新成功/失败统计
	if blocked {
		metrics.BlockedRequests++
		metrics.ConsecutiveErrors++
	} else if success {
		metrics.SuccessRequests++
		metrics.ConsecutiveErrors = 0
	} else {
		metrics.FailedRequests++
		metrics.ConsecutiveErrors++
	}
	
	// 更新连续错误统计
	if metrics.ConsecutiveErrors > metrics.MaxConsecutiveErrors {
		metrics.MaxConsecutiveErrors = metrics.ConsecutiveErrors
	}
	
	// 更新成功率
	c.updateSuccessRateMetrics(metrics)
	
	// 更新时间窗口统计
	c.updateWindowMetrics(metrics, success, responseTime, blocked)
	
	c.logger.Debug("记录请求指标", "profile", profileName, "success", success, "response_time", responseTime)
}

// RecordStatusChange 记录状态变化
func (c *MetricsCollector) RecordStatusChange(profileName string, newStatus string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	metrics := c.getOrCreateMetrics(profileName)
	
	if metrics.CurrentStatus != newStatus {
		metrics.StatusChanges++
		metrics.LastStatusChange = time.Now()
		metrics.CurrentStatus = newStatus
		
		c.logger.Debug("记录状态变化", "profile", profileName, "status", newStatus)
	}
}

// GetProfileMetrics 获取指纹指标
func (c *MetricsCollector) GetProfileMetrics(profileName string) *ProfileMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	if metrics, exists := c.metrics[profileName]; exists {
		return c.copyProfileMetrics(metrics)
	}
	
	return nil
}

// GetAllMetrics 获取所有指标
func (c *MetricsCollector) GetAllMetrics() map[string]*ProfileMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	result := make(map[string]*ProfileMetrics)
	for name, metrics := range c.metrics {
		result[name] = c.copyProfileMetrics(metrics)
	}
	
	return result
}

// GetGlobalMetrics 获取全局指标
func (c *MetricsCollector) GetGlobalMetrics() *GlobalMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	return c.copyGlobalMetrics(c.globalMetrics)
}

// GetTopPerformers 获取表现最好的指纹
func (c *MetricsCollector) GetTopPerformers(limit int) []*ProfileMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	var performers []*ProfileMetrics
	for _, metrics := range c.metrics {
		if metrics.TotalRequests > 10 { // 至少有一定的样本量
			performers = append(performers, c.copyProfileMetrics(metrics))
		}
	}
	
	// 按成功率排序
	for i := 0; i < len(performers)-1; i++ {
		for j := i + 1; j < len(performers); j++ {
			if performers[i].SuccessRate < performers[j].SuccessRate {
				performers[i], performers[j] = performers[j], performers[i]
			}
		}
	}
	
	if len(performers) > limit {
		performers = performers[:limit]
	}
	
	return performers
}

// GetWorstPerformers 获取表现最差的指纹
func (c *MetricsCollector) GetWorstPerformers(limit int) []*ProfileMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	var performers []*ProfileMetrics
	for _, metrics := range c.metrics {
		if metrics.TotalRequests > 10 {
			performers = append(performers, c.copyProfileMetrics(metrics))
		}
	}
	
	// 按成功率排序（升序）
	for i := 0; i < len(performers)-1; i++ {
		for j := i + 1; j < len(performers); j++ {
			if performers[i].SuccessRate > performers[j].SuccessRate {
				performers[i], performers[j] = performers[j], performers[i]
			}
		}
	}
	
	if len(performers) > limit {
		performers = performers[:limit]
	}
	
	return performers
}

// 内部方法

// getOrCreateMetrics 获取或创建指标
func (c *MetricsCollector) getOrCreateMetrics(profileName string) *ProfileMetrics {
	if metrics, exists := c.metrics[profileName]; exists {
		return metrics
	}
	
	metrics := &ProfileMetrics{
		ProfileName:    profileName,
		CurrentStatus:  core.HealthStatusHealthy,
		RecentWindow: &TimeWindowMetrics{
			WindowStart:    time.Now(),
			WindowDuration: c.config.WindowDuration,
		},
	}
	
	c.metrics[profileName] = metrics
	return metrics
}

// updateResponseTimeMetrics 更新响应时间指标
func (c *MetricsCollector) updateResponseTimeMetrics(metrics *ProfileMetrics, responseTime time.Duration) {
	metrics.TotalResponseTime += responseTime
	
	if metrics.MinResponseTime == 0 || responseTime < metrics.MinResponseTime {
		metrics.MinResponseTime = responseTime
	}
	
	if responseTime > metrics.MaxResponseTime {
		metrics.MaxResponseTime = responseTime
	}
	
	if metrics.SuccessRequests > 0 {
		metrics.AvgResponseTime = metrics.TotalResponseTime / time.Duration(metrics.SuccessRequests)
	}
}

// updateSuccessRateMetrics 更新成功率指标
func (c *MetricsCollector) updateSuccessRateMetrics(metrics *ProfileMetrics) {
	if metrics.TotalRequests > 0 {
		metrics.SuccessRate = float64(metrics.SuccessRequests) / float64(metrics.TotalRequests)
		metrics.ErrorRate = float64(metrics.FailedRequests+metrics.BlockedRequests) / float64(metrics.TotalRequests)
	}
	
	// 计算最近的成功率（基于时间窗口）
	if metrics.RecentWindow.Requests > 0 {
		metrics.RecentSuccessRate = float64(metrics.RecentWindow.Successes) / float64(metrics.RecentWindow.Requests)
	}
}

// updateWindowMetrics 更新时间窗口指标
func (c *MetricsCollector) updateWindowMetrics(metrics *ProfileMetrics, success bool, responseTime time.Duration, blocked bool) {
	window := metrics.RecentWindow
	now := time.Now()
	
	// 检查是否需要重置窗口
	if now.Sub(window.WindowStart) > window.WindowDuration {
		window.WindowStart = now
		window.Requests = 0
		window.Successes = 0
		window.Failures = 0
		window.Blocks = 0
		window.AvgResponseTime = 0
	}
	
	// 更新窗口统计
	window.Requests++
	if blocked {
		window.Blocks++
	} else if success {
		window.Successes++
		// 更新窗口平均响应时间
		if window.Successes == 1 {
			window.AvgResponseTime = responseTime
		} else {
			window.AvgResponseTime = (window.AvgResponseTime*time.Duration(window.Successes-1) + responseTime) / time.Duration(window.Successes)
		}
	} else {
		window.Failures++
	}
}

// updateGlobalMetrics 更新全局指标
func (c *MetricsCollector) updateGlobalMetrics() {
	var totalRequests, totalSuccesses, totalFailures, totalBlocks int64
	var totalResponseTime time.Duration
	var minResponseTime, maxResponseTime time.Duration
	var healthyCount, degradedCount, blockedCount int
	
	for _, metrics := range c.metrics {
		totalRequests += metrics.TotalRequests
		totalSuccesses += metrics.SuccessRequests
		totalFailures += metrics.FailedRequests
		totalBlocks += metrics.BlockedRequests
		totalResponseTime += metrics.TotalResponseTime
		
		if minResponseTime == 0 || (metrics.MinResponseTime > 0 && metrics.MinResponseTime < minResponseTime) {
			minResponseTime = metrics.MinResponseTime
		}
		
		if metrics.MaxResponseTime > maxResponseTime {
			maxResponseTime = metrics.MaxResponseTime
		}
		
		switch metrics.CurrentStatus {
		case core.HealthStatusHealthy:
			healthyCount++
		case core.HealthStatusDegraded:
			degradedCount++
		case core.HealthStatusBlocked:
			blockedCount++
		}
	}
	
	// 更新全局指标
	c.globalMetrics.TotalProfiles = len(c.metrics)
	c.globalMetrics.ActiveProfiles = len(c.metrics)
	c.globalMetrics.HealthyProfiles = healthyCount
	c.globalMetrics.DegradedProfiles = degradedCount
	c.globalMetrics.BlockedProfiles = blockedCount
	c.globalMetrics.TotalRequests = totalRequests
	c.globalMetrics.TotalSuccesses = totalSuccesses
	c.globalMetrics.TotalFailures = totalFailures
	c.globalMetrics.TotalBlocks = totalBlocks
	c.globalMetrics.MinResponseTime = minResponseTime
	c.globalMetrics.MaxResponseTime = maxResponseTime
	c.globalMetrics.LastUpdated = time.Now()
	
	if totalRequests > 0 {
		c.globalMetrics.OverallSuccessRate = float64(totalSuccesses) / float64(totalRequests)
	}
	
	if totalSuccesses > 0 {
		c.globalMetrics.AvgResponseTime = totalResponseTime / time.Duration(totalSuccesses)
	}
	
	// 计算平均指纹成功率
	if len(c.metrics) > 0 {
		var totalSuccessRate float64
		for _, metrics := range c.metrics {
			totalSuccessRate += metrics.SuccessRate
		}
		c.globalMetrics.AvgProfileSuccessRate = totalSuccessRate / float64(len(c.metrics))
	}
}

// copyProfileMetrics 复制指纹指标
func (c *MetricsCollector) copyProfileMetrics(metrics *ProfileMetrics) *ProfileMetrics {
	return &ProfileMetrics{
		ProfileName:          metrics.ProfileName,
		TotalRequests:        metrics.TotalRequests,
		SuccessRequests:      metrics.SuccessRequests,
		FailedRequests:       metrics.FailedRequests,
		BlockedRequests:      metrics.BlockedRequests,
		TotalResponseTime:    metrics.TotalResponseTime,
		MinResponseTime:      metrics.MinResponseTime,
		MaxResponseTime:      metrics.MaxResponseTime,
		AvgResponseTime:      metrics.AvgResponseTime,
		SuccessRate:          metrics.SuccessRate,
		RecentSuccessRate:    metrics.RecentSuccessRate,
		UseCount:             metrics.UseCount,
		LastUsed:             metrics.LastUsed,
		FirstUsed:            metrics.FirstUsed,
		ConsecutiveErrors:    metrics.ConsecutiveErrors,
		MaxConsecutiveErrors: metrics.MaxConsecutiveErrors,
		ErrorRate:            metrics.ErrorRate,
		StatusChanges:        metrics.StatusChanges,
		LastStatusChange:     metrics.LastStatusChange,
		CurrentStatus:        metrics.CurrentStatus,
	}
}

// copyGlobalMetrics 复制全局指标
func (c *MetricsCollector) copyGlobalMetrics(metrics *GlobalMetrics) *GlobalMetrics {
	return &GlobalMetrics{
		TotalProfiles:         metrics.TotalProfiles,
		ActiveProfiles:        metrics.ActiveProfiles,
		HealthyProfiles:       metrics.HealthyProfiles,
		DegradedProfiles:      metrics.DegradedProfiles,
		BlockedProfiles:       metrics.BlockedProfiles,
		TotalRequests:         metrics.TotalRequests,
		TotalSuccesses:        metrics.TotalSuccesses,
		TotalFailures:         metrics.TotalFailures,
		TotalBlocks:           metrics.TotalBlocks,
		OverallSuccessRate:    metrics.OverallSuccessRate,
		AvgProfileSuccessRate: metrics.AvgProfileSuccessRate,
		AvgResponseTime:       metrics.AvgResponseTime,
		MinResponseTime:       metrics.MinResponseTime,
		MaxResponseTime:       metrics.MaxResponseTime,
		LastUpdated:           metrics.LastUpdated,
	}
}

// collectionLoop 收集循环
func (c *MetricsCollector) collectionLoop() {
	defer c.wg.Done()
	
	ticker := time.NewTicker(c.config.CollectionInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			c.performCollection()
		case <-c.stopCh:
			return
		}
	}
}

// performCollection 执行收集
func (c *MetricsCollector) performCollection() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// 更新全局指标
	c.updateGlobalMetrics()
	
	// 清理过期指标
	c.cleanupExpiredMetrics()
	
	c.logger.Debug("指标收集完成", "profiles", len(c.metrics))
}

// cleanupExpiredMetrics 清理过期指标
func (c *MetricsCollector) cleanupExpiredMetrics() {
	now := time.Now()
	var toDelete []string
	
	for name, metrics := range c.metrics {
		if now.Sub(metrics.LastUsed) > c.config.RetentionPeriod {
			toDelete = append(toDelete, name)
		}
	}
	
	for _, name := range toDelete {
		delete(c.metrics, name)
	}
	
	if len(toDelete) > 0 {
		c.logger.Debug("清理过期指标", "count", len(toDelete))
	}
}

// Close 关闭指标收集器
func (c *MetricsCollector) Close() error {
	close(c.stopCh)
	c.wg.Wait()
	c.logger.Info("指标收集器已关闭")
	return nil
}
