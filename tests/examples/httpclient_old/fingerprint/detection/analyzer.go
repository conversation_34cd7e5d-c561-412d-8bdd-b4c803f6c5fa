// Package detection 响应分析器
package detection

import (
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"go-monitor/pkg/logging"
)

// ResponseAnalyzer 响应分析器
type ResponseAnalyzer struct {
	// 模式管理器
	patternManager *PatternManager

	// 分析缓存
	analysisCache map[string]*AnalysisResult
	cacheExpiry   map[string]time.Time

	// 配置
	config AnalyzerConfig

	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	IsBlocked        bool
	BlockProbability float64
	RiskLevel        string
	DetectedPatterns []string
	Recommendations  []string
	AnalyzedAt       time.Time
}

// AnalyzerConfig 分析器配置
type AnalyzerConfig struct {
	CacheExpiry    time.Duration // 缓存过期时间
	MaxCacheSize   int           // 最大缓存大小
	EnableDeepScan bool          // 启用深度扫描
	ScanTimeout    time.Duration // 扫描超时
}

// NewResponseAnalyzer 创建响应分析器
func NewResponseAnalyzer(patternManager *PatternManager, config AnalyzerConfig) *ResponseAnalyzer {
	return &ResponseAnalyzer{
		patternManager: patternManager,
		analysisCache:  make(map[string]*AnalysisResult),
		cacheExpiry:    make(map[string]time.Time),
		config:         config,
		logger:         logging.GetLogger("fingerprint.detection.analyzer"),
	}
}

// AnalyzeResponse 分析响应
func (ra *ResponseAnalyzer) AnalyzeResponse(serverName string, statusCode int, responseBody string, headers map[string]string) *AnalysisResult {
	// 生成缓存键
	cacheKey := ra.generateCacheKey(serverName, statusCode, responseBody)

	// 检查缓存
	if result := ra.getCachedResult(cacheKey); result != nil {
		return result
	}

	// 执行分析
	result := ra.performAnalysis(serverName, statusCode, responseBody, headers)

	// 缓存结果
	ra.cacheResult(cacheKey, result)

	return result
}

// AnalyzeBatch 批量分析响应
func (ra *ResponseAnalyzer) AnalyzeBatch(requests []AnalysisRequest) []AnalysisResult {
	var results []AnalysisResult

	for _, req := range requests {
		result := ra.AnalyzeResponse(req.ServerName, req.StatusCode, req.ResponseBody, req.Headers)
		results = append(results, *result)
	}

	return results
}

// AnalysisRequest 分析请求
type AnalysisRequest struct {
	ServerName   string
	StatusCode   int
	ResponseBody string
	Headers      map[string]string
}

// GetBlockedProbability 获取被风控概率
func (ra *ResponseAnalyzer) GetBlockedProbability(serverName string, statusCode int, responseBody string) float64 {
	result := ra.AnalyzeResponse(serverName, statusCode, responseBody, nil)
	return result.BlockProbability
}

// IsHighRisk 检查是否高风险
func (ra *ResponseAnalyzer) IsHighRisk(serverName string, statusCode int, responseBody string) bool {
	result := ra.AnalyzeResponse(serverName, statusCode, responseBody, nil)
	return result.RiskLevel == "high" || result.BlockProbability > 0.8
}

// GetRecommendations 获取建议
func (ra *ResponseAnalyzer) GetRecommendations(serverName string, statusCode int, responseBody string) []string {
	result := ra.AnalyzeResponse(serverName, statusCode, responseBody, nil)
	return result.Recommendations
}

// ClearCache 清理缓存
func (ra *ResponseAnalyzer) ClearCache() {
	ra.mu.Lock()
	defer ra.mu.Unlock()

	ra.analysisCache = make(map[string]*AnalysisResult)
	ra.cacheExpiry = make(map[string]time.Time)

	ra.logger.Debug("清理分析缓存")
}

// GetCacheStats 获取缓存统计
func (ra *ResponseAnalyzer) GetCacheStats() map[string]interface{} {
	ra.mu.RLock()
	defer ra.mu.RUnlock()

	return map[string]interface{}{
		"cache_size":     len(ra.analysisCache),
		"max_cache_size": ra.config.MaxCacheSize,
		"cache_hit_rate": ra.calculateCacheHitRate(),
	}
}

// 内部方法

// performAnalysis 执行分析
func (ra *ResponseAnalyzer) performAnalysis(serverName string, statusCode int, responseBody string, headers map[string]string) *AnalysisResult {
	result := &AnalysisResult{
		AnalyzedAt: time.Now(),
	}

	// 1. 基础风控检测
	result.IsBlocked = ra.patternManager.IsBlocked(serverName, statusCode, responseBody)
	result.BlockProbability = ra.patternManager.GetBlockedProbability(serverName, statusCode, responseBody)

	// 2. 深度分析
	if ra.config.EnableDeepScan {
		ra.performDeepAnalysis(result, serverName, statusCode, responseBody, headers)
	}

	// 3. 确定风险级别
	result.RiskLevel = ra.determineRiskLevel(result.BlockProbability)

	// 4. 检测模式
	result.DetectedPatterns = ra.detectPatterns(statusCode, responseBody)

	// 5. 生成建议
	result.Recommendations = ra.generateRecommendations(result)

	return result
}

// performDeepAnalysis 执行深度分析
func (ra *ResponseAnalyzer) performDeepAnalysis(result *AnalysisResult, serverName string, statusCode int, responseBody string, headers map[string]string) {
	// 分析响应头
	if headers != nil {
		headerRisk := ra.analyzeHeaders(headers)
		result.BlockProbability = (result.BlockProbability + headerRisk) / 2
	}

	// 分析响应体结构
	bodyRisk := ra.analyzeBodyStructure(responseBody)
	result.BlockProbability = (result.BlockProbability + bodyRisk) / 2

	// 分析响应时间模式（如果有的话）
	// 这里可以添加响应时间分析逻辑

	// 确保概率在合理范围内
	if result.BlockProbability > 1.0 {
		result.BlockProbability = 1.0
	}
	if result.BlockProbability < 0.0 {
		result.BlockProbability = 0.0
	}

	// 更新阻塞状态
	if result.BlockProbability > 0.7 {
		result.IsBlocked = true
	}
}

// analyzeHeaders 分析响应头
func (ra *ResponseAnalyzer) analyzeHeaders(headers map[string]string) float64 {
	risk := 0.0

	// 检查可疑的响应头
	suspiciousHeaders := map[string][]string{
		"server":          {"cloudflare", "nginx/1.0", "apache/1.0"},
		"x-frame-options": {"deny", "sameorigin"},
		"x-robots-tag":    {"noindex", "nofollow"},
		"retry-after":     {}, // 任何值都可疑
		"x-rate-limit":    {}, // 任何值都可疑
	}

	for headerName, suspiciousValues := range suspiciousHeaders {
		if headerValue, exists := headers[strings.ToLower(headerName)]; exists {
			if len(suspiciousValues) == 0 {
				// 任何值都可疑
				risk += 0.3
			} else {
				// 检查特定值
				for _, suspicious := range suspiciousValues {
					if strings.Contains(strings.ToLower(headerValue), suspicious) {
						risk += 0.2
						break
					}
				}
			}
		}
	}

	return risk
}

// analyzeBodyStructure 分析响应体结构
func (ra *ResponseAnalyzer) analyzeBodyStructure(responseBody string) float64 {
	if responseBody == "" {
		return 0.0
	}

	risk := 0.0
	lowerBody := strings.ToLower(responseBody)

	// 检查HTML结构特征
	if strings.Contains(lowerBody, "<html") {
		// 检查是否为错误页面
		errorIndicators := []string{
			"error", "forbidden", "access denied", "blocked",
			"captcha", "verification", "unusual activity",
			"rate limit", "too many requests",
		}

		for _, indicator := range errorIndicators {
			if strings.Contains(lowerBody, indicator) {
				risk += 0.4
			}
		}

		// 检查页面标题
		if titleMatch := regexp.MustCompile(`<title[^>]*>([^<]+)</title>`).FindStringSubmatch(lowerBody); titleMatch != nil {
			title := strings.ToLower(titleMatch[1])
			if strings.Contains(title, "error") || strings.Contains(title, "blocked") {
				risk += 0.3
			}
		}
	}

	// 检查JSON结构特征
	if strings.Contains(lowerBody, "{") && strings.Contains(lowerBody, "}") {
		jsonErrorIndicators := []string{
			`"error"`, `"message"`, `"code"`, `"status"`,
			`"blocked"`, `"forbidden"`, `"denied"`,
		}

		for _, indicator := range jsonErrorIndicators {
			if strings.Contains(lowerBody, indicator) {
				risk += 0.2
			}
		}
	}

	return risk
}

// determineRiskLevel 确定风险级别
func (ra *ResponseAnalyzer) determineRiskLevel(probability float64) string {
	if probability >= 0.8 {
		return "high"
	} else if probability >= 0.5 {
		return "medium"
	} else if probability >= 0.2 {
		return "low"
	} else {
		return "none"
	}
}

// detectPatterns 检测模式
func (ra *ResponseAnalyzer) detectPatterns(statusCode int, responseBody string) []string {
	var patterns []string

	// 状态码模式
	switch statusCode {
	case 403:
		patterns = append(patterns, "forbidden_access")
	case 429:
		patterns = append(patterns, "rate_limiting")
	case 418:
		patterns = append(patterns, "teapot_anti_bot")
	case 470, 471:
		patterns = append(patterns, "custom_blocking")
	case 503:
		patterns = append(patterns, "service_unavailable")
	}

	// 响应体模式
	if responseBody != "" {
		lowerBody := strings.ToLower(responseBody)

		if strings.Contains(lowerBody, "captcha") {
			patterns = append(patterns, "captcha_challenge")
		}
		if strings.Contains(lowerBody, "cloudflare") {
			patterns = append(patterns, "cloudflare_protection")
		}
		if strings.Contains(lowerBody, "rate limit") {
			patterns = append(patterns, "rate_limit_message")
		}
		if strings.Contains(lowerBody, "unusual activity") {
			patterns = append(patterns, "suspicious_activity")
		}
	}

	return patterns
}

// generateRecommendations 生成建议
func (ra *ResponseAnalyzer) generateRecommendations(result *AnalysisResult) []string {
	var recommendations []string

	if result.IsBlocked {
		recommendations = append(recommendations, "立即停止使用当前指纹")
		recommendations = append(recommendations, "等待冷却期后重试")

		if result.RiskLevel == "high" {
			recommendations = append(recommendations, "考虑更换IP地址")
			recommendations = append(recommendations, "使用更保守的请求频率")
		}
	}

	// 基于检测到的模式给出建议
	for _, pattern := range result.DetectedPatterns {
		switch pattern {
		case "captcha_challenge":
			recommendations = append(recommendations, "需要人工解决验证码")
		case "rate_limiting":
			recommendations = append(recommendations, "降低请求频率")
		case "cloudflare_protection":
			recommendations = append(recommendations, "考虑使用更真实的浏览器指纹")
		}
	}

	if result.BlockProbability > 0.5 && result.BlockProbability < 0.8 {
		recommendations = append(recommendations, "建议更换指纹配置")
		recommendations = append(recommendations, "监控后续请求状态")
	}

	return recommendations
}

// generateCacheKey 生成缓存键
func (ra *ResponseAnalyzer) generateCacheKey(serverName string, statusCode int, responseBody string) string {
	// 简化的缓存键生成
	bodyHash := ra.simpleHash(responseBody)
	return serverName + "_" + strconv.Itoa(statusCode) + "_" + bodyHash
}

// simpleHash 简单哈希
func (ra *ResponseAnalyzer) simpleHash(s string) string {
	if len(s) > 100 {
		s = s[:100] // 只取前100个字符
	}

	hash := 0
	for _, c := range s {
		hash = hash*31 + int(c)
	}

	return strconv.Itoa(hash)
}

// getCachedResult 获取缓存结果
func (ra *ResponseAnalyzer) getCachedResult(cacheKey string) *AnalysisResult {
	ra.mu.RLock()
	defer ra.mu.RUnlock()

	if result, exists := ra.analysisCache[cacheKey]; exists {
		if expiry, hasExpiry := ra.cacheExpiry[cacheKey]; hasExpiry {
			if time.Now().Before(expiry) {
				return result
			}
			// 过期了，删除
			delete(ra.analysisCache, cacheKey)
			delete(ra.cacheExpiry, cacheKey)
		}
	}

	return nil
}

// cacheResult 缓存结果
func (ra *ResponseAnalyzer) cacheResult(cacheKey string, result *AnalysisResult) {
	ra.mu.Lock()
	defer ra.mu.Unlock()

	// 检查缓存大小限制
	if len(ra.analysisCache) >= ra.config.MaxCacheSize {
		// 简单的LRU：删除最旧的条目
		ra.evictOldestCache()
	}

	ra.analysisCache[cacheKey] = result
	ra.cacheExpiry[cacheKey] = time.Now().Add(ra.config.CacheExpiry)
}

// evictOldestCache 驱逐最旧的缓存
func (ra *ResponseAnalyzer) evictOldestCache() {
	var oldestKey string
	var oldestTime time.Time = time.Now()

	for key, expiry := range ra.cacheExpiry {
		if expiry.Before(oldestTime) {
			oldestTime = expiry
			oldestKey = key
		}
	}

	if oldestKey != "" {
		delete(ra.analysisCache, oldestKey)
		delete(ra.cacheExpiry, oldestKey)
	}
}

// calculateCacheHitRate 计算缓存命中率
func (ra *ResponseAnalyzer) calculateCacheHitRate() float64 {
	// 简化实现，实际应该跟踪命中和未命中次数
	return 0.0
}
