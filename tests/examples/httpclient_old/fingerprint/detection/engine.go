// Package detection 反风控引擎
package detection

import (
	"sync"
	"time"

	"go-monitor/pkg/httpclient/fingerprint/core"
	"go-monitor/pkg/logging"
)

// Engine 反风控引擎
type Engine struct {
	// 检测模式
	patterns map[string]*core.DetectionPattern

	// 服务器状态跟踪
	serverStatus map[string]*ServerStatus

	// 配置
	config EngineConfig

	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger

	// 生命周期管理
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// ServerStatus 服务器状态
type ServerStatus struct {
	ServerName       string
	LastBlockTime    time.Time
	BlockCount       int64
	RecentBlocks     []BlockEvent
	RiskLevel        string
	RecommendedDelay time.Duration
	LastAnalyzed     time.Time
}

// BlockEvent 封禁事件
type BlockEvent struct {
	Timestamp    time.Time
	StatusCode   int
	ProfileName  string
	ResponseBody string
	UserAgent    string
}

// EngineConfig 引擎配置
type EngineConfig struct {
	MaxRecentBlocks  int           // 最大近期封禁记录数
	BlockWindowSize  time.Duration // 封禁窗口大小
	AnalysisInterval time.Duration // 分析间隔
	DefaultCooldown  time.Duration // 默认冷却时间
	MaxCooldown      time.Duration // 最大冷却时间
}

// NewEngine 创建反风控引擎
func NewEngine(config EngineConfig) *Engine {
	return &Engine{
		patterns:     make(map[string]*core.DetectionPattern),
		serverStatus: make(map[string]*ServerStatus),
		config:       config,
		logger:       logging.GetLogger("fingerprint.detection.engine"),
		stopCh:       make(chan struct{}),
	}
}

// Initialize 初始化引擎
func (e *Engine) Initialize() error {
	// 加载预定义检测模式
	e.loadPredefinedPatterns()

	// 启动分析任务
	e.wg.Add(1)
	go e.analysisLoop()

	e.logger.Info("反风控引擎初始化完成", "patterns", len(e.patterns))
	return nil
}

// AnalyzeResponse 分析响应
func (e *Engine) AnalyzeResponse(serverName string, statusCode int, body string, responseTime time.Duration, profileName string) {
	e.mu.Lock()
	defer e.mu.Unlock()

	// 获取或创建服务器状态
	status := e.getOrCreateServerStatus(serverName)

	// 检查是否为风控响应
	riskInfo := core.AnalyzeRiskControl(statusCode, nil)

	if riskInfo.Type != core.RiskControlNone {
		// 记录封禁事件
		blockEvent := BlockEvent{
			Timestamp:    time.Now(),
			StatusCode:   statusCode,
			ProfileName:  profileName,
			ResponseBody: e.truncateBody(body),
		}

		status.RecentBlocks = append(status.RecentBlocks, blockEvent)
		status.BlockCount++
		status.LastBlockTime = time.Now()

		// 限制近期封禁记录数量
		if len(status.RecentBlocks) > e.config.MaxRecentBlocks {
			status.RecentBlocks = status.RecentBlocks[1:]
		}

		// 更新风险级别
		e.updateRiskLevel(status)

		e.logger.Warn("检测到风控响应",
			"server", serverName,
			"status_code", statusCode,
			"profile", profileName,
			"risk_type", riskInfo.Type.String())
	}

	// 更新最后分析时间
	status.LastAnalyzed = time.Now()
}

// IsBlocked 检查指纹是否被封
func (e *Engine) IsBlocked(profileName string) bool {
	e.mu.RLock()
	defer e.mu.RUnlock()

	// 检查所有服务器的近期封禁记录
	for _, status := range e.serverStatus {
		for _, block := range status.RecentBlocks {
			if block.ProfileName == profileName {
				// 检查是否在冷却期内
				if time.Since(block.Timestamp) < e.calculateCooldown(status) {
					return true
				}
			}
		}
	}

	return false
}

// GetRecommendedStrategy 获取推荐策略
func (e *Engine) GetRecommendedStrategy(serverName string) *core.DetectionStrategy {
	e.mu.RLock()
	defer e.mu.RUnlock()

	status, exists := e.serverStatus[serverName]
	if !exists {
		// 返回默认策略
		return &core.DetectionStrategy{
			RecommendedProfiles: []string{},
			AvoidProfiles:       []string{},
			CooldownDuration:    e.config.DefaultCooldown,
			SwitchProbability:   0.1,
		}
	}

	strategy := &core.DetectionStrategy{
		CooldownDuration:  e.calculateCooldown(status),
		SwitchProbability: e.calculateSwitchProbability(status),
	}

	// 收集需要避免的指纹
	var avoidProfiles []string
	recentWindow := time.Now().Add(-e.config.BlockWindowSize)

	for _, block := range status.RecentBlocks {
		if block.Timestamp.After(recentWindow) {
			avoidProfiles = append(avoidProfiles, block.ProfileName)
		}
	}

	strategy.AvoidProfiles = e.removeDuplicates(avoidProfiles)

	// 根据风险级别推荐指纹
	switch status.RiskLevel {
	case "low":
		strategy.RecommendedProfiles = []string{"PopMart_Charles_Ultra_Exact"}
	case "medium":
		strategy.RecommendedProfiles = []string{"Minimal_TLS13_v1", "Proxy_Like_v1"}
	case "high":
		strategy.RecommendedProfiles = []string{"Ultra_Minimal_TLS13"}
	default:
		strategy.RecommendedProfiles = []string{"PopMart_Charles_Ultra_Exact", "PopMart_Chrome_Stable"}
	}

	return strategy
}

// GetServerStatus 获取服务器状态
func (e *Engine) GetServerStatus(serverName string) *ServerStatus {
	e.mu.RLock()
	defer e.mu.RUnlock()

	if status, exists := e.serverStatus[serverName]; exists {
		return e.copyServerStatus(status)
	}

	return nil
}

// GetAllServerStatus 获取所有服务器状态
func (e *Engine) GetAllServerStatus() map[string]*ServerStatus {
	e.mu.RLock()
	defer e.mu.RUnlock()

	result := make(map[string]*ServerStatus)
	for name, status := range e.serverStatus {
		result[name] = e.copyServerStatus(status)
	}

	return result
}

// ResetServerStatus 重置服务器状态
func (e *Engine) ResetServerStatus(serverName string) {
	e.mu.Lock()
	defer e.mu.Unlock()

	if status, exists := e.serverStatus[serverName]; exists {
		status.BlockCount = 0
		status.RecentBlocks = []BlockEvent{}
		status.RiskLevel = "low"
		status.RecommendedDelay = e.config.DefaultCooldown
		status.LastBlockTime = time.Time{}

		e.logger.Info("重置服务器状态", "server", serverName)
	}
}

// 内部方法

// loadPredefinedPatterns 加载预定义检测模式
func (e *Engine) loadPredefinedPatterns() {
	now := time.Now()

	// PopMart检测模式
	popmartPattern := &core.DetectionPattern{
		ServerName:         "prod-intl-api.popmart.com",
		BlockedStatusCodes: []int{470, 471, 418, 403, 429},
		BlockedKeywords:    []string{"blocked", "forbidden", "rate limit", "too many requests"},
		DetectionMethods:   []string{"status_code", "response_body", "response_time"},
		Sensitivity:        0.8,
		LastUpdated:        now,
		SampleCount:        0,
	}
	e.patterns["popmart"] = popmartPattern

	// 通用检测模式
	genericPattern := &core.DetectionPattern{
		ServerName:         "*",
		BlockedStatusCodes: []int{403, 429, 418, 470, 471},
		BlockedKeywords:    []string{"blocked", "banned", "forbidden", "rate limit", "captcha"},
		DetectionMethods:   []string{"status_code", "response_body"},
		Sensitivity:        0.7,
		LastUpdated:        now,
		SampleCount:        0,
	}
	e.patterns["generic"] = genericPattern
}

// getOrCreateServerStatus 获取或创建服务器状态
func (e *Engine) getOrCreateServerStatus(serverName string) *ServerStatus {
	if status, exists := e.serverStatus[serverName]; exists {
		return status
	}

	status := &ServerStatus{
		ServerName:       serverName,
		RecentBlocks:     []BlockEvent{},
		RiskLevel:        "low",
		RecommendedDelay: e.config.DefaultCooldown,
		LastAnalyzed:     time.Now(),
	}

	e.serverStatus[serverName] = status
	return status
}

// updateRiskLevel 更新风险级别
func (e *Engine) updateRiskLevel(status *ServerStatus) {
	recentWindow := time.Now().Add(-e.config.BlockWindowSize)
	recentBlockCount := 0

	for _, block := range status.RecentBlocks {
		if block.Timestamp.After(recentWindow) {
			recentBlockCount++
		}
	}

	// 根据近期封禁次数确定风险级别
	if recentBlockCount >= 5 {
		status.RiskLevel = "high"
		status.RecommendedDelay = e.config.MaxCooldown
	} else if recentBlockCount >= 2 {
		status.RiskLevel = "medium"
		status.RecommendedDelay = e.config.DefaultCooldown * 2
	} else {
		status.RiskLevel = "low"
		status.RecommendedDelay = e.config.DefaultCooldown
	}
}

// calculateCooldown 计算冷却时间
func (e *Engine) calculateCooldown(status *ServerStatus) time.Duration {
	baseCooldown := e.config.DefaultCooldown

	// 根据风险级别调整
	switch status.RiskLevel {
	case "high":
		baseCooldown = e.config.MaxCooldown
	case "medium":
		baseCooldown = e.config.DefaultCooldown * 2
	}

	// 根据近期封禁频率调整
	if len(status.RecentBlocks) > 0 {
		recentWindow := time.Now().Add(-time.Hour)
		recentCount := 0

		for _, block := range status.RecentBlocks {
			if block.Timestamp.After(recentWindow) {
				recentCount++
			}
		}

		if recentCount > 3 {
			baseCooldown = baseCooldown * time.Duration(recentCount)
		}
	}

	// 确保不超过最大冷却时间
	if baseCooldown > e.config.MaxCooldown {
		baseCooldown = e.config.MaxCooldown
	}

	return baseCooldown
}

// calculateSwitchProbability 计算切换概率
func (e *Engine) calculateSwitchProbability(status *ServerStatus) float64 {
	baseProbability := 0.1

	// 根据风险级别调整
	switch status.RiskLevel {
	case "high":
		baseProbability = 0.8
	case "medium":
		baseProbability = 0.4
	case "low":
		baseProbability = 0.1
	}

	// 根据近期封禁次数调整
	recentWindow := time.Now().Add(-time.Hour)
	recentCount := 0

	for _, block := range status.RecentBlocks {
		if block.Timestamp.After(recentWindow) {
			recentCount++
		}
	}

	if recentCount > 0 {
		baseProbability += float64(recentCount) * 0.1
	}

	// 确保在合理范围内
	if baseProbability > 1.0 {
		baseProbability = 1.0
	}

	return baseProbability
}

// truncateBody 截断响应体
func (e *Engine) truncateBody(body string) string {
	maxLength := 500
	if len(body) > maxLength {
		return body[:maxLength] + "..."
	}
	return body
}

// removeDuplicates 移除重复项
func (e *Engine) removeDuplicates(items []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, item := range items {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}

// copyServerStatus 复制服务器状态
func (e *Engine) copyServerStatus(status *ServerStatus) *ServerStatus {
	copied := &ServerStatus{
		ServerName:       status.ServerName,
		LastBlockTime:    status.LastBlockTime,
		BlockCount:       status.BlockCount,
		RiskLevel:        status.RiskLevel,
		RecommendedDelay: status.RecommendedDelay,
		LastAnalyzed:     status.LastAnalyzed,
	}

	// 复制近期封禁记录
	copied.RecentBlocks = make([]BlockEvent, len(status.RecentBlocks))
	copy(copied.RecentBlocks, status.RecentBlocks)

	return copied
}

// analysisLoop 分析循环
func (e *Engine) analysisLoop() {
	defer e.wg.Done()

	ticker := time.NewTicker(e.config.AnalysisInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			e.performAnalysis()
		case <-e.stopCh:
			return
		}
	}
}

// performAnalysis 执行分析
func (e *Engine) performAnalysis() {
	e.mu.Lock()
	defer e.mu.Unlock()

	now := time.Now()
	cleanupWindow := now.Add(-e.config.BlockWindowSize * 2)

	// 清理过期的封禁记录
	for _, status := range e.serverStatus {
		var validBlocks []BlockEvent
		for _, block := range status.RecentBlocks {
			if block.Timestamp.After(cleanupWindow) {
				validBlocks = append(validBlocks, block)
			}
		}
		status.RecentBlocks = validBlocks

		// 重新评估风险级别
		e.updateRiskLevel(status)
	}

	e.logger.Debug("反风控分析完成", "servers", len(e.serverStatus))
}

// Close 关闭引擎
func (e *Engine) Close() error {
	close(e.stopCh)
	e.wg.Wait()
	e.logger.Info("反风控引擎已关闭")
	return nil
}
