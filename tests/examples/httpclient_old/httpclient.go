// Package httpclient 提供HTTP客户端功能，专注于纯传输层实现
package httpclient

// 重新导出核心包，保持向后兼容性
import (
	"context"
	"crypto/tls"
	"fmt"
	"go-monitor/pkg/httpclient/core"
	"go-monitor/pkg/httpclient/fingerprint"
	"go-monitor/pkg/httpclient/monitor"
	"go-monitor/pkg/logging"
	"net/http"
)

// 初始化指纹管理器桥接
func init() {
	// 设置指纹管理器获取函数，避免循环依赖
	core.SetFingerprintManagerGetter(func() core.FingerprintManager {
		manager := fingerprint.GetGlobalManager()
		if manager == nil {
			return nil
		}
		return &fingerprintManagerAdapter{manager: manager}
	})
}

// fingerprintManagerAdapter 指纹管理器适配器
type fingerprintManagerAdapter struct {
	manager *fingerprint.Manager
}

// SelectFingerprint 选择指纹
func (a *fingerprintManagerAdapter) SelectFingerprint(ctx context.Context, req *core.FingerprintRequest) (interface{}, error) {
	// 转换请求格式
	fingerprintReq := &fingerprint.FingerprintRequest{
		ServerName:      req.ServerName,
		FingerprintName: req.FingerprintName,
	}
	return a.manager.SelectFingerprint(ctx, fingerprintReq)
}

// CreateTLSConfig 创建TLS配置
func (a *fingerprintManagerAdapter) CreateTLSConfig(profile interface{}, serverName string) (*tls.Config, error) {
	// 假设profile是*fingerprint.Profile类型
	if fingerprintProfile, ok := profile.(*fingerprint.Profile); ok {
		return a.manager.CreateTLSConfig(fingerprintProfile, serverName)
	}
	return nil, fmt.Errorf("invalid profile type")
}

// 重新导出核心类型，保持向后兼容性
type (
	// 配置相关
	Config        = core.Config
	RequestConfig = core.RequestConfig
	HTTPVersion   = core.HTTPVersion

	// 请求/响应相关
	Request  = core.Request
	Response = core.Response

	// 客户端相关
	Client = core.Client

	// 监控相关
	ConnectionStats = monitor.ConnectionStats

	// 爬虫相关（在spider.go中定义）
	// SpiderConfig 和 SpiderRequestBuilder 直接在当前包中定义，无需重新导出
)

// 重新导出常量
const (
	HTTPVersionAuto           = core.HTTPVersionAuto
	HTTPVersionHTTP1Only      = core.HTTPVersionHTTP1Only
	HTTPVersionHTTP2Preferred = core.HTTPVersionHTTP2Preferred
	HTTPVersionHTTP2Only      = core.HTTPVersionHTTP2Only
)

// 重新导出核心函数，保持向后兼容性

// NewClient 创建HTTP客户端配置管理器
func NewClient(config *Config) *Client {
	return core.NewClient(config)
}

// NewClientWithFingerprint 创建启用TLS指纹的HTTP客户端
func NewClientWithFingerprint(config *Config) *Client {
	return core.NewClientWithFingerprint(config)
}

// DoWithClient 使用指定的HTTP Client执行请求
func DoWithClient(ctx context.Context, httpClient *http.Client, request *Request, logger logging.Logger) (*Response, error) {
	return core.DoWithClient(ctx, httpClient, request, logger)
}

// CreateClientForRequest 为请求创建独立的HTTP Client（保持向后兼容性）
func CreateClientForRequest(baseConfig *Config, requestConfig *RequestConfig) (*http.Client, error) {
	return core.CreateClientForRequest(baseConfig, requestConfig, monitor.GetConnectionMonitor())
}

// CreateClientForRequestWithURL 为请求创建独立的HTTP Client（带URL信息用于指纹选择）
func CreateClientForRequestWithURL(baseConfig *Config, requestConfig *RequestConfig, requestURL string) (*http.Client, error) {
	return core.CreateClientForRequestWithURL(baseConfig, requestConfig, requestURL, monitor.GetConnectionMonitor())
}

// CreateClientForRequestWithMeta 为请求创建独立的HTTP Client（支持元数据传递）
func CreateClientForRequestWithMeta(baseConfig *Config, requestConfig *RequestConfig, request *Request) (*http.Client, error) {
	return core.CreateClientForRequestWithMeta(baseConfig, requestConfig, request, monitor.GetConnectionMonitor())
}

// BuildURL 构建URL（兼容性函数）
func BuildURL(baseURL, path string, params map[string]string) string {
	return core.BuildURL(baseURL, path, params)
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *Config {
	return core.GetDefaultConfig()
}

// GetConnectionMonitor 获取全局连接监控器
func GetConnectionMonitor() *monitor.ConnectionMonitor {
	return monitor.GetConnectionMonitor()
}

// GetNetworkIOMonitor 获取网络I/O监控器
func GetNetworkIOMonitor() *monitor.NetworkIOMonitor {
	return monitor.GetNetworkIOMonitor()
}

// EmergencyCleanup 紧急清理 - 解决深层网络阻塞
func EmergencyCleanup() {
	monitor.EmergencyCleanup()
}

// GetFingerprintManager 获取指纹管理器
func GetFingerprintManager() *fingerprint.Manager {
	return fingerprint.GetGlobalManager()
}

// GetTLSFingerprintStats 获取指纹统计信息
func GetTLSFingerprintStats() map[string]interface{} {
	manager := fingerprint.GetGlobalManager()
	if manager != nil {
		return manager.GetStats()
	}
	return map[string]interface{}{
		"error": "TLS fingerprint manager not initialized",
	}
}

// ForceRefreshTLSFingerprintsForDomain 为特定域名强制刷新TLS指纹池
func ForceRefreshTLSFingerprintsForDomain(domain string) error {
	manager := fingerprint.GetGlobalManager()
	if manager == nil {
		return fmt.Errorf("TLS指纹管理器未初始化")
	}
	// TODO: 实现ForceRefreshFingerprintsForDomain方法
	return fmt.Errorf("ForceRefreshFingerprintsForDomain方法暂未实现")
}

// ShutdownHTTPClient 优雅关闭HTTP客户端相关资源
func ShutdownHTTPClient() {
	logger := logging.GetLogger("httpclient.shutdown")
	logger.Info("开始关闭HTTP客户端")

	// 关闭指纹管理器
	manager := fingerprint.GetGlobalManager()
	if manager != nil {
		if err := manager.Close(); err != nil {
			logger.Error("关闭指纹管理器失败", "error", err.Error())
		} else {
			logger.Debug("指纹管理器已关闭")
		}
	}

	// 关闭监控器
	monitor.ShutdownConnectionMonitor()
	monitor.ShutdownNetworkMonitor()

	logger.Info("HTTP客户端已关闭")
}
