package httpclient

import (
	"encoding/json"
)

// SpiderConfig 爬虫配置接口
type SpiderConfig interface {
	GetPlatform() string
	GetName() string
	GetCountry() string
	GetSpiderSettings() map[string]interface{}
}

// SpiderRequestBuilder 爬虫请求构建器
type SpiderRequestBuilder struct {
	platform string
	baseURL  string
	method   string
	headers  map[string]string
	params   map[string]string
	metadata map[string]interface{}
	body     []byte
}

// NewSpiderRequestBuilder 创建爬虫请求构建器
func NewSpiderRequestBuilder(platform, baseURL string) *SpiderRequestBuilder {
	return &SpiderRequestBuilder{
		platform: platform,
		baseURL:  baseURL,
		method:   "GET",
		headers:  make(map[string]string),
		params:   make(map[string]string),
		metadata: make(map[string]interface{}),
	}
}

// SetMethod 设置HTTP方法
func (b *SpiderRequestBuilder) SetMethod(method string) *SpiderRequestBuilder {
	b.method = method
	return b
}

// AddHeader 添加请求头
func (b *SpiderRequestBuilder) AddHeader(key, value string) *SpiderRequestBuilder {
	b.headers[key] = value
	return b
}

// AddHeaders 批量添加请求头
func (b *SpiderRequestBuilder) AddHeaders(headers map[string]string) *SpiderRequestBuilder {
	for k, v := range headers {
		b.headers[k] = v
	}
	return b
}

// AddParam 添加查询参数
func (b *SpiderRequestBuilder) AddParam(key, value string) *SpiderRequestBuilder {
	b.params[key] = value
	return b
}

// AddParams 批量添加查询参数
func (b *SpiderRequestBuilder) AddParams(params map[string]string) *SpiderRequestBuilder {
	for k, v := range params {
		b.params[k] = v
	}
	return b
}

// SetBody 设置请求体
func (b *SpiderRequestBuilder) SetBody(body []byte) *SpiderRequestBuilder {
	b.body = body
	return b
}

// SetJSONBody 设置JSON请求体
func (b *SpiderRequestBuilder) SetJSONBody(data interface{}) *SpiderRequestBuilder {
	if data == nil {
		return b
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		b.metadata["json_marshal_error"] = err.Error()
		return b
	}

	b.body = jsonData
	b.headers["Content-Type"] = "application/json; charset=utf-8"
	return b
}

// SetMetadata 设置元数据
func (b *SpiderRequestBuilder) SetMetadata(key string, value interface{}) *SpiderRequestBuilder {
	b.metadata[key] = value
	return b
}

// SetSpiderMetadata 设置爬虫标准元数据
func (b *SpiderRequestBuilder) SetSpiderMetadata(config SpiderConfig, productID string) *SpiderRequestBuilder {
	// 使用构建器的平台参数，而不是配置中的平台
	b.metadata["spider_type"] = b.platform

	if config != nil {
		b.metadata["name"] = config.GetName()
		b.metadata["country"] = config.GetCountry()

		if settings := config.GetSpiderSettings(); settings != nil {
			if proxyGroup, ok := settings["proxies"].([]interface{}); ok {
				b.metadata["proxies"] = proxyGroup
			}
			if cookieGroup, ok := settings["cookies"].([]interface{}); ok {
				b.metadata["cookies"] = cookieGroup
			}
			if fingerprint, ok := settings["fingerprint"].(string); ok {
				b.metadata["fingerprint"] = fingerprint
			}
		}
	}

	if productID != "" {
		b.metadata["product_id"] = productID
	}

	return b
}

// SetCommonHeaders 设置常用的爬虫请求头
func (b *SpiderRequestBuilder) SetCommonHeaders() *SpiderRequestBuilder {
	b.headers["accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
	b.headers["accept-language"] = "en-US,en;q=0.5"
	b.headers["accept-encoding"] = "gzip, deflate"
	b.headers["connection"] = "keep-alive"
	b.headers["upgrade-Iinsecure-Requests"] = "1"
	b.headers["sec-fetch-dest"] = "document"
	b.headers["sec-fetch-mode"] = "navigate"
	b.headers["sec-fetch-site"] = "none"
	b.headers["sec-fetch-user"] = "?1"
	return b
}

// SetAPIHeaders 设置API请求头
func (b *SpiderRequestBuilder) SetAPIHeaders() *SpiderRequestBuilder {
	b.headers["accept"] = "application/json, text/plain, */*"
	b.headers["content-type"] = "application/json"
	return b
}

// SetFormHeaders 设置表单请求头
func (b *SpiderRequestBuilder) SetFormHeaders() *SpiderRequestBuilder {
	b.headers["content-type"] = "application/x-www-form-urlencoded; charset=UTF-8"
	return b
}

// Build 构建请求对象
func (b *SpiderRequestBuilder) Build() *Request {
	return &Request{
		URL:     b.baseURL,
		Method:  b.method,
		Headers: b.headers,
		Params:  b.params,
		Body:    b.body,
		Meta:    b.metadata,
	}
}

// 便捷的爬虫请求创建函数

// CreateAmazonRequest 创建Amazon爬虫请求
func CreateAmazonRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("amazon", baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}

// CreateAliExpressRequest 创建AliExpress爬虫请求
func CreateAliExpressRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("aliexpress", baseURL).
		SetMethod("POST").
		SetFormHeaders().
		SetSpiderMetadata(config, productID)
}

// CreatePopMartRequest 创建PopMart爬虫请求
func CreatePopMartRequest(baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("popmart", baseURL).
		SetAPIHeaders().
		SetSpiderMetadata(config, productID)
}

// CreateGenericSpiderRequest 创建通用爬虫请求
func CreateGenericSpiderRequest(platform, baseURL string, config SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder(platform, baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}
