package core

import (
	"crypto/tls"
	"time"
)

// Config HTTP客户端配置
type Config struct {
	Timeout     time.Duration
	Retries     int
	UserAgent   string
	ProxyURL    string
	MaxIdleConn int

	// TLS指纹配置
	EnableTLSFingerprint bool                   // 全局启用指纹
	TLSFingerprintConfig map[string]interface{} // 指纹详细配置
}

// HTTPVersion HTTP协议版本控制
type HTTPVersion int

const (
	HTTPVersionAuto           HTTPVersion = iota // 自动协商（默认）
	HTTPVersionHTTP1Only                         // 仅HTTP/1.1
	HTTPVersionHTTP2Preferred                    // 优先HTTP/2
	HTTPVersionHTTP2Only                         // 仅HTTP/2
)

// String 返回HTTP版本的字符串表示
func (v HTTPVersion) String() string {
	switch v {
	case HTTPVersionAuto:
		return "auto"
	case HTTPVersionHTTP1Only:
		return "http1-only"
	case HTTPVersionHTTP2Preferred:
		return "http2-preferred"
	case HTTPVersionHTTP2Only:
		return "http2-only"
	default:
		return "unknown"
	}
}

// RequestConfig 请求级配置
type RequestConfig struct {
	ProxyURL       string
	Timeout        time.Duration
	UserAgent      string
	TLSConfig      *tls.Config
	DisableCookies bool

	// HTTP协议版本控制
	HTTPVersion HTTPVersion // HTTP协议版本控制（统一参数）

	// TLS指纹相关配置
	EnableTLSFingerprint bool     // 是否启用TLS指纹
	ExcludeTLSProfiles   []string // 排除的指纹列表
	ForceTLSProfile      string   // 强制使用特定指纹
}

// ResolveHTTPVersion 解析HTTP版本配置
func (rc *RequestConfig) ResolveHTTPVersion() HTTPVersion {
	if rc == nil {
		return HTTPVersionAuto
	}

	// 直接返回设置的HTTP版本，默认为自动协商
	return rc.HTTPVersion
}

// ShouldDisableHTTP2 判断是否应该禁用HTTP/2
func (rc *RequestConfig) ShouldDisableHTTP2() bool {
	version := rc.ResolveHTTPVersion()
	return version == HTTPVersionHTTP1Only
}

// ShouldForceHTTP2 判断是否应该强制启用HTTP/2
func (rc *RequestConfig) ShouldForceHTTP2() bool {
	version := rc.ResolveHTTPVersion()
	return version == HTTPVersionHTTP2Only || version == HTTPVersionHTTP2Preferred
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *Config {
	return &Config{
		Timeout:              30 * time.Second,
		Retries:              3,
		UserAgent:            "Go-Monitor/1.0",
		MaxIdleConn:          100,
		EnableTLSFingerprint: false, // 默认禁用指纹功能
		TLSFingerprintConfig: nil,
	}
}
