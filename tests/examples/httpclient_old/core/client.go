package core

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"go-monitor/pkg/httpclient/compression"
	"go-monitor/pkg/httpclient/monitor"
	"go-monitor/pkg/logging"
)

// Client HTTP客户端 - 重构为配置管理器
type Client struct {
	baseConfig *Config
	logger     logging.Logger
	monitor    *monitor.ConnectionMonitor
}

// NewClient 创建HTTP客户端配置管理器
func NewClient(config *Config) *Client {
	if config == nil {
		config = GetDefaultConfig()
	}

	return &Client{
		baseConfig: config,
		logger:     logging.GetLogger("httpclient"),
		monitor:    monitor.GetConnectionMonitor(),
	}
}

// Get 发起GET请求
func (c *Client) Get(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	req := &Request{
		URL:     url,
		Method:  "GET",
		Headers: headers,
	}
	return c.Do(ctx, req)
}

// Post 发起POST请求
func (c *Client) Post(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error) {
	req := &Request{
		URL:     url,
		Method:  "POST",
		Headers: headers,
		Body:    body,
	}
	return c.Do(ctx, req)
}

// Do 执行HTTP请求 - 使用独立Client
func (c *Client) Do(ctx context.Context, request *Request) (*Response, error) {
	var lastErr error

	for attempt := 0; attempt <= c.baseConfig.Retries; attempt++ {
		if attempt > 0 {
			c.logger.Debug("重试HTTP请求", "url", request.URL, "attempt", attempt)

			// 重试延迟
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Duration(attempt) * time.Second):
			}
		}

		resp, err := c.doRequest(ctx, request)
		if err == nil {
			return resp, nil
		}

		lastErr = err
		c.logger.Warn("HTTP请求失败", "url", request.URL, "attempt", attempt, "error", err.Error())
	}

	return nil, fmt.Errorf("HTTP请求失败，已重试%d次: %w", c.baseConfig.Retries, lastErr)
}

// doRequest 执行单次HTTP请求
func (c *Client) doRequest(ctx context.Context, request *Request) (*Response, error) {
	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 创建独立的HTTP Client
	httpClient, err := CreateClientForRequestWithMeta(c.baseConfig, nil, request, c.monitor)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP客户端失败: %w", err)
	}
	defer httpClient.CloseIdleConnections()

	return DoWithClient(ctx, httpClient, request, c.logger)
}

// DoWithClient 使用指定的HTTP Client执行请求
func DoWithClient(ctx context.Context, httpClient *http.Client, request *Request, logger logging.Logger) (*Response, error) {
	startTime := time.Now()

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 构建完整的请求URL
	fullURL := BuildRequestURL(request)

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, request.Method, fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求体
	if len(request.Body) > 0 {
		httpReq.Body = io.NopCloser(bytes.NewReader(request.Body))
		httpReq.ContentLength = int64(len(request.Body))
		httpReq.GetBody = func() (io.ReadCloser, error) {
			return io.NopCloser(bytes.NewReader(request.Body)), nil
		}
	}

	// 设置请求头
	if request.Headers != nil {
		for key, value := range request.Headers {
			httpReq.Header.Set(key, value)
		}
	}

	// 设置Cookies
	if request.Cookies != nil {
		for name, value := range request.Cookies {
			httpReq.AddCookie(&http.Cookie{Name: name, Value: value})
		}
	}

	// 执行HTTP请求
	httpResp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("执行HTTP请求失败: %w", err)
	}
	defer httpResp.Body.Close()

	// 解码响应体（处理压缩）
	body, err := compression.DecodeResponse(httpResp, logger)
	if err != nil {
		return nil, fmt.Errorf("解码响应体失败: %w", err)
	}

	// 构建响应对象
	response := &Response{
		StatusCode:   httpResp.StatusCode,
		Headers:      httpResp.Header,
		Body:         body,
		URL:          fullURL,
		ResponseTime: time.Since(startTime),
		Request:      request,
	}

	if logger != nil {
		logger.Debug("HTTP请求完成",
			"url", fullURL,
			"status", httpResp.StatusCode,
			"response_time", response.ResponseTime,
			"body_size", len(body))
	}

	return response, nil
}

// NewClientWithFingerprint 创建启用TLS指纹的HTTP客户端
func NewClientWithFingerprint(config *Config) *Client {
	if config == nil {
		config = GetDefaultConfig()
	}

	// 启用指纹功能
	config.EnableTLSFingerprint = true

	return &Client{
		baseConfig: config,
		logger:     logging.GetLogger("httpclient.fingerprint"),
		monitor:    monitor.GetConnectionMonitor(),
	}
}

// DoWithFingerprint 使用TLS指纹执行HTTP请求
func (c *Client) DoWithFingerprint(ctx context.Context, request *Request, excludeProfiles []string) (*Response, error) {
	// 在请求元数据中设置排除的指纹列表
	if request.Meta == nil {
		request.Meta = make(map[string]interface{})
	}
	if len(excludeProfiles) > 0 {
		request.Meta["exclude_tls_profiles"] = excludeProfiles
	}

	return c.Do(ctx, request)
}

// DoWithSpecificFingerprint 使用特定TLS指纹执行HTTP请求
func (c *Client) DoWithSpecificFingerprint(ctx context.Context, request *Request, profileName string) (*Response, error) {
	// 在请求元数据中设置强制使用的指纹
	if request.Meta == nil {
		request.Meta = make(map[string]interface{})
	}
	request.Meta["force_tls_profile"] = profileName

	return c.Do(ctx, request)
}
