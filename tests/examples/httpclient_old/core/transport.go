package core

import (
	"context"
	"crypto/tls"
	"fmt"
	"go-monitor/pkg/logging"
	"net"
	"net/http"
	"net/url"
	"sync"
	"sync/atomic"
	"time"
)

var (
	// 全局连接管理器
	globalConnectionManager *ConnectionManager
	connectionManagerOnce   sync.Once
)

// GetGlobalConnectionManager 获取全局连接管理器（单例模式）
func GetGlobalConnectionManager() *ConnectionManager {
	connectionManagerOnce.Do(func() {
		globalConnectionManager = NewConnectionManager(
			200,             // 最大连接数
			30*time.Second,  // 清理间隔（30秒）
			120*time.Second, // 连接超时（2分钟）
		)
	})
	return globalConnectionManager
}

// ManagedHTTPClient 管理HTTP客户端生命周期的包装器
type ManagedHTTPClient struct {
	*http.Client
	transport     *http.Transport
	transportName string
	monitor       ConnectionMonitor
	connManager   *ConnectionManager
}

// ConnectionMonitor 连接监控器接口
type ConnectionMonitor interface {
	RegisterTransport(transport *http.Transport, name string)
	UnregisterTransport(transport *http.Transport)
}

// ConnectionManager 连接生命周期管理器
type ConnectionManager struct {
	// 连接跟踪
	activeConnections sync.Map // map[*http.Transport]*ConnectionInfo
	connectionCount   int64    // 原子计数器

	// 配置
	maxConnections    int
	cleanupInterval   time.Duration
	connectionTimeout time.Duration

	// 控制
	stopCh chan struct{}
	logger logging.Logger
	mu     sync.RWMutex
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	Transport    *http.Transport
	CreatedAt    time.Time
	LastUsed     time.Time
	RequestCount int64
	Name         string
	Context      context.Context
	Cancel       context.CancelFunc
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(maxConnections int, cleanupInterval, connectionTimeout time.Duration) *ConnectionManager {
	cm := &ConnectionManager{
		maxConnections:    maxConnections,
		cleanupInterval:   cleanupInterval,
		connectionTimeout: connectionTimeout,
		stopCh:            make(chan struct{}),
		logger:            logging.GetLogger("httpclient.connection_manager"),
	}

	// 启动清理goroutine
	go cm.cleanupLoop()

	return cm
}

// RegisterConnection 注册新连接
func (cm *ConnectionManager) RegisterConnection(transport *http.Transport, name string, ctx context.Context) {
	if ctx == nil {
		ctx = context.Background()
	}

	connCtx, cancel := context.WithTimeout(ctx, cm.connectionTimeout)

	info := &ConnectionInfo{
		Transport:    transport,
		CreatedAt:    time.Now(),
		LastUsed:     time.Now(),
		RequestCount: 0,
		Name:         name,
		Context:      connCtx,
		Cancel:       cancel,
	}

	cm.activeConnections.Store(transport, info)
	atomic.AddInt64(&cm.connectionCount, 1)

	cm.logger.Debug("注册新连接", "name", name, "total_connections", atomic.LoadInt64(&cm.connectionCount))
}

// UnregisterConnection 注销连接
func (cm *ConnectionManager) UnregisterConnection(transport *http.Transport) {
	if value, ok := cm.activeConnections.LoadAndDelete(transport); ok {
		info := value.(*ConnectionInfo)
		info.Cancel()                    // 取消context
		transport.CloseIdleConnections() // 强制关闭空闲连接

		atomic.AddInt64(&cm.connectionCount, -1)
		cm.logger.Debug("注销连接", "name", info.Name, "total_connections", atomic.LoadInt64(&cm.connectionCount))
	}
}

// UpdateLastUsed 更新连接最后使用时间
func (cm *ConnectionManager) UpdateLastUsed(transport *http.Transport) {
	if value, ok := cm.activeConnections.Load(transport); ok {
		info := value.(*ConnectionInfo)
		info.LastUsed = time.Now()
		atomic.AddInt64(&info.RequestCount, 1)
	}
}

// cleanupLoop 清理循环 - 定期清理超时和僵尸连接
func (cm *ConnectionManager) cleanupLoop() {
	ticker := time.NewTicker(cm.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cm.performCleanup()
		case <-cm.stopCh:
			cm.logger.Info("连接管理器清理循环停止")
			return
		}
	}
}

// performCleanup 执行清理操作
func (cm *ConnectionManager) performCleanup() {
	now := time.Now()
	var toRemove []*http.Transport
	cleanupCount := 0
	totalConnections := 0

	cm.logger.Debug("开始连接清理检查")

	// 遍历所有连接，找出需要清理的
	cm.activeConnections.Range(func(key, value interface{}) bool {
		transport := key.(*http.Transport)
		info := value.(*ConnectionInfo)
		totalConnections++

		idleDuration := now.Sub(info.LastUsed)
		shouldRemove := false

		// 检查连接是否超时 (5分钟空闲才清理，给重试请求足够时间)
		if idleDuration > 5*time.Minute {
			shouldRemove = true
		}

		// 检查context是否已取消
		select {
		case <-info.Context.Done():
			shouldRemove = true
		default:
		}

		// 强制清理过多连接（超过最大连接数的5倍且空闲超过3分钟）
		if totalConnections > cm.maxConnections*5 && idleDuration > 3*time.Minute {
			shouldRemove = true
		}

		// 智能清理：如果连接很久没有请求活动，即使在正常范围内也清理
		if idleDuration > 10*time.Minute && atomic.LoadInt64(&info.RequestCount) == 0 {
			shouldRemove = true
		}

		if shouldRemove {
			toRemove = append(toRemove, transport)
			cleanupCount++
		}

		return true
	})

	// 清理标记的连接
	for _, transport := range toRemove {
		cm.UnregisterConnection(transport)
	}

	// 静默清理，只在异常情况下记录日志
	if totalConnections > cm.maxConnections*10 {
		cm.logger.Warn("连接数严重超标",
			"current", totalConnections,
			"max", cm.maxConnections,
			"cleaned", cleanupCount)
	}
}

// ForceCleanupAll 强制清理所有连接
func (cm *ConnectionManager) ForceCleanupAll() {
	cm.logger.Warn("强制清理所有连接")

	var allTransports []*http.Transport
	cm.activeConnections.Range(func(key, value interface{}) bool {
		allTransports = append(allTransports, key.(*http.Transport))
		return true
	})

	for _, transport := range allTransports {
		cm.UnregisterConnection(transport)
	}

	cm.logger.Info("强制清理完成", "cleaned_connections", len(allTransports))
}

// Stop 停止连接管理器
func (cm *ConnectionManager) Stop() {
	close(cm.stopCh)
	cm.ForceCleanupAll()
}

// GetStats 获取连接统计信息
func (cm *ConnectionManager) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"total_connections":  atomic.LoadInt64(&cm.connectionCount),
		"max_connections":    cm.maxConnections,
		"cleanup_interval":   cm.cleanupInterval.String(),
		"connection_timeout": cm.connectionTimeout.String(),
	}

	// 统计各种状态的连接
	var activeCount, timeoutCount int64
	now := time.Now()

	cm.activeConnections.Range(func(key, value interface{}) bool {
		info := value.(*ConnectionInfo)
		// 使用5分钟作为超时判断标准（与清理逻辑一致）
		if now.Sub(info.LastUsed) > 5*time.Minute {
			timeoutCount++
		} else {
			activeCount++
		}
		return true
	})

	stats["active_connections"] = activeCount
	stats["timeout_connections"] = timeoutCount

	return stats
}

// NewManagedHTTPClient 创建管理的HTTP客户端
func NewManagedHTTPClient(client *http.Client, transport *http.Transport, transportName string, monitor ConnectionMonitor) *ManagedHTTPClient {
	return &ManagedHTTPClient{
		Client:        client,
		transport:     transport,
		transportName: transportName,
		monitor:       monitor,
		connManager:   GetGlobalConnectionManager(),
	}
}

// Close 关闭管理的HTTP客户端并清理资源
func (m *ManagedHTTPClient) Close() {
	if m.transport != nil {
		m.transport.CloseIdleConnections()
		if m.monitor != nil {
			m.monitor.UnregisterTransport(m.transport)
		}
		// 从连接管理器中注销
		if m.connManager != nil {
			m.connManager.UnregisterConnection(m.transport)
		}
	}
}

// Do 执行HTTP请求并更新连接使用统计
func (m *ManagedHTTPClient) Do(req *http.Request) (*http.Response, error) {
	// 执行请求
	resp, err := m.Client.Do(req)

	// 更新连接使用时间（无论成功还是失败）
	if m.connManager != nil && m.transport != nil {
		m.connManager.UpdateLastUsed(m.transport)
	}

	return resp, err
}

// CreateClientForRequestWithURL 为请求创建独立的HTTP Client（带URL信息用于指纹选择）
func CreateClientForRequestWithURL(baseConfig *Config, requestConfig *RequestConfig, requestURL string, monitor ConnectionMonitor) (*http.Client, error) {
	return createClientForRequestInternal(baseConfig, requestConfig, requestURL, nil, monitor)
}

// CreateClientForRequest 为请求创建独立的HTTP Client（保持向后兼容性）
func CreateClientForRequest(baseConfig *Config, requestConfig *RequestConfig, monitor ConnectionMonitor) (*http.Client, error) {
	return createClientForRequestInternal(baseConfig, requestConfig, "", nil, monitor)
}

// CreateClientForRequestWithMeta 为请求创建独立的HTTP Client（支持元数据传递）
func CreateClientForRequestWithMeta(baseConfig *Config, requestConfig *RequestConfig, request *Request, monitor ConnectionMonitor) (*http.Client, error) {
	requestURL := ""
	if request != nil {
		requestURL = request.URL
	}
	return createClientForRequestInternal(baseConfig, requestConfig, requestURL, request, monitor)
}

// createClientForRequestInternal 内部实现函数
func createClientForRequestInternal(baseConfig *Config, requestConfig *RequestConfig, requestURL string, request *Request, monitor ConnectionMonitor) (*http.Client, error) {
	if baseConfig == nil {
		baseConfig = GetDefaultConfig()
	}

	// 创建优化的HTTP传输配置 - 解决深层网络阻塞问题
	transport := &http.Transport{
		// 连接池优化 - 解决连接建立阻塞
		MaxIdleConns:        baseConfig.MaxIdleConn, // 全局最大空闲连接
		MaxIdleConnsPerHost: 50,                     // 增加每个主机的空闲连接数，减少新连接建立
		MaxConnsPerHost:     100,                    // 限制每个主机的最大连接数，防止连接泄漏

		// 超时配置 - 平衡性能和稳定性
		IdleConnTimeout:       90 * time.Second, // 适中的空闲连接超时
		TLSHandshakeTimeout:   10 * time.Second, // 适中的TLS握手超时
		ExpectContinueTimeout: 1 * time.Second,  // Expect: 100-continue超时
		ResponseHeaderTimeout: 15 * time.Second, // 适中的响应头超时

		// 连接管理
		DisableKeepAlives:  false, // 启用Keep-Alive，重要！
		DisableCompression: false, // 启用压缩
		ForceAttemptHTTP2:  false, // 不强制HTTP/2，避免协议协商问题

		// 网络层优化 - 增强连接管理和超时控制
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			// 创建带超时的context
			dialCtx, cancel := context.WithTimeout(ctx, 5*time.Second) // 更短的连接建立超时
			defer cancel()

			dialer := &net.Dialer{
				Timeout:   5 * time.Second,  // 连接建立超时
				KeepAlive: 30 * time.Second, // Keep-Alive间隔
				DualStack: true,             // 启用IPv4/IPv6双栈
			}

			conn, err := dialer.DialContext(dialCtx, network, addr)
			if err != nil {
				// 连接失败时记录日志
				logger := logging.GetLogger("httpclient.dial")
				logger.Debug("连接建立失败", "network", network, "addr", addr, "error", err.Error())
				return nil, err
			}

			// 连接成功时记录日志
			logger := logging.GetLogger("httpclient.dial")
			logger.Debug("连接建立成功", "network", network, "addr", addr, "local", conn.LocalAddr().String())

			return conn, nil
		},

		// 添加连接状态监控
		WriteBufferSize: 32 * 1024, // 32KB写缓冲区
		ReadBufferSize:  32 * 1024, // 32KB读缓冲区
	}

	// 根据请求配置控制HTTP协议版本
	if requestConfig != nil {
		httpVersion := requestConfig.ResolveHTTPVersion()

		switch httpVersion {
		case HTTPVersionHTTP1Only:
			// 完全禁用HTTP/2协议协商 - 强制使用HTTP/1.1
			transport.TLSNextProto = make(map[string]func(authority string, c *tls.Conn) http.RoundTripper)
			transport.ForceAttemptHTTP2 = false

		case HTTPVersionHTTP2Only, HTTPVersionHTTP2Preferred:
			// 强制启用HTTP/2
			transport.ForceAttemptHTTP2 = true
			// 不设置TLSNextProto，允许HTTP/2协商

		case HTTPVersionAuto:
			// 默认行为：允许自动协商
			// Go默认会尝试HTTP/2，如果失败则降级到HTTP/1.1
		}
	}

	// 应用请求级配置
	if requestConfig != nil {
		// 设置代理
		if requestConfig.ProxyURL != "" {
			proxyURL, err := url.Parse(requestConfig.ProxyURL)
			if err != nil {
				return nil, fmt.Errorf("解析代理URL失败: %w", err)
			}
			transport.Proxy = http.ProxyURL(proxyURL)
		}

		// 设置TLS配置 - 集成指纹功能（安全模式）
		if requestConfig.TLSConfig != nil {
			transport.TLSClientConfig = requestConfig.TLSConfig
		} else {
			// 尝试使用TLS指纹功能（带超时保护）
			serverName := extractServerNameFromURL(requestURL)

			// 使用通道进行超时控制
			configDone := make(chan error, 1)
			go func() {
				defer func() {
					if r := recover(); r != nil {
						configDone <- fmt.Errorf("TLS指纹配置panic: %v", r)
					}
				}()
				configDone <- configureTLSWithFingerprint(transport, baseConfig, requestConfig, serverName, request)
			}()

			select {
			case err := <-configDone:
				if err != nil {
					// 如果指纹配置失败，使用安全的默认TLS配置
					transport.TLSClientConfig = &tls.Config{
						InsecureSkipVerify: false,
						MinVersion:         tls.VersionTLS12, // 强制最小TLS版本
						MaxVersion:         tls.VersionTLS13, // 限制最大TLS版本
					}
				}
			case <-time.After(5 * time.Second):
				// 指纹配置超时，使用默认配置
				transport.TLSClientConfig = &tls.Config{
					InsecureSkipVerify: false,
					MinVersion:         tls.VersionTLS12,
					MaxVersion:         tls.VersionTLS13,
				}
			}
		}
	} else {
		// 没有请求配置时，尝试使用全局指纹配置
		serverName := extractServerNameFromURL(requestURL)
		if err := configureTLSWithFingerprint(transport, baseConfig, nil, serverName, request); err != nil {
			// 检查是否明确设置了fingerprint字段
			hasFingerprint := false
			if request != nil && request.Meta != nil {
				if _, ok := request.Meta["fingerprint"].(string); ok {
					hasFingerprint = true
				}
			}

			if hasFingerprint {
				// 如果明确设置了fingerprint但失败，返回错误而不是回退
				return nil, fmt.Errorf("TLS指纹配置失败: %w", err)
			} else {
				// 如果没有设置fingerprint，使用默认TLS配置
				transport.TLSClientConfig = &tls.Config{
					InsecureSkipVerify: false,
				}
			}
		}
	}

	// 如果没有请求级配置但有基础代理配置
	if requestConfig == nil || requestConfig.ProxyURL == "" {
		if baseConfig.ProxyURL != "" {
			if proxyURL, err := url.Parse(baseConfig.ProxyURL); err == nil {
				transport.Proxy = http.ProxyURL(proxyURL)
			}
		}
	}

	// 确定超时时间 - 使用更保守的超时设置
	timeout := baseConfig.Timeout
	if requestConfig != nil && requestConfig.Timeout > 0 {
		timeout = requestConfig.Timeout
	}

	// 确保超时时间不会太长，避免长时间阻塞
	maxTimeout := 60 * time.Second
	if timeout > maxTimeout {
		timeout = maxTimeout
	}

	// 确保最小超时时间，避免过于激进的超时
	minTimeout := 5 * time.Second
	if timeout < minTimeout {
		timeout = minTimeout
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}

	// 注册传输器到连接监控器
	if monitor != nil {
		transportName := fmt.Sprintf("transport_%p", transport)
		monitor.RegisterTransport(transport, transportName)
	}

	// 注册到全局连接管理器
	cm := GetGlobalConnectionManager()
	transportName := fmt.Sprintf("req_%p", transport)
	if requestConfig != nil && requestConfig.ProxyURL != "" {
		transportName = fmt.Sprintf("proxy_%p", transport)
	}
	cm.RegisterConnection(transport, transportName, context.Background())

	// 如果禁用cookies，则不设置cookie jar
	if requestConfig == nil || !requestConfig.DisableCookies {
		// 可以在这里设置cookie jar，但为了爬虫隔离性，暂时不设置
	}

	return httpClient, nil
}

// extractServerNameFromURL 从URL中提取服务器名称
func extractServerNameFromURL(requestURL string) string {
	if requestURL == "" {
		return ""
	}

	parsedURL, err := url.Parse(requestURL)
	if err != nil {
		return ""
	}

	return parsedURL.Host
}

// configureTLSWithFingerprint 配置TLS指纹
func configureTLSWithFingerprint(transport *http.Transport, baseConfig *Config, requestConfig *RequestConfig, serverName string, request *Request) error {
	// 如果明确禁用了TLS指纹，使用默认配置
	if baseConfig != nil && !baseConfig.EnableTLSFingerprint {
		transport.TLSClientConfig = &tls.Config{
			ServerName:         serverName,
			InsecureSkipVerify: false,
			MinVersion:         tls.VersionTLS12,
			MaxVersion:         tls.VersionTLS13,
		}
		return nil
	}

	// 尝试获取指纹管理器（通过包级函数避免循环依赖）
	manager := getGlobalFingerprintManager()
	if manager == nil {
		// 指纹管理器不可用，使用默认TLS配置
		transport.TLSClientConfig = &tls.Config{
			ServerName:         serverName,
			InsecureSkipVerify: false,
			MinVersion:         tls.VersionTLS12,
			MaxVersion:         tls.VersionTLS13,
		}
		return nil
	}

	// 构建指纹请求
	fingerprintReq := &FingerprintRequest{
		ServerName: serverName,
	}

	// 从请求元数据中获取指纹信息
	if request != nil && request.Meta != nil {
		// 支持指纹池名称（如 "popmart-us"）
		if poolName, ok := request.Meta["fingerprint"].(string); ok {
			fingerprintReq.PoolName = poolName
		}
		// 也支持直接指定指纹名称（向后兼容）
		if fingerprintName, ok := request.Meta["fingerprint_name"].(string); ok {
			fingerprintReq.FingerprintName = fingerprintName
		}
	}

	// 选择指纹
	ctx := context.Background()
	profile, err := manager.SelectFingerprint(ctx, fingerprintReq)
	if err != nil {
		// 指纹选择失败，使用默认TLS配置
		transport.TLSClientConfig = &tls.Config{
			ServerName:         serverName,
			InsecureSkipVerify: false,
			MinVersion:         tls.VersionTLS12,
			MaxVersion:         tls.VersionTLS13,
		}
		return nil
	}

	// 创建TLS配置
	tlsConfig, err := manager.CreateTLSConfig(profile, serverName)
	if err != nil {
		// TLS配置创建失败，使用默认配置
		transport.TLSClientConfig = &tls.Config{
			ServerName:         serverName,
			InsecureSkipVerify: false,
			MinVersion:         tls.VersionTLS12,
			MaxVersion:         tls.VersionTLS13,
		}
		return nil
	}

	transport.TLSClientConfig = tlsConfig
	return nil
}

// FingerprintRequest 指纹请求结构体（简化版本，避免循环依赖）
type FingerprintRequest struct {
	ServerName      string
	PoolName        string // 指纹池名称（如 "popmart-us"）
	FingerprintName string // 具体指纹名称（向后兼容）
}

// FingerprintManager 指纹管理器接口（简化版本，避免循环依赖）
type FingerprintManager interface {
	SelectFingerprint(ctx context.Context, req *FingerprintRequest) (interface{}, error)
	CreateTLSConfig(profile interface{}, serverName string) (*tls.Config, error)
}

// fingerprintManagerGetter 指纹管理器获取函数类型
var fingerprintManagerGetter func() FingerprintManager

// SetFingerprintManagerGetter 设置指纹管理器获取函数（由上层包调用）
func SetFingerprintManagerGetter(getter func() FingerprintManager) {
	fingerprintManagerGetter = getter
}

// getGlobalFingerprintManager 获取全局指纹管理器（避免循环依赖的桥接函数）
func getGlobalFingerprintManager() FingerprintManager {
	if fingerprintManagerGetter != nil {
		return fingerprintManagerGetter()
	}
	return nil
}
