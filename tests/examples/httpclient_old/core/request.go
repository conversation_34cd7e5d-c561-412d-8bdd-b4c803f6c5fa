package core

import (
	"net/url"
	"strings"
	"time"
)

// Request HTTP请求
type Request struct {
	URL          string // 基础请求URL（不含查询参数）
	Method       string
	Headers      map[string]string
	Body         []byte
	Params       map[string]string
	Cookies      map[string]string // Cookies
	ReverseProxy string            // 反向代理URL
	Proxy        string            // 代理地址
	Timeout      time.Duration

	// 元数据
	Meta map[string]interface{}
}

// Response HTTP响应
type Response struct {
	StatusCode   int
	Headers      map[string][]string
	Body         []byte
	URL          string
	ResponseTime time.Duration
	Request      *Request
}

// BuildRequestURL 构建完整的请求URL（考虑反向代理和查询参数）
func BuildRequestURL(request *Request) string {
	// 确定基础URL（考虑反向代理）
	baseURL := request.URL
	if request.ReverseProxy != "" {
		// 如果有反向代理，将原始URL进行URL编码后添加到反向代理URL
		encodedURL := url.QueryEscape(request.URL)
		baseURL = request.ReverseProxy + "/" + encodedURL
	}

	// 如果没有参数，直接返回基础URL
	if len(request.Params) == 0 {
		return baseURL
	}

	// 组装查询参数
	params := url.Values{}
	for k, v := range request.Params {
		params.Add(k, v)
	}

	// 添加参数到URL
	if strings.Contains(baseURL, "?") {
		return baseURL + "&" + params.Encode()
	} else {
		return baseURL + "?" + params.Encode()
	}
}

// RebuildURL 重新构建完整URL（用于重试）
// 注意：现在URL字段直接存储基础URL，参数通过BuildRequestURL动态组装
func (r *Request) RebuildURL() {
	// 不需要做任何事情，因为URL字段已经是基础URL
	// 参数会在BuildRequestURL中动态组装
}

// BuildURL 构建URL（兼容性函数）
func BuildURL(baseURL, path string, params map[string]string) string {
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL
	}

	u.Path = strings.TrimSuffix(u.Path, "/") + "/" + strings.TrimPrefix(path, "/")

	if len(params) > 0 {
		q := u.Query()
		for k, v := range params {
			q.Add(k, v)
		}
		u.RawQuery = q.Encode()
	}

	return u.String()
}

// ExtractCookiesFromResponse 从HTTP响应中提取cookies - 通用方法
func ExtractCookiesFromResponse(resp *Response) map[string]string {
	cookies := make(map[string]string)

	// 从Set-Cookie响应头中解析cookies
	if setCookieHeaders, exists := resp.Headers["Set-Cookie"]; exists {
		for _, setCookie := range setCookieHeaders {
			name, value := parseCookieHeader(setCookie)
			if name != "" {
				cookies[name] = value
			}
		}
	}

	return cookies
}

// parseCookieHeader 解析单个Set-Cookie头
func parseCookieHeader(setCookie string) (string, string) {
	// 简单解析Set-Cookie头：name=value; 其他属性...
	parts := strings.Split(setCookie, ";")
	if len(parts) == 0 {
		return "", ""
	}

	// 获取第一部分：name=value
	nameValue := strings.TrimSpace(parts[0])
	equalIndex := strings.Index(nameValue, "=")
	if equalIndex == -1 {
		return "", ""
	}

	name := strings.TrimSpace(nameValue[:equalIndex])
	value := strings.TrimSpace(nameValue[equalIndex+1:])

	if name == "" {
		return "", ""
	}

	// 移除cookie值首尾的双引号（如果存在）
	if len(value) >= 2 && value[0] == '"' && value[len(value)-1] == '"' {
		value = value[1 : len(value)-1]
	}

	return name, value
}
