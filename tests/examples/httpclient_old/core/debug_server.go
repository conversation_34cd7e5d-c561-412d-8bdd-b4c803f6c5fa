package core

import (
	"encoding/json"
	"fmt"
	"net/http"
	_ "net/http/pprof" // 导入pprof
	"runtime"
	"time"

	"go-monitor/pkg/logging"
)

// DebugServer HTTP调试服务器
type DebugServer struct {
	server *http.Server
	logger logging.Logger
}

// NewDebugServer 创建调试服务器
func NewDebugServer(port int) *DebugServer {
	mux := http.NewServeMux()
	
	ds := &DebugServer{
		server: &http.Server{
			Addr:    fmt.Sprintf(":%d", port),
			Handler: mux,
		},
		logger: logging.GetLogger("httpclient.debug_server"),
	}
	
	// 注册路由
	mux.HandleFunc("/debug/connections", ds.handleConnections)
	mux.HandleFunc("/debug/memory", ds.handleMemory)
	mux.HandleFunc("/debug/gc", ds.handleGC)
	mux.HandleFunc("/debug/stats", ds.handleStats)
	
	// pprof路由会自动注册到默认的ServeMux
	// 我们需要手动添加到我们的mux
	mux.Handle("/debug/pprof/", http.DefaultServeMux)
	
	return ds
}

// Start 启动调试服务器
func (ds *DebugServer) Start() error {
	ds.logger.Info("启动HTTP调试服务器", "addr", ds.server.Addr)
	
	go func() {
		if err := ds.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ds.logger.Error("调试服务器启动失败", "error", err)
		}
	}()
	
	return nil
}

// Stop 停止调试服务器
func (ds *DebugServer) Stop() error {
	ds.logger.Info("停止HTTP调试服务器")
	return ds.server.Close()
}

// handleConnections 处理连接统计请求
func (ds *DebugServer) handleConnections(w http.ResponseWriter, r *http.Request) {
	cm := GetGlobalConnectionManager()
	stats := cm.GetStats()
	
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(stats); err != nil {
		ds.logger.Error("编码连接统计失败", "error", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}
}

// handleMemory 处理内存统计请求
func (ds *DebugServer) handleMemory(w http.ResponseWriter, r *http.Request) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	stats := map[string]interface{}{
		"alloc_mb":        bToMb(m.Alloc),
		"total_alloc_mb":  bToMb(m.TotalAlloc),
		"sys_mb":          bToMb(m.Sys),
		"heap_alloc_mb":   bToMb(m.HeapAlloc),
		"heap_sys_mb":     bToMb(m.HeapSys),
		"heap_idle_mb":    bToMb(m.HeapIdle),
		"heap_inuse_mb":   bToMb(m.HeapInuse),
		"heap_released_mb": bToMb(m.HeapReleased),
		"heap_objects":    m.HeapObjects,
		"stack_inuse_mb":  bToMb(m.StackInuse),
		"stack_sys_mb":    bToMb(m.StackSys),
		"gc_runs":         m.NumGC,
		"gc_pause_ns":     m.PauseNs[(m.NumGC+255)%256],
		"goroutines":      runtime.NumGoroutine(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(stats); err != nil {
		ds.logger.Error("编码内存统计失败", "error", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}
}

// handleGC 处理垃圾回收请求
func (ds *DebugServer) handleGC(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}
	
	ds.logger.Info("手动触发垃圾回收")
	runtime.GC()
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status": "ok",
		"message": "垃圾回收已触发",
		"time": time.Now().Format("2006-01-02 15:04:05"),
	})
}

// handleStats 处理综合统计请求
func (ds *DebugServer) handleStats(w http.ResponseWriter, r *http.Request) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	cm := GetGlobalConnectionManager()
	connStats := cm.GetStats()
	
	stats := map[string]interface{}{
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
		"memory": map[string]interface{}{
			"alloc_mb":     bToMb(m.Alloc),
			"sys_mb":       bToMb(m.Sys),
			"heap_alloc_mb": bToMb(m.HeapAlloc),
			"goroutines":   runtime.NumGoroutine(),
			"gc_runs":      m.NumGC,
		},
		"connections": connStats,
	}
	
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(stats); err != nil {
		ds.logger.Error("编码综合统计失败", "error", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}
}

// bToMb 字节转MB
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}
