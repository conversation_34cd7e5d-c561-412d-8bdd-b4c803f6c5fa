package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"go-monitor/pkg/httpclient"
)

// HTTP版本控制使用示例
func main() {
	fmt.Println("HTTP版本控制功能使用示例")
	fmt.Println("================================")

	// 基础配置
	config := &httpclient.Config{
		Timeout:              30 * time.Second,
		EnableTLSFingerprint: true,
	}

	// 示例1：强制使用HTTP/1.1（适用于PopMart等API）
	fmt.Println("\n1. 强制使用HTTP/1.1")
	requestConfig1 := &httpclient.RequestConfig{
		HTTPVersion: httpclient.HTTPVersionHTTP1Only,
	}
	fmt.Printf("   配置: %s\n", requestConfig1.HTTPVersion.String())

	// 示例2：优先使用HTTP/2，降级到HTTP/1.1
	fmt.Println("\n2. 优先使用HTTP/2")
	requestConfig2 := &httpclient.RequestConfig{
		HTTPVersion: httpclient.HTTPVersionHTTP2Preferred,
	}
	fmt.Printf("   配置: %s\n", requestConfig2.HTTPVersion.String())

	// 示例3：强制使用HTTP/2
	fmt.Println("\n3. 强制使用HTTP/2")
	requestConfig3 := &httpclient.RequestConfig{
		HTTPVersion: httpclient.HTTPVersionHTTP2Only,
	}
	fmt.Printf("   配置: %s\n", requestConfig3.HTTPVersion.String())

	// 示例4：自动协商（默认）
	fmt.Println("\n4. 自动协商")
	requestConfig4 := &httpclient.RequestConfig{
		HTTPVersion: httpclient.HTTPVersionAuto,
	}
	fmt.Printf("   配置: %s\n", requestConfig4.HTTPVersion.String())

	// 示例5：简化后的使用
	fmt.Println("\n5. 简化使用 - 一个参数搞定")
	fmt.Printf("   只需要设置 HTTPVersion，其他都不用管！\n")

	// 实际使用示例
	fmt.Println("\n6. 实际使用示例")
	demonstrateUsage(config)
}

func demonstrateUsage(config *httpclient.Config) {
	// PopMart API示例（需要HTTP/1.1）
	fmt.Println("   PopMart API请求（HTTP/1.1）:")

	requestConfig := &httpclient.RequestConfig{
		HTTPVersion:          httpclient.HTTPVersionHTTP1Only,
		EnableTLSFingerprint: true,
		ExcludeTLSProfiles: []string{
			"Chrome_120_Win11_Real", "Chrome_119_macOS_Real",
		},
	}

	client, err := httpclient.CreateClientForRequestWithURL(
		config,
		requestConfig,
		"https://prod-asia-api.popmart.com",
	)
	if err != nil {
		log.Printf("   创建客户端失败: %v", err)
		return
	}

	request := &httpclient.Request{
		URL:    "https://prod-asia-api.popmart.com/shop/v1/search",
		Method: "POST",
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body:    []byte(`{"term": "test", "pageSize": 1, "page": 1}`),
		Timeout: 10 * time.Second,
	}

	ctx := context.Background()
	response, err := httpclient.DoWithClient(ctx, client, request, nil)
	if err != nil {
		fmt.Printf("   请求失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 请求成功! 状态码: %d\n", response.StatusCode)
}
