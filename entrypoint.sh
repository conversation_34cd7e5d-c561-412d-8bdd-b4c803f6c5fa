#!/bin/sh

# Monitor项目 - Docker容器启动脚本
# 处理权限问题并启动应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# 检查目录权限
check_directory_permissions() {
    local dir="$1"
    local dir_name="$2"
    
    log_info "检查${dir_name}目录权限: $dir"
    
    # 检查目录是否存在
    if [ ! -d "$dir" ]; then
        log_warning "${dir_name}目录不存在，尝试创建..."
        if ! mkdir -p "$dir" 2>/dev/null; then
            log_error "无法创建${dir_name}目录: $dir"
            return 1
        fi
        log_success "${dir_name}目录创建成功"
    fi
    
    # 检查写入权限
    local test_file="$dir/.write_test_$$"
    if touch "$test_file" 2>/dev/null; then
        rm -f "$test_file" 2>/dev/null
        log_success "${dir_name}目录权限正常"
        return 0
    else
        log_warning "${dir_name}目录权限不足，尝试修复..."
        
        # 尝试修复权限
        if chmod 755 "$dir" 2>/dev/null; then
            # 再次测试写入权限
            if touch "$test_file" 2>/dev/null; then
                rm -f "$test_file" 2>/dev/null
                log_success "${dir_name}目录权限修复成功"
                return 0
            fi
        fi
        
        log_error "${dir_name}目录权限修复失败: $dir"
        log_error "请检查Docker卷映射配置和宿主机目录权限"
        return 1
    fi
}

# 主要权限检查函数
check_permissions() {
    log_info "开始检查容器内关键目录权限..."
    
    local failed=0
    
    # 检查日志目录
    if ! check_directory_permissions "/app/logs" "日志"; then
        failed=1
    fi
    
    # 检查数据目录
    if ! check_directory_permissions "/app/data" "数据"; then
        failed=1
    fi
    
    if [ $failed -eq 1 ]; then
        log_error "权限检查失败，无法启动应用"
        log_error "解决方案："
        log_error "1. 检查宿主机上的logs和data目录权限"
        log_error "2. 运行: chmod 777 logs data"
        log_error "3. 或者运行: sudo chown 1001:1001 logs data"
        exit 1
    fi
    
    log_success "所有目录权限检查通过"
}

# 显示环境信息
show_environment() {
    log_info "容器环境信息:"
    echo "  用户: $(whoami) (UID: $(id -u), GID: $(id -g))"
    echo "  工作目录: $(pwd)"
    echo "  时区: ${TZ:-未设置}"
    echo "  Go版本: $(go version 2>/dev/null || echo '未安装')"
}

# 主函数
main() {
    log_info "🚀 Monitor容器启动中..."
    
    # 显示环境信息
    show_environment
    
    # 检查权限
    check_permissions
    
    # 启动应用
    log_info "启动Monitor应用..."
    log_success "权限检查完成，开始执行应用"
    
    # 执行原始命令
    exec "$@"
}

# 脚本入口
main "$@"
