#!/bin/bash

# Monitor项目 - 服务器资源打包脚本
# 编译Go应用程序并打包服务器所需资源

set -euo pipefail

# 配置
readonly PROJECT_NAME="monitor"
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 工具函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔄 $1${NC}"; }

# 显示帮助信息
show_help() {
    cat << EOF
${CYAN}Monitor项目 服务器资源打包脚本${NC}

${YELLOW}用法:${NC}
  $0 [选项]

${YELLOW}选项:${NC}
  -h, --help              显示此帮助信息
  -v, --version VERSION   指定版本号 (默认: 1.0.0)
  -c, --clean             构建前清理旧文件
  --verbose               显示详细输出

${YELLOW}示例:${NC}
  $0                      # 默认构建（Linux/amd64）
  $0 -v 1.2.0             # 指定版本
  $0 -c                   # 清理构建

${YELLOW}环境要求:${NC}
  - Go 1.21+
  - Git (用于获取提交信息)
EOF
}

# 环境检查
check_environment() {
    log_step "检查构建环境..."

    if ! command -v go &> /dev/null; then
        log_error "Go命令未找到，请先安装Go"
        exit 1
    fi

    if ! command -v git &> /dev/null; then
        log_error "Git命令未找到，请先安装Git"
        exit 1
    fi

    if [[ ! -f "go.mod" ]]; then
        log_error "go.mod文件不存在，请确保在Go项目根目录运行"
        exit 1
    fi

    local go_version=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
    log_success "Go版本: $go_version"
    log_success "环境检查完成"
}

# 获取构建信息
get_build_info() {
    VERSION="${VERSION:-1.0.0}"
    BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

    log_info "构建信息:"
    log_info "  版本: $VERSION"
    log_info "  构建时间: $BUILD_TIME"
    log_info "  Git提交: $GIT_COMMIT"
}

# 编译Go应用程序
build_go_binary() {
    log_step "编译Go应用程序..."

    # 清理旧文件
    rm -f "$PROJECT_NAME"

    # 下载依赖
    go mod download

    # 编译 (Linux amd64)
    local ldflags="-X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'main.GitCommit=${GIT_COMMIT}' -w -s"

    if ! CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
        -ldflags "${ldflags}" \
        -o "$PROJECT_NAME" \
        ./cmd/monitor; then
        log_error "Go编译失败"
        exit 1
    fi

    local binary_size=$(du -h "$PROJECT_NAME" | cut -f1)
    log_success "Go编译完成 (大小: $binary_size)"
}

# 打包服务器资源
package_resources() {
    log_step "打包服务器资源..."

    local archive_name="monitor-${VERSION}-linux-amd64.tar.gz"
    local temp_dir="dist/temp"

    # 创建打包目录
    rm -rf dist
    mkdir -p "$temp_dir"

    # 复制运行时文件
    cp "$PROJECT_NAME" "$temp_dir/"
    cp -r configs "$temp_dir/"
    mkdir -p "$temp_dir/logs" "$temp_dir/data"

    # 复制Docker文件
    cp start-docker.sh "$temp_dir/"
    cp docker-compose.yml "$temp_dir/"
    cp Dockerfile "$temp_dir/"
    cp entrypoint.sh "$temp_dir/"






    # 创建README文件
    cat > "$temp_dir/README.md" << EOF
# Monitor服务部署包

版本: $VERSION | 构建时间: $BUILD_TIME | Git: $GIT_COMMIT

## 部署方式

### 方式1: Docker部署 (推荐)
1. 解压: tar -xzf monitor-${VERSION}-linux-amd64.tar.gz
2. 管理: ./start-docker.sh

### 方式2: 直接运行
1. 解压: tar -xzf monitor-${VERSION}-linux-amd64.tar.gz
2. 启动: ./monitor

## 文件说明
- monitor: 预编译二进制文件
- start-docker.sh: Docker管理脚本
- Dockerfile: Docker镜像构建文件
- docker-compose.yml: Docker编排文件
- configs/: 配置文件目录
EOF

    # 创建压缩包
    cd "$temp_dir"
    tar --no-xattrs -czf "../$archive_name" .
    cd ../..

    local archive_size=$(du -h "dist/$archive_name" | cut -f1)
    log_success "打包完成: dist/$archive_name ($archive_size)"
}

# 显示构建结果
show_build_result() {
    echo
    log_success "🎉 构建完成！"
    echo
    log_info "构建结果:"
    log_info "  二进制文件: $PROJECT_NAME ($(du -h "$PROJECT_NAME" | cut -f1))"
    log_info "  部署包: dist/monitor-${VERSION}-linux-amd64.tar.gz"
    echo
    log_info "服务器部署步骤:"
    log_info "  1. 上传: scp dist/monitor-${VERSION}-linux-amd64.tar.gz user@server:/path/"
    log_info "  2. 解压: tar -xzf monitor-${VERSION}-linux-amd64.tar.gz"
    log_info "  3. 启动: ./monitor"
    echo
    log_info "本地操作:"
    log_info "  本地运行: ./$PROJECT_NAME"
    log_info "  Docker管理: ./start-docker.sh"
    echo
}

# 主函数
main() {
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help) show_help; exit 0 ;;
            -v|--version) VERSION="$2"; shift 2 ;;
            -c|--clean) rm -rf dist "$PROJECT_NAME"; shift ;;
            --verbose) VERBOSE="true"; shift ;;
            *) log_error "未知选项: $1"; show_help; exit 1 ;;
        esac
    done

    # 设置默认值
    VERSION="${VERSION:-1.0.0}"

    # 执行构建
    echo -e "${CYAN}🚀 Monitor项目 服务器资源打包${NC}"
    echo -e "${CYAN}================================${NC}"
    echo

    check_environment
    get_build_info
    build_go_binary
    package_resources
    show_build_result
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
