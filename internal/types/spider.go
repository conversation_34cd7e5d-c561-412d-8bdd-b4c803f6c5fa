package types

import (
	"strconv"
	"time"
)

// BatchProcessingConfig 批量处理配置
type BatchProcessingConfig struct {
	Enabled   bool   `yaml:"enabled"`    // 是否启用批量处理
	BatchSize int    `yaml:"batch_size"` // 批量大小，0表示使用爬虫默认值
	Strategy  string `yaml:"strategy"`   // 处理策略：optimal(自动选择), single(强制单个), batch(强制批量)
}

// SpiderConfig 爬虫配置 - 统一的配置结构，开箱即用
type SpiderConfig struct {
	Name     string `yaml:"name"`
	Platform string `yaml:"platform"`
	Enabled  bool   `yaml:"enabled"`
	Interval string `yaml:"check_interval"` // 匹配配置文件中的check_interval
	// Timeout        string                 `yaml:"timeout"`
	// MaxRetries     int                    `yaml:"max_retries"`
	Country        string                 `yaml:"country"`
	SiteURL        string                 `yaml:"url"` // 匹配配置文件中的url
	Notifications  []string               `yaml:"notifications"`
	SpiderSettings map[string]interface{} `yaml:",inline"` // 内联处理，收集所有未映射字段

	// 批量处理配置
	BatchProcessing *BatchProcessingConfig `yaml:"batch_processing,omitempty"`

	// Redis管理器 - 用于缓存和数据存储（不序列化）
	RedisManager interface{} `json:"-" yaml:"-"`
}

// 实现httpclient.SpiderConfig接口
func (c *SpiderConfig) GetPlatform() string {
	return c.Platform
}

func (c *SpiderConfig) GetName() string {
	return c.Name
}

func (c *SpiderConfig) GetCountry() string {
	return c.Country
}

func (c *SpiderConfig) GetSpiderSettings() map[string]interface{} {
	return c.SpiderSettings
}

// 解析后的配置字段
func (c *SpiderConfig) GetIntervalDuration() time.Duration {
	// 优先尝试解析Interval字段（字符串格式）
	if c.Interval != "" {
		// 首先尝试标准的duration格式（如"2.5s", "30s"）
		if duration, err := time.ParseDuration(c.Interval); err == nil {
			return duration
		}

		// 如果失败，尝试解析为纯数字（假设单位为秒）
		if f, err := strconv.ParseFloat(c.Interval, 64); err == nil {
			return time.Duration(f * float64(time.Second))
		}
	}

	// 如果Interval字段解析失败，尝试从SpiderSettings中获取check_interval
	if c.SpiderSettings != nil {
		if value, exists := c.SpiderSettings["check_interval"]; exists {
			switch v := value.(type) {
			case time.Duration:
				return v
			case string:
				// 首先尝试标准的duration格式
				if duration, err := time.ParseDuration(v); err == nil {
					return duration
				}
				// 如果失败，尝试解析为纯数字（假设单位为秒）
				if f, err := strconv.ParseFloat(v, 64); err == nil {
					return time.Duration(f * float64(time.Second))
				}
			case int:
				return time.Duration(v) * time.Second
			case int64:
				return time.Duration(v) * time.Second
			case float64:
				return time.Duration(v * float64(time.Second))
			case float32:
				return time.Duration(v * float32(time.Second))
			}
		}
	}

	return 30 * time.Second // 默认值
}
