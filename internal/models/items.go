package models

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ProductItem 产品数据项
type ProductItem struct {
	// 基础信息
	Name      string  `json:"name" gorm:"not null"`
	ProductID string  `json:"productId" gorm:"primaryKey"`
	SkuID     *string `json:"skuId,omitempty"`
	Title     string  `json:"title" gorm:"not null"`
	URL       string  `json:"url" gorm:"not null"`
	Platform  string  `json:"platform" gorm:"not null"`

	// 价格信息
	Price    float64 `json:"price"`
	Currency string  `json:"currency"`

	// 库存信息
	Stock        int    `json:"stock"`
	InStock      bool   `json:"inStock"`
	Availability string `json:"availability"`

	// 位置信息
	Country string `json:"country"`
	SiteURL string `json:"siteUrl"`

	// 可选信息
	OfferID     *string `json:"offerId,omitempty"`
	ImageURL    *string `json:"imageUrl,omitempty"`
	Addition    *string `json:"addition,omitempty"`
	ReleaseDate *string `json:"releaseDate,omitempty"`
	AtcLink     *string `json:"atcLink,omitempty"`

	// 元数据
	Notifications []string               `json:"notifications" gorm:"serializer:json"`
	CrawledAt     time.Time              `json:"crawledAt"`
	Metadata      map[string]interface{} `json:"metadata" gorm:"serializer:json"`

	// GORM字段
	CreatedAt time.Time      `json:"-"`
	UpdatedAt time.Time      `json:"-"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// ToJSON 转换为JSON字符串
func (p *ProductItem) ToJSON() (string, error) {
	data, err := json.Marshal(p)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串创建实例
func (p *ProductItem) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), p)
}

// String 字符串表示
func (p *ProductItem) String() string {
	return fmt.Sprintf("ProductItem(productId=%s, title=%s, price=%.2f %s)",
		p.ProductID, p.Title, p.Price, p.Currency)
}

// NotificationItem 通知数据项
type NotificationItem struct {
	ID          uuid.UUID   `json:"id" gorm:"type:uuid;primaryKey"`
	ProductData ProductItem `json:"productData" gorm:"embedded"`

	// 变化信息
	ChangeType    string   `json:"changeType"`
	ChangedFields []string `json:"changedFields" gorm:"serializer:json"`

	// Discord通知配置
	ChannelIDs []string `json:"channelIds" gorm:"serializer:json"`

	// 系统元数据
	Timestamp time.Time              `json:"timestamp"`
	Priority  int                    `json:"priority"`
	Metadata  map[string]interface{} `json:"metadata" gorm:"serializer:json"`

	// 通知状态
	NotificationStatus  string  `json:"notificationStatus" gorm:"default:pending"`
	NotificationRetries int     `json:"notificationRetries" gorm:"default:0"`
	NotificationError   *string `json:"notificationError,omitempty"`

	// 通知组配置 (可选)
	NotificationGroups map[string]map[string]interface{} `json:"notificationGroups,omitempty" gorm:"serializer:json"`

	// GORM字段
	CreatedAt time.Time      `json:"-"`
	UpdatedAt time.Time      `json:"-"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate GORM钩子 - 创建前自动生成UUID
func (n *NotificationItem) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	if n.Timestamp.IsZero() {
		n.Timestamp = time.Now()
	}
	if n.Metadata == nil {
		n.Metadata = map[string]interface{}{
			"source":       "monitor",
			"version":      "1.0.0",
			"spiderConfig": nil,
			"fingerprint":  nil,
		}
	}
	return nil
}

// ToJSON 转换为JSON字符串
func (n *NotificationItem) ToJSON() (string, error) {
	data, err := json.Marshal(n)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串创建实例
func (n *NotificationItem) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), n)
}

// String 字符串表示
func (n *NotificationItem) String() string {
	return fmt.Sprintf("NotificationItem(productId=%s, changeType=%s, status=%s)",
		n.ProductData.ProductID, n.ChangeType, n.NotificationStatus)
}

// ErrorLogItem 错误日志数据项
type ErrorLogItem struct {
	ID           uuid.UUID              `json:"id" gorm:"type:uuid;primaryKey"`
	ErrorType    string                 `json:"errorType"`                           // system, spider, pipeline, middleware 等
	ErrorLevel   string                 `json:"errorLevel"`                          // error, warning, info
	ErrorMessage string                 `json:"errorMessage"`                        // 错误消息
	ErrorDetails map[string]interface{} `json:"errorDetails" gorm:"serializer:json"` // 错误详情
	Timestamp    time.Time              `json:"timestamp"`
	Source       string                 `json:"source"`                          // 错误来源
	Metadata     map[string]interface{} `json:"metadata" gorm:"serializer:json"` // 额外元数据

	// GORM字段
	CreatedAt time.Time      `json:"-"`
	UpdatedAt time.Time      `json:"-"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate GORM钩子 - 创建前自动生成UUID
func (e *ErrorLogItem) BeforeCreate(tx *gorm.DB) error {
	if e.ID == uuid.Nil {
		e.ID = uuid.New()
	}
	if e.Timestamp.IsZero() {
		e.Timestamp = time.Now()
	}
	if e.Source == "" {
		e.Source = "monitor"
	}
	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}
	return nil
}

// ToJSON 转换为JSON字符串
func (e *ErrorLogItem) ToJSON() (string, error) {
	data, err := json.Marshal(e)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串创建实例
func (e *ErrorLogItem) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), e)
}
