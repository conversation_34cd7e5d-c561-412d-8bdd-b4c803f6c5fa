package models

import (
	"time"
)

// PriceInfo 价格信息
type PriceInfo struct {
	Current  float64 `json:"current"`
	Original float64 `json:"original"`
	Currency string  `json:"currency"`
	Discount float64 `json:"discount,omitempty"`
	OnSale   bool    `json:"on_sale"`
}

// StockInfo 库存信息
type StockInfo struct {
	Available   bool      `json:"available"`
	Quantity    int       `json:"quantity"`
	Status      string    `json:"status"`
	LastUpdated time.Time `json:"last_updated"`
	LowStock    bool      `json:"low_stock,omitempty"`
	OutOfStock  bool      `json:"out_of_stock,omitempty"`
}

// ErrorInfo 错误信息
type ErrorInfo struct {
	Type       string                 `json:"type"`
	Message    string                 `json:"message"`
	Code       string                 `json:"code,omitempty"`
	Timestamp  time.Time              `json:"timestamp"`
	Context    map[string]interface{} `json:"context,omitempty"`
	Retryable  bool                   `json:"retryable,omitempty"`
	RetryCount int                    `json:"retry_count,omitempty"`
}

// NotificationMessage 通知消息接口
type NotificationMessage interface {
	GetID() string
	GetType() string
	GetContent() interface{}
	GetTimestamp() time.Time
	GetPriority() int
	Validate() error
}

// ProductNotificationMessage 产品通知消息
type ProductNotificationMessage struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	ProductID   string                 `json:"product_id"`
	ProductName string                 `json:"product_name"`
	ProductURL  string                 `json:"product_url"`
	Platform    string                 `json:"platform"`
	Price       *PriceInfo             `json:"price,omitempty"`
	Stock       *StockInfo             `json:"stock,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
	Priority    int                    `json:"priority"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ErrorNotificationMessage 错误通知消息
type ErrorNotificationMessage struct {
	ID        string     `json:"id"`
	Type      string     `json:"type"`
	Component string     `json:"component"`
	ErrorInfo *ErrorInfo `json:"error_info"`
	Timestamp time.Time  `json:"timestamp"`
	Priority  int        `json:"priority"`
	Severity  string     `json:"severity"` // low, medium, high, critical
}

// SystemNotificationMessage 系统通知消息
type SystemNotificationMessage struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Event     string                 `json:"event"`
	Component string                 `json:"component"`
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Priority  int                    `json:"priority"`
}

// NotificationDeliveryStatus 通知传递状态
type NotificationDeliveryStatus struct {
	MessageID   string                 `json:"message_id"`
	Status      string                 `json:"status"` // pending, sent, delivered, failed
	Platform    string                 `json:"platform"`
	Attempts    int                    `json:"attempts"`
	LastAttempt time.Time              `json:"last_attempt"`
	NextRetry   time.Time              `json:"next_retry,omitempty"`
	Error       string                 `json:"error,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Type      string                 `json:"type"`
	Platform  string                 `json:"platform"`
	Subject   string                 `json:"subject,omitempty"`
	Content   string                 `json:"content"`
	Format    string                 `json:"format"` // text, markdown, html
	Variables []string               `json:"variables"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
	Version   int                    `json:"version"`
	IsActive  bool                   `json:"is_active"`
}

// NotificationRule 通知规则
type NotificationRule struct {
	ID          string                  `json:"id"`
	Name        string                  `json:"name"`
	Description string                  `json:"description"`
	Type        string                  `json:"type"`
	Conditions  []NotificationCondition `json:"conditions"`
	Actions     []NotificationAction    `json:"actions"`
	IsEnabled   bool                    `json:"is_enabled"`
	Priority    int                     `json:"priority"`
	Cooldown    time.Duration           `json:"cooldown"`
	MaxRetries  int                     `json:"max_retries"`
	CreatedAt   time.Time               `json:"created_at"`
	UpdatedAt   time.Time               `json:"updated_at"`
}

// NotificationCondition 通知条件
type NotificationCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // eq, ne, gt, gte, lt, lte, in, nin, contains
	Value    interface{} `json:"value"`
	Logical  string      `json:"logical,omitempty"` // and, or
}

// NotificationAction 通知动作
type NotificationAction struct {
	Type       string                 `json:"type"`
	Platform   string                 `json:"platform"`
	Template   string                 `json:"template"`
	Recipients []string               `json:"recipients"`
	Config     map[string]interface{} `json:"config,omitempty"`
}

// NotificationQueue 通知队列项
type NotificationQueue struct {
	ID          string                `json:"id"`
	Message     NotificationMessage   `json:"message"`
	Recipients  []string              `json:"recipients"`
	Template    *NotificationTemplate `json:"template,omitempty"`
	ScheduledAt time.Time             `json:"scheduled_at"`
	CreatedAt   time.Time             `json:"created_at"`
	Status      string                `json:"status"`
	Attempts    int                   `json:"attempts"`
	LastError   string                `json:"last_error,omitempty"`
}

// NotificationBatch 批量通知
type NotificationBatch struct {
	ID          string              `json:"id"`
	Messages    []NotificationQueue `json:"messages"`
	BatchSize   int                 `json:"batch_size"`
	CreatedAt   time.Time           `json:"created_at"`
	StartedAt   time.Time           `json:"started_at,omitempty"`
	CompletedAt time.Time           `json:"completed_at,omitempty"`
	Status      string              `json:"status"`
	Progress    int                 `json:"progress"`
	TotalCount  int                 `json:"total_count"`
	Errors      []string            `json:"errors,omitempty"`
}

// NotificationChannel 通知渠道
type NotificationChannel struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Platform  string                 `json:"platform"`
	Type      string                 `json:"type"`
	Config    map[string]interface{} `json:"config"`
	IsEnabled bool                   `json:"is_enabled"`
	IsHealthy bool                   `json:"is_healthy"`
	LastCheck time.Time              `json:"last_check"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// NotificationSubscription 通知订阅
type NotificationSubscription struct {
	ID        string                  `json:"id"`
	UserID    string                  `json:"user_id"`
	Channels  []string                `json:"channels"`
	Topics    []string                `json:"topics"`
	Filters   []NotificationCondition `json:"filters"`
	IsEnabled bool                    `json:"is_enabled"`
	CreatedAt time.Time               `json:"created_at"`
	UpdatedAt time.Time               `json:"updated_at"`
}

// NotificationPreferences 通知偏好设置
type NotificationPreferences struct {
	UserID          string            `json:"user_id"`
	DefaultChannels []string          `json:"default_channels"`
	QuietHours      []QuietHour       `json:"quiet_hours"`
	Frequency       map[string]string `json:"frequency"` // immediate, hourly, daily, weekly
	Topics          map[string]bool   `json:"topics"`
	Languages       []string          `json:"languages"`
	Timezone        string            `json:"timezone"`
	UpdatedAt       time.Time         `json:"updated_at"`
}

// QuietHour 免打扰时间段
type QuietHour struct {
	StartTime string   `json:"start_time"` // HH:MM format
	EndTime   string   `json:"end_time"`   // HH:MM format
	Days      []string `json:"days"`       // monday, tuesday, etc.
	Timezone  string   `json:"timezone"`
}

// NotificationClient 通知客户端接口
type NotificationClient interface {
	// 发送单条消息
	SendMessage(message NotificationMessage) error

	// 批量发送消息
	SendBatch(messages []NotificationMessage) error

	// 检查健康状态
	HealthCheck() error

	// 获取配置
	GetConfig() map[string]interface{}

	// 验证配置
	ValidateConfig(config map[string]interface{}) error
}

// NotificationService 通知服务接口
type NotificationService interface {
	// 发送通知
	SendNotification(message NotificationMessage, recipients []string) error

	// 批量发送通知
	SendBulkNotifications(messages []NotificationMessage) error

	// 添加通知到队列
	QueueNotification(message NotificationMessage, recipients []string, scheduledAt time.Time) error

	// 处理队列中的通知
	ProcessQueue() error

	// 获取通知状态
	GetNotificationStatus(messageID string) (*NotificationDeliveryStatus, error)

	// 重试失败的通知
	RetryFailedNotifications() error

	// 注册通知客户端
	RegisterClient(platform string, client NotificationClient) error

	// 健康检查
	HealthCheck() map[string]error
}

// 实现NotificationMessage接口的方法
func (p *ProductNotificationMessage) GetID() string           { return p.ID }
func (p *ProductNotificationMessage) GetType() string         { return p.Type }
func (p *ProductNotificationMessage) GetContent() interface{} { return p }
func (p *ProductNotificationMessage) GetTimestamp() time.Time { return p.Timestamp }
func (p *ProductNotificationMessage) GetPriority() int        { return p.Priority }
func (p *ProductNotificationMessage) Validate() error         { return nil }

func (e *ErrorNotificationMessage) GetID() string           { return e.ID }
func (e *ErrorNotificationMessage) GetType() string         { return e.Type }
func (e *ErrorNotificationMessage) GetContent() interface{} { return e }
func (e *ErrorNotificationMessage) GetTimestamp() time.Time { return e.Timestamp }
func (e *ErrorNotificationMessage) GetPriority() int        { return e.Priority }
func (e *ErrorNotificationMessage) Validate() error         { return nil }

func (s *SystemNotificationMessage) GetID() string           { return s.ID }
func (s *SystemNotificationMessage) GetType() string         { return s.Type }
func (s *SystemNotificationMessage) GetContent() interface{} { return s }
func (s *SystemNotificationMessage) GetTimestamp() time.Time { return s.Timestamp }
func (s *SystemNotificationMessage) GetPriority() int        { return s.Priority }
func (s *SystemNotificationMessage) Validate() error         { return nil }
