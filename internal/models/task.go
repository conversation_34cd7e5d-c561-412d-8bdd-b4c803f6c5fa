package models

import (
	"time"
)

// MonitorTask 监控任务模型
type MonitorTask struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Platform string `json:"platform"`
	URL      string `json:"url"`
	Country  string `json:"country"`
	TaskType string `json:"task_type"` // wishlist, product, search等
	Priority int    `json:"priority"`

	// 任务参数
	Parameters map[string]interface{} `json:"parameters"`

	// 时间信息
	CreatedAt   time.Time `json:"created_at"`
	ScheduledAt time.Time `json:"scheduled_at"`

	// 元数据
	Meta map[string]interface{} `json:"meta"`

	// 配置信息
	Config TaskConfig `json:"config"`
}

// TaskConfig 任务配置
type TaskConfig struct {
	Timeout       time.Duration     `json:"timeout"`
	MaxRetries    int               `json:"max_retries"`
	UserAgent     string            `json:"user_agent"`
	Headers       map[string]string `json:"headers"`
	Notifications []string          `json:"notifications"`
}
