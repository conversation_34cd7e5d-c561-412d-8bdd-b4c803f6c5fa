package models

import (
	"time"
)

// RabbitMQConfig RabbitMQ配置
type RabbitMQConfig struct {
	URL           string        `json:"url"`
	Exchange      string        `json:"exchange"`
	ExchangeType  string        `json:"exchange_type"`
	RoutingKey    string        `json:"routing_key"`
	QueueName     string        `json:"queue_name"`
	Durable       bool          `json:"durable"`
	AutoDelete    bool          `json:"auto_delete"`
	Exclusive     bool          `json:"exclusive"`
	NoWait        bool          `json:"no_wait"`
	PrefetchCount int           `json:"prefetch_count"`
	Timeout       time.Duration `json:"timeout"`
	RetryCount    int           `json:"retry_count"`
	RetryDelay    time.Duration `json:"retry_delay"`
}

// ProducerConfig 生产者配置
type ProducerConfig struct {
	RoutingKey    string                 `json:"routing_key"`
	Exchange      string                 `json:"exchange"`
	Mandatory     bool                   `json:"mandatory"`
	Immediate     bool                   `json:"immediate"`
	Headers       map[string]interface{} `json:"headers"`
	ContentType   string                 `json:"content_type"`
	DeliveryMode  uint8                  `json:"delivery_mode"`
	Expiration    string                 `json:"expiration"`
	Priority      uint8                  `json:"priority"`
	CorrelationID string                 `json:"correlation_id"`
	ReplyTo       string                 `json:"reply_to"`
	MessageID     string                 `json:"message_id"`
	Timestamp     bool                   `json:"timestamp"`
	Type          string                 `json:"type"`
	UserID        string                 `json:"user_id"`
	AppID         string                 `json:"app_id"`
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	QueueName     string                 `json:"queue_name"`
	ConsumerTag   string                 `json:"consumer_tag"`
	AutoAck       bool                   `json:"auto_ack"`
	Exclusive     bool                   `json:"exclusive"`
	NoLocal       bool                   `json:"no_local"`
	NoWait        bool                   `json:"no_wait"`
	PrefetchCount int                    `json:"prefetch_count"`
	PrefetchSize  int                    `json:"prefetch_size"`
	Global        bool                   `json:"global"`
	Args          map[string]interface{} `json:"args"`
}

// SignatureConfig 签名配置
type SignatureConfig struct {
	Platform string                 `json:"platform"`
	Method   string                 `json:"method"`
	Secret   string                 `json:"secret"`
	AppKey   string                 `json:"app_key"`
	Version  string                 `json:"version"`
	Config   map[string]interface{} `json:"config"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	Password     string        `json:"password"`
	DB           int           `json:"db"`
	PoolSize     int           `json:"pool_size"`
	MinIdleConns int           `json:"min_idle_conns"`
	MaxIdleConns int           `json:"max_idle_conns"`
	Timeout      time.Duration `json:"timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
}

// 注释：移除了 HTTPClientConfig，因为它无用且未被实际使用

// DiscordConfig Discord通知配置
type DiscordConfig struct {
	WebhookURL    string        `json:"webhook_url"`
	Username      string        `json:"username"`
	AvatarURL     string        `json:"avatar_url"`
	RateLimit     int           `json:"rate_limit"`
	Timeout       time.Duration `json:"timeout"`
	RetryCount    int           `json:"retry_count"`
	RetryDelay    time.Duration `json:"retry_delay"`
	MaxBatchSize  int           `json:"max_batch_size"`
	BatchInterval time.Duration `json:"batch_interval"`
}

// SpiderConfig 爬虫配置
type SpiderConfig struct {
	// 基础配置
	Name       string `json:"name"`
	Enabled    bool   `json:"enabled"`
	CheckURL   string `json:"checkUrl"`
	Interval   string `json:"interval"`
	TimeoutStr string `json:"timeout"`
	MaxRetries int    `json:"maxRetries"`

	// 解析后的配置
	IntervalDuration time.Duration `json:"-"`
	TimeoutDuration  time.Duration `json:"-"`

	// 通知配置
	Notifications []string `json:"notifications"`

	// 爬虫特定配置
	SpiderSettings map[string]interface{} `json:"spiderSettings"`

	// HTTP配置
	UserAgent     string            `json:"userAgent"`
	CustomHeaders map[string]string `json:"customHeaders"`
	ProxyURL      string            `json:"proxyUrl"`

	// 认证配置
	Auth *AuthInfo `json:"auth"`

	// 其他配置
	Country  string `json:"country"`
	SiteURL  string `json:"siteUrl"`
	Platform string `json:"platform"`

	// 错误处理
	ErrorThreshold int    `json:"errorThreshold"`
	ErrorAction    string `json:"errorAction"` // stop, continue, notify
}

// MiddlewareConfig 中间件配置基类
type MiddlewareConfig struct {
	Name     string                 `json:"name"`
	Enabled  bool                   `json:"enabled"`
	Priority int                    `json:"priority"`
	Settings map[string]interface{} `json:"settings"`
}

// PipelineConfig 管道配置基类
type PipelineConfig struct {
	Name     string                 `json:"name"`
	Enabled  bool                   `json:"enabled"`
	Priority int                    `json:"priority"`
	Settings map[string]interface{} `json:"settings"`
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Name         string            `json:"name"`
	Level        string            `json:"level"`
	Format       string            `json:"format"`
	Output       string            `json:"output"`
	File         string            `json:"file"`
	MaxSize      int64             `json:"max_size"`
	MaxBackups   int               `json:"max_backups"`
	MaxAge       int               `json:"max_age"`
	Compress     bool              `json:"compress"`
	EnableRotate bool              `json:"enable_rotate"`
	Metadata     map[string]string `json:"metadata"`
}
