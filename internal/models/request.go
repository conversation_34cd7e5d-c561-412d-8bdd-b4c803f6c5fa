package models

import (
	"net/url"
	"strings"
	"time"
)

// Request 请求模型 - 表示一个HTTP请求
type Request struct {
	// 基本信息
	SpiderType string            `json:"spider_type"` // 爬虫类型
	URL        string            `json:"url"`         // 完整请求URL（仅在最终发送时组装）
	Method     string            `json:"method"`      // HTTP方法 (GET, POST, etc.)
	Headers    map[string]string `json:"headers"`     // 请求头
	Cookies    map[string]string `json:"cookies"`     // Cookie
	Body       []byte            `json:"body"`        // 请求体
	Params     map[string]string `json:"params"`      // 请求参数（中间件可修改）

	// 配置参数
	Timeout      time.Duration `json:"timeout"`       // 超时时间
	Proxy        string        `json:"proxy"`         // 代理地址
	ReverseProxy string        `json:"reverse_proxy"` // 反向代理URL
	MaxRetries   int           `json:"max_retries"`   // 最大重试次数
	RetryCount   int           `json:"retry_count"`   // 当前重试次数

	// 元数据
	MonitorName string    `json:"monitor_name"` // 监控名称
	ProductID   string    `json:"product_id"`   // 产品ID
	RequestTime time.Time `json:"request_time"` // 请求时间

	// 认证信息
	Auth *AuthInfo `json:"auth"` // 认证信息

	// 中间件参数
	Metadata map[string]interface{} `json:"metadata"` // 自定义元数据
}

// AuthInfo 认证信息
type AuthInfo struct {
	Type     string `json:"type"`     // 认证类型 (basic, bearer, etc.)
	Token    string `json:"token"`    // 认证令牌
	Username string `json:"username"` // 用户名 (for basic auth)
	Password string `json:"password"` // 密码 (for basic auth)
}

// NewRequest 创建新的请求实例
func NewRequest(spiderType, url string) *Request {
	return &Request{
		SpiderType:  spiderType,
		URL:         url, // 初始时URL和BaseURL相同
		Method:      "GET",
		Headers:     make(map[string]string),
		Cookies:     make(map[string]string),
		Params:      make(map[string]string),
		Timeout:     30 * time.Second,
		MaxRetries:  3,
		RetryCount:  0,
		RequestTime: time.Now(),
		Metadata:    make(map[string]interface{}),
	}
}

// Clone 克隆请求对象
func (r *Request) Clone() *Request {
	clone := *r
	// BaseURL和URL都会被复制

	// 深拷贝map
	clone.Headers = make(map[string]string)
	for k, v := range r.Headers {
		clone.Headers[k] = v
	}

	clone.Cookies = make(map[string]string)
	for k, v := range r.Cookies {
		clone.Cookies[k] = v
	}

	clone.Params = make(map[string]string)
	for k, v := range r.Params {
		clone.Params[k] = v
	}

	clone.Metadata = make(map[string]interface{})
	for k, v := range r.Metadata {
		clone.Metadata[k] = v
	}

	// 拷贝body
	if r.Body != nil {
		clone.Body = make([]byte, len(r.Body))
		copy(clone.Body, r.Body)
	}

	// 拷贝认证信息
	if r.Auth != nil {
		authClone := *r.Auth
		clone.Auth = &authClone
	}

	return &clone
}

// BuildURL 从BaseURL和Params组装完整URL（仅在发送请求时调用）
func (r *Request) BuildURL() string {
	// 确定基础URL（考虑反向代理）
	URL := r.URL
	if r.ReverseProxy != "" {
		// 如果有反向代理，将原始URL作为参数添加到反向代理URL
		URL = r.ReverseProxy + r.URL
	}

	// 如果没有参数，直接返回基础URL
	if len(r.Params) == 0 {
		return URL
	}

	// 组装查询参数
	params := url.Values{}
	for k, v := range r.Params {
		params.Add(k, v)
	}

	// 添加参数到URL
	if strings.Contains(URL, "?") {
		return URL + "&" + params.Encode()
	} else {
		return URL + "?" + params.Encode()
	}
}

// SetHeader 设置请求头
func (r *Request) SetHeader(key, value string) {
	if r.Headers == nil {
		r.Headers = make(map[string]string)
	}
	r.Headers[key] = value
}

// GetHeader 获取请求头
func (r *Request) GetHeader(key string) string {
	if r.Headers == nil {
		return ""
	}
	return r.Headers[key]
}

// SetMetadata 设置元数据
func (r *Request) SetMetadata(key string, value interface{}) {
	if r.Metadata == nil {
		r.Metadata = make(map[string]interface{})
	}
	r.Metadata[key] = value
}

// GetMetadata 获取元数据
func (r *Request) GetMetadata(key string) interface{} {
	if r.Metadata == nil {
		return nil
	}
	return r.Metadata[key]
}

// GetMetadataString 获取字符串类型的元数据
func (r *Request) GetMetadataString(key, defaultValue string) string {
	if val := r.GetMetadata(key); val != nil {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return defaultValue
}

// SetCookie 设置Cookie
func (r *Request) SetCookie(name, value string) {
	if r.Cookies == nil {
		r.Cookies = make(map[string]string)
	}
	r.Cookies[name] = value
}

// GetCookie 获取Cookie
func (r *Request) GetCookie(name string) string {
	if r.Cookies == nil {
		return ""
	}
	return r.Cookies[name]
}
