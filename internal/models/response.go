package models

import (
	"encoding/json"
	"time"
)

// Response 响应模型 - 表示一个HTTP响应
type Response struct {
	// 关联请求
	Request *Request `json:"request"` // 关联的请求对象

	// 基本信息
	SpiderType string              `json:"spider_type"` // 爬虫类型
	StatusCode int                 `json:"status_code"` // HTTP状态码
	Headers    map[string][]string `json:"headers"`     // 响应头 (支持多值)
	Body       []byte              `json:"body"`        // 响应体
	URL        string              `json:"url"`         // 实际响应URL

	// 时间信息
	RequestTime  time.Time     `json:"request_time"`  // 请求发起时间
	ResponseTime time.Duration `json:"response_time"` // 响应时间
	Timestamp    time.Time     `json:"timestamp"`     // 响应时间戳

	// 元数据
	Size          int64  `json:"size"`           // 响应大小
	ContentLength int64  `json:"content_length"` // 内容长度
	Encoding      string `json:"encoding"`       // 编码格式

	// 扩展信息
	Cookies  map[string]string      `json:"cookies"`  // Cookie信息
	Metadata map[string]interface{} `json:"metadata"` // 自定义元数据
}

// NewResponse 创建新的响应实例
func NewResponse(spiderType, url string) *Response {
	return &Response{
		SpiderType:  spiderType,
		URL:         url,
		Headers:     make(map[string][]string),
		Cookies:     make(map[string]string),
		Metadata:    make(map[string]interface{}),
		RequestTime: time.Now(),
	}
}

// IsSuccess 判断响应是否成功
func (r *Response) IsSuccess() bool {
	return r.StatusCode >= 200 && r.StatusCode < 300
}

// IsRedirect 判断是否为重定向
func (r *Response) IsRedirect() bool {
	return r.StatusCode >= 300 && r.StatusCode < 400
}

// IsClientError 判断是否为客户端错误
func (r *Response) IsClientError() bool {
	return r.StatusCode >= 400 && r.StatusCode < 500
}

// IsServerError 判断是否为服务器错误
func (r *Response) IsServerError() bool {
	return r.StatusCode >= 500
}

// GetHeader 获取响应头
func (r *Response) GetHeader(key string) string {
	if r.Headers == nil {
		return ""
	}
	if values, exists := r.Headers[key]; exists && len(values) > 0 {
		return values[0]
	}
	return ""
}

// SetHeader 设置响应头
func (r *Response) SetHeader(key, value string) {
	if r.Headers == nil {
		r.Headers = make(map[string][]string)
	}
	r.Headers[key] = append(r.Headers[key], value)
}

// GetCookie 获取Cookie
func (r *Response) GetCookie(key string) string {
	if r.Cookies == nil {
		return ""
	}
	return r.Cookies[key]
}

// SetCookie 设置Cookie
func (r *Response) SetCookie(key, value string) {
	if r.Cookies == nil {
		r.Cookies = make(map[string]string)
	}
	r.Cookies[key] = value
}

// GetMetadata 获取元数据
func (r *Response) GetMetadata(key string) interface{} {
	if r.Metadata == nil {
		return nil
	}
	return r.Metadata[key]
}

// SetMetadata 设置元数据
func (r *Response) SetMetadata(key string, value interface{}) {
	if r.Metadata == nil {
		r.Metadata = make(map[string]interface{})
	}
	r.Metadata[key] = value
}

// Text 获取响应文本内容
func (r *Response) Text() string {
	return string(r.Body)
}

// JSON 解析响应为JSON
func (r *Response) JSON(v interface{}) error {
	return json.Unmarshal(r.Body, v)
}

// Clone 克隆响应对象
func (r *Response) Clone() *Response {
	clone := *r

	// 深拷贝map
	clone.Headers = make(map[string][]string)
	for k, v := range r.Headers {
		clone.Headers[k] = append([]string{}, v...)
	}

	clone.Cookies = make(map[string]string)
	for k, v := range r.Cookies {
		clone.Cookies[k] = v
	}

	clone.Metadata = make(map[string]interface{})
	for k, v := range r.Metadata {
		clone.Metadata[k] = v
	}

	// 拷贝body
	if r.Body != nil {
		clone.Body = make([]byte, len(r.Body))
		copy(clone.Body, r.Body)
	}

	return &clone
}
