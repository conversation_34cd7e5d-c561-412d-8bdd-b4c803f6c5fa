package models

import (
	"sync"
	"time"
)

// RabbitMQStats RabbitMQ统计信息
type RabbitMQStats struct {
	// 连接统计
	ConnectedAt         time.Time `json:"connected_at"`
	DisconnectedAt      time.Time `json:"disconnected_at"`
	ConnectionRetries   int64     `json:"connection_retries"`
	LastConnectionError string    `json:"last_connection_error"`

	// 消息统计
	MessagesPublished int64 `json:"messages_published"`
	MessagesConsumed  int64 `json:"messages_consumed"`
	MessagesAcked     int64 `json:"messages_acked"`
	MessagesNacked    int64 `json:"messages_nacked"`
	MessagesReturned  int64 `json:"messages_returned"`

	// 错误统计
	PublishErrors int64 `json:"publish_errors"`
	ConsumeErrors int64 `json:"consume_errors"`

	// 性能统计
	AvgPublishLatency time.Duration `json:"avg_publish_latency"`
	AvgConsumeLatency time.Duration `json:"avg_consume_latency"`

	// 队列统计
	QueueLength   int `json:"queue_length"`
	ConsumerCount int `json:"consumer_count"`

	// 状态信息
	IsConnected bool `json:"is_connected"`
	IsHealthy   bool `json:"is_healthy"`

	// 线程安全
	mutex sync.RWMutex `json:"-"`
}

// SpiderStats 爬虫统计信息
type SpiderStats struct {
	// 基础信息
	SpiderName  string    `json:"spider_name"`
	StartTime   time.Time `json:"start_time"`
	LastRunTime time.Time `json:"last_run_time"`

	// 运行统计
	TotalRuns      int64 `json:"total_runs"`
	SuccessfulRuns int64 `json:"successful_runs"`
	FailedRuns     int64 `json:"failed_runs"`

	// 请求统计
	TotalRequests      int64 `json:"total_requests"`
	SuccessfulRequests int64 `json:"successful_requests"`
	FailedRequests     int64 `json:"failed_requests"`

	// 数据统计
	ItemsScraped   int64 `json:"items_scraped"`
	ItemsProcessed int64 `json:"items_processed"`
	ItemsStored    int64 `json:"items_stored"`
	DuplicateItems int64 `json:"duplicate_items"`

	// 性能统计
	AvgResponseTime time.Duration `json:"avg_response_time"`
	MaxResponseTime time.Duration `json:"max_response_time"`
	MinResponseTime time.Duration `json:"min_response_time"`

	// 错误统计
	HttpErrors    map[int]int64 `json:"http_errors"`
	TimeoutErrors int64         `json:"timeout_errors"`
	ParseErrors   int64         `json:"parse_errors"`

	// 当前状态
	Status    string `json:"status"` // running, stopped, error
	LastError string `json:"last_error"`
	IsRunning bool   `json:"is_running"`

	// 线程安全
	mutex sync.RWMutex `json:"-"`
}

// PipelineStats 管道统计信息
type PipelineStats struct {
	// 基础信息
	PipelineName string    `json:"pipeline_name"`
	StartTime    time.Time `json:"start_time"`

	// 处理统计
	ItemsReceived  int64 `json:"items_received"`
	ItemsProcessed int64 `json:"items_processed"`
	ItemsDropped   int64 `json:"items_dropped"`
	ItemsErrored   int64 `json:"items_errored"`

	// 性能统计
	AvgProcessingTime time.Duration `json:"avg_processing_time"`
	MaxProcessingTime time.Duration `json:"max_processing_time"`
	MinProcessingTime time.Duration `json:"min_processing_time"`

	// 错误统计
	ValidationErrors   int64 `json:"validation_errors"`
	DuplicateErrors    int64 `json:"duplicate_errors"`
	NotificationErrors int64 `json:"notification_errors"`

	// 当前状态
	Status    string `json:"status"`
	LastError string `json:"last_error"`
	IsActive  bool   `json:"is_active"`

	// 线程安全
	mutex sync.RWMutex `json:"-"`
}

// MiddlewareStats 中间件统计信息
type MiddlewareStats struct {
	// 基础信息
	MiddlewareName string    `json:"middleware_name"`
	StartTime      time.Time `json:"start_time"`

	// 处理统计
	RequestsProcessed int64 `json:"requests_processed"`
	RequestsBlocked   int64 `json:"requests_blocked"`
	RequestsModified  int64 `json:"requests_modified"`

	// 性能统计
	AvgProcessingTime time.Duration `json:"avg_processing_time"`
	MaxProcessingTime time.Duration `json:"max_processing_time"`
	MinProcessingTime time.Duration `json:"min_processing_time"`

	// 错误统计
	ProcessingErrors    int64 `json:"processing_errors"`
	ConfigurationErrors int64 `json:"configuration_errors"`

	// 当前状态
	Status    string `json:"status"`
	LastError string `json:"last_error"`
	IsEnabled bool   `json:"is_enabled"`

	// 线程安全
	mutex sync.RWMutex `json:"-"`
}

// NotificationStats 通知统计信息
type NotificationStats struct {
	// 基础信息
	ServiceName string    `json:"service_name"`
	StartTime   time.Time `json:"start_time"`

	// 消息统计
	MessagesSent    int64 `json:"messages_sent"`
	MessagesQueued  int64 `json:"messages_queued"`
	MessagesFailed  int64 `json:"messages_failed"`
	MessagesRetried int64 `json:"messages_retried"`

	// 性能统计
	AvgSendTime time.Duration `json:"avg_send_time"`
	MaxSendTime time.Duration `json:"max_send_time"`
	MinSendTime time.Duration `json:"min_send_time"`

	// 错误统计
	HttpErrors      map[int]int64 `json:"http_errors"`
	NetworkErrors   int64         `json:"network_errors"`
	RateLimitErrors int64         `json:"rate_limit_errors"`

	// 服务统计
	ServicesByPlatform map[string]int64 `json:"services_by_platform"`

	// 当前状态
	Status    string `json:"status"`
	LastError string `json:"last_error"`
	IsHealthy bool   `json:"is_healthy"`

	// 线程安全
	mutex sync.RWMutex `json:"-"`
}

// SystemStats 系统统计信息
type SystemStats struct {
	// 基础信息
	StartTime time.Time     `json:"start_time"`
	Uptime    time.Duration `json:"uptime"`

	// 内存统计
	MemoryUsage    int64 `json:"memory_usage"`
	MaxMemoryUsage int64 `json:"max_memory_usage"`

	// CPU统计
	CPUUsage    float64 `json:"cpu_usage"`
	MaxCPUUsage float64 `json:"max_cpu_usage"`

	// 网络统计
	NetworkBytesReceived int64 `json:"network_bytes_received"`
	NetworkBytesSent     int64 `json:"network_bytes_sent"`

	// 协程统计
	GoroutineCount    int `json:"goroutine_count"`
	MaxGoroutineCount int `json:"max_goroutine_count"`

	// 垃圾回收统计
	GCCount     uint32        `json:"gc_count"`
	GCTotalTime time.Duration `json:"gc_total_time"`

	// 连接统计
	ActiveConnections int `json:"active_connections"`
	MaxConnections    int `json:"max_connections"`

	// 当前状态
	Status    string `json:"status"`
	IsHealthy bool   `json:"is_healthy"`

	// 线程安全
	mutex sync.RWMutex `json:"-"`
}

// HealthCheck 健康检查结果
type HealthCheck struct {
	// 基础信息
	CheckTime time.Time `json:"check_time"`
	Component string    `json:"component"`

	// 检查结果
	IsHealthy bool   `json:"is_healthy"`
	Status    string `json:"status"`
	Message   string `json:"message"`

	// 性能指标
	ResponseTime time.Duration `json:"response_time"`

	// 错误信息
	Error string `json:"error,omitempty"`

	// 详细信息
	Details map[string]interface{} `json:"details,omitempty"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	// 时间戳
	Timestamp time.Time `json:"timestamp"`

	// 吞吐量
	RequestsPerSecond float64 `json:"requests_per_second"`
	ItemsPerSecond    float64 `json:"items_per_second"`

	// 响应时间
	P50ResponseTime time.Duration `json:"p50_response_time"`
	P95ResponseTime time.Duration `json:"p95_response_time"`
	P99ResponseTime time.Duration `json:"p99_response_time"`

	// 错误率
	ErrorRate   float64 `json:"error_rate"`
	TimeoutRate float64 `json:"timeout_rate"`

	// 资源使用
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage int64   `json:"memory_usage"`

	// 队列长度
	QueueLength int `json:"queue_length"`
}

// 统计信息管理器接口
type StatsManager interface {
	// 获取统计信息
	GetSpiderStats(name string) *SpiderStats
	GetPipelineStats(name string) *PipelineStats
	GetMiddlewareStats(name string) *MiddlewareStats
	GetNotificationStats() *NotificationStats
	GetRabbitMQStats() *RabbitMQStats
	GetSystemStats() *SystemStats

	// 更新统计信息
	UpdateSpiderStats(name string, stats *SpiderStats)
	UpdatePipelineStats(name string, stats *PipelineStats)
	UpdateMiddlewareStats(name string, stats *MiddlewareStats)

	// 健康检查
	PerformHealthCheck() []*HealthCheck

	// 性能指标
	GetPerformanceMetrics() *PerformanceMetrics

	// 重置统计
	ResetStats(component string)

	// 导出统计
	ExportStats() map[string]interface{}
}
