package middleware

import (
	"context"

	"go-monitor/internal/models"
	"go-monitor/pkg/logging"
)

// TLSFingerprintMiddleware TLS指纹中间件
type TLSFingerprintMiddleware struct {
	name     string
	priority int
	enabled  bool
	config   map[string]interface{}
	logger   logging.Logger
}

// NewTLSFingerprintMiddleware 创建TLS指纹中间件
func NewTLSFingerprintMiddleware(priority int, config map[string]interface{}) *TLSFingerprintMiddleware {
	middleware := &TLSFingerprintMiddleware{
		name:     "TLSFingerprintMiddleware",
		priority: priority,
		enabled:  true,
		config:   config,
		logger:   logging.GetLogger("middleware.tls_fingerprint"),
	}

	// 初始化TLS指纹管理器
	if err := middleware.initialize(); err != nil {
		middleware.logger.Error("TLS指纹中间件初始化失败", "error", err.Error())
		middleware.enabled = false
	}

	return middleware
}

// Name 返回中间件名称
func (m *TLSFingerprintMiddleware) Name() string {
	return m.name
}

// Priority 返回中间件优先级
func (m *TLSFingerprintMiddleware) Priority() int {
	return m.priority
}

// ProcessRequest 处理请求 - TLS指纹中间件基本不需要工作
func (m *TLSFingerprintMiddleware) ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	if !m.enabled {
		return req, nil
	}

	// TLS指纹中间件在请求阶段基本不需要工作
	// 指纹选择由底层的指纹管理器根据fingerprint字段自动处理

	return req, nil
}

// ProcessResponse 处理响应 - 检测风控状态码并设置重试标记
func (m *TLSFingerprintMiddleware) ProcessResponse(ctx context.Context, resp *models.Response) (bool, *models.Response, error) {
	if !m.enabled {
		return false, resp, nil
	}

	// 从配置中获取风控状态码
	severeStatusCodes := m.getSevereStatusCodes()
	moderateStatusCodes := m.getModerateStatusCodes()
	lightStatusCodes := m.getLightStatusCodes()

	// 检查是否为风控状态码
	needRetry := false
	riskLevel := ""

	if m.containsStatusCode(severeStatusCodes, resp.StatusCode) {
		riskLevel = "severe"
		needRetry = true
	} else if m.containsStatusCode(moderateStatusCodes, resp.StatusCode) {
		riskLevel = "moderate"
		needRetry = true
	} else if m.containsStatusCode(lightStatusCodes, resp.StatusCode) {
		riskLevel = "light"
		needRetry = true
	}

	if needRetry {
		// 设置风控检测标记（供底层系统使用）
		if resp.Metadata == nil {
			resp.Metadata = make(map[string]interface{})
		}
		resp.Metadata["tls_risk_detected"] = true
		resp.Metadata["tls_risk_level"] = riskLevel
		resp.Metadata["tls_risk_status_code"] = resp.StatusCode

		// 标记需要切换指纹
		resp.Metadata["fingerprint_switch_required"] = true

		m.logger.Debug("检测到风控状态码，建议切换指纹",
			"status_code", resp.StatusCode,
			"risk_level", riskLevel,
			"url", resp.URL)
	} else {
		// 清理风控标记
		if resp.Metadata != nil {
			delete(resp.Metadata, "tls_risk_detected")
			delete(resp.Metadata, "tls_risk_level")
			delete(resp.Metadata, "tls_risk_status_code")
			delete(resp.Metadata, "fingerprint_switch_required")
		}
	}

	return needRetry, resp, nil
}

// ProcessException 处理异常 - TLS指纹中间件不处理异常，由底层系统处理
func (m *TLSFingerprintMiddleware) ProcessException(ctx context.Context, err error, req *models.Request) (bool, error) {
	if !m.enabled {
		return false, err
	}

	// TLS指纹中间件不处理异常
	// 异常处理由底层的指纹管理器和HTTP客户端处理
	return false, err
}

// initialize 初始化TLS指纹中间件
func (m *TLSFingerprintMiddleware) initialize() error {
	// 检查是否启用
	if enabled, ok := m.config["enabled"].(bool); ok && !enabled {
		m.logger.Info("TLS指纹中间件已禁用")
		m.enabled = false
		return nil
	}

	m.logger.Info("TLS指纹中间件初始化完成 - 专注于风控状态码检测")
	return nil
}

// GetStats 获取TLS指纹中间件统计信息
func (m *TLSFingerprintMiddleware) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"name":        m.name,
		"enabled":     m.enabled,
		"priority":    m.priority,
		"description": "专注于风控状态码检测的TLS指纹中间件",
	}
}

// Close 关闭TLS指纹中间件
func (m *TLSFingerprintMiddleware) Close() error {
	m.logger.Info("关闭TLS指纹中间件")
	m.enabled = false
	return nil
}

// getSevereStatusCodes 获取严重风控状态码
func (m *TLSFingerprintMiddleware) getSevereStatusCodes() []int {
	if riskControl, exists := m.config["risk_control"].(map[string]interface{}); exists {
		if codes, exists := riskControl["severe_status_codes"].([]interface{}); exists {
			var result []int
			for _, code := range codes {
				if intCode, ok := code.(int); ok {
					result = append(result, intCode)
				}
			}
			return result
		}
	}
	// 默认严重风控状态码
	return []int{470, 471}
}

// getModerateStatusCodes 获取中等风控状态码
func (m *TLSFingerprintMiddleware) getModerateStatusCodes() []int {
	if riskControl, exists := m.config["risk_control"].(map[string]interface{}); exists {
		if codes, exists := riskControl["moderate_status_codes"].([]interface{}); exists {
			var result []int
			for _, code := range codes {
				if intCode, ok := code.(int); ok {
					result = append(result, intCode)
				}
			}
			return result
		}
	}
	// 默认中等风控状态码
	return []int{418}
}

// getLightStatusCodes 获取轻微风控状态码
func (m *TLSFingerprintMiddleware) getLightStatusCodes() []int {
	if riskControl, exists := m.config["risk_control"].(map[string]interface{}); exists {
		if codes, exists := riskControl["light_status_codes"].([]interface{}); exists {
			var result []int
			for _, code := range codes {
				if intCode, ok := code.(int); ok {
					result = append(result, intCode)
				}
			}
			return result
		}
	}
	// 默认轻微风控状态码
	return []int{403, 429}
}

// containsStatusCode 检查状态码是否在列表中
func (m *TLSFingerprintMiddleware) containsStatusCode(codes []int, statusCode int) bool {
	for _, code := range codes {
		if code == statusCode {
			return true
		}
	}
	return false
}
