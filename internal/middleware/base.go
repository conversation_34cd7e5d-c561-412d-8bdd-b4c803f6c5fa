package middleware

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"go-monitor/internal/models"
	"go-monitor/pkg/logging"
)

// Middleware 中间件接口 - 对应Python版本的BaseMiddleware
type Middleware interface {
	// ProcessRequest 处理请求
	ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error)

	// ProcessResponse 处理响应
	// 返回值: (需要重试?, 处理后的响应, 错误)
	ProcessResponse(ctx context.Context, resp *models.Response) (bool, *models.Response, error)

	// ProcessException 处理异常
	// 返回值: (需要重试?, 错误)
	ProcessException(ctx context.Context, err error, req *models.Request) (bool, error)

	// Name 返回中间件名称
	Name() string

	// Priority 返回中间件优先级
	Priority() int

	// Close 关闭中间件，清理资源
	Close() error
}

// BaseMiddleware 中间件基类 - 对应Python版本的BaseMiddleware
type BaseMiddleware struct {
	config   map[string]interface{}
	logger   logging.Logger
	name     string
	priority int
}

// NewBaseMiddleware 创建基础中间件
func NewBaseMiddleware(name string, priority int, config map[string]interface{}) *BaseMiddleware {

	return &BaseMiddleware{
		config:   config,
		logger:   logging.GetLogger("middleware." + name),
		name:     name,
		priority: priority,
	}
}

// ProcessRequest 默认请求处理 - 直接返回原请求
func (b *BaseMiddleware) ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	return req, nil
}

// ProcessResponse 默认响应处理 - 直接返回原响应
func (b *BaseMiddleware) ProcessResponse(ctx context.Context, resp *models.Response) (bool, *models.Response, error) {
	return false, resp, nil
}

// ProcessException 默认异常处理 - 不重试
func (b *BaseMiddleware) ProcessException(ctx context.Context, err error, req *models.Request) (bool, error) {
	return false, nil
}

// Name 返回中间件名称
func (b *BaseMiddleware) Name() string {
	return b.name
}

// Priority 返回中间件优先级
func (b *BaseMiddleware) Priority() int {
	return b.priority
}

// Close 默认关闭方法
func (b *BaseMiddleware) Close() error {
	b.logger.Debug(fmt.Sprintf("关闭中间件：%s", b.name))
	return nil
}

// GetConfig 获取配置值
func (b *BaseMiddleware) GetConfig(key string) interface{} {
	if b.config == nil {
		return nil
	}
	return b.config[key]
}

// GetConfigString 获取字符串配置
func (b *BaseMiddleware) GetConfigString(key string, defaultValue string) string {
	if val := b.GetConfig(key); val != nil {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return defaultValue
}

// GetConfigInt 获取整数配置
func (b *BaseMiddleware) GetConfigInt(key string, defaultValue int) int {
	if val := b.GetConfig(key); val != nil {
		if i, ok := val.(int); ok {
			return i
		}
		if f, ok := val.(float64); ok {
			return int(f)
		}
	}
	return defaultValue
}

// GetConfigBool 获取布尔配置
func (b *BaseMiddleware) GetConfigBool(key string, defaultValue bool) bool {
	if val := b.GetConfig(key); val != nil {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return defaultValue
}

// GetConfigDuration 获取时间段配置
func (b *BaseMiddleware) GetConfigDuration(key string, defaultValue time.Duration) time.Duration {
	if val := b.GetConfig(key); val != nil {
		switch v := val.(type) {
		case time.Duration:
			return v
		case int:
			return time.Duration(v) * time.Second
		case float64:
			return time.Duration(v) * time.Second
		case string:
			if d, err := time.ParseDuration(v); err == nil {
				return d
			}
		}
	}
	return defaultValue
}

// CalculateDelay 计算重试延迟 - 对应Python版本的_calculate_delay
func (b *BaseMiddleware) CalculateDelay() time.Duration {
	// 添加随机扰动，避免同时重试
	delay := time.Duration(rand.Float64()*(1.8-0.2)+0.2) * time.Second
	return delay
}

// GetStats 获取基础统计信息
func (b *BaseMiddleware) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"name":     b.name,
		"priority": b.priority,
	}
}
