package middleware

import (
	"context"
	"fmt"
	"maps"
	"strings"
	"sync"

	"go-monitor/internal/models"
)

// ============================================================================
// 工具函数
// ============================================================================

// parseCookieString 解析cookie字符串为键值对
// 返回: name, value, success
func parseCookieString(cookieStr string) (string, string, bool) {
	if !strings.Contains(cookieStr, "=") {
		return "", "", false
	}

	parts := strings.SplitN(cookieStr, "=", 2)
	if len(parts) != 2 {
		return "", "", false
	}

	name := strings.TrimSpace(parts[0])
	value := strings.TrimSpace(parts[1])

	if name == "" {
		return "", "", false
	}

	return name, value, true
}

// parseCookieStrings 批量解析cookie字符串
func parseCookieStrings(cookieStrings []string) map[string]string {
	cookies := make(map[string]string, len(cookieStrings))
	for _, cookieStr := range cookieStrings {
		if name, value, ok := parseCookieString(cookieStr); ok {
			cookies[name] = value
		}
	}
	return cookies
}

// assertStringArray 安全地将interface{}转换为字符串数组
func assertStringArray(value interface{}) ([]string, bool) {
	switch v := value.(type) {
	case []string:
		return v, true
	case []interface{}:
		result := make([]string, 0, len(v))
		for _, item := range v {
			if str, ok := item.(string); ok {
				result = append(result, str)
			}
		}
		return result, len(result) > 0
	default:
		return nil, false
	}
}

// ============================================================================
// 类型定义
// ============================================================================

// CookiesMiddleware Cookie中间件 - 对应Python版本的cookies.py
type CookiesMiddleware struct {
	*BaseMiddleware

	// 配置
	config CookiesConfig

	// 运行时状态
	// 域名cookies映射
	domainCookies map[string]map[string]string

	mu sync.RWMutex
}

// CookieGroup Cookie组配置
type CookieGroup struct {
	Enabled bool     `yaml:"enabled"`
	Domain  string   `yaml:"domain"`
	Cookies []string `yaml:"cookies"`
}

// CookiesConfig Cookie中间件配置
type CookiesConfig struct {
	Enabled      bool                   `yaml:"enabled"`
	CookieGroups map[string]CookieGroup `yaml:"cookie_groups"`
	Persistence  PersistenceConfig      `yaml:"persistence"`
}

// PersistenceConfig 持久化配置
type PersistenceConfig struct {
	Enabled     bool     `yaml:"enabled"`
	SpiderTypes []string `yaml:"spider_types"`
	CookieNames []string `yaml:"cookie_names"`
}

// ============================================================================
// 构造函数和配置
// ============================================================================

// NewCookiesMiddleware 创建Cookie中间件
func NewCookiesMiddleware(priority int, config map[string]interface{}) *CookiesMiddleware {
	base := NewBaseMiddleware("cookies", priority, config)

	// 解析enabled配置
	enabled := true
	if val, ok := config["enabled"].(bool); ok {
		enabled = val
	}

	// 创建中间件实例
	middleware := &CookiesMiddleware{
		BaseMiddleware: base,
		config: CookiesConfig{
			Enabled:      enabled,
			CookieGroups: make(map[string]CookieGroup),
			Persistence: PersistenceConfig{
				Enabled:     false,
				SpiderTypes: []string{},
				CookieNames: []string{},
			},
		},
		domainCookies: make(map[string]map[string]string),
	}

	// 如果禁用，直接返回
	if !enabled {
		middleware.logger.Info("Cookie中间件已禁用")
		return middleware
	}

	// 解析配置
	middleware.parseConfig(config)

	// 初始化cookies
	middleware.initCookies()

	middleware.logger.Info(fmt.Sprintf("Cookie中间件初始化完成，启用：%t，Cookie组数量：%d",
		enabled, len(middleware.config.CookieGroups)))

	return middleware
}

// parseConfig 解析配置
func (c *CookiesMiddleware) parseConfig(config map[string]interface{}) {
	// 解析Cookie组配置
	if cookieGroups, ok := config["cookie_groups"].(map[string]interface{}); ok {
		parseCookieGroups(c.config.CookieGroups, cookieGroups)
	}

	// 解析持久化配置
	if persistence, ok := config["persistence"].(map[string]interface{}); ok {
		parsePersistenceConfig(&c.config.Persistence, persistence)
	}
}

// parseCookieGroups 解析Cookie组配置
func parseCookieGroups(cookieGroups map[string]CookieGroup, cookieGroupsConfig map[string]interface{}) {
	for groupName, groupConfig := range cookieGroupsConfig {
		groupMap, ok := groupConfig.(map[string]interface{})
		if !ok {
			continue
		}

		group := CookieGroup{Enabled: true}

		// 解析enabled字段
		if enabled, ok := groupMap["enabled"].(bool); ok {
			group.Enabled = enabled
		}

		// 解析domain字段
		if domain, ok := groupMap["domain"].(string); ok {
			group.Domain = domain
		}

		// 解析cookies字段，使用工具函数
		if cookiesValue, exists := groupMap["cookies"]; exists {
			if cookieStrings, ok := assertStringArray(cookiesValue); ok {
				group.Cookies = cookieStrings
			}
		}

		cookieGroups[groupName] = group
	}
}

// parsePersistenceConfig 解析持久化配置
func parsePersistenceConfig(persistence *PersistenceConfig, persistenceConfig map[string]interface{}) {
	// 解析enabled字段
	if enabled, ok := persistenceConfig["enabled"].(bool); ok {
		persistence.Enabled = enabled
	}

	// 解析spider_types字段，使用工具函数
	if spiderTypesValue, exists := persistenceConfig["spider_types"]; exists {
		if spiderTypes, ok := assertStringArray(spiderTypesValue); ok {
			persistence.SpiderTypes = spiderTypes
		}
	}

	// 解析cookie_names字段，使用工具函数
	if cookieNamesValue, exists := persistenceConfig["cookie_names"]; exists {
		if cookieNames, ok := assertStringArray(cookieNamesValue); ok {
			persistence.CookieNames = cookieNames
		}
	}
}

// initCookies 初始化cookies
func (c *CookiesMiddleware) initCookies() {
	c.loadCookieGroups()
}

// loadCookieGroups 加载配置中的cookie组
func (c *CookiesMiddleware) loadCookieGroups() {
	if len(c.config.CookieGroups) == 0 {
		c.logger.Debug("未配置cookie组")
		return
	}

	for groupName, group := range c.config.CookieGroups {
		if !c.validateCookieGroup(groupName, group) {
			continue
		}

		// 使用工具函数解析cookies
		cookiesDict := parseCookieStrings(group.Cookies)

		// 保存域名cookies
		if _, exists := c.domainCookies[group.Domain]; exists {
			maps.Copy(c.domainCookies[group.Domain], cookiesDict)
		} else {
			c.domainCookies[group.Domain] = cookiesDict
		}

		c.logger.Info(fmt.Sprintf("从配置加载域名cookies：%s，Cookie数量：%d，组：%s",
			group.Domain, len(cookiesDict), groupName))
	}
}

// validateCookieGroup 验证cookie组配置
func (c *CookiesMiddleware) validateCookieGroup(groupName string, group CookieGroup) bool {
	if !group.Enabled {
		c.logger.Debug(fmt.Sprintf("Cookie组已禁用：%s", groupName))
		return false
	}

	if group.Domain == "" {
		c.logger.Warn(fmt.Sprintf("cookie组没有指定域名：%s", groupName))
		return false
	}

	if len(group.Cookies) == 0 {
		c.logger.Debug(fmt.Sprintf("Cookie组没有定义cookies：%s", groupName))
		return false
	}

	return true
}

// ============================================================================
// 核心处理方法
// ============================================================================

// ProcessRequest 处理请求，添加cookies
func (c *CookiesMiddleware) ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	if !c.config.Enabled || req.URL == "" {
		return req, nil
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	// 检查请求元数据中指定的cookie组
	cookieGroups := req.GetMetadata("cookies")
	if cookieGroups == nil {
		return req, nil
	}

	// 使用工具函数解析cookie组数组
	groupNames, ok := assertStringArray(cookieGroups)
	if !ok {
		c.logger.Debug(fmt.Sprintf("cookie组类型不匹配，期望字符串数组，实际类型：%T", cookieGroups))
		return req, nil
	}

	// 尝试为每个cookie组添加cookies
	for _, groupName := range groupNames {
		cookies := c.getCookiesForGroup(groupName)
		if len(cookies) > 0 {
			// 添加cookies到请求
			if req.Cookies == nil {
				req.Cookies = make(map[string]string)
			}
			maps.Copy(req.Cookies, cookies)

			c.logger.Debug(fmt.Sprintf("通过cookie组添加了cookies：%s，数量：%d", groupName, len(cookies)))
			return req, nil
		}
		c.logger.Debug(fmt.Sprintf("指定的cookie组不存在或为空：%s", groupName))
	}

	return req, nil
}

// ProcessResponse 处理响应，检测Token过期并管理cookies
func (c *CookiesMiddleware) ProcessResponse(ctx context.Context, resp *models.Response) (bool, *models.Response, error) {
	if !c.config.Enabled {
		return false, resp, nil
	}

	// 获取spider类型
	spiderType := c.getSpiderType(resp)

	// 检查AliExpress的token过期情况，需要重试
	if spiderType == "aliexpress" && c.isTokenExpired(string(resp.Body)) {
		c.logger.Debug("检测到AliExpress Token过期，准备重试", "platform", spiderType)
		// if resp.Request.RetryCount == 3 {
		// 	resp.Request.RetryCount = 2
		// }

		// 更新cookie组（如果有新cookies）
		// c.updateCookieGroupsFromResponse(resp)

		c.logger.Debug("AliExpress Token过期，请求重试", "platform", spiderType)
		return true, resp, nil
	}

	// 检查响应中是否有新的cookies需要保存到cookie组（主要用于Amazon）
	c.updateCookieGroupsFromResponse(resp)

	return false, resp, nil
}

// ============================================================================
// 辅助方法
// ============================================================================

// getSpiderType 获取spider类型
func (c *CookiesMiddleware) getSpiderType(resp *models.Response) string {
	if resp.SpiderType != "" {
		return resp.SpiderType
	}
	if resp.Request != nil {
		return resp.Request.SpiderType
	}
	return ""
}

// isTokenExpired 检查是否Token过期（主要用于AliExpress）
func (c *CookiesMiddleware) isTokenExpired(content string) bool {
	// 检查AliExpress特定的Token过期标识
	tokenExpiredPatterns := []string{
		"FAIL_SYS_USER_VALIDATE", // AliExpress token过期
		"token_invalid",          // 通用token无效
		"token_expired",          // 通用token过期
	}

	for _, pattern := range tokenExpiredPatterns {
		if strings.Contains(content, pattern) {
			return true
		}
	}

	return false
}

// updateCookieGroupsFromResponse 从响应中更新cookie组配置
func (c *CookiesMiddleware) updateCookieGroupsFromResponse(resp *models.Response) {
	// 提取响应中的cookies
	cookies := c.extractCookiesFromResponse(resp)
	if len(cookies) == 0 || resp.Request == nil {
		return
	}

	// 获取cookie组名
	cookieGroups := resp.Request.GetMetadata("cookies")
	if cookieGroups == nil {
		return
	}

	// 使用工具函数解析cookie组数组
	groupNames, ok := assertStringArray(cookieGroups)
	if !ok {
		return
	}

	// 收集需要更新的token
	updates := c.collectTokenUpdates(groupNames, cookies)
	if len(updates) == 0 {
		return
	}

	// 批量更新（只需要一次写锁）
	c.mu.Lock()
	defer c.mu.Unlock()

	spiderType := c.getSpiderType(resp)
	updated := false

	for _, update := range updates {
		if c.updateCookieGroupToken(update.groupName, update.tokenName, update.tokenValue) {
			updated = true
			c.logger.Debug(fmt.Sprintf("从响应更新cookie组token：%s，token：%s，长度：%d",
				update.groupName, update.tokenName, len(update.tokenValue)), "platform", spiderType)
		}
	}

	if updated {
		c.logger.Debug(fmt.Sprintf("cookie组配置已更新，共%d个组", len(groupNames)), "platform", spiderType)
	}
}

// tokenUpdate 表示一个token更新操作
type tokenUpdate struct {
	groupName  string
	tokenName  string
	tokenValue string
}

// collectTokenUpdates 收集需要更新的tokens
func (c *CookiesMiddleware) collectTokenUpdates(groupNames []string, cookies map[string]string) []tokenUpdate {
	var updates []tokenUpdate

	for _, groupName := range groupNames {
		for _, tokenName := range c.config.Persistence.CookieNames {
			if tokenValue, exists := cookies[tokenName]; exists {
				updates = append(updates, tokenUpdate{
					groupName:  groupName,
					tokenName:  tokenName,
					tokenValue: tokenValue,
				})
			}
		}
	}

	return updates
}

// ProcessException 处理异常（继承基类默认行为）
func (c *CookiesMiddleware) ProcessException(ctx context.Context, err error, req *models.Request) (bool, error) {
	return c.BaseMiddleware.ProcessException(ctx, err, req)
}

// getCookiesForGroup 根据cookie组名称获取cookies
func (c *CookiesMiddleware) getCookiesForGroup(groupName string) map[string]string {
	group, exists := c.config.CookieGroups[groupName]
	if !exists || !group.Enabled {
		return make(map[string]string)
	}

	// 使用工具函数解析cookies
	return parseCookieStrings(group.Cookies)
}

// GetStats 获取统计信息（重写基类方法以包含Cookie特定信息）
func (c *CookiesMiddleware) GetStats() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	stats := c.BaseMiddleware.GetStats()
	stats["enabled"] = c.config.Enabled
	stats["domain_count"] = len(c.domainCookies)
	stats["persistence_enabled"] = c.config.Persistence.Enabled

	return stats
}

// extractCookiesFromResponse 从响应头中提取cookies
func (c *CookiesMiddleware) extractCookiesFromResponse(resp *models.Response) map[string]string {
	cookies := make(map[string]string)

	// 从Set-Cookie响应头中解析cookies
	if setCookieHeaders, exists := resp.Headers["Set-Cookie"]; exists {
		for _, setCookie := range setCookieHeaders {
			name, value := c.parseCookieHeader(setCookie)
			if name != "" && value != "-" {
				cookies[name] = value
			}
		}
	}

	return cookies
}

// parseCookieHeader 解析单个Set-Cookie头
func (c *CookiesMiddleware) parseCookieHeader(setCookie string) (string, string) {
	// 简单解析Set-Cookie头：name=value; 其他属性...
	parts := strings.Split(setCookie, ";")
	if len(parts) == 0 {
		return "", ""
	}

	// 获取第一部分：name=value
	nameValue := strings.TrimSpace(parts[0])
	equalIndex := strings.Index(nameValue, "=")
	if equalIndex == -1 {
		return "", ""
	}

	name := strings.TrimSpace(nameValue[:equalIndex])
	value := strings.TrimSpace(nameValue[equalIndex+1:])

	// 移除cookie值首尾的双引号（如果存在）
	if len(value) >= 2 && value[0] == '"' && value[len(value)-1] == '"' {
		value = value[1 : len(value)-1]
	}

	return name, value
}

// updateCookieGroupToken 更新cookie组中的特定token
func (c *CookiesMiddleware) updateCookieGroupToken(groupName, tokenName, tokenValue string) bool {
	group, exists := c.config.CookieGroups[groupName]
	if !exists || !group.Enabled {
		return false
	}

	// 查找并更新现有的token
	for i, cookieStr := range group.Cookies {
		if strings.Contains(cookieStr, tokenName+"=") {
			// 找到现有的token，更新它
			parts := strings.SplitN(cookieStr, "=", 2)
			if len(parts) == 2 && strings.TrimSpace(parts[0]) == tokenName {
				group.Cookies[i] = tokenName + "=" + tokenValue
				c.config.CookieGroups[groupName] = group
				c.logger.Debug(fmt.Sprintf("更新cookie组token：%s，token：%s，新值长度：%d",
					groupName, tokenName, len(tokenValue)))
				return true
			}
		}
	}

	// 如果没有找到现有的token，添加新的
	group.Cookies = append(group.Cookies, tokenName+"="+tokenValue)
	c.config.CookieGroups[groupName] = group
	c.logger.Debug(fmt.Sprintf("添加新cookie组token：%s，token：%s，值长度：%d",
		groupName, tokenName, len(tokenValue)))
	return true
}
