package middleware

import (
	"context"
	"fmt"
	"math/rand"
	"strings"

	"go-monitor/internal/models"
)

// HeadersMiddleware 请求头中间件 - 对应Python版本的headers.py
type HeadersMiddleware struct {
	*BaseMiddleware

	// 从配置文件读取的参数
	defaultHeaders map[string]string

	// 设备配置 - 从配置文件读取
	androidConfig AndroidConfig
	iosConfig     IOSConfig
	desktopConfig DesktopConfig

	// 默认参数
	defaultSpiderType string
	country           string
}

// AndroidConfig Android设备配置
type AndroidConfig struct {
	Versions []string `yaml:"versions"`
	Devices  []string `yaml:"devices"`
	Builds   []string `yaml:"builds"`
}

// IOSConfig iOS设备配置
type IOSConfig struct {
	Versions []string `yaml:"versions"`
	Devices  []string `yaml:"devices"`
}

// DesktopConfig 桌面设备配置
type DesktopConfig struct {
	Windows struct {
		OSVersions    []string `yaml:"os_versions"`
		Architectures []string `yaml:"architectures"`
	} `yaml:"windows"`
	MacOS struct {
		OSVersions []string `yaml:"os_versions"`
	} `yaml:"macos"`
	Linux struct {
		OSVersions []string `yaml:"os_versions"`
	} `yaml:"linux"`
	Browsers map[string][]string `yaml:"browsers"`
}

// NewHeadersMiddleware 创建Headers中间件
func NewHeadersMiddleware(priority int, config map[string]interface{}) *HeadersMiddleware {
	base := NewBaseMiddleware("headers", priority, config)

	middleware := &HeadersMiddleware{
		BaseMiddleware: base,
		defaultHeaders: make(map[string]string),
	}

	// 从配置读取默认请求头
	if headers, ok := config["request_headers"].(map[string]interface{}); ok {
		for key, value := range headers {
			if str, ok := value.(string); ok {
				middleware.defaultHeaders[key] = str
			}
		}
	}

	// 从配置读取设备配置
	if android, ok := config["android"].(map[string]interface{}); ok {
		middleware.parseAndroidConfig(android)
	}
	if ios, ok := config["ios"].(map[string]interface{}); ok {
		middleware.parseIOSConfig(ios)
	}
	if desktop, ok := config["desktop"].(map[string]interface{}); ok {
		middleware.parseDesktopConfig(desktop)
	}

	if country, ok := config["country"].(string); ok {
		middleware.country = country
	} else {
		middleware.country = "US" // 最小必要默认值
	}

	middleware.logger.Info(fmt.Sprintf("Headers中间件已启用，国家：%s", middleware.country))

	return middleware
}

// parseAndroidConfig 解析Android配置
func (h *HeadersMiddleware) parseAndroidConfig(config map[string]interface{}) {
	if versions, ok := config["versions"].([]interface{}); ok {
		h.androidConfig.Versions = make([]string, len(versions))
		for i, v := range versions {
			if str, ok := v.(string); ok {
				h.androidConfig.Versions[i] = str
			}
		}
	}
	if devices, ok := config["devices"].([]interface{}); ok {
		h.androidConfig.Devices = make([]string, len(devices))
		for i, v := range devices {
			if str, ok := v.(string); ok {
				h.androidConfig.Devices[i] = str
			}
		}
	}
	if builds, ok := config["builds"].([]interface{}); ok {
		h.androidConfig.Builds = make([]string, len(builds))
		for i, v := range builds {
			if str, ok := v.(string); ok {
				h.androidConfig.Builds[i] = str
			}
		}
	}
}

// parseIOSConfig 解析iOS配置
func (h *HeadersMiddleware) parseIOSConfig(config map[string]interface{}) {
	if versions, ok := config["versions"].([]interface{}); ok {
		h.iosConfig.Versions = make([]string, len(versions))
		for i, v := range versions {
			if str, ok := v.(string); ok {
				h.iosConfig.Versions[i] = str
			}
		}
	}
	if devices, ok := config["devices"].([]interface{}); ok {
		h.iosConfig.Devices = make([]string, len(devices))
		for i, v := range devices {
			if str, ok := v.(string); ok {
				h.iosConfig.Devices[i] = str
			}
		}
	}
}

// parseDesktopConfig 解析Desktop配置
func (h *HeadersMiddleware) parseDesktopConfig(config map[string]interface{}) {
	if windows, ok := config["windows"].(map[string]interface{}); ok {
		if osVersions, ok := windows["os_versions"].([]interface{}); ok {
			h.desktopConfig.Windows.OSVersions = make([]string, len(osVersions))
			for i, v := range osVersions {
				if str, ok := v.(string); ok {
					h.desktopConfig.Windows.OSVersions[i] = str
				}
			}
		}
		if architectures, ok := windows["architectures"].([]interface{}); ok {
			h.desktopConfig.Windows.Architectures = make([]string, len(architectures))
			for i, v := range architectures {
				if str, ok := v.(string); ok {
					h.desktopConfig.Windows.Architectures[i] = str
				}
			}
		}
	}

	if macos, ok := config["macos"].(map[string]interface{}); ok {
		if osVersions, ok := macos["os_versions"].([]interface{}); ok {
			h.desktopConfig.MacOS.OSVersions = make([]string, len(osVersions))
			for i, v := range osVersions {
				if str, ok := v.(string); ok {
					h.desktopConfig.MacOS.OSVersions[i] = str
				}
			}
		}
	}

	if linux, ok := config["linux"].(map[string]interface{}); ok {
		if osVersions, ok := linux["os_versions"].([]interface{}); ok {
			h.desktopConfig.Linux.OSVersions = make([]string, len(osVersions))
			for i, v := range osVersions {
				if str, ok := v.(string); ok {
					h.desktopConfig.Linux.OSVersions[i] = str
				}
			}
		}
	}

	if browsers, ok := config["browsers"].(map[string]interface{}); ok {
		h.desktopConfig.Browsers = make(map[string][]string)
		for browserName, versionsInterface := range browsers {
			if versions, ok := versionsInterface.([]interface{}); ok {
				browserVersions := make([]string, len(versions))
				for i, v := range versions {
					if str, ok := v.(string); ok {
						browserVersions[i] = str
					}
				}
				h.desktopConfig.Browsers[browserName] = browserVersions
			}
		}
	}
}

// generateAndroidUA 生成Android User-Agent
func (h *HeadersMiddleware) generateAndroidUA() string {
	if len(h.androidConfig.Versions) == 0 || len(h.androidConfig.Devices) == 0 || len(h.androidConfig.Builds) == 0 {
		// 如果配置不完整，返回基础UA
		return "Mozilla/5.0 (Linux; Android 12; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36"
	}

	version := h.androidConfig.Versions[rand.Intn(len(h.androidConfig.Versions))]
	device := h.androidConfig.Devices[rand.Intn(len(h.androidConfig.Devices))]
	build := h.androidConfig.Builds[rand.Intn(len(h.androidConfig.Builds))]

	return fmt.Sprintf("Mozilla/5.0 (Linux; Android %s; %s Build/%s) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
		version, device, build)
}

// generateIOSUA 生成iOS User-Agent
func (h *HeadersMiddleware) generateIOSUA() string {
	if len(h.iosConfig.Versions) == 0 || len(h.iosConfig.Devices) == 0 {
		// 如果配置不完整，返回基础UA
		return "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1"
	}

	version := h.iosConfig.Versions[rand.Intn(len(h.iosConfig.Versions))]
	device := h.iosConfig.Devices[rand.Intn(len(h.iosConfig.Devices))]

	// 将版本号中的点替换为下划线
	versionFormatted := ""
	for _, char := range version {
		if char == '.' {
			versionFormatted += "_"
		} else {
			versionFormatted += string(char)
		}
	}

	return fmt.Sprintf("Mozilla/5.0 (%s; CPU iPhone OS %s like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
		device, versionFormatted)
}

// generateDesktopUA 生成桌面版User-Agent
func (h *HeadersMiddleware) generateDesktopUA() string {
	// 检查配置是否完整
	if len(h.desktopConfig.Windows.OSVersions) == 0 {
		// 如果配置不完整，返回基础UA
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}

	// 随机选择操作系统
	osTypes := []string{}
	if len(h.desktopConfig.Windows.OSVersions) > 0 {
		osTypes = append(osTypes, "windows")
	}
	if len(h.desktopConfig.MacOS.OSVersions) > 0 {
		osTypes = append(osTypes, "macos")
	}
	if len(h.desktopConfig.Linux.OSVersions) > 0 {
		osTypes = append(osTypes, "linux")
	}

	if len(osTypes) == 0 {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}

	osType := osTypes[rand.Intn(len(osTypes))]

	// 随机选择浏览器
	browsers := []string{}
	for browser := range h.desktopConfig.Browsers {
		browsers = append(browsers, browser)
	}
	if len(browsers) == 0 {
		browsers = []string{"chrome"}
	}
	browser := browsers[rand.Intn(len(browsers))]

	browserVersions := h.desktopConfig.Browsers[browser]
	if len(browserVersions) == 0 {
		browserVersions = []string{"120.0.0.0"}
	}
	browserVersion := browserVersions[rand.Intn(len(browserVersions))]

	switch osType {
	case "windows":
		osVersion := h.desktopConfig.Windows.OSVersions[rand.Intn(len(h.desktopConfig.Windows.OSVersions))]
		architecture := "Win64; x64"
		if len(h.desktopConfig.Windows.Architectures) > 0 {
			architecture = h.desktopConfig.Windows.Architectures[rand.Intn(len(h.desktopConfig.Windows.Architectures))]
		}

		switch browser {
		case "chrome":
			return fmt.Sprintf("Mozilla/5.0 (%s; %s) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",
				osVersion, architecture, browserVersion)
		case "firefox":
			return fmt.Sprintf("Mozilla/5.0 (%s; rv:%s) Gecko/20100101 Firefox/%s",
				osVersion, browserVersion, browserVersion)
		case "edge":
			return fmt.Sprintf("Mozilla/5.0 (%s; %s) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36 Edg/%s",
				osVersion, architecture, browserVersion, browserVersion)
		}

	case "macos":
		osVersion := h.desktopConfig.MacOS.OSVersions[rand.Intn(len(h.desktopConfig.MacOS.OSVersions))]

		switch browser {
		case "chrome":
			return fmt.Sprintf("Mozilla/5.0 (%s) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",
				osVersion, browserVersion)
		case "safari":
			return fmt.Sprintf("Mozilla/5.0 (%s) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/%s Safari/605.1.15",
				osVersion, browserVersion)
		case "firefox":
			return fmt.Sprintf("Mozilla/5.0 (%s; rv:%s) Gecko/20100101 Firefox/%s",
				osVersion, browserVersion, browserVersion)
		}

	case "linux":
		osVersion := h.desktopConfig.Linux.OSVersions[rand.Intn(len(h.desktopConfig.Linux.OSVersions))]

		switch browser {
		case "chrome":
			return fmt.Sprintf("Mozilla/5.0 (%s) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",
				osVersion, browserVersion)
		case "firefox":
			return fmt.Sprintf("Mozilla/5.0 (%s; rv:%s) Gecko/20100101 Firefox/%s",
				osVersion, browserVersion, browserVersion)
		}
	}

	// 默认Chrome UA
	return fmt.Sprintf("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",
		browserVersion)
}

// generateUserAgent 生成随机User-Agent
func (h *HeadersMiddleware) generateUserAgent() string {
	// 检查配置，决定设备类型
	hasAndroid := len(h.androidConfig.Versions) > 0
	hasIOS := len(h.iosConfig.Versions) > 0
	hasDesktop := len(h.desktopConfig.Windows.OSVersions) > 0 || len(h.desktopConfig.MacOS.OSVersions) > 0

	if !hasAndroid && !hasIOS && !hasDesktop {
		// 如果都没有配置，返回基础UA
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}

	// 根据配置的设备类型随机选择
	deviceTypes := []string{}
	if hasAndroid {
		deviceTypes = append(deviceTypes, "android")
	}
	if hasIOS {
		deviceTypes = append(deviceTypes, "ios")
	}
	if hasDesktop {
		deviceTypes = append(deviceTypes, "desktop")
	}

	deviceType := deviceTypes[rand.Intn(len(deviceTypes))]

	switch deviceType {
	case "android":
		return h.generateAndroidUA()
	case "ios":
		return h.generateIOSUA()
	case "desktop":
		return h.generateDesktopUA()
	default:
		return h.generateDesktopUA()
	}
}

// generatePopmartUA 生成PopMart特定User-Agent
func (h *HeadersMiddleware) generatePopmartUA(appVersion string) string {
	if len(h.iosConfig.Versions) == 0 {
		return fmt.Sprintf("POPGlobalClient/%s (iPhone; iOS 15.6.1; Scale/2.00)", appVersion)
	}

	version := strings.ReplaceAll(h.iosConfig.Versions[rand.Intn(len(h.iosConfig.Versions))], "_", ".")
	return fmt.Sprintf("POPGlobalClient/%s (iPhone; iOS %s; Scale/2.00)", appVersion, version)
}

// ProcessRequest 处理请求，添加必要的请求头
func (h *HeadersMiddleware) ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	if req.Headers == nil {
		req.Headers = make(map[string]string)
		// 应用默认请求头
		for key, value := range h.defaultHeaders {
			if req.GetHeader(key) == "" {
				req.SetHeader(key, value)
			}
		}
	}

	// 获取爬虫类型
	spiderType := req.GetMetadataString("spider_type", req.SpiderType)
	if spiderType == "" {
		spiderType = h.defaultSpiderType
	}

	// 设置User-Agent
	if req.GetHeader("User-Agent") == "" {
		var userAgent string

		switch spiderType {
		case "popmart":
			userAgent = h.generateDesktopUA()
		case "amazon":
			userAgent = h.generateDesktopUA() // Amazon主要使用桌面版
		case "amazon-wishlist":
			userAgent = h.generateDesktopUA() // Amazon主要使用桌面版
		case "aliexpress":
			userAgent = h.generateAndroidUA()
		default:
			userAgent = h.generateUserAgent()
		}

		req.SetHeader("User-Agent", userAgent)
	}

	h.logger.Debug(fmt.Sprintf("请求头处理完成：%s", req.URL), "platform", spiderType)

	return req, nil
}
