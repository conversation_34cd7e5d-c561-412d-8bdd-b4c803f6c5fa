package middleware

import (
	"context"
	"fmt"
	"sort"
	"sync"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/pkg/logging"
)

// Manager 中间件管理器 - 负责加载和管理所有中间件
type Manager struct {
	middlewares []Middleware
	logger      logging.Logger
	mu          sync.RWMutex
	initialized bool
}

// NewManager 创建新的中间件管理器
func NewManager() *Manager {
	return &Manager{
		logger:      logging.GetLogger("middleware.manager"),
		middlewares: make([]Middleware, 0),
	}
}

// LoadMiddlewares 加载所有中间件 - 直接使用MiddlewareConfig，无需转换
func (m *Manager) LoadMiddlewares(middlewareConfigs map[string]config.MiddlewareConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.initialized {
		m.logger.Debug("中间件已初始化，跳过重复加载")
		return nil
	}

	// 如果没有配置，直接返回（允许空配置）
	if len(middlewareConfigs) == 0 {
		m.logger.Info("中间件配置为空，跳过中间件加载")
		m.initialized = true
		return nil
	}

	// 直接使用传入的中间件配置，无需转换
	for name, config := range middlewareConfigs {

		middleware, err := m.createMiddleware(name, config)
		if err != nil {
			m.logger.Error(fmt.Sprintf("创建中间件 %s 失败：%s", name, err.Error()))
			continue
		}

		m.middlewares = append(m.middlewares, middleware)
		m.logger.Debug(fmt.Sprintf("加载中间件 %s 成功，优先级：%d", name, middleware.Priority()))
	}

	// 按优先级排序
	sort.Slice(m.middlewares, func(i, j int) bool {
		return m.middlewares[i].Priority() < m.middlewares[j].Priority()
	})

	// 记录加载结果
	middlewareNames := make([]string, len(m.middlewares))
	for i, mw := range m.middlewares {
		middlewareNames[i] = mw.Name()
	}

	m.logger.Info(fmt.Sprintf("中间件加载完成，数量：%d，中间件：%v", len(m.middlewares), middlewareNames))

	m.initialized = true
	return nil
}

// ProcessRequest 处理请求 - 对应Python版本的process_request
func (m *Manager) ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if len(m.middlewares) == 0 {
		return req, nil
	}

	processedReq := req

	// 按优先级顺序处理请求
	for _, middleware := range m.middlewares {
		var err error
		processedReq, err = middleware.ProcessRequest(ctx, processedReq)
		if err != nil {
			m.logger.Error(fmt.Sprintf("中间件 %s 处理请求失败：%s", middleware.Name(), err.Error()),
				"platform", req.SpiderType)
			return nil, fmt.Errorf("middleware %s process request failed: %w", middleware.Name(), err)
		}

		// 如果中间件返回nil，表示请求应该被跳过
		if processedReq == nil {
			m.logger.Info(fmt.Sprintf("请求被中间件 %s 跳过", middleware.Name()),
				"platform", req.SpiderType)
			return nil, nil
		}
	}

	return processedReq, nil
}

// ProcessResponse 处理响应 - 对应Python版本的process_response
func (m *Manager) ProcessResponse(ctx context.Context, resp *models.Response) (bool, *models.Response, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if len(m.middlewares) == 0 {
		return false, resp, nil
	}

	processedResp := resp

	// 按优先级逆序处理响应
	for i := len(m.middlewares) - 1; i >= 0; i-- {
		middleware := m.middlewares[i]

		needRetry, newResp, err := middleware.ProcessResponse(ctx, processedResp)
		if err != nil {
			m.logger.Error(fmt.Sprintf("中间件 %s 处理响应失败：%s", middleware.Name(), err.Error()),
				"platform", resp.SpiderType)
			return false, resp, fmt.Errorf("middleware %s process response failed: %w", middleware.Name(), err)
		}

		if needRetry {
			m.logger.Debug(fmt.Sprintf("中间件 %s 请求重试", middleware.Name()),
				"platform", resp.SpiderType)
			return true, newResp, nil
		}

		processedResp = newResp
	}

	return false, processedResp, nil
}

// ProcessException 处理异常 - 对应Python版本的process_exception
func (m *Manager) ProcessException(ctx context.Context, err error, req *models.Request) (bool, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if len(m.middlewares) == 0 {
		return false, nil
	}

	// 处理异常
	for _, middleware := range m.middlewares {
		needRetry, processedErr := middleware.ProcessException(ctx, err, req)
		if processedErr != nil {
			m.logger.Error(fmt.Sprintf("中间件 %s 处理异常失败，原始错误：%s，处理错误：%s",
				middleware.Name(), err.Error(), processedErr.Error()), "platform", req.SpiderType)
			return false, fmt.Errorf("middleware %s process exception failed: %w", middleware.Name(), processedErr)
		}

		if needRetry {
			m.logger.Info(fmt.Sprintf("中间件 %s 异常处理要求重试，错误：%s",
				middleware.Name(), err.Error()), "platform", req.SpiderType)
			return true, nil
		}
	}

	return false, nil
}

// Count 返回中间件数量
func (m *Manager) Count() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.middlewares)
}

// Close 关闭所有中间件
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	for _, middleware := range m.middlewares {
		if err := middleware.Close(); err != nil {
			m.logger.Error(fmt.Sprintf("关闭中间件 %s 失败：%s", middleware.Name(), err.Error()))
		}
	}

	m.middlewares = nil
	m.initialized = false

	m.logger.Info("中间件管理器已关闭")
	return nil
}

// createMiddleware 创建中间件实例
func (m *Manager) createMiddleware(name string, config config.MiddlewareConfig) (Middleware, error) {
	m.logger.Debug(fmt.Sprintf("创建中间件实例 %s，优先级：%d，启用：%t",
		name, config.Priority, config.Enabled))

	switch name {
	case "HeadersMiddleware":
		return NewHeadersMiddleware(config.Priority, config.Settings), nil
	case "RetryMiddleware":
		return NewRetryMiddleware(config.Priority, config.Settings), nil
	case "ProxyMiddleware":
		return NewProxyMiddleware(config.Priority, config.Settings), nil
	case "CookiesMiddleware":
		// CookiesMiddleware 需要完整配置，因为它有自己的配置结构
		fullConfig := make(map[string]interface{})
		fullConfig["enabled"] = config.Enabled
		fullConfig["priority"] = config.Priority
		for key, value := range config.Settings {
			fullConfig[key] = value
		}
		return NewCookiesMiddleware(config.Priority, fullConfig), nil
	case "SignatureMiddleware":
		return NewSignatureMiddleware(config.Priority, config.Settings), nil
	case "tls_fingerprint":
		return NewTLSFingerprintMiddleware(config.Priority, config.Settings), nil
	default:
		return nil, fmt.Errorf("unknown middleware type: %s", name)
	}
}

// Register 注册单个中间件
func (m *Manager) Register(name string, middleware interface{}) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 检查是否实现了Middleware接口
	if mw, ok := middleware.(Middleware); ok {
		m.middlewares = append(m.middlewares, mw)

		// 重新排序
		sort.Slice(m.middlewares, func(i, j int) bool {
			return m.middlewares[i].Priority() < m.middlewares[j].Priority()
		})

		m.logger.Info(fmt.Sprintf("注册中间件 %s 成功，优先级：%d", name, mw.Priority()))

		return nil
	}

	return fmt.Errorf("middleware %s does not implement Middleware interface", name)
}
