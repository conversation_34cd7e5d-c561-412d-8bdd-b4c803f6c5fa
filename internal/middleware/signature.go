package middleware

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"go-monitor/internal/models"
)

// SignatureMiddleware 签名中间件 - 对应Python版本的signature.py
type SignatureMiddleware struct {
	*BaseMiddleware

	// AliExpress签名配置
	aliexpressConfig AliExpressSignConfig

	// Popmart签名配置
	popmartConfig PopMartSignConfig
}

// AliExpressSignConfig AliExpress签名配置
type AliExpressSignConfig struct {
	SignParams []string `yaml:"sign_params"`
	SignFormat string   `yaml:"sign_format"`
	Debug      bool     `yaml:"debug"`
}

// PopMartSignConfig Popmart签名配置
type PopMartSignConfig struct {
	Debug bool `yaml:"debug"`
}

// NewSignatureMiddleware 创建签名中间件
func NewSignatureMiddleware(priority int, config map[string]interface{}) *SignatureMiddleware {
	base := NewBaseMiddleware("signature", priority, config)

	middleware := &SignatureMiddleware{
		BaseMiddleware: base,
		aliexpressConfig: AliExpressSignConfig{
			SignParams: []string{"appKey", "data"},
			SignFormat: "json",
			Debug:      false,
		},
		popmartConfig: PopMartSignConfig{
			Debug: false,
		},
	}

	// 解析AliExpress配置
	if aliexpress, ok := config["aliexpress"].(map[string]interface{}); ok {
		middleware.parseAliExpressConfig(aliexpress)
	}

	// 解析Popmart配置
	if popmart, ok := config["popmart"].(map[string]interface{}); ok {
		middleware.parsePopMartConfig(popmart)
	}

	middleware.logger.Info("签名中间件已启用")

	return middleware
}

// parseAliExpressConfig 解析AliExpress配置
func (s *SignatureMiddleware) parseAliExpressConfig(config map[string]interface{}) {
	if signParams, ok := config["sign_params"].([]interface{}); ok {
		s.aliexpressConfig.SignParams = make([]string, len(signParams))
		for i, param := range signParams {
			if str, ok := param.(string); ok {
				s.aliexpressConfig.SignParams[i] = str
			}
		}
	}

	if signFormat, ok := config["sign_format"].(string); ok {
		s.aliexpressConfig.SignFormat = signFormat
	}

	if debug, ok := config["debug"].(bool); ok {
		s.aliexpressConfig.Debug = debug
	}
}

// parsePopMartConfig 解析Popmart配置
func (s *SignatureMiddleware) parsePopMartConfig(config map[string]interface{}) {
	if debug, ok := config["debug"].(bool); ok {
		s.popmartConfig.Debug = debug
	}
}

// ProcessRequest 处理请求，添加签名
func (s *SignatureMiddleware) ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	// 获取爬虫类型
	spiderType := req.GetMetadataString("spider_type", req.SpiderType)

	switch spiderType {
	// case "aliexpress":
	// 	return s.processAliExpressRequest(req)
	case "popmart":
		return s.processPopMartRequest(req)
	case "amazon":
		return s.processAmazonRequest(req)
	}

	return req, nil
}

// processAmazonRequest 处理Amazon请求签名
func (s *SignatureMiddleware) processAmazonRequest(req *models.Request) (*models.Request, error) {
	// Amazon请求不需要签名，但某些请求需要提取cookies设置sessionId和ubId

	// 检查请求元数据中是否标记需要处理sessionId和ubId
	needsSessionProcessing := req.GetMetadataString("needs_session_processing", "") == "true"
	if !needsSessionProcessing {
		return req, nil
	}

	// 提取session-id cookie对应sessionId
	sessionId := req.GetCookie("session-id")
	if sessionId != "" {
		req.SetMetadata("sessionId", sessionId)
	}

	// 提取ubid-ac*系列cookie对应ubId
	ubId := s.extractUbIdFromCookies(req)
	if ubId != "" {
		req.SetMetadata("ubId", ubId)
	}

	// 如果请求体包含juvec模板，动态更新requestContext中的sessionId和ubId
	if len(req.Body) > 0 && (sessionId != "" || ubId != "") {
		s.updateJuvecRequestContext(req, sessionId, ubId)
	}

	return req, nil
}

// extractUbIdFromCookies 从cookies中提取ubid-ac*系列cookie作为ubId
func (s *SignatureMiddleware) extractUbIdFromCookies(req *models.Request) string {
	// ubid-ac*系列cookie的优先级顺序
	ubidCookieNames := []string{
		"ubid-acbit", "ubid-acbde", "ubid-acbes", "ubid-acbfr", "ubid-acbuk",
	}

	// 按优先级查找第一个存在的ubid cookie
	for _, cookieName := range ubidCookieNames {
		if ubId := req.GetCookie(cookieName); ubId != "" {
			return ubId
		}
	}

	return ""
}

// updateJuvecRequestContext 更新juvec请求体中的requestContext
func (s *SignatureMiddleware) updateJuvecRequestContext(req *models.Request, sessionId, ubId string) {
	// 尝试解析请求体为JSON
	var requestData map[string]interface{}
	if err := json.Unmarshal(req.Body, &requestData); err != nil {
		return // 不是JSON格式，跳过
	}

	// 检查是否有requestContext字段
	if requestContext, ok := requestData["requestContext"].(map[string]interface{}); ok {
		modified := false

		// 设置sessionId
		if sessionId != "" {
			requestContext["sessionId"] = sessionId
			modified = true
		}

		// 设置ubId
		if ubId != "" {
			requestContext["ubId"] = ubId
			modified = true
		}

		// 如果有修改，重新序列化请求体
		if modified {
			if updatedBody, err := json.Marshal(requestData); err == nil {
				req.Body = updatedBody
			}
		}
	}
}

// processAliExpressRequest 处理AliExpress请求签名
func (s *SignatureMiddleware) processAliExpressRequest(req *models.Request) (*models.Request, error) {
	// 清除旧的签名参数（用于重试场景）
	delete(req.Params, "sign")
	delete(req.Params, "t")

	// 获取Cookie中的token
	token := req.GetCookie("_m_h5_tk")
	tokenValue := ""
	if token != "" {
		// 提取token值（格式：token_timestamp）
		value := strings.Split(token, "_")
		tokenValue = value[0]
	}

	// 检查是否需要签名
	needsSign := false

	// 检查是否有data参数
	dataStr := s.getDataFromRequest(req)
	if dataStr != "" {
		needsSign = true
	}

	// 检查URL参数中是否有appKey参数
	appKey := req.Params["appKey"]
	if appKey != "" {
		needsSign = true
	}

	if !needsSign {
		return req, nil
	}

	// 生成签名
	timestamp := time.Now().UnixMilli() // 毫秒时间戳

	if appKey == "" || dataStr == "" {
		s.logger.Debug(fmt.Sprintf("缺少必要的签名参数，appKey：%t，data：%t",
			appKey != "", dataStr != ""), "platform", req.SpiderType)
		return req, nil
	}

	// 生成签名字符串
	signContent := fmt.Sprintf("%s&%d&%s&%s", tokenValue, timestamp, appKey, dataStr)

	// 计算MD5签名
	hash := md5.Sum([]byte(signContent))
	sign := fmt.Sprintf("%x", hash)

	// 添加签名和时间戳到请求参数 - 这些是HTTP查询参数，不是元数据
	req.Params["sign"] = sign
	req.Params["t"] = strconv.FormatInt(timestamp, 10)

	if s.aliexpressConfig.Debug {
		s.logger.Debug(fmt.Sprintf("AliExpress请求已添加签名：%s，时间戳：%d", sign, timestamp),
			"platform", "aliexpress")
	}

	return req, nil
}

// processPopMartRequest 处理Popmart请求签名
func (s *SignatureMiddleware) processPopMartRequest(req *models.Request) (*models.Request, error) {
	// 1. 生成x-sign header签名
	if err := s.generateXSignHeader(req); err != nil {
		s.logger.Debug(fmt.Sprintf("生成x-sign签名失败：%v", err), "platform", "popmart")
	}

	// 2. 处理数据签名（POST body 或 GET 参数）
	if err := s.signPopMartData(req); err != nil {
		s.logger.Debug(fmt.Sprintf("数据签名失败：%v", err), "platform", "popmart")
	}

	return req, nil
}

// getParamsFromRequest 从请求中获取HTTP查询参数
func (s *SignatureMiddleware) getParamsFromRequest(req *models.Request) map[string]string {
	params := make(map[string]string)

	// 从Params字段中获取HTTP查询参数
	for key, value := range req.Params {
		params[key] = value
	}

	return params
}

// getDataFromRequest 从请求中提取data数据（用于签名计算）
func (s *SignatureMiddleware) getDataFromRequest(req *models.Request) string {
	// 首先尝试从metadata中获取（爬虫设置的data）
	if data := req.GetMetadata("data"); data != nil {
		if str, ok := data.(string); ok {
			return str
		}
	}

	// 如果metadata中没有，尝试从请求体中提取
	if len(req.Body) > 0 {
		// 解析form数据
		if bodyStr := string(req.Body); bodyStr != "" {
			// 解析 "data=xxx" 格式的form数据
			if strings.HasPrefix(bodyStr, "data=") {
				if decoded, err := url.QueryUnescape(bodyStr[5:]); err == nil {
					return decoded
				}
			}
		}
	}

	return ""
}

// GetParamsFromRequest 公开方法用于测试
func (s *SignatureMiddleware) GetParamsFromRequest(req *models.Request) map[string]string {
	return s.getParamsFromRequest(req)
}

// GetDataFromRequest 公开方法用于测试
func (s *SignatureMiddleware) GetDataFromRequest(req *models.Request) string {
	return s.getDataFromRequest(req)
}

// generateXSignHeader 生成x-sign header签名
// 签名规则：sign_input = f"{timestamp},{clientkey}"，signature = f"{_md5(sign_input)},{timestamp}"
func (s *SignatureMiddleware) generateXSignHeader(req *models.Request) error {
	// 从headers中获取clientkey
	clientkey := s.getClientKeyFromHeaders(req)
	if clientkey == "" {
		return fmt.Errorf("缺少clientkey header")
	}

	// 生成时间戳（秒级）
	timestamp := time.Now().Unix()

	// 构建签名输入：timestamp,clientkey
	signInput := fmt.Sprintf("%d,%s", timestamp, clientkey)

	// 计算MD5签名
	hash := md5.Sum([]byte(signInput))
	md5Signature := fmt.Sprintf("%x", hash)

	// 构建最终签名：md5,timestamp
	xSignValue := fmt.Sprintf("%s,%d", md5Signature, timestamp)

	// 设置x-sign header
	if req.Headers == nil {
		req.Headers = make(map[string]string)
	}
	req.Headers["x-sign"] = xSignValue

	if s.popmartConfig.Debug {
		s.logger.Debug(fmt.Sprintf("PopMart x-sign签名生成成功，clientkey：%s，时间戳：%d，签名：%s",
			clientkey, timestamp, xSignValue), "platform", "popmart")
	}

	return nil
}

// getClientKeyFromHeaders 从请求headers中获取clientkey
func (s *SignatureMiddleware) getClientKeyFromHeaders(req *models.Request) string {
	if req.Headers == nil {
		return ""
	}
	return req.Headers["clientkey"]
}

// signPopMartData 统一处理PopMart数据签名（POST body 或 GET 参数）
func (s *SignatureMiddleware) signPopMartData(req *models.Request) error {
	if req.Method == "POST" && len(req.Body) > 0 {
		// 处理POST请求的body签名
		return s.signPopMartRequestBody(req)
	} else {
		// 处理GET请求的参数签名
		return s.signPopMartRequestParams(req)
	}
}

// signPopMartRequestBody 为POST请求的body添加签名
func (s *SignatureMiddleware) signPopMartRequestBody(req *models.Request) error {
	// 解析JSON body
	var bodyData map[string]interface{}
	if err := json.Unmarshal(req.Body, &bodyData); err != nil {
		return fmt.Errorf("解析JSON body失败: %w", err)
	}

	// 删除旧的签名字段（用于重试场景）
	delete(bodyData, "s")
	delete(bodyData, "t")

	// 生成签名并添加到body中
	signedData := s.attachPopMartSignatureToData(bodyData)

	// 重新序列化body
	newBody, err := json.Marshal(signedData)
	if err != nil {
		return fmt.Errorf("序列化签名后的body失败: %w", err)
	}

	// 更新请求body
	req.Body = newBody

	if s.popmartConfig.Debug {
		s.logger.Debug(fmt.Sprintf("POST请求body签名完成，body大小：%d字节", len(newBody)),
			"platform", "popmart")
	}

	return nil
}

// signPopMartRequestParams 为GET请求的参数添加签名
func (s *SignatureMiddleware) signPopMartRequestParams(req *models.Request) error {
	// 获取请求参数
	params := s.getParamsFromRequest(req)
	if len(params) == 0 {
		return nil // 没有参数需要签名
	}

	// 删除旧的签名字段（用于重试场景）
	delete(params, "s")
	delete(params, "t")

	// 生成参数签名
	signedParams := s.attachPopMartSignature(params)

	// 更新请求参数
	for key, value := range signedParams {
		req.Params[key] = value
	}

	if s.popmartConfig.Debug {
		s.logger.Debug(fmt.Sprintf("GET请求参数签名完成，参数数量：%d", len(signedParams)),
			"platform", "popmart")
	}

	return nil
}

// attachPopMartSignatureToData 为JSON数据添加PopMart签名
func (s *SignatureMiddleware) attachPopMartSignatureToData(data map[string]interface{}) map[string]interface{} {
	// 使用UTC秒级时间戳
	timestamp := time.Now().Unix()

	// 复制原数据
	result := make(map[string]interface{})
	for k, v := range data {
		result[k] = v
	}

	// 对键进行排序以确保一致性
	keys := make([]string, 0, len(data))
	for k := range data {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建排序后的数据
	sortedData := make(map[string]interface{})
	for _, k := range keys {
		sortedData[k] = data[k]
	}

	// 生成JSON字符串（无空格，确保格式一致）
	jsonBytes, err := json.Marshal(sortedData)
	if err != nil {
		// 如果JSON序列化失败，使用空字符串
		jsonBytes = []byte("{}")
	}
	jsonStr := string(jsonBytes)

	// 构建签名字符串：JSON + 'W_ak^moHpMla' + 时间戳
	signContent := fmt.Sprintf("%s%s%d", jsonStr, "W_ak^moHpMla", timestamp)

	// 计算MD5签名
	hash := md5.Sum([]byte(signContent))
	signature := fmt.Sprintf("%x", hash)

	// 添加签名字段到数据中
	result["t"] = timestamp
	result["s"] = signature

	if s.popmartConfig.Debug {
		s.logger.Debug(fmt.Sprintf("PopMart body签名详情，时间戳：%d，签名：%s", timestamp, signature),
			"platform", "popmart")
	}

	return result
}

// attachPopMartSignature 为Popmart请求添加签名
// 实现PopMart的具体签名算法：MD5(JSON字符串 + 'W_ak^moHpMla' + 时间戳)
func (s *SignatureMiddleware) attachPopMartSignature(params map[string]string) map[string]string {
	// 使用UTC秒级时间戳
	timestamp := time.Now().Unix()

	// 复制原参数
	result := make(map[string]string)
	for k, v := range params {
		result[k] = v
	}

	// 构建用于签名的数据结构
	signData := make(map[string]interface{})
	for k, v := range params {
		signData[k] = v
	}

	// 对键进行排序以确保一致性
	keys := make([]string, 0, len(signData))
	for k := range signData {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建排序后的数据
	sortedData := make(map[string]interface{})
	for _, k := range keys {
		sortedData[k] = signData[k]
	}

	// 生成JSON字符串（无空格，确保格式一致）
	jsonBytes, err := json.Marshal(sortedData)
	if err != nil {
		// 如果JSON序列化失败，使用空字符串
		jsonBytes = []byte("{}")
	}
	jsonStr := string(jsonBytes)

	// 构建签名字符串：JSON + 'W_ak^moHpMla' + 时间戳
	signContent := fmt.Sprintf("%s%s%d", jsonStr, "W_ak^moHpMla", timestamp)

	// 计算MD5签名
	hash := md5.Sum([]byte(signContent))
	signature := fmt.Sprintf("%x", hash)

	// 添加签名参数
	result["t"] = strconv.FormatInt(timestamp, 10)
	result["s"] = signature

	if s.popmartConfig.Debug {
		s.logger.Debug(fmt.Sprintf("PopMart签名详情，时间戳：%d，签名：%s", timestamp, signature),
			"platform", "popmart")
	}

	return result
}

// ProcessResponse 处理响应（继承基类默认行为）
func (s *SignatureMiddleware) ProcessResponse(ctx context.Context, resp *models.Response) (bool, *models.Response, error) {
	return s.BaseMiddleware.ProcessResponse(ctx, resp)
}

// ProcessException 处理异常（继承基类默认行为）
func (s *SignatureMiddleware) ProcessException(ctx context.Context, err error, req *models.Request) (bool, error) {
	return s.BaseMiddleware.ProcessException(ctx, err, req)
}

// GetStats 获取统计信息（重写基类方法以包含签名特定信息）
func (s *SignatureMiddleware) GetStats() map[string]interface{} {
	stats := s.BaseMiddleware.GetStats()
	stats["aliexpress_debug"] = s.aliexpressConfig.Debug
	stats["popmart_debug"] = s.popmartConfig.Debug

	return stats
}
