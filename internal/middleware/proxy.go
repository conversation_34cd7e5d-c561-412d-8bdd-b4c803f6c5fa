package middleware

import (
	"context"
	"fmt"
	"maps"
	"math/rand"
	"strings"
	"sync"

	"go-monitor/internal/models"
)

// ProxyMiddleware 代理中间件 - 简化版本
type ProxyMiddleware struct {
	*BaseMiddleware

	// 代理组配置
	proxyGroups map[string][]string

	// 反向代理配置
	reverseProxies map[string][]ReverseProxy

	// 简化的故障转移配置
	allowDirect bool
	maxFailures int

	// 代理失败记录
	failedProxies map[string]int
	mu            sync.RWMutex
}

// ReverseProxy 反向代理配置
type ReverseProxy struct {
	URL     string            `yaml:"url"`
	Headers map[string]string `yaml:"headers"`
}

// NewProxyMiddleware 创建Proxy中间件
func NewProxyMiddleware(priority int, config map[string]interface{}) *ProxyMiddleware {
	base := NewBaseMiddleware("proxy", priority, config)

	middleware := &ProxyMiddleware{
		BaseMiddleware: base,
		proxyGroups:    make(map[string][]string),
		reverseProxies: make(map[string][]ReverseProxy),
		failedProxies:  make(map[string]int),
		allowDirect:    true,
		maxFailures:    20,
	}

	// 解析代理组配置
	if proxyGroups, ok := config["proxy_groups"].(map[string]interface{}); ok {
		middleware.parseProxyGroups(proxyGroups)
	}

	// 解析反向代理配置
	if reverseProxies, ok := config["reverse_proxies"].(map[string]interface{}); ok {
		middleware.parseReverseProxies(reverseProxies)
	}

	// 解析故障转移配置
	if failover, ok := config["failover"].(map[string]interface{}); ok {
		if allowDirect, ok := failover["allow_direct"].(bool); ok {
			middleware.allowDirect = allowDirect
		}
		if maxFailures, ok := failover["max_failures"].(int); ok {
			middleware.maxFailures = maxFailures
		} else if maxFailuresFloat, ok := failover["max_failures"].(float64); ok {
			middleware.maxFailures = int(maxFailuresFloat)
		}
	}

	middleware.logger.Info(fmt.Sprintf("Proxy中间件已启用，代理组：%d，反向代理：%d",
		len(middleware.proxyGroups), len(middleware.reverseProxies)))

	return middleware
}

// parseProxyGroups 解析代理组配置
func (p *ProxyMiddleware) parseProxyGroups(proxyGroups map[string]interface{}) {
	for groupName, proxiesInterface := range proxyGroups {
		if proxies, ok := proxiesInterface.([]interface{}); ok {
			proxyList := make([]string, 0, len(proxies))
			for _, proxy := range proxies {
				if proxyStr, ok := proxy.(string); ok {
					proxyList = append(proxyList, proxyStr)
				}
			}
			if len(proxyList) > 0 {
				p.proxyGroups[groupName] = proxyList
			}
		}
	}
}

// parseReverseProxies 解析反向代理配置
func (p *ProxyMiddleware) parseReverseProxies(reverseProxies map[string]interface{}) {
	for groupName, proxiesInterface := range reverseProxies {
		if proxies, ok := proxiesInterface.([]interface{}); ok {
			proxyList := make([]ReverseProxy, 0, len(proxies))
			for _, proxy := range proxies {
				if proxyMap, ok := proxy.(map[string]interface{}); ok {
					reverseProxy := ReverseProxy{
						Headers: make(map[string]string),
					}

					if url, ok := proxyMap["url"].(string); ok {
						reverseProxy.URL = url
					}

					if headers, ok := proxyMap["headers"].(map[string]interface{}); ok {
						for key, value := range headers {
							if str, ok := value.(string); ok {
								reverseProxy.Headers[key] = str
							}
						}
					}

					if reverseProxy.URL != "" {
						proxyList = append(proxyList, reverseProxy)
					}
				}
			}
			if len(proxyList) > 0 {
				p.reverseProxies[groupName] = proxyList
			}
		}
	}
}

// handleProxyFailure 处理代理失败
func (p *ProxyMiddleware) handleProxyFailure(proxy string) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.failedProxies[proxy]++
	if p.failedProxies[proxy] >= p.maxFailures {
		delete(p.failedProxies, proxy)
	}
}

// selectProxy 统一的代理选择逻辑
func (p *ProxyMiddleware) selectProxy(proxyGroup string) interface{} {
	// 检查是否是反向代理组
	if strings.HasPrefix(proxyGroup, "reverse_proxies.") {
		groupName := strings.TrimPrefix(proxyGroup, "reverse_proxies.")
		if proxies, exists := p.reverseProxies[groupName]; exists && len(proxies) > 0 {
			return proxies[rand.Intn(len(proxies))]
		}
		return nil
	}

	// 普通代理组
	if proxies, exists := p.proxyGroups[proxyGroup]; exists && len(proxies) > 0 {
		// 过滤掉失败的代理
		availableProxies := make([]string, 0)
		p.mu.RLock()
		for _, proxy := range proxies {
			if p.failedProxies[proxy] < p.maxFailures {
				availableProxies = append(availableProxies, proxy)
			}
		}
		p.mu.RUnlock()

		if len(availableProxies) > 0 {
			return availableProxies[rand.Intn(len(availableProxies))]
		}
	}
	return nil
}

// convertToStringArray 将各种类型的代理数组转换为字符串数组
func (p *ProxyMiddleware) convertToStringArray(proxies interface{}) []string {
	var result []string

	switch v := proxies.(type) {
	case []string:
		// 直接的字符串数组
		result = v
	case []interface{}:
		// 通用接口数组（包括[]any，因为any是interface{}的别名）
		for _, item := range v {
			if str, ok := item.(string); ok {
				result = append(result, str)
			}
		}
	}

	return result
}

// selectProxyFromArray 从动态代理数组中选择代理（简化版本）
func (p *ProxyMiddleware) selectProxyFromArray(proxies []string) (string, interface{}) {
	if len(proxies) == 0 {
		return "", nil
	}

	// 展开代理数组
	var expandedProxies []string
	var reverseProxyObjs []ReverseProxy

	for _, proxy := range proxies {
		if strings.HasPrefix(proxy, "reverse_proxies.") {
			groupName := strings.TrimPrefix(proxy, "reverse_proxies.")
			if groupReverseProxies, exists := p.reverseProxies[groupName]; exists {
				reverseProxyObjs = append(reverseProxyObjs, groupReverseProxies...)
			}
		} else if !strings.Contains(proxy, "://") {
			// 代理组名称
			if groupProxies, exists := p.proxyGroups[proxy]; exists {
				expandedProxies = append(expandedProxies, groupProxies...)
			}
		} else {
			// 直接代理地址
			expandedProxies = append(expandedProxies, proxy)
		}
	}

	// 优先选择反向代理
	if len(reverseProxyObjs) > 0 {
		return "reverse", reverseProxyObjs[rand.Intn(len(reverseProxyObjs))]
	}

	// 过滤掉失败的普通代理
	availableProxies := make([]string, 0)
	p.mu.RLock()
	for _, proxy := range expandedProxies {
		if p.failedProxies[proxy] < p.maxFailures {
			availableProxies = append(availableProxies, proxy)
		}
	}
	p.mu.RUnlock()

	if len(availableProxies) > 0 {
		return "normal", availableProxies[rand.Intn(len(availableProxies))]
	}

	return "", nil
}

// ProcessRequest 处理请求，添加代理
func (p *ProxyMiddleware) ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	// 优先检查爬虫传递的动态代理数组
	if proxies := req.GetMetadata("proxies"); proxies != nil {
		proxyArray := p.convertToStringArray(proxies)
		if len(proxyArray) > 0 {
			proxyType, proxyValue := p.selectProxyFromArray(proxyArray)
			if proxyValue != nil {
				switch proxyType {
				case "reverse":
					if reverseProxy, ok := proxyValue.(ReverseProxy); ok {
						req.ReverseProxy = reverseProxy.URL
						p.mergeReverseProxyHeaders(req, reverseProxy.Headers)
					}
					return req, nil
				case "normal":
					if proxyStr, ok := proxyValue.(string); ok {
						req.Proxy = proxyStr
					}
					return req, nil
				}
			}
		}
	}

	// 检查是否有代理组配置
	if proxyGroup := req.GetMetadataString("proxy_group", ""); proxyGroup != "" {
		proxy := p.selectProxy(proxyGroup)
		if proxy != nil {
			if reverseProxy, ok := proxy.(ReverseProxy); ok {
				req.ReverseProxy = reverseProxy.URL
				p.mergeReverseProxyHeaders(req, reverseProxy.Headers)
			} else if proxyStr, ok := proxy.(string); ok {
				req.Proxy = proxyStr
			}
		}
	}

	return req, nil
}

// ProcessException 处理异常，判断是否代理问题
func (p *ProxyMiddleware) ProcessException(ctx context.Context, err error, req *models.Request) (bool, error) {
	proxyUsed := req.Proxy
	if proxyUsed == "" {
		return false, nil
	}

	// 检查是否是代理相关异常
	errorStr := err.Error()
	isProxyError := strings.Contains(errorStr, "proxy") ||
		strings.Contains(errorStr, "connection") ||
		strings.Contains(errorStr, "timeout") ||
		strings.Contains(errorStr, "deadline exceeded") ||
		strings.Contains(errorStr, "no route to host") ||
		strings.Contains(errorStr, "connection refused")

	if isProxyError {
		p.handleProxyFailure(proxyUsed)

		// 如果允许直连，清除代理设置
		if p.allowDirect {
			req.Proxy = ""
			return true, nil
		}
	}

	return false, nil
}

// mergeReverseProxyHeaders 合并反向代理的headers到请求中
func (p *ProxyMiddleware) mergeReverseProxyHeaders(req *models.Request, reverseProxyHeaders map[string]string) {
	if req.Headers == nil {
		req.Headers = make(map[string]string)
	}
	maps.Copy(req.Headers, reverseProxyHeaders)
}
