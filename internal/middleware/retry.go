package middleware

import (
	"context"
	"fmt"
	"time"

	"go-monitor/internal/models"
)

// RetryMiddleware 重试中间件 - 对应Python版本的retry.py
type RetryMiddleware struct {
	*BaseMiddleware
	maxRetries    int
	retryStatuses []int
	timeout       int // 超时时间(秒)
}

// NewRetryMiddleware 创建Retry中间件
func NewRetryMiddleware(priority int, config map[string]interface{}) *RetryMiddleware {
	base := NewBaseMiddleware("retry", priority, config)

	maxRetries := 3
	if mr, ok := config["max_retries"].(int); ok {
		maxRetries = mr
	}

	timeout := 30 // 默认30秒
	if t, ok := config["timeout"].(int); ok {
		timeout = t
	}

	// 支持两种配置字段名
	retryStatuses := []int{429, 500, 502, 503, 504}
	if statuses, ok := config["retry_http_codes"].([]interface{}); ok {
		retryStatuses = make([]int, len(statuses))
		for i, status := range statuses {
			if statusInt, ok := status.(int); ok {
				retryStatuses[i] = statusInt
			}
		}
	} else if statuses, ok := config["retry_statuses"].([]int); ok {
		retryStatuses = statuses
	}

	return &RetryMiddleware{
		BaseMiddleware: base,
		maxRetries:     maxRetries,
		retryStatuses:  retryStatuses,
		timeout:        timeout,
	}
}

// ProcessRequest 处理请求，设置重试和超时配置
func (r *RetryMiddleware) ProcessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	// 设置最大重试次数
	req.MaxRetries = r.maxRetries

	// 设置超时时间
	if r.timeout > 0 {
		req.Timeout = time.Duration(r.timeout) * time.Second
	}

	r.logger.Debug(fmt.Sprintf("设置重试配置：%s，最大重试：%d，超时：%.1fs",
		req.URL, req.MaxRetries, req.Timeout.Seconds()), "platform", req.SpiderType)

	return req, nil
}

// ProcessResponse 处理响应，判断是否需要重试
func (r *RetryMiddleware) ProcessResponse(ctx context.Context, resp *models.Response) (bool, *models.Response, error) {
	// 检查状态码是否需要重试
	for _, status := range r.retryStatuses {
		if resp.StatusCode == status {
			r.logger.Debug(fmt.Sprintf("响应状态码需要重试：%s，状态码：%d",
				resp.URL, resp.StatusCode), "platform", resp.SpiderType)
			return true, resp, nil
		}
	}

	return false, resp, nil
}

// ProcessException 处理异常，判断是否需要重试
func (r *RetryMiddleware) ProcessException(ctx context.Context, err error, req *models.Request) (bool, error) {
	// 网络错误通常需要重试
	r.logger.Debug(fmt.Sprintf("异常处理，建议重试：%s，错误：%s",
		req.URL, err.Error()), "platform", req.SpiderType)

	return true, nil
}
