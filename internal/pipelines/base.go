package pipelines

import (
	"context"
	"fmt"
	"sync"

	"go-monitor/pkg/logging"
)

// Pipeline 管道接口 - 对应Python版本的BasePipeline
type Pipeline interface {
	// ProcessItem 处理数据项
	ProcessItem(ctx context.Context, item interface{}) (interface{}, error)

	// ProcessItemAsync 异步处理数据项
	ProcessItemAsync(ctx context.Context, item interface{}) (interface{}, error)

	// Open 打开管道，初始化资源
	Open() error

	// Close 关闭管道，清理资源
	Close() error

	// Name 返回管道名称
	Name() string

	// Priority 返回管道优先级
	Priority() int

	// GetStats 获取统计信息
	GetStats() map[string]interface{}
}

// BasePipeline 管道基类 - 对应Python版本的BasePipeline
type BasePipeline struct {
	config   map[string]interface{}
	logger   logging.Logger
	name     string
	priority int
	mu       sync.RWMutex

	// 统计信息
	stats struct {
		Processed int64 `json:"processed"`
		Dropped   int64 `json:"dropped"`
		Errors    int64 `json:"errors"`
	}
}

// NewBasePipeline 创建基础管道
func NewBasePipeline(name string, priority int, config map[string]interface{}) *BasePipeline {
	// 获取全局日志工厂

	return &BasePipeline{
		config:   config,
		logger:   logging.GetLogger("service.pipeline-" + name),
		name:     name,
		priority: priority,
	}
}

// ProcessItem 默认处理数据项 - 直接返回原数据
func (b *BasePipeline) ProcessItem(ctx context.Context, item interface{}) (interface{}, error) {
	b.incrementProcessed(1)
	return item, nil
}

// ProcessItemAsync 异步处理数据项，默认调用同步版本
func (b *BasePipeline) ProcessItemAsync(ctx context.Context, item interface{}) (interface{}, error) {
	return b.ProcessItem(ctx, item)
}

// Open 默认打开方法
func (b *BasePipeline) Open() error {
	b.logger.Info(fmt.Sprintf("打开管道：%s", b.name))
	return nil
}

// Close 默认关闭方法
func (b *BasePipeline) Close() error {
	stats := b.GetStats()
	b.logger.Info(fmt.Sprintf("关闭管道：%s，处理：%v，丢弃：%v，错误：%v",
		b.name, stats["processed"], stats["dropped"], stats["errors"]))
	return nil
}

// Name 返回管道名称
func (b *BasePipeline) Name() string {
	return b.name
}

// Priority 返回管道优先级
func (b *BasePipeline) Priority() int {
	return b.priority
}

// GetStats 获取统计信息
func (b *BasePipeline) GetStats() map[string]interface{} {
	b.mu.RLock()
	defer b.mu.RUnlock()

	return map[string]interface{}{
		"processed": b.stats.Processed,
		"dropped":   b.stats.Dropped,
		"errors":    b.stats.Errors,
	}
}

// GetConfig 获取配置值
func (b *BasePipeline) GetConfig(key string) interface{} {
	if b.config == nil {
		return nil
	}
	return b.config[key]
}

// GetConfigString 获取字符串配置
func (b *BasePipeline) GetConfigString(key string, defaultValue string) string {
	if val := b.GetConfig(key); val != nil {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return defaultValue
}

// GetConfigInt 获取整数配置
func (b *BasePipeline) GetConfigInt(key string, defaultValue int) int {
	if val := b.GetConfig(key); val != nil {
		if i, ok := val.(int); ok {
			return i
		}
		if f, ok := val.(float64); ok {
			return int(f)
		}
	}
	return defaultValue
}

// GetConfigBool 获取布尔配置
func (b *BasePipeline) GetConfigBool(key string, defaultValue bool) bool {
	if val := b.GetConfig(key); val != nil {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return defaultValue
}

// updateStats 更新统计信息 - 优化版本，避免字符串比较
func (b *BasePipeline) updateStats(field string, value int64) {
	b.mu.Lock()
	defer b.mu.Unlock()

	switch field {
	case "processed":
		b.stats.Processed += value
	case "dropped":
		b.stats.Dropped += value
	case "errors":
		b.stats.Errors += value
	}
}

// 优化的统计更新方法 - 直接操作，避免字符串比较
func (b *BasePipeline) incrementProcessed(value int64) {
	b.mu.Lock()
	b.stats.Processed += value
	b.mu.Unlock()
}

func (b *BasePipeline) incrementDropped(value int64) {
	b.mu.Lock()
	b.stats.Dropped += value
	b.mu.Unlock()
}

func (b *BasePipeline) incrementErrors(value int64) {
	b.mu.Lock()
	b.stats.Errors += value
	b.mu.Unlock()
}

// ItemToMap 将数据项转换为map - 对应Python版本的_item_to_dict
func (b *BasePipeline) ItemToMap(item interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	switch v := item.(type) {
	case map[string]interface{}:
		return v
	case map[string]string:
		for k, val := range v {
			result[k] = val
		}
	default:
		// 使用反射处理结构体等复杂类型
		result["data"] = item
		result["type"] = "unknown"
	}

	return result
}
