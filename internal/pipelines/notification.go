package pipelines

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"go-monitor/internal/models"
	"go-monitor/internal/services/notification"
)

// NotificationPipeline 通知管道 - 对应Python版本的notification.py
type NotificationPipeline struct {
	*BasePipeline

	// 配置驱动的设置
	notificationService *notification.Service

	// 扩展统计信息
	stats struct {
		Processed int64 `json:"processed"`
		Dropped   int64 `json:"dropped"`
		Errors    int64 `json:"errors"`
		Sent      int64 `json:"sent"`
		Skipped   int64 `json:"skipped"` // 新增：跳过的通知数
	}

	// 自己的mutex
	statsMu sync.RWMutex
}

// NewNotificationPipeline 创建通知管道
func NewNotificationPipeline(priority int, config map[string]interface{}) *NotificationPipeline {
	base := NewBasePipeline("notification", priority, config)

	// 直接使用传入的配置创建通知服务
	notificationService := notification.NewService(config)

	pipeline := &NotificationPipeline{
		BasePipeline:        base,
		notificationService: notificationService,
	}

	// 记录初始状态
	configSource := "default"
	if len(config) > 0 {
		configSource = "provided"
	}

	// 简化创建日志，减少冗余信息
	pipeline.logger.Info(fmt.Sprintf("通知管道已创建，服务状态：%s，配置来源：%s",
		notificationService.GetServiceStatus().String(), configSource))

	return pipeline
}

// ProcessItem 处理数据项，生成通知 - 对应Python版本的process_item
func (n *NotificationPipeline) ProcessItem(ctx context.Context, item interface{}) (interface{}, error) {
	// 调用父类方法，增加处理计数
	n.updateStats("processed", 1)

	// 检查数据项类型
	var productItem *models.ProductItem

	switch it := item.(type) {
	case *models.ProductItem:
		productItem = it
	case models.ProductItem:
		productItem = &it
	default:
		n.updateStats("errors", 1)
		n.logger.Error(fmt.Sprintf("不支持的数据项类型：%T", item))
		return nil, fmt.Errorf("unsupported item type: %T", item)
	}

	platform := productItem.Platform
	if platform == "" {
		platform = "monitor"
	}

	// 检查库存
	if productItem.InStock {
		// 获取通知组
		groups := productItem.Notifications
		if len(groups) == 0 {
			groups = []string{"default"}
		}

		// 调用通知服务发送Discord通知，增强错误处理
		result, err := n.notificationService.SendDiscordProductNotification(ctx, productItem, groups, nil)
		if err != nil {
			n.updateStats("errors", 1)
			// 增强错误日志，包含更多上下文信息
			n.logger.Error(fmt.Sprintf("通知服务调用失败 URL：%s，组：%v，产品ID：%s，标题：%s，错误：%s",
				productItem.URL, groups, productItem.ProductID, productItem.Title, err.Error()),
				"platform", platform)

			// 记录错误统计信息用于监控
			n.logNotificationFailure(productItem, groups, err, "service_error")
		} else if result != nil && result.Success {
			n.updateStats("sent", 1)
			n.logger.Info(fmt.Sprintf("通知发送成功 产品：%s，组：%v", productItem.Title, groups),
				"platform", platform)
		} else {
			n.updateStats("errors", 1)
			// 增强错误日志，提供更多诊断信息
			if result != nil {
				n.logger.Error(fmt.Sprintf("通知发送失败 URL：%s，组：%v，产品ID：%s，标题：%s，成功：%t，错误详情：%v",
					productItem.URL, groups, productItem.ProductID, productItem.Title, result.Success, result.Errors),
					"platform", platform)

				// 分析错误类型并记录
				n.analyzeAndLogErrors(productItem, groups, result.Errors)
			} else {
				n.logger.Error(fmt.Sprintf("通知发送失败 URL：%s，组：%v，产品ID：%s，标题：%s，结果：nil",
					productItem.URL, groups, productItem.ProductID, productItem.Title), "platform", platform)

				n.logNotificationFailure(productItem, groups, fmt.Errorf("通知结果为nil"), "null_result")
			}
		}

		return productItem, nil
	} else {
		// 商品缺货，跳过通知（不记录日志以减少输出）
		n.updateStats("skipped", 1)
	}

	return nil, nil
}

// GetStats 获取统计信息 - 重写基类方法以包含扩展统计
func (n *NotificationPipeline) GetStats() map[string]interface{} {
	stats := n.BasePipeline.GetStats()

	n.statsMu.RLock()
	defer n.statsMu.RUnlock()

	// 添加通知特定的统计信息
	stats["sent"] = n.stats.Sent
	stats["skipped"] = n.stats.Skipped

	// 添加通知服务状态
	if n.notificationService != nil {
		serviceStats := n.notificationService.GetStats()
		stats["service"] = serviceStats
		stats["service_healthy"] = n.notificationService.IsHealthy()
		stats["service_status"] = n.notificationService.GetServiceStatus().String()
	}

	return stats
}

// updateStats 更新统计信息 - 重写基类方法以支持扩展统计
func (n *NotificationPipeline) updateStats(field string, value int64) {
	n.BasePipeline.updateStats(field, value)

	n.statsMu.Lock()
	defer n.statsMu.Unlock()

	switch field {
	case "sent":
		n.stats.Sent += value
	case "skipped":
		n.stats.Skipped += value
	}
}

// Close 关闭管道 - 对应Python版本的close
func (n *NotificationPipeline) Close() error {
	n.logger.Info(fmt.Sprintf("关闭通知管道，最终统计：%v", n.GetStats()))

	if n.notificationService != nil {
		err := n.notificationService.Close()
		if err != nil {
			n.logger.Error(fmt.Sprintf("关闭通知服务失败：%s", err.Error()))
		} else {
			n.logger.Info("通知服务已关闭")
		}
	}

	return n.BasePipeline.Close()
}

// GetNotificationService 获取通知服务实例（新增方法，用于测试或外部访问）
func (n *NotificationPipeline) GetNotificationService() *notification.Service {
	return n.notificationService
}

// logNotificationFailure 记录通知失败的详细信息用于监控和分析
func (n *NotificationPipeline) logNotificationFailure(productItem *models.ProductItem, groups []string, err error, errorType string) {
	// 记录结构化的错误信息，便于后续分析和监控
	n.logger.Warn(fmt.Sprintf("通知失败分析 类型：%s，产品：%s，组数量：%d，错误：%v",
		errorType, productItem.ProductID, len(groups), err))

	// 可以在这里添加更多的监控逻辑，比如发送到监控系统
	// 例如：metrics.IncrementCounter("notification_failures", map[string]string{"type": errorType})
}

// analyzeAndLogErrors 分析错误类型并记录详细信息
func (n *NotificationPipeline) analyzeAndLogErrors(productItem *models.ProductItem, groups []string, errors []string) {
	if len(errors) == 0 {
		return
	}

	// 分析错误模式
	networkErrors := 0
	rateLimitErrors := 0
	authErrors := 0
	otherErrors := 0

	for _, errMsg := range errors {
		if strings.Contains(errMsg, "timeout") || strings.Contains(errMsg, "connection") || strings.Contains(errMsg, "network") {
			networkErrors++
		} else if strings.Contains(errMsg, "429") || strings.Contains(errMsg, "rate limit") {
			rateLimitErrors++
		} else if strings.Contains(errMsg, "401") || strings.Contains(errMsg, "403") || strings.Contains(errMsg, "unauthorized") {
			authErrors++
		} else {
			otherErrors++
		}
	}

	// 记录错误分析结果
	n.logger.Warn(fmt.Sprintf("错误分析 产品：%s，网络错误：%d，速率限制：%d，认证错误：%d，其他错误：%d",
		productItem.ProductID, networkErrors, rateLimitErrors, authErrors, otherErrors))

	// 根据错误类型提供建议
	if networkErrors > 0 {
		n.logger.Info("建议：检查网络连接和DNS解析")
	}
	if rateLimitErrors > 0 {
		n.logger.Info("建议：降低通知频率或增加延迟")
	}
	if authErrors > 0 {
		n.logger.Info("建议：检查Discord Webhook URL的有效性")
	}
}
