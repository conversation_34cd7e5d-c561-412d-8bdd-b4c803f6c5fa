package pipelines

import (
	"context"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"sync"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/services/redis"
	"go-monitor/pkg/logging"
)

// Manager 管道管理器 - 负责加载和管理所有数据管道
type Manager struct {
	pipelines    []Pipeline
	logger       logging.Logger
	redisManager *redis.RedisManager
	mu           sync.RWMutex
	initialized  bool
}

// NewManager 创建新的管道管理器
func NewManager(redisManager *redis.RedisManager) *Manager {

	return &Manager{
		logger:       logging.GetLogger("service.pipeline-manager"),
		redisManager: redisManager,
		pipelines:    make([]Pipeline, 0),
	}
}

// LoadPipelines 加载所有管道
func (m *Manager) LoadPipelines(pipelineConfigs map[string]config.PipelineConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.initialized {
		m.logger.Warn("管道已初始化，跳过重复加载")
		return nil
	}

	for name, config := range pipelineConfigs {
		pipeline, err := m.createPipeline(name, config)
		if err != nil {
			m.logger.Error(fmt.Sprintf("创建管道失败：%s，错误：%s", name, err.Error()))
			continue
		}

		// 打开管道
		if err := pipeline.Open(); err != nil {
			m.logger.Error(fmt.Sprintf("打开管道失败：%s，错误：%s", name, err.Error()))
			continue
		}

		m.pipelines = append(m.pipelines, pipeline)
		// 只在管道数量较少时输出详细信息
		if len(m.pipelines) <= 5 {
			m.logger.Info(fmt.Sprintf("加载管道成功：%s，优先级：%d", name, pipeline.Priority()))
		}
	}

	// 按优先级排序
	sort.Slice(m.pipelines, func(i, j int) bool {
		return m.pipelines[i].Priority() < m.pipelines[j].Priority()
	})

	// 记录加载结果
	pipelineNames := make([]string, len(m.pipelines))
	for i, p := range m.pipelines {
		pipelineNames[i] = p.Name()
	}

	m.logger.Info(fmt.Sprintf("管道加载完成，数量：%d，管道：%v", len(m.pipelines), pipelineNames))

	m.initialized = true
	return nil
}

// ProcessItem 处理数据项 - 按优先级顺序通过所有管道
func (m *Manager) ProcessItem(ctx context.Context, item interface{}) (interface{}, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if len(m.pipelines) == 0 {
		return item, nil
	}

	processedItem := item

	// 按优先级顺序处理数据项
	for _, pipeline := range m.pipelines {
		var err error
		processedItem, err = pipeline.ProcessItem(ctx, processedItem)
		if err != nil {
			// 尝试从item中提取spider_type
			spiderType := m.extractSpiderType(processedItem)
			m.logger.Error(fmt.Sprintf("管道 %s 处理数据项失败：%s", pipeline.Name(), err.Error()),
				"platform", spiderType)
			return nil, fmt.Errorf("pipeline %s process item failed: %w", pipeline.Name(), err)
		}

		// 如果管道返回nil，表示数据项应该被丢弃
		if processedItem == nil {
			spiderType := m.extractSpiderType(item) // 使用原始item
			m.logger.Debug(fmt.Sprintf("数据项被管道 %s 丢弃", pipeline.Name()),
				"platform", spiderType)
			return nil, nil
		}
	}

	return processedItem, nil
}

// ProcessItemAsync 异步处理数据项
func (m *Manager) ProcessItemAsync(ctx context.Context, item interface{}) (interface{}, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if len(m.pipelines) == 0 {
		return item, nil
	}

	processedItem := item

	// 按优先级顺序异步处理数据项
	for _, pipeline := range m.pipelines {
		var err error
		processedItem, err = pipeline.ProcessItemAsync(ctx, processedItem)
		if err != nil {
			spiderType := m.extractSpiderType(processedItem)
			m.logger.Error(fmt.Sprintf("管道异步处理数据项失败：%s，错误：%s", pipeline.Name(), err.Error()),
				"platform", spiderType)
			return nil, fmt.Errorf("pipeline %s async process item failed: %w", pipeline.Name(), err)
		}

		// 如果管道返回nil，表示数据项应该被丢弃
		if processedItem == nil {
			spiderType := m.extractSpiderType(item) // 使用原始item
			m.logger.Warn(fmt.Sprintf("数据项被管道异步丢弃：%s", pipeline.Name()),
				"platform", spiderType)
			return nil, nil
		}
	}

	return processedItem, nil
}

// GetStats 获取所有管道的统计信息
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := map[string]interface{}{
		"total_pipelines": len(m.pipelines),
		"pipelines":       make(map[string]interface{}),
	}

	pipelineStats := stats["pipelines"].(map[string]interface{})
	for _, pipeline := range m.pipelines {
		pipelineStats[pipeline.Name()] = pipeline.GetStats()
	}

	return stats
}

// Count 返回管道数量
func (m *Manager) Count() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.pipelines)
}

// Close 关闭所有管道
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	for _, pipeline := range m.pipelines {
		if err := pipeline.Close(); err != nil {
			m.logger.Error(fmt.Sprintf("关闭管道 %s 失败：%s", pipeline.Name(), err.Error()))
		}
	}

	m.pipelines = nil
	m.initialized = false

	m.logger.Info("管道管理器已关闭")
	return nil
}

// createPipeline 创建管道实例
func (m *Manager) createPipeline(name string, config config.PipelineConfig) (Pipeline, error) {
	m.logger.Debug(fmt.Sprintf("创建管道实例 %s，优先级：%d，启用：%t",
		name, config.Priority, config.Enabled))

	switch name {
	case "ValidationPipeline":
		return NewValidationPipeline(config.Priority, config.Settings), nil
	case "DuplicatesPipeline":
		duplicatesPipeline := NewDuplicatesPipeline(config.Priority, config.Settings)
		// 为去重管道注入Redis依赖
		duplicatesPipeline.SetRedisManager(m.redisManager)
		return duplicatesPipeline, nil
	case "NotificationPipeline":
		return NewNotificationPipeline(config.Priority, config.Settings), nil
	default:
		return nil, fmt.Errorf("unknown pipeline type: %s", name)
	}
}

// extractSpiderType 从数据项中提取spider_type信息
func (m *Manager) extractSpiderType(item interface{}) string {
	if item == nil {
		return ""
	}

	// 尝试从ProductItem中提取Platform信息
	if productItem, ok := item.(*models.ProductItem); ok {
		if productItem.Platform != "" {
			return strings.ToLower(productItem.Platform)
		}
	}

	// 使用反射尝试获取Platform字段
	v := reflect.ValueOf(item)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() == reflect.Struct {
		if platformField := v.FieldByName("Platform"); platformField.IsValid() && platformField.Kind() == reflect.String {
			platform := platformField.String()
			if platform != "" {
				return strings.ToLower(platform)
			}
		}
	}

	return ""
}
