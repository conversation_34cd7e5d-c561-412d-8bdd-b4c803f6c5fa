package pipelines

import (
	"context"
	"fmt"
	"maps"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"go-monitor/internal/models"
)

// ValidationPipeline 数据验证管道 - 对应Python版本的validation.py
type ValidationPipeline struct {
	*BasePipeline

	// 配置驱动的字段定义
	strictMode     bool
	requiredFields map[string][]string
	fieldTypes     map[string]map[string]interface{}

	// 扩展统计信息
	stats struct {
		Processed int64 `json:"processed"`
		Dropped   int64 `json:"dropped"`
		Errors    int64 `json:"errors"`
		Fixed     int64 `json:"fixed"`
		Valid     int64 `json:"valid"`
	}

	// 自己的mutex
	statsMu sync.RWMutex
}

// 默认必填字段定义 - 对应Python版本的DEFAULT_REQUIRED_FIELDS
var defaultRequiredFields = map[string][]string{
	"ProductItem": {
		"ProductID",
		"Title",
		"URL",
		"Platform",
		"Price",
		"Currency",
		"Stock",
		"InStock",
		"Availability",
		"Country",
		"SiteURL",
	},
	"NotificationItem": {
		"Level",
		"Title",
		"Message",
	},
}

// 默认字段类型定义 - 对应Python版本的DEFAULT_FIELD_TYPES
var defaultFieldTypes = map[string]map[string]interface{}{
	"ProductItem": {
		"ProductID":     "string",
		"Title":         "string",
		"URL":           "string",
		"Platform":      "string",
		"Price":         "float64",
		"Currency":      "string",
		"Stock":         "int",
		"InStock":       "bool",
		"Availability":  "string",
		"Country":       "string",
		"SiteURL":       "string",
		"SkuID":         "*string",
		"OfferID":       "*string",
		"ImageURL":      "*string",
		"Addition":      "*string",
		"ReleaseDate":   "*string",
		"AtcLink":       "*string",
		"Notifications": "[]string",
		"CrawledAt":     "time.Time",
		"Metadata":      "map[string]interface{}",
	},
	"NotificationItem": {
		"Level":     "*string",
		"Title":     "*string",
		"Message":   "*string",
		"Timestamp": "*string",
	},
}

// NewValidationPipeline 创建数据验证管道
func NewValidationPipeline(priority int, config map[string]interface{}) *ValidationPipeline {
	base := NewBasePipeline("validation", priority, config)

	// 从配置中获取严格模式设置
	strictMode := true
	if val, ok := config["strict_mode"].(bool); ok {
		strictMode = val
	}

	// 从配置中获取必填字段定义，如果没有则使用默认值
	requiredFields := defaultRequiredFields
	if configRequiredFields, ok := config["required_fields"].(map[string]interface{}); ok {
		requiredFields = make(map[string][]string)
		for itemType, fields := range configRequiredFields {
			if fieldList, ok := fields.([]interface{}); ok {
				requiredFields[itemType] = make([]string, len(fieldList))
				for i, field := range fieldList {
					if str, ok := field.(string); ok {
						requiredFields[itemType][i] = str
					}
				}
			}
		}
	}

	// 从配置中获取字段类型定义，如果没有则使用默认值
	fieldTypes := defaultFieldTypes
	if configFieldTypes, ok := config["field_types"].(map[string]interface{}); ok {
		fieldTypes = make(map[string]map[string]interface{})
		for itemType, typeMap := range configFieldTypes {
			if typeMapInterface, ok := typeMap.(map[string]interface{}); ok {
				fieldTypes[itemType] = make(map[string]interface{})
				maps.Copy(fieldTypes[itemType], typeMapInterface)
			}
		}
	}

	pipeline := &ValidationPipeline{
		BasePipeline:   base,
		strictMode:     strictMode,
		requiredFields: requiredFields,
		fieldTypes:     fieldTypes,
	}

	pipeline.logger.Info(fmt.Sprintf("验证管道已启用，严格模式：%t", strictMode))

	return pipeline
}

// ProcessItem 处理数据项，进行验证 - 对应Python版本的process_item
func (v *ValidationPipeline) ProcessItem(ctx context.Context, item interface{}) (interface{}, error) {
	// 调用父类方法，增加处理计数
	v.updateStats("processed", 1)

	// 检查数据项类型
	var productItem *models.ProductItem

	switch it := item.(type) {
	case *models.ProductItem:
		productItem = it
	case models.ProductItem:
		productItem = &it
	default:
		v.updateStats("errors", 1)
		v.logger.Error(fmt.Sprintf("不支持的数据项类型：%T", item))
		return nil, fmt.Errorf("unsupported item type: %T", item)
	}

	platform := strings.ToLower(productItem.Platform)
	if platform == "" {
		platform = "monitor"
	}

	// Step 1: 修复字段类型
	itemType := "ProductItem"
	productItem = v.fixFieldTypes(itemType, productItem)

	// Step 2: 清洗数据
	productItem = v.cleanItem(productItem)

	// Step 3: 验证必填字段
	missingFields := v.validateRequiredFields(itemType, productItem)
	if len(missingFields) > 0 && v.strictMode {
		v.updateStats("dropped", 1)
		v.logger.Warn(fmt.Sprintf("缺少必填字段：%s", strings.Join(missingFields, ", ")),
			"platform", platform)
		return nil, nil
	}

	// Step 4: 验证字段类型
	typeMismatches := v.validateFieldTypes(itemType, productItem)
	if len(typeMismatches) > 0 && v.strictMode {
		v.updateStats("dropped", 1)
		mismatchFields := make([]string, 0, len(typeMismatches))
		for field := range typeMismatches {
			mismatchFields = append(mismatchFields, field)
		}
		v.logger.Warn(fmt.Sprintf("字段类型不匹配：%s", strings.Join(mismatchFields, ", ")),
			"platform", platform)
		return nil, nil
	}

	// 验证通过
	v.updateStats("valid", 1)
	return productItem, nil
}

// validateRequiredFields 验证必填字段 - 对应Python版本的_validate_required_fields
func (v *ValidationPipeline) validateRequiredFields(itemType string, item *models.ProductItem) []string {
	missingFields := []string{}
	requiredFields, exists := v.requiredFields[itemType]
	if !exists {
		return missingFields
	}

	for _, field := range requiredFields {
		switch field {
		case "ProductID":
			if strings.TrimSpace(item.ProductID) == "" {
				missingFields = append(missingFields, field)
			}
		case "Title":
			if strings.TrimSpace(item.Title) == "" {
				missingFields = append(missingFields, field)
			}
		case "URL":
			if strings.TrimSpace(item.URL) == "" {
				missingFields = append(missingFields, field)
			}
		case "Platform":
			if strings.TrimSpace(item.Platform) == "" {
				missingFields = append(missingFields, field)
			}
		case "Price":
			if item.Price < 0 {
				missingFields = append(missingFields, field)
			}
		case "Currency":
			if strings.TrimSpace(item.Currency) == "" {
				missingFields = append(missingFields, field)
			}
		case "Stock":
			if item.Stock < 0 {
				missingFields = append(missingFields, field)
			}
		case "InStock":
			// bool类型不需要验证
		case "Availability":
			if strings.TrimSpace(item.Availability) == "" {
				missingFields = append(missingFields, field)
			}
		case "Country":
			if strings.TrimSpace(item.Country) == "" {
				missingFields = append(missingFields, field)
			}
		case "SiteURL":
			if strings.TrimSpace(item.SiteURL) == "" {
				missingFields = append(missingFields, field)
			}
		}
	}
	return missingFields
}

// validateFieldTypes 验证字段类型 - 对应Python版本的_validate_field_types
func (v *ValidationPipeline) validateFieldTypes(itemType string, item *models.ProductItem) map[string]interface{} {
	typeMismatches := make(map[string]interface{})
	fieldTypes, exists := v.fieldTypes[itemType]
	if !exists {
		return typeMismatches
	}

	itemValue := reflect.ValueOf(item).Elem()
	itemTypeStruct := itemValue.Type()

	for i := 0; i < itemValue.NumField(); i++ {
		field := itemTypeStruct.Field(i)
		fieldValue := itemValue.Field(i)
		fieldName := field.Name

		expectedType, exists := fieldTypes[fieldName]
		if !exists {
			continue
		}

		// 检查字段类型是否匹配
		if !v.isTypeMatch(fieldValue, expectedType) {
			typeMismatches[fieldName] = map[string]interface{}{
				"expected": expectedType,
				"actual":   fieldValue.Type().String(),
			}
		}
	}

	return typeMismatches
}

// fixFieldTypes 修复字段类型 - 对应Python版本的_fix_field_types
func (v *ValidationPipeline) fixFieldTypes(itemType string, item *models.ProductItem) *models.ProductItem {
	fixedCount := 0
	fieldTypes, exists := v.fieldTypes[itemType]
	if !exists {
		return item
	}

	itemValue := reflect.ValueOf(item).Elem()
	itemTypeStruct := itemValue.Type()

	for i := 0; i < itemValue.NumField(); i++ {
		field := itemTypeStruct.Field(i)
		fieldValue := itemValue.Field(i)
		fieldName := field.Name

		expectedType, exists := fieldTypes[fieldName]
		if !exists {
			continue
		}

		// 尝试修复字段类型
		if v.tryFixFieldType(fieldValue, expectedType) {
			fixedCount++
		}
	}

	if fixedCount > 0 {
		v.updateStats("fixed", int64(fixedCount))
		v.logger.Debug(fmt.Sprintf("修复了字段类型，修复数量：%d", fixedCount),
			"platform", item.Platform)
	}

	return item
}

// cleanItem 清洗数据项 - 对应Python版本的_clean_item
func (v *ValidationPipeline) cleanItem(item *models.ProductItem) *models.ProductItem {
	// 清理字符串类型的字段
	item.Title = strings.TrimSpace(item.Title)
	item.URL = strings.TrimSpace(item.URL)
	item.Platform = strings.TrimSpace(item.Platform)
	item.Currency = strings.TrimSpace(item.Currency)
	item.Availability = strings.TrimSpace(item.Availability)
	item.Country = strings.TrimSpace(item.Country)
	item.SiteURL = strings.TrimSpace(item.SiteURL)

	// 清理指针字段
	if item.ImageURL != nil {
		trimmed := strings.TrimSpace(*item.ImageURL)
		item.ImageURL = &trimmed
	}
	if item.SkuID != nil {
		trimmed := strings.TrimSpace(*item.SkuID)
		item.SkuID = &trimmed
	}
	if item.OfferID != nil {
		trimmed := strings.TrimSpace(*item.OfferID)
		item.OfferID = &trimmed
	}
	if item.Addition != nil {
		trimmed := strings.TrimSpace(*item.Addition)
		item.Addition = &trimmed
	}
	if item.ReleaseDate != nil {
		trimmed := strings.TrimSpace(*item.ReleaseDate)
		item.ReleaseDate = &trimmed
	}
	if item.AtcLink != nil {
		trimmed := strings.TrimSpace(*item.AtcLink)
		item.AtcLink = &trimmed
	}

	// 添加时间戳
	if item.CrawledAt.IsZero() {
		item.CrawledAt = time.Now()
	}

	return item
}

// isTypeMatch 检查类型是否匹配
func (v *ValidationPipeline) isTypeMatch(value reflect.Value, expectedType interface{}) bool {
	actualType := value.Type()

	switch expectedType {
	case "string":
		return actualType.Kind() == reflect.String
	case "float64":
		return actualType.Kind() == reflect.Float64
	case "int":
		return actualType.Kind() == reflect.Int
	case "bool":
		return actualType.Kind() == reflect.Bool
	case "*string":
		return actualType.Kind() == reflect.Ptr && actualType.Elem().Kind() == reflect.String
	case "[]string":
		return actualType.Kind() == reflect.Slice && actualType.Elem().Kind() == reflect.String
	case "time.Time":
		return actualType.String() == "time.Time"
	case "map[string]interface{}":
		return actualType.Kind() == reflect.Map
	}

	return false
}

// tryFixFieldType 尝试修复字段类型
func (v *ValidationPipeline) tryFixFieldType(value reflect.Value, expectedType interface{}) bool {
	if !value.CanSet() {
		return false
	}

	switch expectedType {
	case "string":
		if value.Kind() != reflect.String {
			value.SetString(fmt.Sprintf("%v", value.Interface()))
			return true
		}
	case "float64":
		if value.Kind() != reflect.Float64 {
			if str, ok := value.Interface().(string); ok {
				if f, err := strconv.ParseFloat(str, 64); err == nil {
					value.SetFloat(f)
					return true
				}
			}
		}
	case "int":
		if value.Kind() != reflect.Int {
			if str, ok := value.Interface().(string); ok {
				if i, err := strconv.Atoi(str); err == nil {
					value.SetInt(int64(i))
					return true
				}
			}
		}
	case "bool":
		if value.Kind() != reflect.Bool {
			if str, ok := value.Interface().(string); ok {
				b := strings.ToLower(str) == "true" || str == "1" || str == "yes"
				value.SetBool(b)
				return true
			}
		}
	}

	return false
}

// GetStats 获取统计信息 - 重写基类方法以包含扩展统计
func (v *ValidationPipeline) GetStats() map[string]interface{} {
	baseStats := v.BasePipeline.GetStats()

	v.statsMu.RLock()
	defer v.statsMu.RUnlock()

	baseStats["fixed"] = v.stats.Fixed
	baseStats["valid"] = v.stats.Valid
	return baseStats
}

// updateStats 更新统计信息 - 重写基类方法以支持扩展统计
func (v *ValidationPipeline) updateStats(field string, value int64) {
	v.BasePipeline.updateStats(field, value)

	v.statsMu.Lock()
	defer v.statsMu.Unlock()

	switch field {
	case "fixed":
		v.stats.Fixed += value
	case "valid":
		v.stats.Valid += value
	}
}

// Close 关闭管道 - 对应Python版本的close
func (v *ValidationPipeline) Close() error {
	v.statsMu.RLock()
	processed := v.stats.Processed
	valid := v.stats.Valid
	fixed := v.stats.Fixed
	dropped := v.stats.Dropped
	v.statsMu.RUnlock()

	v.logger.Info(fmt.Sprintf("验证管道关闭 - 处理：%d，有效：%d，修复：%d，丢弃：%d",
		processed, valid, fixed, dropped))
	return v.BasePipeline.Close()
}
