package pipelines

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/redis"
)

// ProductStatus 产品状态 - 对应Python版本的ProductStatus
type ProductStatus struct {
	ProductKey        string    `json:"product_key"`
	ProductID         string    `json:"product_id"`
	SkuID             *string   `json:"sku_id"`
	URL               string    `json:"url"`
	InStock           bool      `json:"in_stock"`
	Price             float64   `json:"price"`
	ReleaseDate       *string   `json:"release_date"`
	StatusFingerprint string    `json:"status_fingerprint"`
	LastChanged       time.Time `json:"last_changed"`
	CreatedAt         time.Time `json:"created_at"`
}

// GenerateProductKey 生成产品唯一标识
func (ps *ProductStatus) GenerateProductKey(url string) string {
	hash := md5.Sum([]byte(url))
	return fmt.Sprintf("%x", hash)
}

// StockConfirmation 库存确认状态
type StockConfirmation struct {
	LastStockStatus       bool      `json:"last_stock_status"`
	OutOfStockCount       int       `json:"out_of_stock_count"`
	FirstSeenOutOfStock   time.Time `json:"first_seen_out_of_stock"`
	ConfirmationThreshold int       `json:"confirmation_threshold"`
}

// DuplicatesPipeline 去重管道 - 使用Redis存储
type DuplicatesPipeline struct {
	*BasePipeline

	// 配置驱动的字段定义
	statusFields []string

	// Redis连接（通过依赖注入）
	redisManager *redis.RedisManager
	keyPrefix    string
	defaultTTL   time.Duration

	// 库存确认配置
	stockConfirmationEnabled bool
	outOfStockThreshold      int

	// 扩展统计信息
	stats struct {
		Processed          int64 `json:"processed"`
		Dropped            int64 `json:"dropped"`
		Errors             int64 `json:"errors"`
		Duplicates         int64 `json:"duplicates"`
		StatusChanges      int64 `json:"status_changes"`
		StockConfirmations int64 `json:"stock_confirmations"`
		StockRejections    int64 `json:"stock_rejections"`
	}

	// 自己的mutex
	statsMu sync.RWMutex
}

// 默认状态字段 - 对应Python版本的DEFAULT_STATUS_FIELDS
var defaultStatusFields = []string{
	"ProductID",
	"SkuID",
	"URL",
	"InStock",
	"Price",
	"ReleaseDate",
}

// NewDuplicatesPipeline 创建去重管道
func NewDuplicatesPipeline(priority int, config map[string]interface{}) *DuplicatesPipeline {
	base := NewBasePipeline("duplicates", priority, config)

	// 从配置中获取指纹计算字段
	statusFields := defaultStatusFields
	if fields, ok := config["fingerprint_fields"].([]string); ok {
		statusFields = fields
	} else if fields, ok := config["fingerprint_fields"].([]interface{}); ok {
		statusFields = make([]string, len(fields))
		for i, field := range fields {
			if str, ok := field.(string); ok {
				statusFields[i] = str
			}
		}
	}

	// 从配置中获取Redis相关参数
	keyPrefix := "dedup:"
	if prefix, ok := config["key_prefix"].(string); ok {
		keyPrefix = prefix
	}

	defaultTTL := 24 * time.Hour
	if ttlStr, ok := config["ttl"].(string); ok {
		if ttl, err := time.ParseDuration(ttlStr); err == nil {
			defaultTTL = ttl
		}
	}

	// 库存确认配置
	stockConfirmationEnabled := true
	if enabled, ok := config["stock_confirmation_enabled"].(bool); ok {
		stockConfirmationEnabled = enabled
	}

	outOfStockThreshold := 3
	if threshold, ok := config["out_of_stock_threshold"].(int); ok {
		outOfStockThreshold = int(threshold)
	}

	pipeline := &DuplicatesPipeline{
		BasePipeline:             base,
		statusFields:             statusFields,
		keyPrefix:                keyPrefix,
		defaultTTL:               defaultTTL,
		stockConfirmationEnabled: stockConfirmationEnabled,
		outOfStockThreshold:      outOfStockThreshold,
	}

	pipeline.logger.Info(fmt.Sprintf("去重管道已创建，将在Open时连接Redis，键前缀：%s，TTL：%s，库存确认：%t，缺货阈值：%d",
		keyPrefix, defaultTTL.String(), stockConfirmationEnabled, outOfStockThreshold))

	return pipeline
}

// SetRedisManager 设置Redis管理器（依赖注入）
func (d *DuplicatesPipeline) SetRedisManager(redisManager *redis.RedisManager) {
	d.redisManager = redisManager
}

// Open 打开管道
func (d *DuplicatesPipeline) Open() error {
	// 先调用基类的Open方法
	if err := d.BasePipeline.Open(); err != nil {
		return err
	}

	// 检查Redis管理器是否已注入
	if d.redisManager == nil {
		return fmt.Errorf("Redis管理器未注入，请先调用SetRedisManager")
	}

	d.logger.Info(fmt.Sprintf("去重管道已打开，使用共享Redis连接，键前缀：%s，TTL：%s",
		d.keyPrefix, d.defaultTTL.String()))

	return nil
}

// Close 关闭管道
func (d *DuplicatesPipeline) Close() error {
	d.logger.Info(fmt.Sprintf("去重管道已关闭，统计：%v", d.GetStats()))

	// Redis连接由ServiceManager管理，这里不需要关闭
	// 调用基类的Close方法
	return d.BasePipeline.Close()
}

// ProcessItem 处理数据项，检查重复 - 对应Python版本的process_item
func (d *DuplicatesPipeline) ProcessItem(ctx context.Context, item interface{}) (interface{}, error) {
	// 调用父类方法，增加处理计数
	d.updateStats("processed", 1)

	// 检查数据项类型
	var productItem *models.ProductItem

	switch it := item.(type) {
	case *models.ProductItem:
		productItem = it
	case models.ProductItem:
		productItem = &it
	default:
		d.updateStats("errors", 1)
		d.logger.Error(fmt.Sprintf("不支持的数据项类型：%T", item))
		return nil, fmt.Errorf("unsupported item type: %T", item)
	}

	// 检查产品状态
	isDuplicate, isStatusChanged, existingRecord := d.checkProductStatus(productItem)

	if isDuplicate && !isStatusChanged {
		// 完全重复，丢弃
		d.updateStats("duplicates", 1)
		d.logger.Debug(fmt.Sprintf("检测到重复数据项：%s", productItem.URL),
			"platform", productItem.Platform)
		return nil, nil
	}

	// 库存确认检查
	if d.stockConfirmationEnabled && isStatusChanged {
		shouldProcess, reason := d.checkStockConfirmation(productItem, existingRecord)
		if !shouldProcess {
			d.updateStats("stock_rejections", 1)
			d.logger.Debug(fmt.Sprintf("库存变化被拒绝：%s，原因：%s", productItem.URL, reason),
				"platform", productItem.Platform)
			return nil, nil
		}
		if reason != "" {
			d.updateStats("stock_confirmations", 1)
			d.logger.Debug(fmt.Sprintf("库存变化已确认：%s, url：%s，原因：%s", productItem.Name, productItem.URL, reason),
				"platform", productItem.Platform)
		}
	}

	// 保存产品状态
	d.saveProduct(productItem, existingRecord)

	return productItem, nil
}

// checkProductStatus 检查产品状态 - 使用Redis存储
func (d *DuplicatesPipeline) checkProductStatus(item *models.ProductItem) (bool, bool, *ProductStatus) {
	// 检查Redis连接是否可用
	if d.redisManager == nil {
		d.logger.Error("Redis连接未初始化", "platform", item.Platform)
		d.updateStats("errors", 1)
		return false, true, nil
	}

	// 生成产品唯一标识
	productKey := (&ProductStatus{}).GenerateProductKey(item.URL)
	redisKey := d.keyPrefix + item.Platform + ":" + productKey

	// 从Redis查询现有记录
	ctx := context.Background()
	data, err := d.redisManager.Get(ctx, redisKey)

	var productStatus *ProductStatus
	if err == nil {
		// 反序列化产品状态
		productStatus = &ProductStatus{}
		if err := json.Unmarshal(data, productStatus); err != nil {
			d.logger.Error(fmt.Sprintf("反序列化产品状态失败，键：%s，错误：%s",
				redisKey, err.Error()), "platform", item.Platform)
			productStatus = nil
		}
	} else if err.Error() != "key not found" {
		// Redis错误（非键不存在）
		d.logger.Error(fmt.Sprintf("从Redis获取产品状态失败，键：%s，错误：%s",
			redisKey, err.Error()), "platform", item.Platform)
		d.updateStats("errors", 1)
	}

	// 计算当前状态指纹
	statusFingerprint := d.getStatusFingerprint(item)

	if productStatus != nil {
		// 检查状态是否变化
		isStatusChanged := productStatus.StatusFingerprint != statusFingerprint
		if isStatusChanged {
			d.updateStats("status_changes", 1)
			d.logStatusChange(productStatus, item)
		}
		return true, isStatusChanged, productStatus
	} else {
		// 新产品
		return false, true, nil
	}
}

// saveProduct 保存产品状态 - 使用Redis存储
func (d *DuplicatesPipeline) saveProduct(item *models.ProductItem, existingRecord *ProductStatus) {
	// 检查Redis连接是否可用
	if d.redisManager == nil {
		d.logger.Error("Redis连接未初始化，无法保存产品状态", "platform", item.Platform)
		d.updateStats("errors", 1)
		return
	}

	// 计算状态指纹
	productKey := (&ProductStatus{}).GenerateProductKey(item.URL)
	statusFingerprint := d.getStatusFingerprint(item)
	redisKey := d.keyPrefix + item.Platform + ":" + productKey

	var productStatus *ProductStatus

	if existingRecord != nil {
		// 更新现有记录
		productStatus = existingRecord
		for _, field := range d.statusFields {
			switch field {
			case "ProductID":
				productStatus.ProductID = item.ProductID
			case "SkuID":
				productStatus.SkuID = item.SkuID
			case "URL":
				productStatus.URL = item.URL
			case "InStock":
				productStatus.InStock = item.InStock
			case "Price":
				// 仅当新价格大于0时才更新价格
				if item.Price > 0 {
					productStatus.Price = item.Price
				}
			case "ReleaseDate":
				productStatus.ReleaseDate = item.ReleaseDate
			}
		}
		productStatus.StatusFingerprint = statusFingerprint
		productStatus.LastChanged = time.Now()
	} else {
		// 创建新记录
		productStatus = &ProductStatus{
			ProductKey:        productKey,
			ProductID:         item.ProductID,
			SkuID:             item.SkuID,
			URL:               item.URL,
			InStock:           item.InStock,
			Price:             item.Price,
			ReleaseDate:       item.ReleaseDate,
			StatusFingerprint: statusFingerprint,
			LastChanged:       time.Now(),
			CreatedAt:         time.Now(),
		}
	}

	// 序列化并保存到Redis
	ctx := context.Background()
	data, err := json.Marshal(productStatus)
	if err != nil {
		d.logger.Error(fmt.Sprintf("序列化产品状态失败，键：%s，错误：%s",
			redisKey, err.Error()), "platform", item.Platform)
		d.updateStats("errors", 1)
		return
	}

	// 保存到Redis，设置TTL
	if err := d.redisManager.Set(ctx, redisKey, data, d.defaultTTL); err != nil {
		d.logger.Error(fmt.Sprintf("保存产品状态到Redis失败，键：%s，错误：%s",
			redisKey, err.Error()), "platform", item.Platform)
		d.updateStats("errors", 1)
		return
	}

	d.logger.Debug(fmt.Sprintf("产品状态已保存到Redis，键：%s，TTL：%s",
		redisKey, d.defaultTTL.String()), "platform", item.Platform)
}

// getStatusFingerprint 生成状态指纹 - 对应Python版本的_get_status_fingerprint
func (d *DuplicatesPipeline) getStatusFingerprint(item *models.ProductItem) string {
	// 使用字典存储字段值，确保排序后顺序稳定
	valuesDict := make(map[string]string)
	for _, field := range d.statusFields {
		switch field {
		case "ProductID":
			if item.ProductID != "" {
				valuesDict[field] = item.ProductID
			}
		case "SkuID":
			if item.SkuID != nil && *item.SkuID != "" {
				valuesDict[field] = *item.SkuID
			}
		case "URL":
			if item.URL != "" {
				valuesDict[field] = item.URL
			}
		case "InStock":
			valuesDict[field] = fmt.Sprintf("%t", item.InStock)
		case "Price":
			valuesDict[field] = fmt.Sprintf("%.2f", item.Price)
		case "ReleaseDate":
			if item.ReleaseDate != nil && *item.ReleaseDate != "" {
				valuesDict[field] = *item.ReleaseDate
			}
		}
	}

	// 计算指纹（使用排序后的字典）
	jsonData, _ := json.Marshal(valuesDict)
	hash := md5.Sum(jsonData)
	return fmt.Sprintf("%x", hash)
}

// logStatusChange 记录状态变化 - 对应Python版本的_log_status_change
func (d *DuplicatesPipeline) logStatusChange(oldStatus *ProductStatus, newItem *models.ProductItem) {
	// 获取变化详情
	changes := []string{}
	for _, field := range d.statusFields {
		var oldValue, newValue string

		switch field {
		case "ProductID":
			oldValue = oldStatus.ProductID
			newValue = newItem.ProductID
		case "SkuID":
			if oldStatus.SkuID != nil {
				oldValue = *oldStatus.SkuID
			}
			if newItem.SkuID != nil {
				newValue = *newItem.SkuID
			}
		case "URL":
			oldValue = oldStatus.URL
			newValue = newItem.URL
		case "InStock":
			oldValue = fmt.Sprintf("%t", oldStatus.InStock)
			newValue = fmt.Sprintf("%t", newItem.InStock)
		case "Price":
			oldValue = fmt.Sprintf("%.2f", oldStatus.Price)
			newValue = fmt.Sprintf("%.2f", newItem.Price)
		case "ReleaseDate":
			if oldStatus.ReleaseDate != nil {
				oldValue = *oldStatus.ReleaseDate
			}
			if newItem.ReleaseDate != nil {
				newValue = *newItem.ReleaseDate
			}
		}

		if oldValue != newValue {
			changes = append(changes, fmt.Sprintf("%s: %s -> %s", field, oldValue, newValue))
		}
	}

	// 记录变化
	changesStr := strings.Join(changes, ", ")
	d.logger.Info(fmt.Sprintf("产品状态变化：%s, url: %s，变化：%s", newItem.Name, newItem.URL, changesStr),
		"platform", newItem.Platform)
}

// GetStats 获取统计信息 - 重写基类方法以包含扩展统计
func (d *DuplicatesPipeline) GetStats() map[string]interface{} {
	d.statsMu.RLock()
	defer d.statsMu.RUnlock()

	stats := d.BasePipeline.GetStats()
	stats["duplicates"] = d.stats.Duplicates
	stats["status_changes"] = d.stats.StatusChanges
	stats["stock_confirmations"] = d.stats.StockConfirmations
	stats["stock_rejections"] = d.stats.StockRejections
	stats["redis_key_prefix"] = d.keyPrefix
	stats["redis_ttl"] = d.defaultTTL.String()
	stats["stock_confirmation_enabled"] = d.stockConfirmationEnabled
	stats["out_of_stock_threshold"] = d.outOfStockThreshold

	return stats
}

// updateStats 更新统计信息 - 重写基类方法以支持扩展统计
func (d *DuplicatesPipeline) updateStats(field string, value int64) {
	d.BasePipeline.updateStats(field, value)

	d.statsMu.Lock()
	defer d.statsMu.Unlock()

	switch field {
	case "duplicates":
		d.stats.Duplicates += value
	case "status_changes":
		d.stats.StatusChanges += value
	case "stock_confirmations":
		d.stats.StockConfirmations += value
	case "stock_rejections":
		d.stats.StockRejections += value
	}
}

// checkStockConfirmation 检查库存变化确认
func (d *DuplicatesPipeline) checkStockConfirmation(item *models.ProductItem, existingRecord *ProductStatus) (bool, string) {
	// 如果没有历史记录（新数据），直接通过，无论是有库存还是无库存
	if existingRecord == nil {
		return true, "新数据，直接通过"
	}

	currentInStock := item.InStock
	previousInStock := existingRecord.InStock

	// 有库存 → 有库存：正常处理
	// 无库存 → 无库存：正常处理
	// 无库存 → 有库存：立即通知（宁可误报也不能漏报）
	if !previousInStock && currentInStock {
		// 清理可能存在的确认状态
		d.clearStockConfirmation(item)
		return true, "库存恢复，立即通知"
	}

	// 有库存 → 无库存：需要确认（防止虚假无库存）
	if previousInStock && !currentInStock {
		return d.confirmOutOfStock(item)
	}

	// 其他情况正常处理
	return true, ""
}

// confirmOutOfStock 确认缺货状态
func (d *DuplicatesPipeline) confirmOutOfStock(item *models.ProductItem) (bool, string) {
	productKey := (&ProductStatus{}).GenerateProductKey(item.URL)
	confirmKey := d.keyPrefix + item.Platform + ":confirm:" + productKey

	ctx := context.Background()

	// 获取确认状态
	confirmation, err := d.getStockConfirmation(confirmKey)
	if err != nil {
		d.logger.Error(fmt.Sprintf("获取库存确认状态失败：%s，错误：%s", confirmKey, err.Error()))
		// 出错时保守处理，允许通过
		return true, ""
	}

	now := time.Now()

	if confirmation == nil {
		// 首次检测到缺货，创建确认记录
		confirmation = &StockConfirmation{
			LastStockStatus:       true, // 之前有库存
			OutOfStockCount:       1,
			FirstSeenOutOfStock:   now,
			ConfirmationThreshold: d.outOfStockThreshold,
		}
	} else {
		// 增加缺货计数
		confirmation.OutOfStockCount++
	}

	// 保存确认状态
	if err := d.saveStockConfirmation(confirmKey, confirmation); err != nil {
		d.logger.Error(fmt.Sprintf("保存库存确认状态失败：%s，错误：%s", confirmKey, err.Error()))
	}

	// 检查是否达到确认阈值
	if confirmation.OutOfStockCount >= confirmation.ConfirmationThreshold {
		// 达到阈值，确认缺货，删除确认记录
		d.redisManager.Delete(ctx, confirmKey)
		return true, fmt.Sprintf("缺货状态已确认（%d次确认）", confirmation.OutOfStockCount)
	}

	// 未达到阈值，拒绝此次变化
	return false, fmt.Sprintf("缺货状态待确认（%d/%d次）",
		confirmation.OutOfStockCount, confirmation.ConfirmationThreshold)
}

// getStockConfirmation 获取库存确认状态
func (d *DuplicatesPipeline) getStockConfirmation(confirmKey string) (*StockConfirmation, error) {
	ctx := context.Background()
	data, err := d.redisManager.Get(ctx, confirmKey)

	if err != nil {
		if err.Error() == "key not found" {
			return nil, nil
		}
		return nil, err
	}

	var confirmation StockConfirmation
	if err := json.Unmarshal(data, &confirmation); err != nil {
		return nil, err
	}

	return &confirmation, nil
}

// saveStockConfirmation 保存库存确认状态
func (d *DuplicatesPipeline) saveStockConfirmation(confirmKey string, confirmation *StockConfirmation) error {
	ctx := context.Background()
	data, err := json.Marshal(confirmation)
	if err != nil {
		return err
	}

	return d.redisManager.Set(ctx, confirmKey, data, d.defaultTTL)
}

// clearStockConfirmation 清理库存确认状态
func (d *DuplicatesPipeline) clearStockConfirmation(item *models.ProductItem) {
	productKey := (&ProductStatus{}).GenerateProductKey(item.URL)
	confirmKey := d.keyPrefix + item.Platform + ":confirm:" + productKey

	ctx := context.Background()
	if err := d.redisManager.Delete(ctx, confirmKey); err != nil {
		d.logger.Debug(fmt.Sprintf("清理库存确认状态失败：%s，错误：%s", confirmKey, err.Error()))
	} else {
		d.logger.Debug(fmt.Sprintf("已清理库存确认状态：%s", confirmKey))
	}
}
