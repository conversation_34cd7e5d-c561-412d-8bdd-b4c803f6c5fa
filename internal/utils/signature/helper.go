package signature

import (
	"crypto/md5"
	"encoding/hex"
	"strconv"
	"time"

	"go-monitor/pkg/logging"
)

// SignatureConfig 签名配置
type SignatureConfig struct {
	Platform string                 `json:"platform"`
	Method   string                 `json:"method"`
	Secret   string                 `json:"secret"`
	AppKey   string                 `json:"app_key"`
	Version  string                 `json:"version"`
	Config   map[string]interface{} `json:"config"`
}

// Helper 签名助手
type Helper struct {
	logger  logging.Logger
	configs map[string]*SignatureConfig
}

// NewHelper 创建新的签名助手
func NewHelper() *Helper {

	return &Helper{
		logger:  logging.GetLogger("service.signature"),
		configs: make(map[string]*SignatureConfig),
	}
}

// AddConfig 添加签名配置
func (h *Helper) AddConfig(platform string, config *SignatureConfig) {
	h.configs[platform] = config
}

// GetConfig 获取签名配置
func (h *Helper) GetConfig(platform string) *SignatureConfig {
	return h.configs[platform]
}

// 未使用的签名生成函数已移除，实际签名逻辑在middleware/signature.go中实现

// 未使用的AliExpress签名生成函数已移除

// 未使用的Amazon签名生成函数已移除

// 未使用的Popmart签名生成函数已移除

// 未使用的辅助函数已移除：buildSignString、getCanonicalHeaders

// 未使用的辅助函数已移除：getSignedHeaders、getSigningKey、generateNonce

// 加密和哈希函数

// md5Hash 计算MD5哈希
func (h *Helper) md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

// 未使用的哈希和编码函数已移除：sha1Hash、sha256Hash、hmacSHA256、base64Encode、base64Decode

// 未使用的签名验证函数已移除

// GetTimestamp 获取当前时间戳
func (h *Helper) GetTimestamp() int64 {
	return time.Now().Unix()
}

// GetTimestampMs 获取当前毫秒时间戳
func (h *Helper) GetTimestampMs() int64 {
	return time.Now().UnixMilli()
}

// 辅助函数

// getString 从map中获取字符串值
func getString(params map[string]interface{}, key, defaultValue string) string {
	if value, exists := params[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return defaultValue
}

// getInt 从map中获取整数值
func getInt(params map[string]interface{}, key string, defaultValue int) int {
	if value, exists := params[key]; exists {
		switch v := value.(type) {
		case int:
			return v
		case int64:
			return int(v)
		case float64:
			return int(v)
		case string:
			if i, err := strconv.Atoi(v); err == nil {
				return i
			}
		}
	}
	return defaultValue
}

// DefaultHelper 默认签名助手实例
var DefaultHelper *Helper

func init() {
	DefaultHelper = NewHelper()

	// 添加默认配置
	DefaultHelper.AddConfig("aliexpress", &SignatureConfig{
		Platform: "aliexpress",
		Method:   "mtop",
		Secret:   "",
		AppKey:   "12574478",
		Version:  "1.0",
	})

	DefaultHelper.AddConfig("amazon", &SignatureConfig{
		Platform: "amazon",
		Method:   "aws4",
		Secret:   "",
		AppKey:   "",
		Version:  "4",
	})

	DefaultHelper.AddConfig("popmart", &SignatureConfig{
		Platform: "popmart",
		Method:   "custom",
		Secret:   "",
		AppKey:   "",
		Version:  "1.0",
	})
}
