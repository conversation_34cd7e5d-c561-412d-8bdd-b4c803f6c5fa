package pool

import (
	"sync"
)

// MapPool 字符串map对象池 - 用于复用map[string]interface{}
var MapPool = sync.Pool{
	New: func() interface{} {
		return make(map[string]interface{})
	},
}

// StringMapPool 字符串map对象池 - 用于复用map[string]string
var StringMapPool = sync.Pool{
	New: func() interface{} {
		return make(map[string]string)
	},
}

// SlicePool 字符串切片对象池 - 用于复用[]string
var SlicePool = sync.Pool{
	New: func() interface{} {
		return make([]string, 0, 10) // 预分配10个元素的容量
	},
}

// GetMap 从池中获取map[string]interface{}
func GetMap() map[string]interface{} {
	return MapPool.Get().(map[string]interface{})
}

// PutMap 将map[string]interface{}放回池中
func PutMap(m map[string]interface{}) {
	// 清空map但保留容量
	for k := range m {
		delete(m, k)
	}
	MapPool.Put(m)
}

// GetStringMap 从池中获取map[string]string
func GetStringMap() map[string]string {
	return StringMapPool.Get().(map[string]string)
}

// PutStringMap 将map[string]string放回池中
func PutStringMap(m map[string]string) {
	// 清空map但保留容量
	for k := range m {
		delete(m, k)
	}
	StringMapPool.Put(m)
}

// GetSlice 从池中获取[]string
func GetSlice() []string {
	return SlicePool.Get().([]string)
}

// PutSlice 将[]string放回池中
func PutSlice(s []string) {
	// 重置长度但保留容量
	s = s[:0]
	SlicePool.Put(s)
}
