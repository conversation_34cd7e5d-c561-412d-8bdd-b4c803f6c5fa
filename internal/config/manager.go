package config

import (
	"context"
	"time"
)

// ConfigManager 统一配置管理器 - 协调 ConfigService 和 ConfigProvider
type ConfigManager struct {
	service  *ConfigService
	provider *ConfigProvider
}

// NewConfigManager 创建配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		service: NewConfigService(),
	}
}

// Initialize 初始化配置管理器
func (m *ConfigManager) Initialize(configDir string) error {
	// 初始化配置服务
	if err := m.service.Initialize(configDir); err != nil {
		return err
	}

	// 创建配置提供者
	config := m.service.GetConfig()
	m.provider = NewConfigProvider(config)

	return nil
}

// GetService 获取配置服务（用于配置管理）
func (m *ConfigManager) GetService() *ConfigService {
	return m.service
}

// GetProvider 获取配置提供者（用于配置访问）
func (m *ConfigManager) GetProvider() *ConfigProvider {
	return m.provider
}

// GetConfig 获取完整配置 - 兼容性方法
func (m *ConfigManager) GetConfig() *Config {
	if m.service == nil {
		return nil
	}
	return m.service.GetConfig()
}

// StartWatching 开始监听配置文件变更
func (m *ConfigManager) StartWatching(ctx context.Context) error {
	if m.service == nil {
		return nil
	}
	return m.service.StartWatching(ctx)
}

// StopWatching 停止监听配置文件变更
func (m *ConfigManager) StopWatching() error {
	if m.service == nil {
		return nil
	}
	return m.service.StopWatching()
}

// ReloadConfig 重新加载配置
func (m *ConfigManager) ReloadConfig() error {
	if m.service == nil {
		return nil
	}

	// 重新加载配置
	if err := m.service.reloadConfigs(); err != nil {
		return err
	}

	// 更新配置提供者
	config := m.service.GetConfig()
	m.provider = NewConfigProvider(config)

	return nil
}

// 兼容性接口实现 - 实现 ConfigProvider 接口

// Get 获取配置值
func (m *ConfigManager) Get(key string) (interface{}, bool) {
	config := m.GetConfig()
	if config == nil {
		return nil, false
	}

	// 根据键名返回对应的配置段
	switch key {
	case "app":
		return config.App, true
	case "database":
		return config.Database, true
	case "redis":
		return config.Redis, true
	case "rabbitmq":
		return config.RabbitMQ, true
	case "logging":
		return config.Logging, true
	case "monitors":
		return config.Monitors, true
	case "notifications":
		return config.Notifications, true
	case "middlewares":
		return config.Middlewares, true
	case "pipelines":
		return config.Pipelines, true
	case "resource":
		return config.Resource, true
	default:
		return nil, false
	}
}

// GetString 获取字符串配置
func (m *ConfigManager) GetString(key string) string {
	if value, exists := m.Get(key); exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// GetInt 获取整数配置
func (m *ConfigManager) GetInt(key string) int {
	if value, exists := m.Get(key); exists {
		if i, ok := value.(int); ok {
			return i
		}
	}
	return 0
}

// GetBool 获取布尔配置
func (m *ConfigManager) GetBool(key string) bool {
	if value, exists := m.Get(key); exists {
		if b, ok := value.(bool); ok {
			return b
		}
	}
	return false
}

// GetDuration 获取时间间隔配置
func (m *ConfigManager) GetDuration(key string) time.Duration {
	if value, exists := m.Get(key); exists {
		if d, ok := value.(time.Duration); ok {
			return d
		}
	}
	return 0
}

// GetFloat64 获取浮点数配置
func (m *ConfigManager) GetFloat64(key string) float64 {
	if value, exists := m.Get(key); exists {
		if f, ok := value.(float64); ok {
			return f
		}
	}
	return 0
}

// GetStringSlice 获取字符串切片配置
func (m *ConfigManager) GetStringSlice(key string) []string {
	if value, exists := m.Get(key); exists {
		if slice, ok := value.([]string); ok {
			return slice
		}
	}
	return nil
}

// GetMap 获取map配置
func (m *ConfigManager) GetMap(key string) map[string]interface{} {
	if value, exists := m.Get(key); exists {
		if m, ok := value.(map[string]interface{}); ok {
			return m
		}
	}
	return make(map[string]interface{})
}

// GetManager 获取配置管理器 - 兼容旧代码
func (m *ConfigManager) GetManager() ConfigProviderInterface {
	return m
}

// GetProviders 获取强类型配置提供者 - 兼容旧代码
func (m *ConfigManager) GetProviders() *ConfigProvider {
	return m.provider
}
