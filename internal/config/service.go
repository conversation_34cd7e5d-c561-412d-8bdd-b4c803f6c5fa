package config

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"gopkg.in/yaml.v3"
)

// Logger 日志接口 - 避免循环依赖
type Logger interface {
	Debug(msg string, args ...any)
	Info(msg string, args ...any)
	Warn(msg string, args ...any)
	Error(msg string, args ...any)
}

// ConfigService 新的统一配置服务 - 开箱即用，无死锁
type ConfigService struct {
	// 基础配置
	configDir   string
	configPaths []string
	logger      Logger

	// 配置数据 - 使用细粒度锁
	configMu sync.RWMutex
	config   *Config

	// 热重载相关
	watcher    *fsnotify.Watcher
	isWatching bool
	stopChan   chan struct{}
	watcherWg  sync.WaitGroup
	watcherMu  sync.Mutex

	// 注释：移除了 providers 字段，ConfigService 不再管理 ConfigProvider

	// 注释：移除了原始配置文件内容缓存，实现单一数据源
}

// NewConfigService 创建新的配置服务
func NewConfigService() *ConfigService {
	return &ConfigService{
		logger:    &noopLogger{}, // 默认使用空日志器，稍后会被设置
		stopChan:  make(chan struct{}),
		configDir: "configs", // 默认配置目录
	}
}

// SetLogger 设置日志器 - 避免循环依赖
func (s *ConfigService) SetLogger(logger Logger) {
	s.logger = logger
}

// noopLogger 空日志器实现
type noopLogger struct{}

func (n *noopLogger) Debug(msg string, args ...any) {}
func (n *noopLogger) Info(msg string, args ...any)  {}
func (n *noopLogger) Warn(msg string, args ...any)  {}
func (n *noopLogger) Error(msg string, args ...any) {}

// Initialize 初始化配置服务 - 无死锁设计
func (s *ConfigService) Initialize(configDir string) error {
	if configDir != "" {
		s.configDir = configDir
	}

	s.logger.Info(fmt.Sprintf("初始化配置服务，配置目录：%s", s.configDir))

	// 发现配置文件
	configFiles, err := s.discoverConfigFiles()
	if err != nil {
		return fmt.Errorf("发现配置文件失败: %w", err)
	}

	if len(configFiles) == 0 {
		return fmt.Errorf("未找到任何配置文件在目录: %s", s.configDir)
	}

	s.configPaths = configFiles

	// 加载配置 - 无锁调用
	if err := s.loadConfigs(); err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	s.logger.Info(fmt.Sprintf("配置服务初始化完成，配置文件数量：%d，文件：%v",
		len(s.configPaths), s.configPaths))

	return nil
}

// discoverConfigFiles 自动发现配置文件
func (s *ConfigService) discoverConfigFiles() ([]string, error) {
	var configFiles []string

	// 定义配置文件优先级顺序
	configOrder := []string{
		"base.yaml",         // 基础配置 - 最高优先级
		"database.yaml",     // 数据库配置
		"resource.yaml",     // 资源配置
		"middleware.yaml",   // 中间件配置
		"pipeline.yaml",     // 管道配置
		"notification.yaml", // 通知配置
		"monitor.yaml",      // 监控配置 - 最后加载，会覆盖前面的配置
	}

	// 按优先级顺序查找配置文件
	for _, filename := range configOrder {
		fullPath := filepath.Join(s.configDir, filename)
		if fileExists(fullPath) {
			configFiles = append(configFiles, fullPath)
			s.logger.Debug(fmt.Sprintf("发现配置文件：%s", fullPath))
		} else {
			s.logger.Debug(fmt.Sprintf("配置文件不存在：%s", fullPath))
		}
	}

	return configFiles, nil
}

// loadConfigs 加载所有配置文件 - 无死锁设计
func (s *ConfigService) loadConfigs() error {
	if len(s.configPaths) == 0 {
		return fmt.Errorf("没有配置文件需要加载")
	}

	// 首先加载base.yaml作为主配置
	mainConfigPath := s.configPaths[0]
	cfg, err := LoadConfig(mainConfigPath)
	if err != nil {
		return fmt.Errorf("加载主配置文件失败 %s: %w", mainConfigPath, err)
	}

	// 加载并合并其他配置文件
	for i := 1; i < len(s.configPaths); i++ {
		if err := s.mergeConfigFile(cfg, s.configPaths[i]); err != nil {
			s.logger.Warn(fmt.Sprintf("加载配置文件失败：%s，错误：%s", s.configPaths[i], err.Error()))
		}
	}

	// 使用细粒度锁设置配置
	s.configMu.Lock()
	s.config = cfg
	s.configMu.Unlock()

	// 注释：移除了 ConfigProvider 的创建，ConfigService 不再管理配置访问

	s.logger.Info(fmt.Sprintf("所有配置文件加载完成，总文件数：%d，应用：%s v%s，环境：%s",
		len(s.configPaths), cfg.App.Name, cfg.App.Version, cfg.App.Env))

	return nil
}

// mergeConfigFile 合并配置文件
func (s *ConfigService) mergeConfigFile(baseConfig *Config, configPath string) error {
	filename := filepath.Base(configPath)

	s.logger.Info(fmt.Sprintf("开始加载配置文件：%s", configPath))

	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败 [%s]: %w", configPath, err)
	}

	// 展开环境变量
	content := expandEnvVars(string(data))

	// 解析为通用map
	var configData map[string]interface{}
	if err := yaml.Unmarshal([]byte(content), &configData); err != nil {
		return fmt.Errorf("解析配置文件失败 [%s]: %w", configPath, err)
	}

	// 验证配置数据
	if len(configData) == 0 {
		s.logger.Warn(fmt.Sprintf("配置文件为空：%s", configPath))
		return nil
	}

	// 根据文件名合并到对应的配置段
	if err := s.mergeConfigData(baseConfig, filename, configData); err != nil {
		return fmt.Errorf("合并配置数据失败 [%s]: %w", configPath, err)
	}

	s.logger.Debug(fmt.Sprintf("配置文件加载完成：%s，配置项数量：%d", configPath, len(configData)))

	return nil
}

// mergeConfigData 合并配置数据到主配置
func (s *ConfigService) mergeConfigData(baseConfig *Config, filename string, configData map[string]interface{}) error {
	switch filename {
	case "database.yaml":
		if dbData, ok := configData["database"]; ok {
			if err := s.mergeStructConfig(&baseConfig.Database, dbData); err != nil {
				return fmt.Errorf("合并数据库配置失败: %w", err)
			}
		}
	case "resource.yaml":
		if resourceData, ok := configData["resource"]; ok {
			if err := s.mergeStructConfig(&baseConfig.Resource, resourceData); err != nil {
				return fmt.Errorf("合并资源配置失败: %w", err)
			}
		}
	case "middleware.yaml":
		// 直接合并中间件配置，不再缓存原始数据
		if err := s.mergeStructConfig(&baseConfig.Middlewares, configData); err != nil {
			return fmt.Errorf("合并中间件配置失败: %w", err)
		}
	case "pipeline.yaml":
		// 直接合并管道配置，不再缓存原始数据
		if err := s.mergeStructConfig(&baseConfig.Pipelines, configData); err != nil {
			return fmt.Errorf("合并管道配置失败: %w", err)
		}
	case "notification.yaml":
		// 处理通知配置文件的实际结构：notifications.discord 和 notifications.notification_groups
		if baseConfig.Notifications == nil {
			baseConfig.Notifications = make(map[string]NotificationConfig)
		}

		// 处理 notifications 顶级配置
		if notificationData, ok := configData["notifications"]; ok {
			if notifMap, ok := notificationData.(map[string]interface{}); ok {
				// 处理 Discord 配置 (notifications.discord)
				if discordData, ok := notifMap["discord"]; ok {
					if discordMap, ok := discordData.(map[string]interface{}); ok {
						baseConfig.Notifications["discord"] = NotificationConfig{
							Type:     "discord",
							Enabled:  getBoolFromMap(discordMap, "enabled", true),
							Settings: discordMap,
						}
					}
				}

				// 处理 notification_groups (notifications.notification_groups)
				if groups, ok := notifMap["notification_groups"]; ok {
					if groupsMap, ok := groups.(map[string]interface{}); ok {
						for groupName, groupConfig := range groupsMap {
							if groupConfigMap, ok := groupConfig.(map[string]interface{}); ok {
								baseConfig.Notifications[groupName] = NotificationConfig{
									Type:     "notification_group",
									Enabled:  true,
									Settings: groupConfigMap,
								}
							}
						}
					}
				}

				// 将 notification.yaml 的配置合并到 pipeline.notification 中
				if baseConfig.Pipelines.Notification.Settings == nil {
					baseConfig.Pipelines.Notification.Settings = make(map[string]interface{})
				}
				// 合并详细的通知配置到管道配置中
				for key, value := range notifMap {
					if key != "notification_groups" { // notification_groups 单独处理
						baseConfig.Pipelines.Notification.Settings[key] = value
					}
				}

				// 处理其他通知配置字段（enabled, priority, queue 等）
				baseConfig.Notifications["notification_pipeline"] = NotificationConfig{
					Type:     "notification_pipeline",
					Enabled:  getBoolFromMap(notifMap, "enabled", true),
					Settings: notifMap,
				}
			}
		}
	case "monitor.yaml":
		if monitorData, ok := configData["monitors"]; ok {
			if err := s.mergeStructConfig(&baseConfig.Monitors, monitorData); err != nil {
				return fmt.Errorf("合并监控配置失败: %w", err)
			}
		}
	default:
		// 直接合并所有顶级配置项
		for key, value := range configData {
			if err := s.mergeTopLevelConfig(baseConfig, key, value); err != nil {
				s.logger.Warn(fmt.Sprintf("合并顶级配置项失败，键：%s，错误：%s", key, err.Error()))
			}
		}
	}

	return nil
}

// mergeStructConfig 合并结构体配置
func (s *ConfigService) mergeStructConfig(target interface{}, source interface{}) error {
	// 将source转换为YAML字节，然后解析到target
	sourceBytes, err := yaml.Marshal(source)
	if err != nil {
		return fmt.Errorf("序列化源配置失败: %w", err)
	}

	if err := yaml.Unmarshal(sourceBytes, target); err != nil {
		return fmt.Errorf("反序列化到目标配置失败: %w", err)
	}

	return nil
}

// mergeTopLevelConfig 合并顶级配置项
func (s *ConfigService) mergeTopLevelConfig(baseConfig *Config, key string, value interface{}) error {
	switch key {
	case "app":
		return s.mergeStructConfig(&baseConfig.App, value)
	case "database":
		return s.mergeStructConfig(&baseConfig.Database, value)
	case "redis":
		return s.mergeStructConfig(&baseConfig.Redis, value)
	case "rabbitmq":
		return s.mergeStructConfig(&baseConfig.RabbitMQ, value)
	case "logging":
		return s.mergeStructConfig(&baseConfig.Logging, value)
	case "monitors":
		return s.mergeStructConfig(&baseConfig.Monitors, value)
	case "notifications":
		return s.mergeStructConfig(&baseConfig.Notifications, value)
	case "middlewares":
		return s.mergeStructConfig(&baseConfig.Middlewares, value)
	case "pipelines":
		return s.mergeStructConfig(&baseConfig.Pipelines, value)
	case "resource":
		return s.mergeStructConfig(&baseConfig.Resource, value)
	default:
		s.logger.Debug(fmt.Sprintf("跳过未知配置项：%s", key))
	}
	return nil
}

// GetConfig 获取完整配置 - 线程安全
func (s *ConfigService) GetConfig() *Config {
	s.configMu.RLock()
	defer s.configMu.RUnlock()
	return s.config
}

// 注释：移除了 GetProviders 方法，ConfigService 不再提供配置访问接口

// StartWatching 开始监听配置文件变更
func (s *ConfigService) StartWatching(ctx context.Context) error {
	s.watcherMu.Lock()
	defer s.watcherMu.Unlock()

	if s.isWatching {
		return fmt.Errorf("配置监听已经启动")
	}

	if len(s.configPaths) == 0 {
		return fmt.Errorf("没有配置文件需要监听")
	}

	var err error
	s.watcher, err = fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("创建文件监听器失败: %w", err)
	}

	// 添加配置文件到监听列表
	for _, path := range s.configPaths {
		if err := s.watcher.Add(path); err != nil {
			s.logger.Warn(fmt.Sprintf("添加配置文件监听失败：%s，错误：%s", path, err.Error()))
		} else {
			s.logger.Debug(fmt.Sprintf("添加配置文件监听：%s", path))
		}
	}

	s.isWatching = true

	// 启动监听协程
	s.watcherWg.Add(1)
	go s.watchConfigFiles(ctx)

	s.logger.Info(fmt.Sprintf("配置文件热更新已启动，监听文件数：%d", len(s.configPaths)))

	return nil
}

// StopWatching 停止监听配置文件变更
func (s *ConfigService) StopWatching() error {
	s.watcherMu.Lock()
	defer s.watcherMu.Unlock()

	if !s.isWatching {
		return nil
	}

	close(s.stopChan)

	if s.watcher != nil {
		if err := s.watcher.Close(); err != nil {
			s.logger.Error(fmt.Sprintf("关闭文件监听器失败：%s", err.Error()))
		}
	}

	s.watcherWg.Wait()
	s.isWatching = false

	s.logger.Info("配置文件热更新已停止")
	return nil
}

// watchConfigFiles 监听配置文件变更
func (s *ConfigService) watchConfigFiles(ctx context.Context) {
	defer s.watcherWg.Done()

	// 防抖动 - 避免频繁重载
	debounce := time.NewTimer(0)
	debounce.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("配置监听协程收到取消信号")
			return
		case <-s.stopChan:
			s.logger.Info("配置监听协程收到停止信号")
			return
		case event, ok := <-s.watcher.Events:
			if !ok {
				s.logger.Warn("配置文件监听器事件通道关闭")
				return
			}

			if event.Op&fsnotify.Write == fsnotify.Write {
				s.logger.Debug(fmt.Sprintf("检测到配置文件变更：%s，操作：%s", event.Name, event.Op.String()))

				// 重置防抖动定时器
				debounce.Reset(500 * time.Millisecond)
			}

		case err, ok := <-s.watcher.Errors:
			if !ok {
				return
			}
			s.logger.Error(fmt.Sprintf("配置文件监听器错误：%s", err.Error()))

		case <-debounce.C:
			// 防抖动时间到，重新加载配置
			s.logger.Info("开始热重载配置文件")
			if err := s.reloadConfigs(); err != nil {
				s.logger.Error(fmt.Sprintf("热重载配置失败：%s", err.Error()))
			} else {
				s.logger.Info("热重载配置成功")
			}
		}
	}
}

// reloadConfigs 重新加载配置文件
func (s *ConfigService) reloadConfigs() error {
	return s.loadConfigs()
}

// fileExists 检查文件是否存在
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// 全局配置管理器实例
var globalConfigManager *ConfigManager

func init() {
	globalConfigManager = NewConfigManager()
}

// InitializeGlobalConfig 初始化全局配置服务
func InitializeGlobalConfig(configDir string) error {
	return globalConfigManager.Initialize(configDir)
}

// GetGlobalService 获取全局配置服务 - 返回 ConfigManager 以保持兼容性
func GetGlobalService() *ConfigManager {
	return globalConfigManager
}

// GetGlobalManager 获取全局配置管理器
func GetGlobalManager() *ConfigManager {
	return globalConfigManager
}

// StartGlobalWatching 启动全局配置监听
func StartGlobalWatching(ctx context.Context) error {
	return globalConfigManager.StartWatching(ctx)
}

// StopGlobalWatching 停止全局配置监听
func StopGlobalWatching() error {
	return globalConfigManager.StopWatching()
}

// 注释：移除了所有兼容性接口和类型别名，这些现在在 adapter.go 和 manager.go 中处理

// 注释：移除了所有配置访问适配器代码，这些功能现在在 manager.go 和 adapter.go 中实现

// 注释：移除了所有 ConfigProvider 接口实现，ConfigService 现在专注于配置管理

// getBoolFromMap 从map中获取布尔值的辅助函数
func getBoolFromMap(m map[string]interface{}, key string, defaultValue bool) bool {
	if value, exists := m[key]; exists {
		if b, ok := value.(bool); ok {
			return b
		}
	}
	return defaultValue
}
