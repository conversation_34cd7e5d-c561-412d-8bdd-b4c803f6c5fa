package config

import (
	"go-monitor/internal/types"
	"time"
)

// ConfigProviderInterface 配置提供者接口 - 兼容旧代码
type ConfigProviderInterface interface {
	Get(key string) (interface{}, bool)
	GetString(key string) string
	GetInt(key string) int
	GetBool(key string) bool
	GetDuration(key string) time.Duration
	GetFloat64(key string) float64
	GetStringSlice(key string) []string
	GetMap(key string) map[string]interface{}
}

// LegacyConfigServiceAdapter 旧版配置服务适配器 - 实现向后兼容
type LegacyConfigServiceAdapter struct {
	manager *ConfigManager
}

// NewLegacyConfigServiceAdapter 创建旧版配置服务适配器
func NewLegacyConfigServiceAdapter(manager *ConfigManager) *LegacyConfigServiceAdapter {
	return &LegacyConfigServiceAdapter{manager: manager}
}

// Get 获取配置值
func (a *LegacyConfigServiceAdapter) Get(key string) (interface{}, bool) {
	return a.manager.Get(key)
}

// GetString 获取字符串配置
func (a *LegacyConfigServiceAdapter) GetString(key string) string {
	return a.manager.GetString(key)
}

// GetInt 获取整数配置
func (a *LegacyConfigServiceAdapter) GetInt(key string) int {
	return a.manager.GetInt(key)
}

// GetBool 获取布尔配置
func (a *LegacyConfigServiceAdapter) GetBool(key string) bool {
	return a.manager.GetBool(key)
}

// GetDuration 获取时间间隔配置
func (a *LegacyConfigServiceAdapter) GetDuration(key string) time.Duration {
	return a.manager.GetDuration(key)
}

// GetFloat64 获取浮点数配置
func (a *LegacyConfigServiceAdapter) GetFloat64(key string) float64 {
	return a.manager.GetFloat64(key)
}

// GetStringSlice 获取字符串切片配置
func (a *LegacyConfigServiceAdapter) GetStringSlice(key string) []string {
	return a.manager.GetStringSlice(key)
}

// GetMap 获取map配置
func (a *LegacyConfigServiceAdapter) GetMap(key string) map[string]interface{} {
	return a.manager.GetMap(key)
}

// ConfigProvidersAdapter 旧版 ConfigProviders 适配器
type ConfigProvidersAdapter struct {
	provider *ConfigProvider
}

// NewConfigProvidersAdapter 创建旧版 ConfigProviders 适配器
func NewConfigProvidersAdapter(provider *ConfigProvider) *ConfigProvidersAdapter {
	return &ConfigProvidersAdapter{provider: provider}
}

// App 获取应用配置
func (a *ConfigProvidersAdapter) App() *AppConfig {
	return a.provider.App()
}

// Database 获取数据库配置
func (a *ConfigProvidersAdapter) Database() *DatabaseConfig {
	return a.provider.Database()
}

// Redis 获取Redis配置
func (a *ConfigProvidersAdapter) Redis() *RedisConfig {
	return a.provider.Redis()
}

// RabbitMQ 获取RabbitMQ配置
func (a *ConfigProvidersAdapter) RabbitMQ() *RabbitMQConfig {
	return a.provider.RabbitMQ()
}

// 注释：移除了 HTTPClient 方法，因为 HTTPClientConfig 无用且未被实际使用

// Logging 获取日志配置
func (a *ConfigProvidersAdapter) Logging() *LoggingConfig {
	return a.provider.Logging()
}

// Monitors 获取所有监控配置
func (a *ConfigProvidersAdapter) Monitors() map[string]*types.SpiderConfig {
	return a.provider.Monitors()
}

// Notifications 获取所有通知配置
func (a *ConfigProvidersAdapter) Notifications() map[string]NotificationConfig {
	return a.provider.Notifications()
}

// Middlewares 获取中间件配置
func (a *ConfigProvidersAdapter) Middlewares() *MiddlewaresConfig {
	return a.provider.Middlewares()
}

// Pipelines 获取管道配置
func (a *ConfigProvidersAdapter) Pipelines() *PipelinesConfig {
	return a.provider.Pipelines()
}

// Resource 获取资源配置
func (a *ConfigProvidersAdapter) Resource() *ResourceConfig {
	return a.provider.Resource()
}

// GetSpiderConfig 获取爬虫配置
func (a *ConfigProvidersAdapter) GetSpiderConfig(monitorName string) *types.SpiderConfig {
	return a.provider.GetSpiderConfig(monitorName)
}

// GetAllSpiderConfigs 获取所有爬虫配置
func (a *ConfigProvidersAdapter) GetAllSpiderConfigs() map[string]*types.SpiderConfig {
	return a.provider.GetAllSpiderConfigs()
}

// GetAllSpiderConfigsForManager 获取所有爬虫配置（管理器版本）
func (a *ConfigProvidersAdapter) GetAllSpiderConfigsForManager() map[string]*types.SpiderConfig {
	return a.provider.GetAllSpiderConfigsForManager()
}

// GetPipelineConfigs 获取管道配置列表
func (a *ConfigProvidersAdapter) GetPipelineConfigs() map[string]PipelineConfig {
	return a.provider.GetPipelineConfigs()
}

// GetMiddlewareConfigs 获取中间件配置列表
func (a *ConfigProvidersAdapter) GetMiddlewareConfigs() map[string]MiddlewareConfig {
	return a.provider.GetMiddlewareConfigs()
}

// GetResourceManagerConfig 获取资源管理器配置
func (a *ConfigProvidersAdapter) GetResourceManagerConfig() (map[string]interface{}, map[string]map[string]interface{}) {
	return a.provider.GetResourceManagerConfig()
}

// 类型别名 - 保持向后兼容性

// Service 类型别名 - 兼容旧代码
type Service = ConfigManager

// ConfigProviders 类型别名 - 兼容旧代码
type ConfigProviders = ConfigProvider
