package config

import (
	"encoding/json"
	"fmt"
	"go-monitor/internal/types"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

// ConfigUtils 配置工具类 - 统一的配置处理功能
type ConfigUtils struct{}

// 全局配置工具实例
var Utils = &ConfigUtils{}

// GetString 从配置中获取字符串值
func (u *ConfigUtils) GetString(config map[string]interface{}, key string, defaultValue string) string {
	if value, exists := config[key]; exists {
		switch v := value.(type) {
		case string:
			return v
		case int:
			return strconv.Itoa(v)
		case int64:
			return strconv.FormatInt(v, 10)
		case float64:
			return strconv.FormatFloat(v, 'f', -1, 64)
		case float32:
			return strconv.FormatFloat(float64(v), 'f', -1, 32)
		case bool:
			return strconv.FormatBool(v)
		default:
			return fmt.Sprintf("%v", v)
		}
	}
	return defaultValue
}

// GetInt 从配置中获取整数值
func (u *ConfigUtils) GetInt(config map[string]interface{}, key string, defaultValue int) int {
	if value, exists := config[key]; exists {
		switch v := value.(type) {
		case int:
			return v
		case int64:
			return int(v)
		case float64:
			return int(v)
		case float32:
			return int(v)
		case string:
			if intVal, err := strconv.Atoi(v); err == nil {
				return intVal
			}
		}
	}
	return defaultValue
}

// GetBool 从配置中获取布尔值
func (u *ConfigUtils) GetBool(config map[string]interface{}, key string, defaultValue bool) bool {
	if value, exists := config[key]; exists {
		switch v := value.(type) {
		case bool:
			return v
		case string:
			if boolVal, err := strconv.ParseBool(v); err == nil {
				return boolVal
			}
		case int:
			return v != 0
		case int64:
			return v != 0
		case float64:
			return v != 0
		}
	}
	return defaultValue
}

// GetDuration 从配置中获取时间间隔值
func (u *ConfigUtils) GetDuration(config map[string]interface{}, key string, defaultValue time.Duration) time.Duration {
	if value, exists := config[key]; exists {
		switch v := value.(type) {
		case time.Duration:
			return v
		case string:
			if duration, err := time.ParseDuration(v); err == nil {
				return duration
			}
		case int:
			return time.Duration(v) * time.Second
		case int64:
			return time.Duration(v) * time.Second
		case float64:
			return time.Duration(v) * time.Second
		}
	}
	return defaultValue
}

// GetFloat64 从配置中获取浮点数值
func (u *ConfigUtils) GetFloat64(config map[string]interface{}, key string, defaultValue float64) float64 {
	if value, exists := config[key]; exists {
		switch v := value.(type) {
		case float64:
			return v
		case float32:
			return float64(v)
		case int:
			return float64(v)
		case int64:
			return float64(v)
		case string:
			if floatVal, err := strconv.ParseFloat(v, 64); err == nil {
				return floatVal
			}
		}
	}
	return defaultValue
}

// GetStringSlice 从配置中获取字符串切片
func (u *ConfigUtils) GetStringSlice(config map[string]interface{}, key string, defaultValue []string) []string {
	if value, exists := config[key]; exists {
		switch v := value.(type) {
		case []string:
			return v
		case []interface{}:
			result := make([]string, len(v))
			for i, item := range v {
				result[i] = fmt.Sprintf("%v", item)
			}
			return result
		case string:
			// 支持逗号分隔的字符串
			return strings.Split(v, ",")
		}
	}
	return defaultValue
}

// GetMap 从配置中获取map值
func (u *ConfigUtils) GetMap(config map[string]interface{}, key string) map[string]interface{} {
	if value, exists := config[key]; exists {
		if mapVal, ok := value.(map[string]interface{}); ok {
			return mapVal
		}
	}
	return make(map[string]interface{})
}

// HasKey 检查配置中是否存在指定键
func (u *ConfigUtils) HasKey(config map[string]interface{}, key string) bool {
	_, exists := config[key]
	return exists
}

// IsEmpty 检查配置值是否为空
func (u *ConfigUtils) IsEmpty(config map[string]interface{}, key string) bool {
	value, exists := config[key]
	if !exists {
		return true
	}

	if value == nil {
		return true
	}

	switch v := value.(type) {
	case string:
		return strings.TrimSpace(v) == ""
	case []interface{}:
		return len(v) == 0
	case []string:
		return len(v) == 0
	case map[string]interface{}:
		return len(v) == 0
	default:
		return false
	}
}

// MergeConfigs 合并多个配置map，后面的配置会覆盖前面的
func (u *ConfigUtils) MergeConfigs(configs ...map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	for _, config := range configs {
		if config == nil {
			continue
		}
		for key, value := range config {
			result[key] = value
		}
	}

	return result
}

// DeepMergeConfigs 深度合并配置map
func (u *ConfigUtils) DeepMergeConfigs(configs ...map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	for _, config := range configs {
		if config == nil {
			continue
		}
		u.deepMergeInto(result, config)
	}

	return result
}

// deepMergeInto 深度合并配置到目标map
func (u *ConfigUtils) deepMergeInto(target, source map[string]interface{}) {
	for key, value := range source {
		if existingValue, exists := target[key]; exists {
			if existingMap, ok := existingValue.(map[string]interface{}); ok {
				if sourceMap, ok := value.(map[string]interface{}); ok {
					u.deepMergeInto(existingMap, sourceMap)
					continue
				}
			}
		}
		target[key] = value
	}
}

// GetNestedValue 获取嵌套配置值 - 使用GJSON大幅简化
func (u *ConfigUtils) GetNestedValue(config map[string]interface{}, path string) (interface{}, bool) {
	// 将config转换为JSON字符串以使用GJSON
	configBytes, err := json.Marshal(config)
	if err != nil {
		return nil, false
	}

	// 使用GJSON一行查询替代23行复杂循环和类型断言
	result := gjson.Get(string(configBytes), path)
	if !result.Exists() {
		return nil, false
	}

	// 返回原始值以保持接口兼容性
	return result.Value(), true
}

// SetNestedValue 设置嵌套配置值
func (u *ConfigUtils) SetNestedValue(config map[string]interface{}, path string, value interface{}) {
	keys := strings.Split(path, ".")
	current := config

	for i, key := range keys {
		if i == len(keys)-1 {
			// 最后一个键，设置值
			current[key] = value
		} else {
			// 不是最后一个键，确保路径存在
			if _, exists := current[key]; !exists {
				current[key] = make(map[string]interface{})
			}
			if nextMap, ok := current[key].(map[string]interface{}); ok {
				current = nextMap
			} else {
				// 如果不是map，创建新的map
				newMap := make(map[string]interface{})
				current[key] = newMap
				current = newMap
			}
		}
	}
}

// ValidateRequired 验证必需的配置项
func (u *ConfigUtils) ValidateRequired(config map[string]interface{}, requiredKeys []string) error {
	var missingKeys []string

	for _, key := range requiredKeys {
		if u.IsEmpty(config, key) {
			missingKeys = append(missingKeys, key)
		}
	}

	if len(missingKeys) > 0 {
		return fmt.Errorf("缺少必需的配置项: %s", strings.Join(missingKeys, ", "))
	}

	return nil
}

// SetDefaults 为配置设置默认值
func (u *ConfigUtils) SetDefaults(config map[string]interface{}, defaults map[string]interface{}) {
	for key, defaultValue := range defaults {
		if _, exists := config[key]; !exists {
			config[key] = defaultValue
		}
	}
}

// 便捷函数，直接使用全局实例

// GetStringFromMap 从map中获取字符串配置值
func GetStringFromMap(config map[string]interface{}, key string, defaultValue string) string {
	return Utils.GetString(config, key, defaultValue)
}

// GetIntFromMap 从map中获取整数配置值
func GetIntFromMap(config map[string]interface{}, key string, defaultValue int) int {
	return Utils.GetInt(config, key, defaultValue)
}

// GetBoolFromMap 从map中获取布尔配置值
func GetBoolFromMap(config map[string]interface{}, key string, defaultValue bool) bool {
	return Utils.GetBool(config, key, defaultValue)
}

// GetDurationFromMap 从map中获取时间间隔配置值
func GetDurationFromMap(config map[string]interface{}, key string, defaultValue time.Duration) time.Duration {
	return Utils.GetDuration(config, key, defaultValue)
}

// GetMapFromMap 从map中获取map配置值
func GetMapFromMap(config map[string]interface{}, key string) map[string]interface{} {
	return Utils.GetMap(config, key)
}

// MergeConfigs 合并配置
func MergeConfigs(configs ...map[string]interface{}) map[string]interface{} {
	return Utils.MergeConfigs(configs...)
}

// 爬虫配置工具函数 - 兼容旧代码

// GetConfigString 从爬虫配置中获取字符串值
func GetConfigString(config *types.SpiderConfig, key string, defaultValue string) string {
	if config == nil || config.SpiderSettings == nil {
		return defaultValue
	}
	return GetStringFromMap(config.SpiderSettings, key, defaultValue)
}

// GetConfigInt 从爬虫配置中获取整数值
func GetConfigInt(config *types.SpiderConfig, key string, defaultValue int) int {
	if config == nil || config.SpiderSettings == nil {
		return defaultValue
	}
	return GetIntFromMap(config.SpiderSettings, key, defaultValue)
}

// GetConfigBool 从爬虫配置中获取布尔值
func GetConfigBool(config *types.SpiderConfig, key string, defaultValue bool) bool {
	if config == nil || config.SpiderSettings == nil {
		return defaultValue
	}
	return GetBoolFromMap(config.SpiderSettings, key, defaultValue)
}

// GetConfigStringArray 从爬虫配置中获取字符串数组
func GetConfigStringArray(config *types.SpiderConfig, key string) []string {
	if config == nil || config.SpiderSettings == nil {
		return []string{}
	}

	if value, exists := config.SpiderSettings[key]; exists {
		switch v := value.(type) {
		case []string:
			return v
		case []interface{}:
			result := make([]string, len(v))
			for i, item := range v {
				result[i] = fmt.Sprintf("%v", item)
			}
			return result
		case string:
			// 支持逗号分隔的字符串
			return strings.Split(v, ",")
		}
	}
	return []string{}
}

// GetConfigMap 从爬虫配置中获取map值
func GetConfigMap(config *types.SpiderConfig, key string) map[string]interface{} {
	if config == nil || config.SpiderSettings == nil {
		return make(map[string]interface{})
	}
	return GetMapFromMap(config.SpiderSettings, key)
}

// SetDefaultValues 为爬虫配置设置默认值
func SetDefaultValues(config *types.SpiderConfig) {
	if config == nil {
		return
	}

	if config.Country == "" {
		config.Country = "US"
	}

	if config.Interval == "" {
		config.Interval = "30s"
	}

	if config.SpiderSettings == nil {
		config.SpiderSettings = make(map[string]interface{})
	}
}

// GetProductIDsFromConfig 从配置中提取产品ID列表
func GetProductIDsFromConfig(config *types.SpiderConfig) []string {
	// 尝试多种可能的字段名
	fields := []string{"product_ids", "product_id", "wishlist", "wishlist_ids", "wishlist_id", "asinlist", "ids", "id"}

	for _, field := range fields {
		if ids := GetConfigStringArray(config, field); len(ids) > 0 {
			return ids
		}

		// 尝试单个字符串值
		if id := GetConfigString(config, field, ""); id != "" {
			return []string{id}
		}
	}

	return []string{}
}

// GetSiteURLFromConfig 从配置中获取站点URL
func GetSiteURLFromConfig(config *types.SpiderConfig) string {
	// 优先使用SpiderConfig的SiteURL字段
	if config.SiteURL != "" {
		return config.SiteURL
	}

	// 尝试从SpiderSettings中获取
	fields := []string{"site_url", "url", "base_url"}
	for _, field := range fields {
		if url := GetConfigString(config, field, ""); url != "" {
			return url
		}
	}

	return ""
}
