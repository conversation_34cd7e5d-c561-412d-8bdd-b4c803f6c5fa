package config

import (
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"

	"go-monitor/internal/types"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构 - 开箱即用的强类型配置
type Config struct {
	App           AppConfig                      `yaml:"app"`
	Database      DatabaseConfig                 `yaml:"database"`
	Redis         RedisConfig                    `yaml:"redis"`
	RabbitMQ      RabbitMQConfig                 `yaml:"rabbitmq"`
	Logging       LoggingConfig                  `yaml:"logging"`
	Monitors      map[string]*types.SpiderConfig `yaml:"monitors"`
	Notifications map[string]NotificationConfig  `yaml:"notifications"`
	Middlewares   MiddlewaresConfig              `yaml:"middlewares"`
	Pipelines     PipelinesConfig                `yaml:"pipelines"`
	Resource      ResourceConfig                 `yaml:"resource"`
}

// AppConfig 应用配置
type AppConfig struct {
	Name                    string        `yaml:"name"`
	Version                 string        `yaml:"version"`
	Env                     string        `yaml:"env"`
	Port                    int           `yaml:"port"`
	Host                    string        `yaml:"host"`
	TestMode                bool          `yaml:"test_mode"`
	DryRun                  bool          `yaml:"dry_run"`
	HotReload               bool          `yaml:"hot_reload"`
	GracefulShutdownTimeout time.Duration `yaml:"graceful_shutdown_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string        `yaml:"driver"`
	DSN             string        `yaml:"dsn"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	AutoMigrate     bool          `yaml:"auto_migrate"`
	LogQueries      bool          `yaml:"log_queries"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host            string        `yaml:"host"`
	Port            int           `yaml:"port"`
	Password        string        `yaml:"password"`
	DB              int           `yaml:"db"`
	PoolSize        int           `yaml:"pool_size"`
	MinIdleConns    int           `yaml:"min_idle_conns"`
	DialTimeout     time.Duration `yaml:"dial_timeout"`
	ReadTimeout     time.Duration `yaml:"read_timeout"`
	WriteTimeout    time.Duration `yaml:"write_timeout"`
	IdleTimeout     time.Duration `yaml:"idle_timeout"`
	PoolTimeout     time.Duration `yaml:"pool_timeout"`
	MaxRetries      int           `yaml:"max_retries"`
	MinRetryBackoff time.Duration `yaml:"min_retry_backoff"`
	MaxRetryBackoff time.Duration `yaml:"max_retry_backoff"`
	DefaultTTL      time.Duration `yaml:"default_ttl"`
	KeyPrefix       string        `yaml:"key_prefix"`
}

// RabbitMQConfig RabbitMQ配置
type RabbitMQConfig struct {
	URL           string        `yaml:"url"`
	Exchange      string        `yaml:"exchange"`
	ExchangeType  string        `yaml:"exchange_type"`
	RoutingKey    string        `yaml:"routing_key"`
	QueueName     string        `yaml:"queue_name"`
	Durable       bool          `yaml:"durable"`
	AutoDelete    bool          `yaml:"auto_delete"`
	Exclusive     bool          `yaml:"exclusive"`
	NoWait        bool          `yaml:"no_wait"`
	PrefetchCount int           `yaml:"prefetch_count"`
	Timeout       time.Duration `yaml:"timeout"`
	RetryCount    int           `yaml:"retry_count"`
	RetryDelay    time.Duration `yaml:"retry_delay"`
}

// 注释：移除了 HTTPClientConfig，因为它无用且未被实际使用

// LoggingConfig 现代化日志配置
type LoggingConfig struct {
	// 全局基础配置
	Level  string `yaml:"level"`
	Format string `yaml:"format"`

	// 输出配置
	Outputs LoggingOutputsConfig `yaml:"outputs"`

	// 组件特定配置
	Components LoggingComponentsConfig `yaml:"components"`

	// 性能配置
	Performance LoggingPerformanceConfig `yaml:"performance"`
}

// LoggingOutputsConfig 输出配置
type LoggingOutputsConfig struct {
	Console LoggingConsoleConfig `yaml:"console"`
	File    LoggingFileConfig    `yaml:"file"`
}

// LoggingConsoleConfig 控制台输出配置
type LoggingConsoleConfig struct {
	Enabled bool   `yaml:"enabled"`
	Format  string `yaml:"format"`
	Level   string `yaml:"level"`
}

// LoggingFileConfig 文件输出配置
type LoggingFileConfig struct {
	Enabled  bool                      `yaml:"enabled"`
	Path     string                    `yaml:"path"`
	Format   string                    `yaml:"format"`
	Level    string                    `yaml:"level"`
	Rotation LoggingFileRotationConfig `yaml:"rotation"`
}

// LoggingFileRotationConfig 文件轮转配置
type LoggingFileRotationConfig struct {
	MaxSize    int  `yaml:"max_size"`
	MaxBackups int  `yaml:"max_backups"`
	MaxAge     int  `yaml:"max_age"`
	Compress   bool `yaml:"compress"`
	Enabled    bool `yaml:"enabled"`
}

// LoggingComponentsConfig 组件配置
type LoggingComponentsConfig struct {
	Spider     LoggingSpiderConfig    `yaml:"spider"`
	Service    LoggingComponentConfig `yaml:"service"`
	Middleware LoggingComponentConfig `yaml:"middleware"`
	Pipeline   LoggingComponentConfig `yaml:"pipeline"`
}

// LoggingSpiderConfig 爬虫日志配置
type LoggingSpiderConfig struct {
	Level         string `yaml:"level"`
	SeparateFiles bool   `yaml:"separate_files"`
	FilePattern   string `yaml:"file_pattern"`
}

// LoggingComponentConfig 通用组件日志配置
type LoggingComponentConfig struct {
	Level string `yaml:"level"`
}

// LoggingPerformanceConfig 性能配置
type LoggingPerformanceConfig struct {
	AsyncWrite    bool   `yaml:"async_write"`
	BufferSize    int    `yaml:"buffer_size"`
	FlushInterval string `yaml:"flush_interval"`
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Name          string                 `yaml:"name"`
	Platform      string                 `yaml:"platform"`
	SpiderType    string                 `yaml:"spider_type"` // 添加 spider_type 字段
	Enabled       bool                   `yaml:"enabled"`
	Interval      time.Duration          `yaml:"interval"`
	CheckInterval float64                `yaml:"check_interval"` // 添加 check_interval 字段（秒）
	Timeout       time.Duration          `yaml:"timeout"`
	MaxRetries    int                    `yaml:"max_retries"`
	Country       string                 `yaml:"country"`
	SiteURL       string                 `yaml:"site_url"`
	Notifications []string               `yaml:"notifications"`
	Settings      map[string]interface{} `yaml:",inline"` // 使用 inline 捕获其他字段
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	Type     string                 `yaml:"type"`
	Enabled  bool                   `yaml:"enabled"`
	Settings map[string]interface{} `yaml:"settings"`
}

// MiddlewareConfig 中间件配置基类
type MiddlewareConfig struct {
	Enabled  bool                   `yaml:"enabled"`
	Priority int                    `yaml:"priority"`
	Settings map[string]interface{} `yaml:",inline"` // 其他配置内联
}

// MiddlewaresConfig 中间件配置
type MiddlewaresConfig struct {
	Proxy          MiddlewareConfig `yaml:"proxy"`
	Headers        MiddlewareConfig `yaml:"headers"`
	Retry          MiddlewareConfig `yaml:"retry"`
	Cookies        MiddlewareConfig `yaml:"cookies"`
	Signature      MiddlewareConfig `yaml:"signature"`
	TLSFingerprint MiddlewareConfig `yaml:"tls_fingerprint"`
}

// PipelineConfig 管道配置基类
type PipelineConfig struct {
	Enabled  bool                   `yaml:"enabled"`
	Priority int                    `yaml:"priority"`
	Settings map[string]interface{} `yaml:",inline"` // 其他配置内联
}

// PipelinesConfig 管道配置
type PipelinesConfig struct {
	Validation   PipelineConfig `yaml:"validation"`
	Duplicates   PipelineConfig `yaml:"duplicates"`
	Notification PipelineConfig `yaml:"notification"`
}

// ResourceConfig 资源配置
type ResourceConfig struct {
	Global       GlobalResourceConfig         `yaml:"global"`
	Initializers map[string]InitializerConfig `yaml:"initializers"`
}

// GlobalResourceConfig 全局资源配置
type GlobalResourceConfig struct {
	Enabled          bool          `yaml:"enabled"`
	RefreshInterval  time.Duration `yaml:"refresh_interval"`
	MaxRetryAttempts int           `yaml:"max_retry_attempts"`
	RetryDelay       time.Duration `yaml:"retry_delay"`
	CircuitBreaker   bool          `yaml:"circuit_breaker"`
	CacheEnabled     bool          `yaml:"cache_enabled"`
	CacheTTL         time.Duration `yaml:"cache_ttl"`
}

// InitializerConfig 初始化器配置
type InitializerConfig struct {
	Type         string                 `yaml:"type"`
	Enabled      bool                   `yaml:"enabled"`
	Priority     int                    `yaml:"priority"`
	Config       map[string]interface{} `yaml:"config"`
	Dependencies []string               `yaml:"dependencies"`
	TTL          time.Duration          `yaml:"ttl"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 检查文件是否存在
	if !fileExists(configPath) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取文件内容
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 替换环境变量
	content := string(data)
	content = expandEnvVars(content)

	// 解析YAML
	var config Config
	if err := yaml.Unmarshal([]byte(content), &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return &config, nil
}

// expandEnvVars 展开环境变量
func expandEnvVars(content string) string {
	// 匹配 ${VAR} 或 $VAR 格式的环境变量
	re := regexp.MustCompile(`\$\{([^}]+)\}|\$([A-Za-z_][A-Za-z0-9_]*)`)

	return re.ReplaceAllStringFunc(content, func(match string) string {
		var varName string
		if strings.HasPrefix(match, "${") {
			// ${VAR} 格式
			varName = match[2 : len(match)-1]
		} else {
			// $VAR 格式
			varName = match[1:]
		}

		if value := os.Getenv(varName); value != "" {
			return value
		}
		return match // 如果环境变量不存在，保持原样
	})
}
