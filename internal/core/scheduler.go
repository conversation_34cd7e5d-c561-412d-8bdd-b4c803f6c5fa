package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/pkg/logging"
)

// formatDuration 将时间格式化为人性化的字符串
func formatDuration(d time.Duration) string {
	if d < time.Second {
		return fmt.Sprintf("%dms", d.Milliseconds())
	}
	if d < time.Minute {
		return fmt.Sprintf("%.1fs", d.Seconds())
	}
	if d < time.Hour {
		minutes := int(d.Minutes())
		seconds := int(d.Seconds()) % 60
		if seconds == 0 {
			return fmt.Sprintf("%dm", minutes)
		}
		return fmt.Sprintf("%dm%ds", minutes, seconds)
	}
	hours := int(d.Hours())
	minutes := int(d.Minutes()) % 60
	if minutes == 0 {
		return fmt.Sprintf("%dh", hours)
	}
	return fmt.Sprintf("%dh%dm", hours, minutes)
}

// TaskStats 任务统计数据
type TaskStats struct {
	TotalProducts   int
	ProcessedCount  int
	DroppedCount    int
	ErrorCount      int
	RequestDuration time.Duration
	ParseDuration   time.Duration
	ProcessDuration time.Duration
	TotalDuration   time.Duration
}

// Scheduler 调度器 - 系统的调度核心，专注于任务调度
type Scheduler struct {
	// 管理器
	serviceManager *ServiceManager
	spiderManager  *SpiderManager
	eventBus       *EventBus
	configService  *config.ConfigManager
	logger         logging.Logger

	// 调度状态
	isRunning bool
	stopChan  chan struct{}
	wg        sync.WaitGroup
	mu        sync.RWMutex
}

// NewScheduler 创建调度器
func NewScheduler(eventBus *EventBus, configManager *config.ConfigManager) *Scheduler {
	// 直接使用配置管理器
	serviceManager := NewServiceManager(configManager)

	return &Scheduler{
		serviceManager: serviceManager,
		spiderManager:  NewSpiderManager(configManager, serviceManager),
		eventBus:       eventBus,
		configService:  configManager,
		logger:         logging.GetLogger("scheduler"),
		stopChan:       make(chan struct{}),
	}
}

// Start 启动调度器
func (s *Scheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("调度器已经在运行")
	}

	s.logger.Info("启动调度器核心")

	// 1. 初始化服务管理器
	if err := s.serviceManager.Initialize(); err != nil {
		return fmt.Errorf("初始化服务管理器失败: %w", err)
	}

	// 2. 初始化爬虫管理器
	if err := s.spiderManager.Initialize(); err != nil {
		return fmt.Errorf("初始化爬虫管理器失败: %w", err)
	}

	s.isRunning = true

	// 获取统计信息
	spiderStats := s.spiderManager.GetStats()
	serviceStats := s.serviceManager.GetStats()

	s.logger.Info(fmt.Sprintf("调度器启动完成 爬虫配置：%v，启用配置：%v，管道数量：%v",
		spiderStats["total_configs"], spiderStats["enabled_configs"], serviceStats["pipeline_count"]))

	// 3. 启动监控任务循环
	if err := s.startMonitoringTasks(ctx); err != nil {
		s.isRunning = false
		return fmt.Errorf("启动监控任务失败: %w", err)
	}

	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return fmt.Errorf("调度器未运行")
	}

	s.logger.Info("停止调度器")

	// 发送停止信号
	close(s.stopChan)

	// 等待所有任务完成
	s.wg.Wait()

	// 关闭管理器
	if err := s.serviceManager.Close(); err != nil {
		s.logger.Error(fmt.Sprintf("关闭服务管理器失败：%s", err.Error()))
	}

	if err := s.spiderManager.Close(); err != nil {
		s.logger.Error(fmt.Sprintf("关闭爬虫管理器失败：%s", err.Error()))
	}

	s.isRunning = false
	s.logger.Info("调度器已停止")

	return nil
}

// IsRunning 检查调度器是否在运行
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// 辅助函数已移除，现在使用internal/config包中的统一配置工具

// startMonitoringTasks 启动所有监控任务的循环
func (s *Scheduler) startMonitoringTasks(ctx context.Context) error {
	// 从爬虫管理器获取启用的配置
	enabledConfigs := s.spiderManager.GetEnabledConfigs()

	if len(enabledConfigs) == 0 {
		s.logger.Warn("没有启用的监控配置")
		return nil
	}

	s.logger.Info(fmt.Sprintf("启动监控任务循环 启用监控：%d", len(enabledConfigs)))

	// 为每个启用的监控启动独立的goroutine
	for name, config := range enabledConfigs {
		s.wg.Add(1)
		go func(monitorName string, monitorConfig *spiders.SpiderConfig) {
			defer s.wg.Done()
			s.runMonitorLoop(ctx, monitorName, monitorConfig)
		}(name, config)
	}

	return nil
}

// runMonitorLoop 运行单个监控的循环
func (s *Scheduler) runMonitorLoop(ctx context.Context, monitorName string, config *spiders.SpiderConfig) {
	// 使用SpiderConfig的GetIntervalDuration方法解析时间间隔
	intervalDuration := config.GetIntervalDuration()

	s.logger.Info(fmt.Sprintf("启动监控循环 - %s，间隔：%s", monitorName, intervalDuration.String()), "platform", config.Platform)

	for {
		// 先执行一轮监控
		if err := s.runMonitorRound(ctx, monitorName, config); err != nil {
			s.logger.Error(fmt.Sprintf("监控轮次执行失败 %s：%s", monitorName, err.Error()), "platform", config.Platform)

			// 错误后等待一段时间再继续
			errorWaitTime := 10 * time.Second
			if errorWait, ok := config.SpiderSettings["error_wait_time"].(string); ok {
				if duration, err := time.ParseDuration(errorWait); err == nil {
					errorWaitTime = duration
				}
			}

			s.logger.Info(fmt.Sprintf("错误后等待 %s，等待时间：%s", monitorName, errorWaitTime), "platform", config.Platform)

			select {
			case <-time.After(errorWaitTime):
			case <-s.stopChan:
				s.logger.Info(fmt.Sprintf("监控循环停止 - %s", monitorName), "platform", config.Platform)
				return
			case <-ctx.Done():
				s.logger.Info(fmt.Sprintf("监控循环被取消 - %s", monitorName), "platform", config.Platform)
				return
			}
		}

		// 任务完成后等待间隔时间
		select {
		case <-time.After(intervalDuration):
			// 等待完成，继续下一轮
		case <-s.stopChan:
			s.logger.Info(fmt.Sprintf("监控循环停止 - %s", monitorName), "platform", config.Platform)
			return
		case <-ctx.Done():
			s.logger.Info(fmt.Sprintf("监控循环被取消 - %s", monitorName), "platform", config.Platform)
			return
		}
	}
}

// runMonitorRound 执行一轮监控
func (s *Scheduler) runMonitorRound(ctx context.Context, monitorName string, config *spiders.SpiderConfig) error {
	s.logger.Debug(fmt.Sprintf("开始监控轮次：%s", monitorName), "platform", config.Platform)

	// 获取对应的爬虫来获取产品ID列表
	spider, exists := s.spiderManager.GetSpider(config.Platform)
	if !exists {
		return fmt.Errorf("找不到爬虫: %s", config.Platform)
	}

	// 获取产品ID列表
	productIDs := spider.GetProductIDs(config)
	if len(productIDs) == 0 {
		s.logger.Warn(fmt.Sprintf("没有找到产品ID：%s", monitorName), "platform", config.Platform)
		return nil
	}

	s.logger.Debug(fmt.Sprintf("获取到产品ID列表 - %s，数量：%d", monitorName, len(productIDs)), "platform", config.Platform)

	// 根据爬虫能力和配置选择处理策略
	if s.shouldUseBatchProcessing(spider, config, productIDs) {
		return s.executeBatchTasks(ctx, monitorName, config, spider, productIDs)
	} else {
		return s.executeSingleTasks(ctx, monitorName, config, spider, productIDs)
	}
}

// taskToSpiderConfig方法已移动到SpiderManager中

// ========== 批量处理相关方法 ==========

// shouldUseBatchProcessing 判断是否应该使用批量处理
func (s *Scheduler) shouldUseBatchProcessing(spider spiders.Spider, config *spiders.SpiderConfig, productIDs []string) bool {
	// 检查爬虫是否支持批量处理
	if !spider.SupportsBatchProcessing() {
		return false
	}

	// 检查产品ID数量，单个产品不需要批量处理
	if len(productIDs) <= 1 {
		return false
	}

	// 检查配置中的批量处理策略
	if config.BatchProcessing != nil {
		switch config.BatchProcessing.Strategy {
		case "single":
			return false // 强制单个处理
		case "batch":
			return true // 强制批量处理
		case "optimal":
			fallthrough // 自动选择，继续下面的逻辑
		default:
			// 默认策略：自动选择
		}

		// 如果明确禁用了批量处理
		if !config.BatchProcessing.Enabled {
			return false
		}
	}

	// 默认策略：单个处理
	return false
}

// executeBatchTasks 执行批量任务 - 并发执行多个批次，使用 max_concurrency 限流
func (s *Scheduler) executeBatchTasks(ctx context.Context, monitorName string, config *spiders.SpiderConfig, spider spiders.Spider, productIDs []string) error {
	startTime := time.Now() // 记录整个批量任务的开始时间

	// 获取最大并发数
	maxConcurrency := s.getMaxConcurrency(config)

	s.logger.Debug(fmt.Sprintf("使用批量处理模式 - %s，产品数量：%d，最大并发：%d",
		monitorName, len(productIDs), maxConcurrency), "platform", config.Platform)

	// 获取批量大小
	batchSize := spider.GetMaxBatchSize()
	if config.BatchProcessing != nil && config.BatchProcessing.BatchSize > 0 {
		batchSize = config.BatchProcessing.BatchSize
	}

	// 创建批次列表 - 简化版本
	batches := s.splitIntoBatches(productIDs, batchSize)

	s.logger.Debug(fmt.Sprintf("批次分割完成 - %s，总项目：%d，批次数：%d，批次大小：%d",
		monitorName, len(productIDs), len(batches), batchSize), "platform", config.Platform)

	// 使用信号量限制并发数量
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup
	errorChan := make(chan error, len(batches))
	statsChan := make(chan *TaskStats, len(batches))

	// 并发执行每个批次，信号量在网络请求时限流
	for batchIndex, batch := range batches {
		wg.Add(1)
		go func(idx int, batchData []string) {
			defer wg.Done()

			s.logger.Debug(fmt.Sprintf("开始处理批次 - %s，批次索引：%d，批次大小：%d",
				monitorName, idx, len(batchData)), "platform", config.Platform)

			// 直接执行批量任务，传递信号量
			stats, err := s.executeBatchTaskDirectWithSemaphore(ctx, monitorName, config, spider, batchData, idx, semaphore)
			if err != nil {
				s.logger.Error(fmt.Sprintf("执行批量监控任务失败 - %s，批次索引：%d，错误：%s",
					monitorName, idx, err.Error()), "platform", config.Platform)
				errorChan <- err
				return
			}

			// 发送统计数据
			statsChan <- stats
			s.logger.Debug(fmt.Sprintf("批量监控任务完成 - %s，批次索引：%d",
				monitorName, idx), "platform", config.Platform)
		}(batchIndex, batch)
	}

	// 等待所有批次完成
	wg.Wait()
	close(errorChan)
	close(statsChan)

	// 收集错误和统计数据
	var errors []error
	for err := range errorChan {
		errors = append(errors, err)
	}

	// 汇总所有批次的统计数据
	totalStats := &TaskStats{}
	for stats := range statsChan {
		totalStats.TotalProducts += stats.TotalProducts
		totalStats.ProcessedCount += stats.ProcessedCount
		totalStats.DroppedCount += stats.DroppedCount
		totalStats.ErrorCount += stats.ErrorCount
		// 对于并发执行，时间应该取最大值而不是累加
		if stats.RequestDuration > totalStats.RequestDuration {
			totalStats.RequestDuration = stats.RequestDuration
		}
		if stats.ParseDuration > totalStats.ParseDuration {
			totalStats.ParseDuration = stats.ParseDuration
		}
		if stats.ProcessDuration > totalStats.ProcessDuration {
			totalStats.ProcessDuration = stats.ProcessDuration
		}
	}

	// 计算实际的墙钟时间
	actualTotalDuration := time.Since(startTime)

	// 统一输出总体统计
	successBatches := len(batches) - len(errors)
	s.logger.Info(fmt.Sprintf("任务完成 - %s 成功：%d，失败：%d，产品：%d，处理：%d，丢弃：%d，错误：%d，总耗时：%s，请求：%s，解析：%s，处理：%s",
		monitorName, successBatches, len(errors), totalStats.TotalProducts, totalStats.ProcessedCount, totalStats.DroppedCount, totalStats.ErrorCount,
		formatDuration(actualTotalDuration), formatDuration(totalStats.RequestDuration), formatDuration(totalStats.ParseDuration), formatDuration(totalStats.ProcessDuration)), "platform", config.Platform)

	return nil
}

// getMaxConcurrency 获取最大并发数
func (s *Scheduler) getMaxConcurrency(config *spiders.SpiderConfig) int {
	// 默认并发数
	defaultConcurrency := 3

	// 从爬虫配置中获取 max_concurrency
	if config.SpiderSettings != nil {
		if maxConcurrency, ok := config.SpiderSettings["max_concurrency"]; ok {
			switch v := maxConcurrency.(type) {
			case int:
				if v > 0 && v <= 50 { // 限制最大并发数在合理范围内
					return v
				}
			case float64:
				if v > 0 && v <= 50 {
					return int(v)
				}
			}
		}
	}

	return defaultConcurrency
}

// splitIntoBatches 将切片分割成指定大小的批次 - 简洁高效的实现
func (s *Scheduler) splitIntoBatches(items []string, batchSize int) [][]string {
	if len(items) == 0 || batchSize <= 0 {
		return nil
	}

	// 预分配容量，避免多次扩容
	batchCount := (len(items) + batchSize - 1) / batchSize
	batches := make([][]string, 0, batchCount)

	for i := 0; i < len(items); i += batchSize {
		end := i + batchSize
		if end > len(items) {
			end = len(items)
		}
		batches = append(batches, items[i:end])
	}

	return batches
}

// executeBatchTaskDirectWithSemaphore 直接执行批量任务 - 在网络请求时使用信号量限流
func (s *Scheduler) executeBatchTaskDirectWithSemaphore(ctx context.Context, monitorName string, config *spiders.SpiderConfig, spider spiders.Spider, productIDs []string, batchIndex int, semaphore chan struct{}) (*TaskStats, error) {
	startTime := time.Now()

	// 准备请求 - batchConfig 包含所有信息
	request, err := spider.PrepareBatchRequest(ctx, productIDs, config)
	if err != nil {
		return nil, fmt.Errorf("准备批量请求失败: %w", err)
	}

	// 获取信号量，限制网络请求并发数
	semaphore <- struct{}{}
	defer func() { <-semaphore }()

	// 执行请求
	requestStartTime := time.Now()
	response, err := s.serviceManager.GetRequestService().SendRequest(ctx, request)
	requestDuration := time.Since(requestStartTime)
	if err != nil {
		return nil, fmt.Errorf("执行批量请求失败: %w", err)
	}

	// 解析响应
	parseStartTime := time.Now()
	products, err := spider.ParseResponse(ctx, response, config)
	parseDuration := time.Since(parseStartTime)
	if err != nil {
		return nil, fmt.Errorf("解析批量响应失败: %w", err)
	}

	// 处理结果
	processStartTime := time.Now()
	stats, err := s.processResultsWithConfigUnified(ctx, monitorName, "", batchIndex, products, true)
	processDuration := time.Since(processStartTime)

	totalDuration := time.Since(startTime)

	// 将时间信息添加到stats中，用于任务完成日志
	if stats != nil {
		stats.RequestDuration = requestDuration
		stats.ParseDuration = parseDuration
		stats.ProcessDuration = processDuration
		stats.TotalDuration = totalDuration
	}

	return stats, err
}

// processResultsWithConfigUnified 统一处理解析结果 - 支持批次和单个模式
// isBatch: true=批次模式, false=单个模式
func (s *Scheduler) processResultsWithConfigUnified(ctx context.Context, monitorName string, productID string, index int, products []*models.ProductItem, isBatch bool) (*TaskStats, error) {
	var taskID string
	if isBatch {
		taskID = fmt.Sprintf("%s-%d", monitorName, index)
	} else {
		taskID = fmt.Sprintf("%s_%s_%d", monitorName, productID, index)
	}

	stats := &TaskStats{
		TotalProducts: len(products),
	}

	if len(products) == 0 {
		if isBatch {
			s.logger.Debug(fmt.Sprintf("未发现产品数据 任务ID：%s，监控：%s", taskID, monitorName))
		} else {
			s.logger.Debug(fmt.Sprintf("未发现产品数据 任务ID：%s，监控：%s，产品ID：%s",
				taskID, monitorName, productID))
		}
		return stats, nil
	}

	// 通过管道系统处理每个产品
	for i, product := range products {
		s.logger.Debug(fmt.Sprintf("处理产品数据 任务ID：%s，产品索引：%d，产品ID：%s",
			taskID, i+1, product.ProductID))

		// 通过管道系统处理产品数据
		pipelineManager := s.serviceManager.GetPipelineManager()
		processedItem, err := pipelineManager.ProcessItem(ctx, product)
		if err != nil {
			stats.ErrorCount++
			s.logger.Error(fmt.Sprintf("管道处理产品数据失败 任务ID：%s，产品ID：%s，错误：%s",
				taskID, product.ProductID, err.Error()))
			continue
		}

		// 如果管道返回nil，表示数据被丢弃
		if processedItem == nil {
			stats.DroppedCount++
			s.logger.Debug(fmt.Sprintf("产品数据被管道丢弃 任务ID：%s，产品ID：%s",
				taskID, product.ProductID))
		} else {
			stats.ProcessedCount++
		}
	}

	// 记录处理统计（调试级别）
	if isBatch {
		s.logger.Debug(fmt.Sprintf("批次处理完成 任务ID：%s，产品：%d，处理：%d，丢弃：%d，错误：%d",
			taskID, stats.TotalProducts, stats.ProcessedCount, stats.DroppedCount, stats.ErrorCount))
	} else {
		s.logger.Debug(fmt.Sprintf("单个任务处理完成 任务ID：%s，监控：%s，目标产品：%s，总产品：%d，处理：%d，丢弃：%d，错误：%d",
			taskID, monitorName, productID, stats.TotalProducts, stats.ProcessedCount, stats.DroppedCount, stats.ErrorCount))
	}

	return stats, nil
}

// executeSingleTasks 执行单个任务 - 并发执行，使用 max_concurrency 限流
func (s *Scheduler) executeSingleTasks(ctx context.Context, monitorName string, config *spiders.SpiderConfig, spider spiders.Spider, productIDs []string) error {
	startTime := time.Now() // 记录整个单个任务批次的开始时间

	// 获取最大并发数
	maxConcurrency := s.getMaxConcurrency(config)

	s.logger.Debug(fmt.Sprintf("单处理模式 - %s，数量：%d，并发：%d",
		monitorName, len(productIDs), maxConcurrency), "platform", config.Platform)

	// 使用信号量限制并发数量
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup
	errorChan := make(chan error, len(productIDs))
	statsChan := make(chan *TaskStats, len(productIDs))

	// 并发执行每个产品ID的任务，信号量在网络请求时限流
	for i, productID := range productIDs {
		wg.Add(1)
		go func(index int, prodID string) {
			defer wg.Done()

			// 直接执行任务，传递 SpiderConfig 和信号量
			stats, err := s.executeTaskDirectWithSemaphore(ctx, monitorName, config, spider, prodID, index, semaphore)
			if err != nil {
				s.logger.Error(fmt.Sprintf("任务失败 - %s，产品ID：%s，错误：%s",
					monitorName, prodID, err.Error()), "platform", config.Platform)
				errorChan <- err
				return
			}

			// 发送统计数据
			statsChan <- stats
			s.logger.Debug(fmt.Sprintf("任务完成 - %s，产品ID：%s",
				monitorName, prodID), "platform", config.Platform)
		}(i, productID)
	}

	// 等待所有任务完成
	wg.Wait()
	close(errorChan)
	close(statsChan)

	// 收集错误和统计数据
	var errors []error
	for err := range errorChan {
		errors = append(errors, err)
	}

	// 汇总所有任务的统计数据
	totalStats := &TaskStats{}
	for stats := range statsChan {
		totalStats.TotalProducts += stats.TotalProducts
		totalStats.ProcessedCount += stats.ProcessedCount
		totalStats.DroppedCount += stats.DroppedCount
		totalStats.ErrorCount += stats.ErrorCount
		// 对于并发执行，时间应该取最大值而不是累加
		if stats.RequestDuration > totalStats.RequestDuration {
			totalStats.RequestDuration = stats.RequestDuration
		}
		if stats.ParseDuration > totalStats.ParseDuration {
			totalStats.ParseDuration = stats.ParseDuration
		}
		if stats.ProcessDuration > totalStats.ProcessDuration {
			totalStats.ProcessDuration = stats.ProcessDuration
		}
	}

	// 计算实际的墙钟时间
	actualTotalDuration := time.Since(startTime)

	// 统一输出总体统计
	successTasks := len(productIDs) - len(errors)
	s.logger.Info(fmt.Sprintf("任务完成 - %s 成功：%d，失败：%d，产品：%d，处理：%d，丢弃：%d，错误：%d，总耗时：%s，请求：%s，解析：%s，处理：%s",
		monitorName, successTasks, len(errors), totalStats.TotalProducts, totalStats.ProcessedCount, totalStats.DroppedCount, totalStats.ErrorCount,
		formatDuration(actualTotalDuration), formatDuration(totalStats.RequestDuration), formatDuration(totalStats.ParseDuration), formatDuration(totalStats.ProcessDuration)), "platform", config.Platform)

	return nil
}

// executeTaskDirectWithSemaphore 直接执行单个产品任务 - 在网络请求时使用信号量限流
func (s *Scheduler) executeTaskDirectWithSemaphore(ctx context.Context, monitorName string, config *spiders.SpiderConfig, spider spiders.Spider, productID string, index int, semaphore chan struct{}) (*TaskStats, error) {
	startTime := time.Now()

	// 准备请求 - productConfig 包含所有信息
	request, err := spider.PrepareRequest(ctx, productID, config)
	if err != nil {
		return nil, fmt.Errorf("准备请求失败: %w", err)
	}

	// 获取信号量，限制网络请求并发数
	semaphore <- struct{}{}
	defer func() { <-semaphore }()

	// 执行请求
	requestStartTime := time.Now()
	response, err := s.serviceManager.GetRequestService().SendRequest(ctx, request)
	requestDuration := time.Since(requestStartTime)
	if err != nil {
		return nil, fmt.Errorf("执行请求失败: %w", err)
	}

	// 解析响应
	parseStartTime := time.Now()
	products, err := spider.ParseResponse(ctx, response, config)
	parseDuration := time.Since(parseStartTime)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 处理结果 - 直接使用配置信息，无需 MonitorTask
	processStartTime := time.Now()
	stats, err := s.processResultsWithConfigUnified(ctx, monitorName, productID, index, products, false)
	processDuration := time.Since(processStartTime)

	totalDuration := time.Since(startTime)

	// 将时间信息添加到stats中，用于任务完成日志
	if stats != nil {
		stats.RequestDuration = requestDuration
		stats.ParseDuration = parseDuration
		stats.ProcessDuration = processDuration
		stats.TotalDuration = totalDuration
	}

	return stats, err
}
