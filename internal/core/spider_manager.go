package core

import (
	"fmt"
	"sync"

	"go-monitor/internal/config"
	"go-monitor/internal/spiders"
	"go-monitor/internal/spiders/aliexpress"
	"go-monitor/internal/spiders/amazon"
	"go-monitor/internal/spiders/amazon/wishlist"
	"go-monitor/internal/spiders/popmart"
	"go-monitor/pkg/logging"
)

// SpiderManager 爬虫管理器 - 负责爬虫的创建、配置和管理
type SpiderManager struct {
	// 配置和日志
	configProvider config.ConfigProviderInterface
	logger         logging.Logger

	// 服务依赖
	serviceManager *ServiceManager

	// 爬虫管理
	spiders       map[string]spiders.Spider
	spiderConfigs map[string]*spiders.SpiderConfig

	// 并发控制
	mu sync.RWMutex
}

// NewSpiderManager 创建爬虫管理器
func NewSpiderManager(configProvider config.ConfigProviderInterface, serviceManager *ServiceManager) *SpiderManager {

	return &SpiderManager{
		configProvider: configProvider,
		serviceManager: serviceManager,
		logger:         logging.GetLogger("service.spider-manager"),
		spiders:        make(map[string]spiders.Spider),
		spiderConfigs:  make(map[string]*spiders.SpiderConfig),
	}
}

// Initialize 初始化爬虫管理器
func (sm *SpiderManager) Initialize() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.logger.Info("初始化爬虫管理器")

	// 1. 加载爬虫配置
	if err := sm.loadSpiderConfigs(); err != nil {
		return fmt.Errorf("加载爬虫配置失败: %w", err)
	}

	// 2. 初始化爬虫实例
	if err := sm.initializeSpiders(); err != nil {
		return fmt.Errorf("初始化爬虫实例失败: %w", err)
	}

	sm.logger.Info(fmt.Sprintf("爬虫管理器初始化完成 - 爬虫数量：%d，配置数量：%d",
		len(sm.spiders), len(sm.spiderConfigs)))

	return nil
}

// loadSpiderConfigs 加载爬虫配置 - 真正的开箱即用
func (sm *SpiderManager) loadSpiderConfigs() error {
	// 直接使用强类型配置提供者 - 开箱即用
	configManager, ok := sm.configProvider.(*config.ConfigManager)
	if !ok {
		return fmt.Errorf("配置提供者类型错误")
	}

	// 获取预处理的爬虫配置数据 - 开箱即用
	spiderConfigsData := configManager.GetProvider().GetAllSpiderConfigsForManager()

	if len(spiderConfigsData) == 0 {
		return fmt.Errorf("未找到监控配置")
	}

	sm.spiderConfigs = make(map[string]*spiders.SpiderConfig)

	// 直接使用预处理的配置数据 - 真正的开箱即用，无需类型转换
	for name, configData := range spiderConfigsData {
		// 直接使用 types.SpiderConfig，因为 spiders.SpiderConfig 是其类型别名
		spiderConfig := configData
		// 只需要注入运行时依赖
		spiderConfig.RedisManager = sm.getRedisManager()
		sm.spiderConfigs[name] = spiderConfig
	}

	sm.logger.Info(fmt.Sprintf("爬虫配置加载完成，爬虫数量：%d", len(sm.spiderConfigs)))

	return nil
}

// getRedisManager 获取Redis管理器
func (sm *SpiderManager) getRedisManager() interface{} {
	if sm.serviceManager != nil {
		return sm.serviceManager.GetRedisManager()
	}
	return nil
}

// initializeSpiders 初始化爬虫实例
func (sm *SpiderManager) initializeSpiders() error {
	platformTypes := make(map[string]bool)

	// 收集需要的爬虫类型
	for _, config := range sm.spiderConfigs {
		if config.Enabled {
			platformTypes[config.Platform] = true
		}
	}

	// 初始化爬虫实例
	for platform := range platformTypes {
		spider, err := sm.createSpider(platform)
		if err != nil {
			return fmt.Errorf("创建爬虫失败 %s: %w", platform, err)
		}

		sm.spiders[platform] = spider

		sm.logger.Info(fmt.Sprintf("初始化爬虫 %s，名称：%s", platform, spider.GetName()), "platform", platform)
	}

	return nil
}

// createSpider 创建爬虫实例
func (sm *SpiderManager) createSpider(platform string) (spiders.Spider, error) {
	switch platform {
	case "amazon":
		return amazon.NewSpider(), nil
	case "amazon-wishlist":
		return wishlist.NewWishlistSpider(), nil
	case "popmart":
		return popmart.NewSpider(), nil
	case "aliexpress":
		return aliexpress.NewSpider(), nil
	default:
		return nil, fmt.Errorf("不支持的爬虫类型: %s", platform)
	}
}

// GetSpider 获取爬虫实例
func (sm *SpiderManager) GetSpider(platform string) (spiders.Spider, bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	spider, exists := sm.spiders[platform]
	return spider, exists
}

// GetSpiderConfig 获取爬虫配置
func (sm *SpiderManager) GetSpiderConfig(name string) (*spiders.SpiderConfig, bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	config, exists := sm.spiderConfigs[name]
	return config, exists
}

// GetEnabledConfigs 获取所有启用的爬虫配置
func (sm *SpiderManager) GetEnabledConfigs() map[string]*spiders.SpiderConfig {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	enabledConfigs := make(map[string]*spiders.SpiderConfig)
	for name, config := range sm.spiderConfigs {
		if config.Enabled {
			enabledConfigs[name] = config
		}
	}

	return enabledConfigs
}

// GetStats 获取爬虫管理器统计信息
func (sm *SpiderManager) GetStats() map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	enabledCount := 0
	platformCounts := make(map[string]int)

	for _, config := range sm.spiderConfigs {
		if config.Enabled {
			enabledCount++
		}
		platformCounts[config.Platform]++
	}

	return map[string]interface{}{
		"total_configs":    len(sm.spiderConfigs),
		"enabled_configs":  enabledCount,
		"spider_instances": len(sm.spiders),
		"platform_counts":  platformCounts,
	}
}

// Close 关闭爬虫管理器
func (sm *SpiderManager) Close() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.logger.Info("关闭爬虫管理器")

	// 清理资源
	sm.spiders = make(map[string]spiders.Spider)
	sm.spiderConfigs = make(map[string]*spiders.SpiderConfig)

	sm.logger.Info("爬虫管理器已关闭")
	return nil
}
