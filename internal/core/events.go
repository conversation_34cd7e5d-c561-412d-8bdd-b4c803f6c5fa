package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go-monitor/internal/models"
	"go-monitor/pkg/logging"
)

// Event 基础事件接口
type Event interface {
	Type() string
	Timestamp() time.Time
	Data() map[string]interface{}
	Source() string
}

// BaseEvent 基础事件实现
type BaseEvent struct {
	EventType   string                 `json:"type"`
	EventTime   time.Time              `json:"timestamp"`
	EventData   map[string]interface{} `json:"data"`
	EventSource string                 `json:"source"`
}

func (e *BaseEvent) Type() string {
	return e.EventType
}

func (e *BaseEvent) Timestamp() time.Time {
	return e.EventTime
}

func (e *BaseEvent) Data() map[string]interface{} {
	return e.EventData
}

func (e *BaseEvent) Source() string {
	return e.EventSource
}

// ProductCrawledEvent 产品抓取完成事件
type ProductCrawledEvent struct {
	BaseEvent
	Product     *models.ProductItem `json:"product"`
	MonitorName string              `json:"monitorName"`
}

func NewProductCrawledEvent(product *models.ProductItem, monitorName string) *ProductCrawledEvent {
	return &ProductCrawledEvent{
		BaseEvent: BaseEvent{
			EventType:   "product_crawled",
			EventTime:   time.Now(),
			EventSource: "monitor",
			EventData:   make(map[string]interface{}),
		},
		Product:     product,
		MonitorName: monitorName,
	}
}

// ProductValidatedEvent 产品验证完成事件
type ProductValidatedEvent struct {
	BaseEvent
	Product     *models.ProductItem `json:"product"`
	MonitorName string              `json:"monitorName"`
}

func NewProductValidatedEvent(product *models.ProductItem, monitorName string) *ProductValidatedEvent {
	return &ProductValidatedEvent{
		BaseEvent: BaseEvent{
			EventType:   "product_validated",
			EventTime:   time.Now(),
			EventSource: "monitor",
			EventData:   make(map[string]interface{}),
		},
		Product:     product,
		MonitorName: monitorName,
	}
}

// ProductProcessedEvent 产品处理完成事件
type ProductProcessedEvent struct {
	BaseEvent
	Product     *models.ProductItem `json:"product"`
	MonitorName string              `json:"monitorName"`
}

func NewProductProcessedEvent(product *models.ProductItem, monitorName string) *ProductProcessedEvent {
	return &ProductProcessedEvent{
		BaseEvent: BaseEvent{
			EventType:   "product_processed",
			EventTime:   time.Now(),
			EventSource: "monitor",
			EventData:   make(map[string]interface{}),
		},
		Product:     product,
		MonitorName: monitorName,
	}
}

// ConfigUpdatedEvent 配置更新事件
type ConfigUpdatedEvent struct {
	BaseEvent
	Key      string      `json:"key"`
	OldValue interface{} `json:"oldValue"`
	NewValue interface{} `json:"newValue"`
}

func NewConfigUpdatedEvent(key string, oldValue, newValue interface{}) *ConfigUpdatedEvent {
	return &ConfigUpdatedEvent{
		BaseEvent: BaseEvent{
			EventType:   "config_updated",
			EventTime:   time.Now(),
			EventSource: "monitor",
			EventData:   make(map[string]interface{}),
		},
		Key:      key,
		OldValue: oldValue,
		NewValue: newValue,
	}
}

// RequestCompletedEvent 请求完成事件
type RequestCompletedEvent struct {
	BaseEvent
	MonitorName  string        `json:"monitorName"`
	ResponseTime time.Duration `json:"responseTime"`
	StatusCode   int           `json:"statusCode"`
	Success      bool          `json:"success"`
}

func NewRequestCompletedEvent(monitorName string, responseTime time.Duration, statusCode int, success bool) *RequestCompletedEvent {
	return &RequestCompletedEvent{
		BaseEvent: BaseEvent{
			EventType:   "request_completed",
			EventTime:   time.Now(),
			EventSource: "monitor",
			EventData:   make(map[string]interface{}),
		},
		MonitorName:  monitorName,
		ResponseTime: responseTime,
		StatusCode:   statusCode,
		Success:      success,
	}
}

// ErrorOccurredEvent 错误发生事件
type ErrorOccurredEvent struct {
	BaseEvent
	MonitorName string `json:"monitorName"`
	Error       error  `json:"error"`
	Context     string `json:"context"`
}

func NewErrorOccurredEvent(monitorName string, err error, context string) *ErrorOccurredEvent {
	return &ErrorOccurredEvent{
		BaseEvent: BaseEvent{
			EventType:   "error_occurred",
			EventTime:   time.Now(),
			EventSource: "monitor",
			EventData:   make(map[string]interface{}),
		},
		MonitorName: monitorName,
		Error:       err,
		Context:     context,
	}
}

// EventHandler 事件处理器函数类型
type EventHandler func(ctx context.Context, event Event) error

// EventListener 事件监听器
type EventListener struct {
	Priority int
	Handler  EventHandler
}

// EventBus 事件总线
type EventBus struct {
	name      string
	listeners map[string][]*EventListener
	mutex     sync.RWMutex
	logger    logging.Logger
	stats     *EventBusStats
}

// EventBusStats 事件总线统计信息
type EventBusStats struct {
	EventsPublished int64
	CallbackErrors  int64
	TotalListeners  int
	mutex           sync.RWMutex
}

// NewEventBus 创建新的事件总线
func NewEventBus(name string) *EventBus {
	return &EventBus{
		name:      name,
		listeners: make(map[string][]*EventListener),
		logger:    logging.GetLogger("service.EventBus-" + name),
		stats: &EventBusStats{
			EventsPublished: 0,
			CallbackErrors:  0,
			TotalListeners:  0,
		},
	}
}

// Subscribe 订阅事件
func (eb *EventBus) Subscribe(eventType string, handler EventHandler, priority int) {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	listener := &EventListener{
		Priority: priority,
		Handler:  handler,
	}

	eb.listeners[eventType] = append(eb.listeners[eventType], listener)

	// 按优先级排序（数字越小优先级越高）
	eb.sortListeners(eventType)

	eb.stats.mutex.Lock()
	eb.stats.TotalListeners++
	eb.stats.mutex.Unlock()

	eb.logger.Debug(fmt.Sprintf("订阅事件：%s，优先级：%d", eventType, priority))
}

// sortListeners 对监听器按优先级排序
func (eb *EventBus) sortListeners(eventType string) {
	listeners := eb.listeners[eventType]
	for i := 0; i < len(listeners)-1; i++ {
		for j := i + 1; j < len(listeners); j++ {
			if listeners[i].Priority > listeners[j].Priority {
				listeners[i], listeners[j] = listeners[j], listeners[i]
			}
		}
	}
}

// Unsubscribe 取消订阅事件
func (eb *EventBus) Unsubscribe(eventType string, handler EventHandler) bool {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	listeners := eb.listeners[eventType]
	for i, listener := range listeners {
		// 由于函数比较较为复杂，这里使用简单的长度比较作为示例
		// 实际项目中可能需要更精确的方式来识别同一个处理器
		if fmt.Sprintf("%p", listener.Handler) == fmt.Sprintf("%p", handler) {
			// 移除监听器
			eb.listeners[eventType] = append(listeners[:i], listeners[i+1:]...)

			eb.stats.mutex.Lock()
			eb.stats.TotalListeners--
			eb.stats.mutex.Unlock()

			eb.logger.Debug(fmt.Sprintf("取消订阅事件：%s", eventType))
			return true
		}
	}
	return false
}

// Publish 发布事件
func (eb *EventBus) Publish(ctx context.Context, event Event) error {
	eb.mutex.RLock()
	listeners := eb.listeners[event.Type()]
	eb.mutex.RUnlock()

	if len(listeners) == 0 {
		eb.logger.Debug(fmt.Sprintf("事件没有订阅者：%s", event.Type()))
		return nil
	}

	eb.stats.mutex.Lock()
	eb.stats.EventsPublished++
	eb.stats.mutex.Unlock()

	eb.logger.Debug(fmt.Sprintf("发布事件：%s", event.Type()))

	// 按优先级执行回调
	for _, listener := range listeners {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			if err := listener.Handler(ctx, event); err != nil {
				eb.stats.mutex.Lock()
				eb.stats.CallbackErrors++
				eb.stats.mutex.Unlock()

				eb.logger.Error(fmt.Sprintf("事件回调执行失败：%s，错误：%s", event.Type(), err.Error()))
				// 继续执行其他回调，不因单个回调失败而中断
			} else {
				eb.logger.Debug(fmt.Sprintf("事件回调执行成功：%s", event.Type()))
			}
		}
	}

	return nil
}

// PublishAsync 异步发布事件
func (eb *EventBus) PublishAsync(ctx context.Context, event Event) {
	go func() {
		if err := eb.Publish(ctx, event); err != nil {
			eb.logger.Error(fmt.Sprintf("异步发布事件失败：%s，错误：%s", event.Type(), err.Error()))
		}
	}()
}

// PublishAsyncInterface 异步发布任意类型事件 (用于兼容spiders接口)
func (eb *EventBus) PublishAsyncInterface(ctx context.Context, eventData interface{}) {
	// 如果是Event类型，直接发布
	if event, ok := eventData.(Event); ok {
		eb.PublishAsync(ctx, event)
		return
	}

	// 如果是ProductItem类型，转换为ProductCrawledEvent
	if product, ok := eventData.(*models.ProductItem); ok {
		event := NewProductCrawledEvent(product, "spider")
		eb.PublishAsync(ctx, event)
		return
	}

	// 其他类型，创建通用事件
	baseEvent := &BaseEvent{
		EventType:   "generic_data",
		EventTime:   time.Now(),
		EventSource: "spider",
		EventData:   map[string]interface{}{"data": eventData},
	}
	eb.PublishAsync(ctx, baseEvent)
}

// GetListeners 获取指定事件类型的监听器数量
func (eb *EventBus) GetListeners(eventType string) int {
	eb.mutex.RLock()
	defer eb.mutex.RUnlock()
	return len(eb.listeners[eventType])
}

// ClearListeners 清空监听器
func (eb *EventBus) ClearListeners(eventType string) {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	if eventType == "" {
		// 清空所有监听器
		totalCleared := 0
		for _, listeners := range eb.listeners {
			totalCleared += len(listeners)
		}
		eb.listeners = make(map[string][]*EventListener)

		eb.stats.mutex.Lock()
		eb.stats.TotalListeners = 0
		eb.stats.mutex.Unlock()

		eb.logger.Debug(fmt.Sprintf("清空所有事件监听器，清除数量：%d", totalCleared))
	} else {
		// 清空指定类型的监听器
		cleared := len(eb.listeners[eventType])
		delete(eb.listeners, eventType)

		eb.stats.mutex.Lock()
		eb.stats.TotalListeners -= cleared
		eb.stats.mutex.Unlock()

		eb.logger.Debug(fmt.Sprintf("清空指定事件监听器：%s，清除数量：%d", eventType, cleared))
	}
}

// GetStats 获取事件总线统计信息
func (eb *EventBus) GetStats() map[string]interface{} {
	eb.stats.mutex.RLock()
	defer eb.stats.mutex.RUnlock()

	eb.mutex.RLock()
	listenersByType := make(map[string]int)
	for eventType, listeners := range eb.listeners {
		listenersByType[eventType] = len(listeners)
	}
	eb.mutex.RUnlock()

	return map[string]interface{}{
		"eventTypes":      len(eb.listeners),
		"totalListeners":  eb.stats.TotalListeners,
		"eventsPublished": eb.stats.EventsPublished,
		"callbackErrors":  eb.stats.CallbackErrors,
		"listenersByType": listenersByType,
	}
}

// String 返回事件总线的字符串表示
func (eb *EventBus) String() string {
	stats := eb.GetStats()
	return fmt.Sprintf("EventBus(%s): %d types, %d listeners",
		eb.name, stats["eventTypes"], stats["totalListeners"])
}
