package core

import (
	"context"
	"fmt"
	"sync"

	"go-monitor/internal/config"
	"go-monitor/internal/pipelines"
	"go-monitor/internal/services/redis"
	"go-monitor/internal/services/request"
	"go-monitor/internal/services/resource"
	"go-monitor/internal/services/resource/initializers"
	"go-monitor/pkg/logging"
)

// ServiceManager 服务管理器 - 负责管理各种服务的生命周期
type ServiceManager struct {
	// 配置和日志
	configProvider config.ConfigProviderInterface
	logger         logging.Logger

	// 服务实例
	requestService  *request.Service
	pipelineManager *pipelines.Manager
	resourceManager *resource.Manager   // 资源管理器
	redisManager    *redis.RedisManager // Redis 管理器

	// 状态管理
	initialized bool
	mu          sync.RWMutex
}

// NewServiceManager 创建服务管理器
func NewServiceManager(configProvider config.ConfigProviderInterface) *ServiceManager {
	return &ServiceManager{
		configProvider: configProvider,
		logger:         logging.GetLogger("service.manager"),
	}
}

// Initialize 初始化所有服务
func (sm *ServiceManager) Initialize() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if sm.initialized {
		return fmt.Errorf("服务管理器已经初始化")
	}

	sm.logger.Info("初始化服务管理器")

	// 1. 初始化 Redis 管理器
	if err := sm.initializeRedisManager(); err != nil {
		return fmt.Errorf("初始化 Redis 管理器失败: %w", err)
	}

	// 2. 初始化请求服务（必须在资源管理器之前）
	if err := sm.initializeRequestService(); err != nil {
		return fmt.Errorf("初始化请求服务失败: %w", err)
	}

	// 3. 初始化资源管理器（依赖请求服务）
	if err := sm.initializeResourceManager(); err != nil {
		return fmt.Errorf("初始化资源管理器失败: %w", err)
	}

	// 4. 初始化管道系统
	if err := sm.initializePipelineSystem(); err != nil {
		return fmt.Errorf("初始化管道系统失败: %w", err)
	}

	sm.initialized = true
	sm.logger.Info(fmt.Sprintf("服务管理器初始化完成 - Redis管理器：%t，资源管理器：%t，请求服务：%t，管道管理器：%t，管道数量：%d",
		sm.redisManager != nil, sm.resourceManager != nil, sm.requestService != nil,
		sm.pipelineManager != nil, sm.pipelineManager.Count()))

	return nil
}

// initializeRequestService 初始化请求服务
func (sm *ServiceManager) initializeRequestService() error {
	sm.logger.Info("初始化请求服务和中间件")

	// 创建请求服务
	sm.requestService = request.NewService()

	// 直接获取中间件配置，无需转换
	middlewareConfigs := sm.getMiddlewareConfigs()

	// 加载中间件
	if err := sm.requestService.LoadMiddlewares(middlewareConfigs); err != nil {
		return fmt.Errorf("加载中间件失败: %w", err)
	}

	sm.logger.Info("请求服务初始化完成")
	return nil
}

// initializePipelineSystem 初始化管道系统
func (sm *ServiceManager) initializePipelineSystem() error {
	sm.logger.Info("初始化数据管道系统")

	// 验证Redis管理器已初始化
	if sm.redisManager == nil {
		return fmt.Errorf("Redis管理器未初始化，无法创建管道管理器")
	}

	// 创建管道管理器，传递Redis管理器
	sm.pipelineManager = pipelines.NewManager(sm.redisManager)

	// 获取完整的管道配置文件内容
	pipelineConfig := sm.getPipelineConfig()

	// 加载管道
	if err := sm.pipelineManager.LoadPipelines(pipelineConfig); err != nil {
		return fmt.Errorf("加载管道失败: %w", err)
	}

	sm.logger.Info(fmt.Sprintf("管道系统初始化完成，管道数量：%d", sm.pipelineManager.Count()))
	return nil
}

// initializeRedisManager 初始化 Redis 管理器
func (sm *ServiceManager) initializeRedisManager() error {
	sm.logger.Info("初始化 Redis 管理器")

	var redisConfig *config.RedisConfig

	// 直接从配置管理器获取结构化配置
	if configManager, ok := sm.configProvider.(*config.ConfigManager); ok {
		// 直接获取Redis配置结构
		if cfg := configManager.GetConfig(); cfg != nil {
			redisConfig = &cfg.Redis
			sm.logger.Debug(fmt.Sprintf("从配置管理器获取Redis配置 %s:%d", redisConfig.Host, redisConfig.Port))
		}
	}

	// 如果无法获取配置，使用默认配置
	if redisConfig == nil {
		sm.logger.Warn("无法获取Redis配置，使用默认配置")
		redisConfig = nil // 让NewRedisManager使用默认配置
	}

	// 创建Redis管理器
	sm.redisManager = redis.NewRedisManager(redisConfig)

	// 启动Redis连接
	if err := sm.redisManager.Start(); err != nil {
		return fmt.Errorf("启动Redis连接失败: %w", err)
	}

	sm.logger.Info("Redis 管理器初始化完成")
	return nil
}

// initializeResourceManager 初始化资源管理器
func (sm *ServiceManager) initializeResourceManager() error {
	sm.logger.Info("初始化资源管理器")

	// 确保Redis管理器已初始化
	if sm.redisManager == nil {
		return fmt.Errorf("Redis管理器未初始化，无法创建资源管理器")
	}

	// 获取开箱即用的资源配置 - 无需任何转换
	configManager, ok := sm.configProvider.(*config.ConfigManager)
	if !ok {
		return fmt.Errorf("配置提供者类型错误，无法获取资源配置")
	}

	// 直接使用强类型配置，无需手动转换
	resourceConfig := configManager.GetProvider().Resource()
	if resourceConfig == nil {
		return fmt.Errorf("未找到resource配置段")
	}

	sm.logger.Info(fmt.Sprintf("资源配置获取完成 - 全局启用：%t，刷新间隔：%s，初始化器数量：%d",
		resourceConfig.Global.Enabled, resourceConfig.Global.RefreshInterval.String(), len(resourceConfig.Initializers)))

	// 创建资源管理器 - 开箱即用，直接使用配置系统的类型
	sm.resourceManager = resource.NewManagerFromConfig(resourceConfig)

	// 注册Amazon zipCode初始化器
	amazonZipCodeInitializer := initializers.NewAmazonZipCodeInitializer(sm.requestService)
	if err := sm.resourceManager.Register(amazonZipCodeInitializer); err != nil {
		return fmt.Errorf("注册Amazon zipCode初始化器失败: %w", err)
	}

	// 注册Amazon ACP初始化器
	amazonACPInitializer := initializers.NewAmazonACPInitializer(sm.redisManager, sm.requestService)
	if err := sm.resourceManager.Register(amazonACPInitializer); err != nil {
		return fmt.Errorf("注册Amazon ACP初始化器失败: %w", err)
	}

	// 注册远程配置初始化器
	remoteConfigInitializer := initializers.NewRemoteConfigInitializer(sm.redisManager)
	if err := sm.resourceManager.Register(remoteConfigInitializer); err != nil {
		return fmt.Errorf("注册远程配置初始化器失败: %w", err)
	}

	// 初始化资源管理器
	ctx := context.Background()
	if err := sm.resourceManager.Initialize(ctx); err != nil {
		return fmt.Errorf("初始化资源管理器失败: %w", err)
	}

	// 启动资源管理器
	if err := sm.resourceManager.Start(ctx); err != nil {
		return fmt.Errorf("启动资源管理器失败: %w", err)
	}

	sm.logger.Info(fmt.Sprintf("资源管理器初始化完成 - 健康状态：%t，初始化器：%v",
		sm.resourceManager.IsHealthy(), sm.resourceManager.ListInitializers()))
	return nil
}

// GetRequestService 获取请求服务
func (sm *ServiceManager) GetRequestService() *request.Service {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.requestService
}

// GetResourceManager 获取资源管理器
func (sm *ServiceManager) GetResourceManager() *resource.Manager {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.resourceManager
}

// GetRedisManager 获取Redis管理器
func (sm *ServiceManager) GetRedisManager() *redis.RedisManager {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.redisManager
}

// GetPipelineManager 获取管道管理器
func (sm *ServiceManager) GetPipelineManager() *pipelines.Manager {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.pipelineManager
}

// IsInitialized 检查是否已初始化
func (sm *ServiceManager) IsInitialized() bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.initialized
}

// GetStats 获取服务管理器统计信息
func (sm *ServiceManager) GetStats() map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	stats := map[string]interface{}{
		"initialized":      sm.initialized,
		"redis_manager":    sm.redisManager != nil,
		"request_service":  sm.requestService != nil,
		"pipeline_manager": sm.pipelineManager != nil,
		"resource_manager": sm.resourceManager != nil,
	}

	if sm.pipelineManager != nil {
		stats["pipeline_count"] = sm.pipelineManager.Count()
		stats["pipeline_stats"] = sm.pipelineManager.GetStats()
	}

	return stats
}

// Close 关闭所有服务
func (sm *ServiceManager) Close() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if !sm.initialized {
		return nil
	}

	sm.logger.Info("关闭服务管理器")

	var errors []error

	// 关闭资源管理器
	if sm.resourceManager != nil {
		if err := sm.resourceManager.Stop(); err != nil {
			errors = append(errors, fmt.Errorf("关闭资源管理器失败: %w", err))
			sm.logger.Error(fmt.Sprintf("关闭资源管理器失败：%s", err.Error()))
		} else {
			sm.logger.Info("资源管理器已关闭")
		}
		sm.resourceManager = nil
	}

	// 关闭 Redis 管理器
	if sm.redisManager != nil {
		if err := sm.redisManager.Stop(); err != nil {
			errors = append(errors, fmt.Errorf("关闭 Redis 管理器失败: %w", err))
			sm.logger.Error(fmt.Sprintf("关闭Redis管理器失败：%s", err.Error()))
		} else {
			sm.logger.Info("Redis 管理器已关闭")
		}
		sm.redisManager = nil
	}

	// 关闭请求服务
	if sm.requestService != nil {
		if err := sm.requestService.Close(); err != nil {
			errors = append(errors, fmt.Errorf("关闭请求服务失败: %w", err))
			sm.logger.Error(fmt.Sprintf("关闭请求服务失败：%s", err.Error()))
		} else {
			sm.logger.Info("请求服务已关闭")
		}
		sm.requestService = nil
	}

	// 关闭管道管理器
	if sm.pipelineManager != nil {
		if err := sm.pipelineManager.Close(); err != nil {
			errors = append(errors, fmt.Errorf("关闭管道管理器失败: %w", err))
			sm.logger.Error(fmt.Sprintf("关闭管道管理器失败：%s", err.Error()))
		} else {
			sm.logger.Info("管道管理器已关闭")
		}
		sm.pipelineManager = nil
	}

	sm.initialized = false
	sm.logger.Info("服务管理器已关闭")

	// 如果有错误，返回第一个错误
	if len(errors) > 0 {
		return errors[0]
	}

	return nil
}

// Restart 重启所有服务
func (sm *ServiceManager) Restart() error {
	sm.logger.Info("重启服务管理器")

	// 先关闭
	if err := sm.Close(); err != nil {
		return fmt.Errorf("关闭服务失败: %w", err)
	}

	// 再初始化
	if err := sm.Initialize(); err != nil {
		return fmt.Errorf("重新初始化服务失败: %w", err)
	}

	sm.logger.Info("服务管理器重启完成")
	return nil
}

// ReloadConfig 重新加载配置
func (sm *ServiceManager) ReloadConfig() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if !sm.initialized {
		return fmt.Errorf("服务管理器未初始化")
	}

	sm.logger.Info("重新加载服务配置")

	// 重新加载中间件配置
	if sm.requestService != nil {
		middlewareConfigs := sm.getMiddlewareConfigs()
		if err := sm.requestService.LoadMiddlewares(middlewareConfigs); err != nil {
			sm.logger.Error(fmt.Sprintf("重新加载中间件配置失败：%s", err.Error()))
			return fmt.Errorf("重新加载中间件配置失败: %w", err)
		}
	}

	// 重新加载管道配置
	if sm.pipelineManager != nil {
		pipelineConfig := sm.getPipelineConfig()
		if err := sm.pipelineManager.LoadPipelines(pipelineConfig); err != nil {
			sm.logger.Error(fmt.Sprintf("重新加载管道配置失败：%s", err.Error()))
			return fmt.Errorf("重新加载管道配置失败: %w", err)
		}
	}

	sm.logger.Info("服务配置重新加载完成")
	return nil
}

// HealthCheck 健康检查
func (sm *ServiceManager) HealthCheck() map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	health := map[string]interface{}{
		"service_manager": map[string]interface{}{
			"status":      "healthy",
			"initialized": sm.initialized,
		},
	}

	// 检查请求服务
	if sm.requestService != nil {
		health["request_service"] = map[string]interface{}{
			"status": "healthy",
			"active": true,
		}
	} else {
		health["request_service"] = map[string]interface{}{
			"status": "unhealthy",
			"active": false,
		}
	}

	// 检查管道管理器
	if sm.pipelineManager != nil {
		health["pipeline_manager"] = map[string]interface{}{
			"status":         "healthy",
			"active":         true,
			"pipeline_count": sm.pipelineManager.Count(),
		}
	} else {
		health["pipeline_manager"] = map[string]interface{}{
			"status": "unhealthy",
			"active": false,
		}
	}

	return health
}

// getMiddlewareConfigs 直接获取中间件配置，无需转换
func (sm *ServiceManager) getMiddlewareConfigs() map[string]config.MiddlewareConfig {
	// 直接从配置管理器获取MiddlewareConfig，无需转换
	if configManager, ok := sm.configProvider.(*config.ConfigManager); ok {
		return configManager.GetProvider().GetMiddlewareConfigs()
	}

	// 如果无法获取，返回空配置
	return make(map[string]config.MiddlewareConfig)
}

// getPipelineConfig 获取完整的管道配置文件内容
func (sm *ServiceManager) getPipelineConfig() map[string]config.PipelineConfig {
	// 尝试从配置管理器获取原始的管道配置文件内容
	if configManager, ok := sm.configProvider.(*config.ConfigManager); ok {
		return configManager.GetProvider().GetPipelineConfigs()
	}

	// 如果无法获取，返回空配置
	return make(map[string]config.PipelineConfig)
}
