package request

import (
	"context"
	"fmt"
	"maps"
	"sync"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/middleware"
	"go-monitor/internal/models"
	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/foundation"
	"go-monitor/pkg/logging"
)

// Service 请求服务 - 专注于中间件管理和HTTP请求发送
type Service struct {
	logger            logging.Logger
	baseConfig        *foundation.Config    // 使用新的 foundation.Config
	httpClient        foundation.HTTPClient // 主要HTTP客户端（高级客户端）
	basicClient       foundation.HTTPClient // 基础HTTP客户端（回退用）
	middlewareManager *middleware.Manager
	mu                sync.RWMutex

	// 统计信息
	stats RequestStats
}

// RequestStats 请求统计信息
type RequestStats struct {
	TotalRequests   int64
	SuccessCount    int64
	FailureCount    int64
	RetryCount      int64
	AverageResponse time.Duration
	ActiveRequests  int64
	LastRequestTime time.Time
	mu              sync.RWMutex
}

// NewService 创建新的请求服务实例
func NewService() *Service {
	// 创建默认配置
	config := &foundation.Config{
		Timeout:           30 * time.Second,
		MaxIdleConns:      100,
		MaxConnsPerHost:   10,
		UserAgent:         "Go-Monitor/1.0",
		EnableMonitoring:  true,
		EnableRetry:       true,
		EnableFingerprint: true, // 启用指纹功能
		MaxRetries:        3,
		RetryDelay:        1 * time.Second,
		RetryBackoff:      2.0,
	}

	// 创建 HTTP 客户端 - 同时创建高级客户端和基础客户端
	httpClient := httpclient.NewAdvancedClient(config)
	basicClient := httpclient.NewBasicClient(config)

	service := &Service{
		logger:            logging.GetLogger("service.request"),
		baseConfig:        config,
		httpClient:        httpClient,
		basicClient:       basicClient,
		middlewareManager: middleware.NewManager(),
		stats:             RequestStats{},
	}

	// 自动加载中间件配置
	if err := service.loadDefaultMiddlewares(); err != nil {
		service.logger.Warn("加载默认中间件失败", "error", err.Error())
	}

	return service
}

// NewServiceWithConfig 使用指定配置创建请求服务实例
func NewServiceWithConfig(config *foundation.Config) *Service {
	// 创建 HTTP 客户端 - 使用高级客户端以支持指纹功能
	httpClient := httpclient.NewAdvancedClient(config)

	return &Service{
		logger:            logging.GetLogger("service.request"),
		baseConfig:        config,
		httpClient:        httpClient,
		middlewareManager: middleware.NewManager(),
		stats:             RequestStats{},
	}
}

// LoadMiddlewares 加载和注册所有中间件 - 直接使用MiddlewareConfig，无需转换
func (s *Service) LoadMiddlewares(middlewareConfigs map[string]config.MiddlewareConfig) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.logger.Info(fmt.Sprintf("开始加载中间件，数量：%d", len(middlewareConfigs)))

	if err := s.middlewareManager.LoadMiddlewares(middlewareConfigs); err != nil {
		s.logger.Error(fmt.Sprintf("加载中间件失败：%s", err.Error()))
		return fmt.Errorf("failed to load middlewares: %w", err)
	}

	// 中间件系统已完成初始化

	s.logger.Info(fmt.Sprintf("中间件加载完成，数量：%d", s.middlewareManager.Count()))
	return nil
}

// RegisterMiddleware 注册单个中间件
func (s *Service) RegisterMiddleware(name string, middleware interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	return s.middlewareManager.Register(name, middleware)
}

// SendRequest 发送HTTP请求 - 核心请求处理方法
func (s *Service) SendRequest(ctx context.Context, req *models.Request) (*models.Response, error) {
	startTime := time.Now()
	s.updateStats("total_requests", 1)
	s.updateStats("active_requests", 1)
	defer s.updateStats("active_requests", -1)

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		s.logger.Debug(fmt.Sprintf("请求开始前检测到上下文已取消：%s", req.URL), "platform", req.SpiderType)
		return nil, ctx.Err()
	default:
	}

	// 安全检查：防止异常的重试次数
	if req.RetryCount > 10 {
		s.logger.Error(fmt.Sprintf("请求重试次数异常，拒绝处理：%s，重试次数：%d",
			req.URL, req.RetryCount), "platform", req.SpiderType)
		return nil, fmt.Errorf("请求重试次数异常: %d", req.RetryCount)
	}

	s.logger.Debug(fmt.Sprintf("开始处理请求 %s %s，代理：%s，重试次数：%d",
		req.Method, req.URL, req.Proxy, req.RetryCount), "platform", req.SpiderType)

	// 1. 预处理请求 - 应用中间件
	processedReq, err := s.preprocessRequest(ctx, req)
	if err != nil {
		s.updateStats("failure_count", 1)
		return nil, fmt.Errorf("request preprocessing failed: %w", err)
	}

	// 请求被中间件跳过
	if processedReq == nil {
		s.logger.Info(fmt.Sprintf("请求被中间件跳过：%s", req.URL), "platform", req.SpiderType)
		return nil, nil
	}

	// 2. 执行HTTP请求
	resp, err := s.executeRequest(ctx, processedReq)
	if err != nil {
		// 处理异常
		if s.shouldRetry(ctx, err, processedReq) {
			s.updateStats("retry_count", 1)
			return s.retryRequest(ctx, processedReq, err)
		}

		s.updateStats("failure_count", 1)
		return nil, err
	}

	// 3. 后处理响应 - 应用中间件
	processedResp, needRetry, err := s.postprocessResponse(ctx, resp)
	if err != nil {
		s.updateStats("failure_count", 1)
		return nil, fmt.Errorf("响应后处理失败: %w", err)
	}

	// 如果中间件请求重试
	if needRetry {
		if processedReq.RetryCount < processedReq.MaxRetries {
			s.updateStats("retry_count", 1)

			// 检查是否是风控导致的重试
			retryReason := "中间件请求重试"
			if processedResp != nil && processedResp.Metadata != nil {
				if riskDetected, ok := processedResp.Metadata["tls_risk_detected"].(bool); ok && riskDetected {
					retryReason = "风控检测重试"
					// 将风控标识传递到请求元数据中
					s.transferRiskMetadata(processedResp, processedReq)

					// 标记当前使用的指纹为不健康
					if selectedProfile, ok := processedResp.Metadata["selected_fingerprint"].(string); ok && selectedProfile != "" {
						processedReq.Metadata["mark_unhealthy"] = selectedProfile
						processedReq.Metadata["status_code"] = processedResp.StatusCode
					}
				}
			}

			s.logger.Debug(fmt.Sprintf("%s：%s，重试次数：%d",
				retryReason, processedReq.URL, processedReq.RetryCount), "platform", processedReq.SpiderType)
			return s.retryRequest(ctx, processedReq, fmt.Errorf(retryReason))
		} else {
			s.updateStats("failure_count", 1)
			// 获取最后一次响应的状态码（如果有的话）
			lastStatusCode := "未知"
			if resp != nil {
				lastStatusCode = fmt.Sprintf("%d", resp.StatusCode)
			}
			s.logger.Debug(fmt.Sprintf("HTTP请求重试失败：%s，重试：%d/%d，最后状态码：%s",
				processedReq.URL, processedReq.RetryCount, processedReq.MaxRetries, lastStatusCode))
			return nil, fmt.Errorf("中间件请求重试，但超出最大重试次数")
		}
	}

	// 4. 更新统计
	duration := time.Since(startTime)
	s.updateResponseTime(duration)
	s.updateStats("success_count", 1)

	s.logger.Debug(fmt.Sprintf("请求处理完成：%s，状态码：%d，耗时：%v",
		req.URL, processedResp.StatusCode, duration))

	return processedResp, nil
}

// preprocessRequest 预处理请求 - 应用中间件
func (s *Service) preprocessRequest(ctx context.Context, req *models.Request) (*models.Request, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 应用中间件处理请求
	processedReq, err := s.middlewareManager.ProcessRequest(ctx, req)
	if err != nil {
		s.logger.Error(fmt.Sprintf("中间件处理请求失败：%s，错误：%s", req.URL, err.Error()))
		return nil, err
	}

	return processedReq, nil
}

// executeRequest 执行实际的HTTP请求 - 使用新的 HTTPClient 接口
func (s *Service) executeRequest(ctx context.Context, req *models.Request) (*models.Response, error) {
	s.logger.Debug(fmt.Sprintf("开始执行HTTP请求，URL：%s，代理：%s，方法：%s",
		req.URL, req.Proxy, req.Method))

	// 转换为 foundation.Request
	foundationReq := convertToFoundationRequest(req)
	if foundationReq == nil {
		return nil, fmt.Errorf("转换请求失败")
	}

	// 传递指纹相关元数据
	if foundationReq.Meta == nil {
		foundationReq.Meta = make(map[string]interface{})
	}

	// 从原始请求中提取指纹相关元数据
	if fingerprint := req.GetMetadataString("fingerprint", ""); fingerprint != "" {
		foundationReq.Meta["fingerprint"] = fingerprint
	}

	// 传递风控相关的元数据（用于指纹轮转）
	riskFields := []string{
		"tls_risk_detected",
		"tls_risk_level",
		"tls_risk_status_code",
		"fingerprint_switch_required",
		"selected_fingerprint", // 当前使用的指纹
	}

	for _, field := range riskFields {
		if value := req.GetMetadata(field); value != nil {
			foundationReq.Meta[field] = value
		}
	}

	// 执行请求
	foundationResp, err := s.httpClient.Do(ctx, foundationReq)
	if err != nil {
		s.logger.Debug(fmt.Sprintf("HTTP请求失败，URL：%s，代理：%s，错误：%s",
			req.URL, req.Proxy, err.Error()))
		return nil, err
	}

	// 转换为内部响应格式
	resp := convertFromFoundationResponse(foundationResp, req)
	if resp == nil {
		return nil, fmt.Errorf("转换响应失败")
	}

	s.logger.Debug(fmt.Sprintf("HTTP请求完成，URL：%s，代理：%s，状态码：%d，响应时间：%v，响应大小：%d字节",
		req.URL, req.Proxy, resp.StatusCode, resp.ResponseTime, resp.Size))

	return resp, nil
}

// postprocessResponse 后处理响应 - 应用中间件
func (s *Service) postprocessResponse(ctx context.Context, resp *models.Response) (*models.Response, bool, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 应用中间件处理响应
	needRetry, processedResp, err := s.middlewareManager.ProcessResponse(ctx, resp)
	if err != nil {
		s.logger.Error(fmt.Sprintf("中间件处理响应失败：%s，错误：%s", resp.URL, err.Error()),
			"platform", resp.SpiderType)
		return resp, false, err
	}

	return processedResp, needRetry, nil
}

// shouldRetry 判断是否应该重试
func (s *Service) shouldRetry(ctx context.Context, err error, req *models.Request) bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		s.logger.Debug(fmt.Sprintf("上下文已取消，不进行重试：%s", req.URL), "platform", req.SpiderType)
		return false
	default:
	}

	// 严格检查重试次数
	if req.RetryCount >= req.MaxRetries {
		s.logger.Debug(fmt.Sprintf("已达到最大重试次数，不进行重试：%s，当前：%d，最大：%d",
			req.URL, req.RetryCount, req.MaxRetries), "platform", req.SpiderType)
		return false
	}

	// 额外的安全检查
	if req.RetryCount >= 10 {
		s.logger.Error(fmt.Sprintf("重试次数异常，强制停止：%s，重试次数：%d",
			req.URL, req.RetryCount), "platform", req.SpiderType)
		return false
	}

	// 应用中间件处理异常
	needRetry, _ := s.middlewareManager.ProcessException(ctx, err, req)

	result := needRetry && req.RetryCount < req.MaxRetries
	s.logger.Debug(fmt.Sprintf("重试判断结果：%t，URL：%s，重试次数：%d/%d，中间件建议：%t",
		result, req.URL, req.RetryCount, req.MaxRetries, needRetry), "platform", req.SpiderType)

	return result
}

// retryRequest 重试请求
func (s *Service) retryRequest(ctx context.Context, req *models.Request, originalErr error) (*models.Response, error) {
	// 增加重试计数
	req.RetryCount++

	// 详细的重试日志
	s.logger.Debug(fmt.Sprintf("开始重试请求：%s，重试次数：%d/%d，原始错误：%s，当前代理：%s",
		req.URL, req.RetryCount, req.MaxRetries, originalErr.Error(), req.Proxy),
		"platform", req.SpiderType)

	// 严格检查是否超出最大重试次数 - 防止无限递归
	if req.RetryCount > req.MaxRetries {
		s.logger.Error(fmt.Sprintf("HTTP请求最终失败：%s，重试：%d/%d，最终错误：%s",
			req.URL, req.RetryCount-1, req.MaxRetries, originalErr.Error()),
			"platform", req.SpiderType)
		return nil, fmt.Errorf("请求失败，已达到最大重试次数 %d: %w", req.MaxRetries, originalErr)
	}

	// 额外的安全检查：防止异常情况下的无限递归
	if req.RetryCount >= 10 { // 硬编码的最大重试限制
		s.logger.Error(fmt.Sprintf("HTTP请求重试次数异常，强制停止：%s，重试次数：%d",
			req.URL, req.RetryCount), "platform", req.SpiderType)
		return nil, fmt.Errorf("重试次数异常，强制停止重试: %w", originalErr)
	}

	// 重试时需要清理可能被中间件修改的状态，让中间件重新处理
	// 保存原始URL和参数，以便中间件重新处理
	originalURL := req.URL
	originalParams := make(map[string]string)
	maps.Copy(originalParams, req.Params)

	// 清除可能被签名中间件添加的参数，让其重新生成
	// delete(req.Params, "s")    // POPMART签名参数
	// delete(req.Params, "t")    // POPMART时间戳参数
	// delete(req.Params, "sign") // AliExpress签名参数

	s.logger.Debug(fmt.Sprintf("重试前清理状态，原始URL：%s，清理后参数数量：%d",
		originalURL, len(req.Params)), "platform", req.SpiderType)

	// 等待重试间隔（指数退避）
	retryDelay := time.Duration(req.RetryCount) * time.Second
	s.logger.Debug(fmt.Sprintf("重试延迟：%v", retryDelay), "platform", req.SpiderType)

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-time.After(retryDelay):
		// 继续重试
	}

	// 在重试前再次检查上下文状态
	select {
	case <-ctx.Done():
		s.logger.Debug(fmt.Sprintf("重试前检测到上下文已取消：%s", req.URL), "platform", req.SpiderType)
		return nil, ctx.Err()
	default:
		// 继续重试
	}

	// 递归调用发送请求 - 这会重新执行完整的中间件链
	// 包括：代理选择、签名生成、头部设置等
	s.logger.Debug(fmt.Sprintf("开始重新执行中间件链进行重试：%s，重试次数：%d", req.URL, req.RetryCount), "platform", req.SpiderType)
	return s.SendRequest(ctx, req)
}

// loadDefaultMiddlewares 加载默认中间件配置
func (s *Service) loadDefaultMiddlewares() error {
	// 创建配置管理器
	configManager := config.NewConfigManager()
	if err := configManager.Initialize("configs"); err != nil {
		return fmt.Errorf("初始化配置管理器失败: %w", err)
	}

	// 获取中间件配置
	middlewareConfigs := configManager.GetProvider().GetMiddlewareConfigs()
	if len(middlewareConfigs) == 0 {
		s.logger.Info("未找到中间件配置，跳过加载")
		return nil
	}

	// 加载中间件
	return s.LoadMiddlewares(middlewareConfigs)
}

// SendRequestAsync 异步发送请求
func (s *Service) SendRequestAsync(ctx context.Context, req *models.Request, callback func(*models.Response, error)) {
	go func() {
		resp, err := s.SendRequest(ctx, req)
		if callback != nil {
			callback(resp, err)
		}
	}()
}

// BatchSendRequests 批量发送请求
func (s *Service) BatchSendRequests(ctx context.Context, requests []*models.Request) ([]*models.Response, error) {
	results := make([]*models.Response, len(requests))
	errors := make([]error, len(requests))

	var wg sync.WaitGroup
	for i, req := range requests {
		wg.Add(1)
		go func(index int, request *models.Request) {
			defer wg.Done()
			resp, err := s.SendRequest(ctx, request)
			results[index] = resp
			errors[index] = err
		}(i, req)
	}

	wg.Wait()

	// 检查是否有错误
	var firstError error
	for _, err := range errors {
		if err != nil && firstError == nil {
			firstError = err
		}
	}

	return results, firstError
}

// updateStats 更新统计信息
func (s *Service) updateStats(field string, value int64) {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()

	switch field {
	case "total_requests":
		s.stats.TotalRequests += value
		s.stats.LastRequestTime = time.Now()
	case "success_count":
		s.stats.SuccessCount += value
	case "failure_count":
		s.stats.FailureCount += value
	case "retry_count":
		s.stats.RetryCount += value
	case "active_requests":
		s.stats.ActiveRequests += value
	}
}

// updateResponseTime 更新响应时间统计
func (s *Service) updateResponseTime(duration time.Duration) {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()

	if s.stats.TotalRequests > 0 {
		s.stats.AverageResponse = time.Duration(
			(int64(s.stats.AverageResponse)*s.stats.SuccessCount + int64(duration)) / (s.stats.SuccessCount + 1),
		)
	} else {
		s.stats.AverageResponse = duration
	}
}

// GetStats 获取统计信息
func (s *Service) GetStats() map[string]interface{} {
	s.stats.mu.RLock()
	defer s.stats.mu.RUnlock()

	return map[string]interface{}{
		"total_requests":   s.stats.TotalRequests,
		"success_count":    s.stats.SuccessCount,
		"failure_count":    s.stats.FailureCount,
		"retry_count":      s.stats.RetryCount,
		"active_requests":  s.stats.ActiveRequests,
		"average_response": s.stats.AverageResponse,
		"last_request":     s.stats.LastRequestTime,
		"success_rate":     s.getSuccessRate(),
	}
}

// getSuccessRate 计算成功率
func (s *Service) getSuccessRate() float64 {
	if s.stats.TotalRequests == 0 {
		return 0
	}
	return float64(s.stats.SuccessCount) / float64(s.stats.TotalRequests) * 100
}

// Close 关闭服务
func (s *Service) Close() error {
	s.logger.Info("开始关闭请求服务")

	s.mu.Lock()
	defer s.mu.Unlock()

	// 关闭 HTTP 客户端
	if s.httpClient != nil {
		if err := s.httpClient.Close(); err != nil {
			s.logger.Error(fmt.Sprintf("关闭HTTP客户端失败：%s", err.Error()))
		}
		s.httpClient = nil
	}

	// 清理中间件管理器
	if s.middlewareManager != nil {
		// 中间件管理器可能有自己的清理逻辑
		s.middlewareManager = nil
	}

	// 重置统计信息
	s.stats = RequestStats{}

	// 清理基础配置
	s.baseConfig = nil

	s.logger.Info("请求服务已关闭")
	return nil
}

// SetBaseConfig 设置基础配置（用于配置更新）
func (s *Service) SetBaseConfig(config *foundation.Config) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.baseConfig = config

	// 重新创建 HTTP 客户端 - 使用高级客户端以支持指纹功能
	s.httpClient.Close() // 关闭旧客户端
	s.httpClient = httpclient.NewAdvancedClient(config)

	s.logger.Info(fmt.Sprintf("更新基础HTTP配置，超时：%v，重试次数：%d，用户代理：%s，最大空闲连接：%d",
		config.Timeout, config.MaxRetries, config.UserAgent, config.MaxIdleConns))
}

// GetBaseConfig 获取基础配置
func (s *Service) GetBaseConfig() *foundation.Config {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.baseConfig
}

// TLS指纹相关功能由中间件处理

// ========== 类型转换函数 ==========

// convertToFoundationRequest 将 models.Request 转换为 foundation.Request
func convertToFoundationRequest(req *models.Request) *foundation.Request {
	if req == nil {
		return nil
	}

	foundationReq := &foundation.Request{
		URL:     req.URL,
		Method:  req.Method,
		Headers: make(map[string]string),
		Body:    req.Body,
		Params:  make(map[string]string),
		Cookies: make(map[string]string),
		Meta:    make(map[string]interface{}),
	}

	// 复制 Headers
	if req.Headers != nil {
		for k, v := range req.Headers {
			foundationReq.Headers[k] = v
		}
	}

	// 复制 Params
	if req.Params != nil {
		for k, v := range req.Params {
			foundationReq.Params[k] = v
		}
	}

	// 复制 Cookies
	if req.Cookies != nil {
		for k, v := range req.Cookies {
			foundationReq.Cookies[k] = v
		}
	}

	// 复制 Metadata
	if req.Metadata != nil {
		for k, v := range req.Metadata {
			foundationReq.Meta[k] = v
		}
	}

	// 设置代理和超时
	foundationReq.Proxy = req.Proxy
	foundationReq.Timeout = req.Timeout

	return foundationReq
}

// convertFromFoundationResponse 将 foundation.Response 转换为 models.Response
func convertFromFoundationResponse(foundationResp *foundation.Response, originalReq *models.Request) *models.Response {
	if foundationResp == nil {
		return nil
	}

	resp := &models.Response{
		Request:      originalReq,
		StatusCode:   foundationResp.StatusCode,
		Headers:      foundationResp.Headers,
		Body:         foundationResp.Body,
		URL:          foundationResp.URL,
		ResponseTime: foundationResp.ResponseTime,
		Timestamp:    time.Now(),
		Size:         int64(len(foundationResp.Body)),
		SpiderType:   originalReq.SpiderType,
		Metadata:     make(map[string]interface{}),
	}

	// 复制原始请求的元数据
	if originalReq.Metadata != nil {
		for k, v := range originalReq.Metadata {
			resp.Metadata[k] = v
		}
	}

	// 如果 foundation response 有更新的元数据，同步回来
	if foundationResp.Request != nil && foundationResp.Request.Meta != nil {
		for k, v := range foundationResp.Request.Meta {
			resp.Metadata[k] = v
		}
	}

	return resp
}

// transferRiskMetadata 将风控元数据从响应传递到请求中
func (s *Service) transferRiskMetadata(resp *models.Response, req *models.Request) {
	if resp == nil || resp.Metadata == nil || req == nil {
		return
	}

	// 确保请求元数据存在
	if req.Metadata == nil {
		req.Metadata = make(map[string]interface{})
	}

	// 传递风控相关的元数据
	riskFields := []string{
		"tls_risk_detected",
		"tls_risk_level",
		"tls_risk_status_code",
		"fingerprint_switch_required",
	}

	for _, field := range riskFields {
		if value, exists := resp.Metadata[field]; exists {
			req.Metadata[field] = value
		}
	}

	s.logger.Debug("已传递风控元数据到重试请求",
		"url", req.URL,
		"risk_detected", req.Metadata["tls_risk_detected"],
		"risk_level", req.Metadata["tls_risk_level"],
		"switch_required", req.Metadata["fingerprint_switch_required"])
}
