package initializers

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"go-monitor/internal/services/redis"
	"go-monitor/internal/services/resource"
	"go-monitor/pkg/logging"
)

// RemoteCookiesInitializer 远程 cookies 初始化器
// 从远程端点获取 cookies 信息并缓存到 Redis
type RemoteCookiesInitializer struct {
	// 基础信息
	name         string
	priority     int
	dependencies []string
	ttl          time.Duration

	// 配置
	endpoints   map[string]string // platform -> endpoint
	authToken   string
	cachePrefix string
	httpClient  *http.Client

	// Redis 管理器（用于缓存）
	redisManager *redis.RedisManager

	// 状态管理
	initialized   bool
	healthy       bool
	lastRefresh   time.Time
	initializedAt time.Time
	mu            sync.RWMutex

	// 缓存的 cookies 数据
	cookiesData map[string]map[string]string // platform -> cookie_name -> cookie_value
	dataMu      sync.RWMutex

	// 统计信息
	stats struct {
		RefreshCount    int64     `json:"refresh_count"`
		ErrorCount      int64     `json:"error_count"`
		LastError       string    `json:"last_error"`
		CacheHitCount   int64     `json:"cache_hit_count"`
		CacheMissCount  int64     `json:"cache_miss_count"`
		LastSuccessTime time.Time `json:"last_success_time"`
		PlatformCount   int       `json:"platform_count"`
	}
	statsMu sync.RWMutex

	// 日志
	logger logging.Logger
}

// NewRemoteCookiesInitializer 创建新的远程 cookies 初始化器
func NewRemoteCookiesInitializer(redisManager *redis.RedisManager) *RemoteCookiesInitializer {
	return &RemoteCookiesInitializer{
		name:         "remote_cookies",
		priority:     20,
		dependencies: []string{},       // 不再依赖 Redis 初始化器
		ttl:          60 * time.Minute, // 默认 60 分钟过期
		redisManager: redisManager,     // 直接注入 Redis 管理器
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		endpoints:   make(map[string]string),
		cookiesData: make(map[string]map[string]string),
		logger:      logging.GetLogger("service.remote-cookies-initializer"),
	}
}

// Name 返回资源名称
func (r *RemoteCookiesInitializer) Name() string {
	return r.name
}

// Priority 返回初始化优先级
func (r *RemoteCookiesInitializer) Priority() int {
	return r.priority
}

// Dependencies 返回依赖的资源列表
func (r *RemoteCookiesInitializer) Dependencies() []string {
	return r.dependencies
}

// Initialize 初始化远程 cookies 资源
func (r *RemoteCookiesInitializer) Initialize(ctx context.Context, config map[string]interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.initialized {
		return fmt.Errorf("remote cookies initializer already initialized")
	}

	r.logger.Info("初始化远程 cookies 资源", nil)

	// 解析配置
	if err := r.parseConfig(config); err != nil {
		r.updateStats("error", 1, fmt.Sprintf("config parse error: %v", err))
		return fmt.Errorf("failed to parse config: %w", err)
	}

	// Redis 管理器已通过构造函数注入
	if r.redisManager == nil {
		r.logger.Warn("Redis 管理器不可用，将不使用缓存", nil)
	}

	// 首次加载 cookies
	if err := r.loadCookies(ctx); err != nil {
		r.updateStats("error", 1, fmt.Sprintf("initial load error: %v", err))
		return fmt.Errorf("failed to load initial cookies: %w", err)
	}

	r.initialized = true
	r.healthy = true
	r.initializedAt = time.Now()
	r.lastRefresh = time.Now()

	r.logger.Info(fmt.Sprintf("远程 cookies 资源初始化完成 平台数：%d，缓存前缀：%s，cookies数量：%d",
		len(r.endpoints), r.cachePrefix, r.getTotalCookiesCount()))

	return nil
}

// Refresh 刷新远程 cookies 资源
func (r *RemoteCookiesInitializer) Refresh(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.initialized {
		return fmt.Errorf("remote cookies initializer not initialized")
	}

	r.logger.Debug("刷新远程 cookies 资源", nil)

	if err := r.loadCookies(ctx); err != nil {
		r.healthy = false
		r.updateStats("error", 1, fmt.Sprintf("refresh error: %v", err))
		return fmt.Errorf("failed to refresh cookies: %w", err)
	}

	r.healthy = true
	r.lastRefresh = time.Now()
	r.updateStats("refresh", 1, "")

	r.logger.Debug(fmt.Sprintf("远程 cookies 资源刷新完成 cookies数量：%d", r.getTotalCookiesCount()))

	return nil
}

// Close 关闭远程 cookies 资源
func (r *RemoteCookiesInitializer) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.initialized {
		return nil
	}

	r.logger.Info("关闭远程 cookies 资源", nil)

	// 清理 cookies 数据
	r.dataMu.Lock()
	r.cookiesData = make(map[string]map[string]string)
	r.dataMu.Unlock()

	r.initialized = false
	r.healthy = false

	r.logger.Info("远程 cookies 资源已关闭", nil)
	return nil
}

// TTL 返回资源生存时间
func (r *RemoteCookiesInitializer) TTL() time.Duration {
	return r.ttl
}

// IsExpired 检查是否过期
func (r *RemoteCookiesInitializer) IsExpired() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if r.ttl == 0 {
		return false
	}

	return time.Since(r.lastRefresh) > r.ttl
}

// LastRefreshTime 返回最后刷新时间
func (r *RemoteCookiesInitializer) LastRefreshTime() time.Time {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.lastRefresh
}

// IsHealthy 检查资源是否健康
func (r *RemoteCookiesInitializer) IsHealthy() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.initialized && r.healthy
}

// GetStatus 获取详细状态信息
func (r *RemoteCookiesInitializer) GetStatus() resource.ResourceStatus {
	r.mu.RLock()
	defer r.mu.RUnlock()

	r.statsMu.RLock()
	defer r.statsMu.RUnlock()

	nextRefresh := r.lastRefresh.Add(r.ttl)
	if r.ttl == 0 {
		nextRefresh = time.Time{}
	}

	platforms := make([]string, 0, len(r.endpoints))
	for platform := range r.endpoints {
		platforms = append(platforms, platform)
	}

	status := resource.ResourceStatus{
		Name:          r.name,
		Healthy:       r.healthy,
		LastRefresh:   r.lastRefresh,
		NextRefresh:   nextRefresh,
		RefreshCount:  r.stats.RefreshCount,
		ErrorCount:    r.stats.ErrorCount,
		LastError:     r.stats.LastError,
		Dependencies:  r.dependencies,
		TTL:           r.ttl,
		IsExpired:     r.IsExpired(),
		InitializedAt: r.initializedAt,
		Metadata:      make(map[string]string),
	}

	// 添加远程 cookies 特定的元数据
	status.Metadata["platforms"] = strings.Join(platforms, ",")
	status.Metadata["cache_prefix"] = r.cachePrefix
	status.Metadata["cookies_count"] = fmt.Sprintf("%d", r.getTotalCookiesCount())
	status.Metadata["cache_hit_count"] = fmt.Sprintf("%d", r.stats.CacheHitCount)
	status.Metadata["cache_miss_count"] = fmt.Sprintf("%d", r.stats.CacheMissCount)
	status.Metadata["platform_count"] = fmt.Sprintf("%d", len(r.endpoints))

	return status
}

// GetCookies 获取指定平台的 cookies
func (r *RemoteCookiesInitializer) GetCookies(platform string) (map[string]string, bool) {
	r.dataMu.RLock()
	defer r.dataMu.RUnlock()

	cookies, exists := r.cookiesData[platform]
	if !exists {
		return nil, false
	}

	// 返回副本以避免并发修改
	result := make(map[string]string)
	for k, v := range cookies {
		result[k] = v
	}

	return result, true
}

// GetAllCookies 获取所有平台的 cookies
func (r *RemoteCookiesInitializer) GetAllCookies() map[string]map[string]string {
	r.dataMu.RLock()
	defer r.dataMu.RUnlock()

	result := make(map[string]map[string]string)
	for platform, cookies := range r.cookiesData {
		result[platform] = make(map[string]string)
		for k, v := range cookies {
			result[platform][k] = v
		}
	}

	return result
}

// GetCookieString 获取指定平台的 cookies 字符串格式
func (r *RemoteCookiesInitializer) GetCookieString(platform string) (string, bool) {
	cookies, exists := r.GetCookies(platform)
	if !exists {
		return "", false
	}

	var parts []string
	for name, value := range cookies {
		parts = append(parts, fmt.Sprintf("%s=%s", name, value))
	}

	return strings.Join(parts, "; "), true
}

// parseConfig 解析配置
func (r *RemoteCookiesInitializer) parseConfig(config map[string]interface{}) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// 解析端点映射
	if endpoints, ok := config["endpoints"].(map[string]interface{}); ok {
		r.endpoints = make(map[string]string)
		for platform, endpoint := range endpoints {
			if endpointStr, ok := endpoint.(string); ok {
				r.endpoints[platform] = r.expandEnvVars(endpointStr)
			}
		}
	}

	if len(r.endpoints) == 0 {
		return fmt.Errorf("no endpoints configured")
	}

	// 解析认证令牌
	if authToken, ok := config["auth_token"].(string); ok {
		r.authToken = r.expandEnvVars(authToken)
	}

	// 解析缓存前缀
	if cachePrefix, ok := config["cache_prefix"].(string); ok {
		r.cachePrefix = cachePrefix
	} else {
		r.cachePrefix = "cookies:"
	}

	// 解析 TTL
	if ttlStr, ok := config["ttl"].(string); ok {
		if duration, err := time.ParseDuration(ttlStr); err == nil {
			r.ttl = duration
		}
	}

	// 解析 HTTP 超时
	if timeoutStr, ok := config["timeout"].(string); ok {
		if duration, err := time.ParseDuration(timeoutStr); err == nil {
			r.httpClient.Timeout = duration
		}
	}

	return nil
}

// loadCookies 加载所有平台的 cookies
func (r *RemoteCookiesInitializer) loadCookies(ctx context.Context) error {
	newCookiesData := make(map[string]map[string]string)

	for platform, endpoint := range r.endpoints {
		// 首先尝试从缓存加载
		cacheKey := r.cachePrefix + platform
		if r.redisManager != nil {
			if cachedCookies, err := r.loadFromCache(ctx, cacheKey); err == nil {
				newCookiesData[platform] = cachedCookies
				r.updateStats("cache_hit", 1, "")
				r.logger.Debug(fmt.Sprintf("从缓存加载 cookies 成功 平台：%s，cookies数量：%d",
					platform, len(cachedCookies)))
				continue
			}
			r.updateStats("cache_miss", 1, "")
		}

		// 从远程端点加载
		cookies, err := r.loadFromRemote(ctx, platform, endpoint)
		if err != nil {
			r.logger.Error(fmt.Sprintf("从远程端点加载 cookies 失败 平台：%s，端点：%s，错误：%s",
				platform, endpoint, err.Error()))
			continue
		}

		newCookiesData[platform] = cookies

		// 保存到缓存
		if r.redisManager != nil {
			if err := r.saveToCache(ctx, cacheKey, cookies); err != nil {
				r.logger.Warn(fmt.Sprintf("保存 cookies 到缓存失败 平台：%s，错误：%s",
					platform, err.Error()))
			}
		}
	}

	// 更新本地数据
	r.dataMu.Lock()
	r.cookiesData = newCookiesData
	r.dataMu.Unlock()

	r.updateStats("success", 1, "")
	return nil
}

// loadFromRemote 从远程端点加载指定平台的 cookies
func (r *RemoteCookiesInitializer) loadFromRemote(ctx context.Context, platform, endpoint string) (map[string]string, error) {
	r.logger.Debug(fmt.Sprintf("从远程端点加载 cookies 平台：%s，端点：%s", platform, endpoint))

	req, err := http.NewRequestWithContext(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 添加认证头
	if r.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+r.authToken)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "go-monitor/1.0")

	resp, err := r.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var cookiesData map[string]string
	if err := json.Unmarshal(body, &cookiesData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cookies data: %w", err)
	}

	r.logger.Debug(fmt.Sprintf("从远程端点加载 cookies 成功 平台：%s，cookies数量：%d",
		platform, len(cookiesData)))

	return cookiesData, nil
}

// loadFromCache 从缓存加载 cookies
func (r *RemoteCookiesInitializer) loadFromCache(ctx context.Context, cacheKey string) (map[string]string, error) {
	data, err := r.redisManager.Get(ctx, cacheKey)
	if err != nil {
		return nil, fmt.Errorf("failed to get from cache: %w", err)
	}

	var cookiesData map[string]string
	if err := json.Unmarshal(data, &cookiesData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cached data: %w", err)
	}

	return cookiesData, nil
}

// saveToCache 保存 cookies 到缓存
func (r *RemoteCookiesInitializer) saveToCache(ctx context.Context, cacheKey string, cookies map[string]string) error {
	data, err := json.Marshal(cookies)
	if err != nil {
		return fmt.Errorf("failed to marshal cookies data: %w", err)
	}

	if err := r.redisManager.Set(ctx, cacheKey, data, r.ttl); err != nil {
		return fmt.Errorf("failed to save to cache: %w", err)
	}

	return nil
}

// getTotalCookiesCount 获取总 cookies 数量
func (r *RemoteCookiesInitializer) getTotalCookiesCount() int {
	r.dataMu.RLock()
	defer r.dataMu.RUnlock()

	total := 0
	for _, cookies := range r.cookiesData {
		total += len(cookies)
	}
	return total
}

// expandEnvVars 展开环境变量
func (r *RemoteCookiesInitializer) expandEnvVars(s string) string {
	return os.ExpandEnv(s)
}

// updateStats 更新统计信息
func (r *RemoteCookiesInitializer) updateStats(field string, value int64, errorMsg string) {
	r.statsMu.Lock()
	defer r.statsMu.Unlock()

	switch field {
	case "refresh":
		r.stats.RefreshCount += value
	case "error":
		r.stats.ErrorCount += value
		if errorMsg != "" {
			r.stats.LastError = errorMsg
		}
	case "cache_hit":
		r.stats.CacheHitCount += value
	case "cache_miss":
		r.stats.CacheMissCount += value
	case "success":
		r.stats.LastSuccessTime = time.Now()
		r.stats.LastError = "" // 清除错误信息
	}
}
