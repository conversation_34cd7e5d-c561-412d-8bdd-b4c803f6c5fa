package initializers

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/internal/services/resource"
	"go-monitor/pkg/logging"

	"github.com/tidwall/gjson"
)

// ============================================================================
// 共享结构体和接口定义
// ============================================================================

// AmazonRegionConfig 统一的Amazon地区配置结构
type AmazonRegionConfig struct {
	URL     string   `yaml:"url"`
	Country string   `yaml:"country,omitempty"`
	Cookies []string `yaml:"cookies"`
	Proxies []string `yaml:"proxies"`
	// 可选字段，由具体初始化器使用
	ZipCode       string `yaml:"zipCode,omitempty"`
	IgnoreCountry bool   `yaml:"ignoreCountry,omitempty"`
}

// RegionProcessor 地区处理器接口，由具体初始化器实现
type RegionProcessor interface {
	// ProcessRegion 处理单个地区的初始化逻辑
	ProcessRegion(ctx context.Context, regionConfig AmazonRegionConfig) error

	// GetProcessorName 获取处理器名称，用于日志记录
	GetProcessorName() string
}

// AmazonInitializerBase Amazon初始化器基础结构体
type AmazonInitializerBase struct {
	name           string
	priority       int
	dependencies   []string
	ttl            time.Duration
	requestService *request.Service
	logger         logging.Logger

	// 地区配置
	regions map[string]AmazonRegionConfig

	// 保存的配置，用于Refresh时使用
	savedConfig map[string]interface{}

	// 并发控制
	mu          sync.RWMutex
	initialized bool
	healthy     bool
	lastRefresh time.Time

	// 地区处理器
	processor RegionProcessor
}

// ============================================================================
// 构造函数和基础方法
// ============================================================================

// NewAmazonInitializerBase 创建Amazon初始化器基础结构
func NewAmazonInitializerBase(name string, priority int, dependencies []string, ttl time.Duration,
	requestService *request.Service, processor RegionProcessor) *AmazonInitializerBase {
	return &AmazonInitializerBase{
		name:           name,
		priority:       priority,
		dependencies:   dependencies,
		ttl:            ttl,
		requestService: requestService,
		logger:         logging.GetLogger(fmt.Sprintf("amazon-%s-initializer", name)),
		regions:        make(map[string]AmazonRegionConfig),
		processor:      processor,
	}
}

// ============================================================================
// ResourceInitializer接口实现
// ============================================================================

func (a *AmazonInitializerBase) Name() string           { return a.name }
func (a *AmazonInitializerBase) Priority() int          { return a.priority }
func (a *AmazonInitializerBase) Dependencies() []string { return a.dependencies }
func (a *AmazonInitializerBase) TTL() time.Duration     { return a.ttl }

func (a *AmazonInitializerBase) IsInitialized() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.initialized
}

func (a *AmazonInitializerBase) IsHealthy() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.healthy && a.initialized && !a.IsExpired()
}

func (a *AmazonInitializerBase) GetStatus() resource.ResourceStatus {
	a.mu.RLock()
	defer a.mu.RUnlock()

	return resource.ResourceStatus{
		Name:        a.name,
		Healthy:     a.healthy,
		LastRefresh: a.lastRefresh,
		Metadata: map[string]string{
			"initialized":   fmt.Sprintf("%t", a.initialized),
			"regions_count": fmt.Sprintf("%d", len(a.regions)),
			"processor":     a.processor.GetProcessorName(),
		},
	}
}

func (a *AmazonInitializerBase) IsExpired() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	if !a.initialized {
		return true
	}
	return time.Since(a.lastRefresh) > a.ttl
}

func (a *AmazonInitializerBase) LastRefreshTime() time.Time {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.lastRefresh
}

func (a *AmazonInitializerBase) Cleanup() error {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.initialized = false
	a.healthy = false
	return nil
}

func (a *AmazonInitializerBase) Close() error {
	return a.Cleanup()
}

// ============================================================================
// 初始化和刷新方法
// ============================================================================

// Initialize 初始化Amazon资源
func (a *AmazonInitializerBase) Initialize(ctx context.Context, config map[string]interface{}) error {
	// 验证必需的配置
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 解析配置
	if err := a.parseConfig(config); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	// 保存配置供Refresh时使用
	a.mu.Lock()
	a.savedConfig = config
	a.mu.Unlock()

	// 初始化所有地区
	if err := a.initializeAllRegions(ctx); err != nil {
		return fmt.Errorf("初始化地区失败: %w", err)
	}

	a.mu.Lock()
	a.initialized = true
	a.healthy = true
	a.lastRefresh = time.Now()
	a.mu.Unlock()

	a.logger.Info(fmt.Sprintf("Amazon %s 资源初始化完成，共配置 %d 个地区，TTL：%s",
		a.processor.GetProcessorName(), len(a.regions), a.ttl.String()))
	return nil
}

// Refresh 刷新Amazon资源
func (a *AmazonInitializerBase) Refresh(ctx context.Context) error {
	// 获取保存的配置
	a.mu.RLock()
	config := a.savedConfig
	a.mu.RUnlock()

	if config == nil {
		return fmt.Errorf("没有保存的配置，无法刷新资源")
	}

	if err := a.initializeAllRegions(ctx); err != nil {
		a.mu.Lock()
		a.healthy = false
		a.mu.Unlock()
		return err
	}

	a.mu.Lock()
	a.healthy = true
	a.lastRefresh = time.Now()
	a.mu.Unlock()

	a.logger.Info(fmt.Sprintf("Amazon %s 资源刷新完成，共刷新 %d 个地区",
		a.processor.GetProcessorName(), len(a.regions)))
	return nil
}

// ============================================================================
// 共享工具函数
// ============================================================================

// parseJSONResponse 解析JSON响应并提取指定字段
func parseJSONResponse(responseBody, jsonPath string) (string, bool) {
	result := gjson.Get(responseBody, jsonPath)
	if !result.Exists() {
		return "", false
	}
	return result.String(), true
}

// validateHTTPResponse 验证HTTP响应状态
func validateHTTPResponse(resp *models.Response, operation string) error {
	if resp.StatusCode != 200 {
		return fmt.Errorf("%s失败，状态码: %d", operation, resp.StatusCode)
	}
	return nil
}

// buildHTTPRequest 构建HTTP请求，使用地区特定的配置
func (a *AmazonInitializerBase) buildHTTPRequest(method, url string, headers map[string]string,
	body []byte, params map[string]string, regionConfig AmazonRegionConfig) *models.Request {

	req := &models.Request{
		URL:     url,
		Method:  method,
		Headers: headers,
		Body:    body,
		Metadata: map[string]interface{}{
			"spider_type": "amazon",
			"proxies":     regionConfig.Proxies,
			"cookies":     regionConfig.Cookies,
		},
	}

	if params != nil {
		req.Params = params
	}

	return req
}

// ============================================================================
// 配置解析方法
// ============================================================================

// parseConfig 解析配置参数
func (a *AmazonInitializerBase) parseConfig(config map[string]interface{}) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	// 解析基础配置
	a.parseBasicConfig(config)

	// 解析地区配置
	if err := a.parseRegionsConfig(config); err != nil {
		return fmt.Errorf("解析地区配置失败: %w", err)
	}

	return nil
}

// parseBasicConfig 解析基础配置
func (a *AmazonInitializerBase) parseBasicConfig(config map[string]interface{}) {
	// 解析TTL配置
	if ttlValue, exists := config["ttl"]; exists {
		switch v := ttlValue.(type) {
		case string:
			if duration, err := time.ParseDuration(v); err == nil {
				a.ttl = duration
			}
		case time.Duration:
			a.ttl = v
		}
	}

	// 解析优先级配置
	if priority, exists := config["priority"]; exists {
		if p, ok := priority.(int); ok {
			a.priority = p
		}
	}
}

// parseRegionsConfig 解析地区配置
func (a *AmazonInitializerBase) parseRegionsConfig(config map[string]interface{}) error {
	regionsConfig, ok := config["regions"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("regions配置格式错误")
	}

	for regionName, regionValue := range regionsConfig {
		regionMap, ok := regionValue.(map[string]interface{})
		if !ok {
			continue
		}

		regionConfig := AmazonRegionConfig{}

		// 解析通用字段
		if url, ok := regionMap["url"].(string); ok {
			regionConfig.URL = url
		}
		if cookies, ok := regionMap["cookies"].([]interface{}); ok {
			regionConfig.Cookies = make([]string, 0, len(cookies))
			for _, cookie := range cookies {
				if cookieStr, ok := cookie.(string); ok {
					regionConfig.Cookies = append(regionConfig.Cookies, cookieStr)
				}
			}
		}
		if proxies, ok := regionMap["proxies"].([]interface{}); ok {
			regionConfig.Proxies = make([]string, 0, len(proxies))
			for _, proxy := range proxies {
				if proxyStr, ok := proxy.(string); ok {
					regionConfig.Proxies = append(regionConfig.Proxies, proxyStr)
				}
			}
		}

		// 解析可选字段（如zipCode）
		if zipCode, ok := regionMap["zipCode"].(string); ok {
			regionConfig.ZipCode = zipCode
		}

		// 解析country字段
		if country, ok := regionMap["country"].(string); ok {
			regionConfig.Country = country
		}

		if ignoreCountry, ok := regionMap["ignoreCountry"].(bool); ok {
			regionConfig.IgnoreCountry = ignoreCountry
		}

		a.regions[regionName] = regionConfig
	}

	return nil
}

// ============================================================================
// 并发处理方法
// ============================================================================

// initializeAllRegions 初始化所有地区（并发处理）
func (a *AmazonInitializerBase) initializeAllRegions(ctx context.Context) error {
	a.mu.RLock()
	regions := make(map[string]AmazonRegionConfig)
	for k, v := range a.regions {
		regions[k] = v
	}
	a.mu.RUnlock()

	// 并发处理所有地区
	var wg sync.WaitGroup
	errChan := make(chan error, len(regions))
	successCount := 0
	var successMu sync.Mutex

	for regionName, regionConfig := range regions {
		wg.Add(1)
		go func(name string, cfg AmazonRegionConfig) {
			defer wg.Done()

			if err := a.processor.ProcessRegion(ctx, cfg); err != nil {
				a.logger.Error(fmt.Sprintf("地区 %s %s处理失败：%s", name, a.processor.GetProcessorName(), err.Error()))
				errChan <- fmt.Errorf("地区 %s: %w", name, err)
			} else {
				successMu.Lock()
				successCount++
				successMu.Unlock()
				a.logger.Info(fmt.Sprintf("地区 %s %s处理成功", name, a.processor.GetProcessorName()))
			}
		}(regionName, regionConfig)
	}

	wg.Wait()
	close(errChan)

	// 收集错误
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	// 记录处理结果
	totalRegions := len(regions)
	failedCount := len(errors)

	a.logger.Info(fmt.Sprintf("地区%s处理完成，总计：%d，成功：%d，失败：%d",
		a.processor.GetProcessorName(), totalRegions, successCount, failedCount))

	// 如果所有地区都失败，返回错误
	if successCount == 0 && len(errors) > 0 {
		return fmt.Errorf("所有地区%s处理失败: %v", a.processor.GetProcessorName(), errors)
	}

	// 如果有部分失败，记录警告但不返回错误
	if len(errors) > 0 {
		a.logger.Warn(fmt.Sprintf("部分地区%s处理失败，详细错误: %v", a.processor.GetProcessorName(), errors))
	}

	return nil
}
