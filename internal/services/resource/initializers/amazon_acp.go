package initializers

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"sync"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/redis"
	"go-monitor/internal/services/request"
	"go-monitor/internal/services/resource"
	"go-monitor/pkg/logging"

	"github.com/tidwall/gjson"
)

// ACPData 包含ACP参数和路径的数据结构
type ACPData struct {
	Params string // ACP参数字符串
	Path   string // ACP路径
}

// ACPRegionConfig ACP地区配置
type ACPRegionConfig struct {
	URL     string   `yaml:"url"`
	Cookies []string `yaml:"cookies"`
	Proxies []string `yaml:"proxies"`
}

// 常量定义
const (
	// 默认配置
	defaultACPInitializerName     = "amazon_acp"
	defaultACPInitializerPriority = 17
	defaultACPTTL                 = 30 * time.Minute
	defaultACPRedisKey            = "x-amz-acp-params"
	defaultACPPathRedisKey        = "x-amz-acp-path"

	// HTTP状态码
	httpStatusOK = 200

	// 配置路径
	configPathACPPatterns = "parsing_config.acp_patterns"
	configPathACPKey      = "parsing_config.acp_redis_key"
	configPathACPPathKey  = "parsing_config.acp_path_redis_key"

	// 默认ACP参数提取模式
	defaultACPParamsPattern = `data-acp-params="([^"]+)"`
	defaultACPPathPattern   = `data-acp-path="([^"]+)"`
	defaultACPFormatPattern = `tok=([^;]+);ts=([^;]+);rid=([^;]+);d1=([^;]+);d2=([^;]+);tpm=([^;]+);ref=([^;"]+)`
)

// getDefaultACPPatterns 获取默认ACP提取模式
func getDefaultACPPatterns() []string {
	return []string{
		defaultACPParamsPattern,
		defaultACPPathPattern,
		defaultACPFormatPattern,
	}
}

// getDefaultHeaders 获取默认HTTP请求头
func getDefaultHeaders() map[string]string {
	return map[string]string{
		"Accept":           "*/*",
		"Accept-Language":  "zh-CN,zh;q=0.9",
		"X-Requested-With": "XMLHttpRequest",
		"Sec-Fetch-Site":   "same-origin",
		"Sec-Fetch-Mode":   "cors",
		"Sec-Fetch-Dest":   "empty",
	}
}

// AmazonACPInitializer Amazon ACP参数初始化器
type AmazonACPInitializer struct {
	name           string
	priority       int
	dependencies   []string
	ttl            time.Duration
	redisManager   *redis.RedisManager
	requestService *request.Service
	logger         logging.Logger

	// 地区配置
	regions map[string]ACPRegionConfig

	// 保存的配置，用于Refresh时使用
	savedConfig map[string]interface{}

	mu          sync.RWMutex
	initialized bool
	healthy     bool
	lastRefresh time.Time
}

// NewAmazonACPInitializer 创建Amazon ACP资源初始化器
func NewAmazonACPInitializer(redisManager *redis.RedisManager, requestService *request.Service) *AmazonACPInitializer {
	return &AmazonACPInitializer{
		name:           defaultACPInitializerName,
		priority:       defaultACPInitializerPriority,
		dependencies:   []string{"amazon_zipcode_cookies"},
		ttl:            defaultACPTTL,
		redisManager:   redisManager,
		requestService: requestService,
		logger:         logging.GetLogger("amazon-acp-initializer"),
		regions:        make(map[string]ACPRegionConfig),
	}
}

// 实现ResourceInitializer接口
func (a *AmazonACPInitializer) Name() string           { return a.name }
func (a *AmazonACPInitializer) Priority() int          { return a.priority }
func (a *AmazonACPInitializer) Dependencies() []string { return a.dependencies }
func (a *AmazonACPInitializer) TTL() time.Duration     { return a.ttl }

func (a *AmazonACPInitializer) IsInitialized() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.initialized
}

func (a *AmazonACPInitializer) IsHealthy() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.healthy && a.initialized && !a.IsExpired()
}

func (a *AmazonACPInitializer) GetStatus() resource.ResourceStatus {
	a.mu.RLock()
	defer a.mu.RUnlock()

	return resource.ResourceStatus{
		Name:        a.name,
		Healthy:     a.healthy,
		LastRefresh: a.lastRefresh,
		Metadata: map[string]string{
			"initialized": "true",
		},
	}
}

// Initialize 初始化Amazon ACP资源
func (a *AmazonACPInitializer) Initialize(ctx context.Context, config map[string]interface{}) error {
	// 验证配置
	if err := a.validateConfig(config); err != nil {
		return err
	}

	// 解析配置
	if err := a.parseConfig(config); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	// 保存配置供Refresh时使用
	a.mu.Lock()
	a.savedConfig = config
	a.mu.Unlock()

	// 获取并存储ACP参数
	if err := a.fetchAndStoreACPParams(ctx, config); err != nil {
		return err
	}

	a.mu.Lock()
	a.initialized = true
	a.healthy = true
	a.lastRefresh = time.Now()
	a.mu.Unlock()

	a.logger.Info("Amazon ACP资源初始化完成")
	return nil
}

// Refresh 刷新Amazon ACP资源
func (a *AmazonACPInitializer) Refresh(ctx context.Context) error {
	// 获取保存的配置
	a.mu.RLock()
	config := a.savedConfig
	a.mu.RUnlock()

	if config == nil {
		return fmt.Errorf("没有保存的配置，无法刷新资源")
	}

	if err := a.fetchAndStoreACPParams(ctx, config); err != nil {
		a.mu.Lock()
		a.healthy = false
		a.mu.Unlock()
		return err
	}

	a.mu.Lock()
	a.healthy = true
	a.lastRefresh = time.Now()
	a.mu.Unlock()

	a.logger.Info("Amazon ACP资源刷新完成")
	return nil
}

// parseConfig 解析配置参数
func (a *AmazonACPInitializer) parseConfig(config map[string]interface{}) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	// 解析TTL配置
	if ttlValue, exists := config["ttl"]; exists {
		switch v := ttlValue.(type) {
		case string:
			if duration, err := time.ParseDuration(v); err == nil {
				a.ttl = duration
			} else {
				a.logger.Warn(fmt.Sprintf("无法解析TTL配置 %s，使用默认值 %s，错误：%s", v, a.ttl.String(), err.Error()))
			}
		case time.Duration:
			a.ttl = v
		default:
			a.logger.Warn(fmt.Sprintf("TTL配置类型 %T 不支持，使用默认值 %s", v, a.ttl.String()))
		}
	}

	// 解析优先级配置
	if priority, exists := config["priority"]; exists {
		if p, ok := priority.(int); ok {
			a.priority = p
		}
	}

	// 解析地区配置
	if err := a.parseRegionsConfig(config); err != nil {
		return fmt.Errorf("解析地区配置失败: %w", err)
	}

	return nil
}

// parseRegionsConfig 解析地区配置
func (a *AmazonACPInitializer) parseRegionsConfig(config map[string]interface{}) error {
	regionsConfig, ok := config["regions"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("regions配置格式错误")
	}

	for regionName, regionValue := range regionsConfig {
		regionMap, ok := regionValue.(map[string]interface{})
		if !ok {
			continue
		}

		regionConfig := ACPRegionConfig{}

		if url, ok := regionMap["url"].(string); ok {
			regionConfig.URL = url
		}
		if cookies, ok := regionMap["cookies"].([]interface{}); ok {
			regionConfig.Cookies = make([]string, 0, len(cookies))
			for _, cookie := range cookies {
				if cookieStr, ok := cookie.(string); ok {
					regionConfig.Cookies = append(regionConfig.Cookies, cookieStr)
				}
			}
		}
		if proxies, ok := regionMap["proxies"].([]interface{}); ok {
			regionConfig.Proxies = make([]string, 0, len(proxies))
			for _, proxy := range proxies {
				if proxyStr, ok := proxy.(string); ok {
					regionConfig.Proxies = append(regionConfig.Proxies, proxyStr)
				}
			}
		}

		a.regions[regionName] = regionConfig
	}

	return nil
}

// validateConfig 验证配置
func (a *AmazonACPInitializer) validateConfig(config map[string]interface{}) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 验证请求配置
	requestConfig, ok := config["request_config"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("request_config 配置缺失")
	}

	fallbackURL, ok := requestConfig["fallback_url"].(string)
	if !ok || fallbackURL == "" {
		return fmt.Errorf("fallback_url 配置不能为空")
	}

	return nil
}

// fetchAndStoreACPParams 获取并存储ACP参数（分组请求）
func (a *AmazonACPInitializer) fetchAndStoreACPParams(ctx context.Context, config map[string]interface{}) error {
	a.mu.RLock()
	regions := make(map[string]ACPRegionConfig)
	for k, v := range a.regions {
		regions[k] = v
	}
	a.mu.RUnlock()

	// 并发处理所有地区
	var wg sync.WaitGroup
	errChan := make(chan error, len(regions))
	successCount := 0
	var successMu sync.Mutex

	for regionName, regionConfig := range regions {
		wg.Add(1)
		go func(name string, cfg ACPRegionConfig) {
			defer wg.Done()

			if err := a.fetchAndStoreRegionACPParams(ctx, name, cfg, config); err != nil {
				a.logger.Error(fmt.Sprintf("地区 %s ACP参数获取失败：%s", name, err.Error()))
				errChan <- fmt.Errorf("地区 %s: %w", name, err)
			} else {
				successMu.Lock()
				successCount++
				successMu.Unlock()
				a.logger.Info(fmt.Sprintf("地区 %s ACP参数获取成功", name))
			}
		}(regionName, regionConfig)
	}

	wg.Wait()
	close(errChan)

	// 收集错误
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	// 如果所有地区都失败，返回错误
	if successCount == 0 && len(errors) > 0 {
		return fmt.Errorf("所有地区ACP参数获取失败: %v", errors)
	}

	// 如果有部分成功，记录警告但不返回错误
	if len(errors) > 0 {
		a.logger.Warn(fmt.Sprintf("部分地区ACP参数获取失败，成功：%d，失败：%d", successCount, len(errors)))
	}

	return nil
}

// fetchAndStoreRegionACPParams 获取并存储单个地区的ACP参数
func (a *AmazonACPInitializer) fetchAndStoreRegionACPParams(ctx context.Context, regionName string, regionConfig ACPRegionConfig, config map[string]interface{}) error {
	// 构建并发送HTTP请求
	resp, err := a.sendRegionAmazonRequest(ctx, regionName, regionConfig, config)
	if err != nil {
		return err
	}

	// 从HTML中提取ACP参数和路径
	acpData := a.extractACPData(string(resp.Body), config)
	if acpData.Params == "" {
		return fmt.Errorf("ACP参数提取失败")
	}

	// 存储到Redis
	if err := a.storeACPData(ctx, acpData, config); err != nil {
		return err
	}

	return nil
}

// sendRegionAmazonRequest 发送地区特定的Amazon请求
func (a *AmazonACPInitializer) sendRegionAmazonRequest(ctx context.Context, _ string, regionConfig ACPRegionConfig, config map[string]interface{}) (*models.Response, error) {
	// 从配置中获取请求配置
	requestConfig, ok := config["request_config"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("request_config 配置缺失")
	}

	// 获取URL
	amazonURL := fmt.Sprintf("%s%s", regionConfig.URL, requestConfig["fallback_url"].(string))
	if amazonURL == "" {
		return nil, fmt.Errorf("fallback_url 配置不能为空")
	}

	// 构建请求
	req := a.buildRegionAmazonRequest(amazonURL, regionConfig, requestConfig)

	// 发送请求
	resp, err := a.requestService.SendRequest(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}

	if resp.StatusCode != httpStatusOK {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	return resp, nil
}

// buildRegionAmazonRequest 构建地区特定的Amazon请求
func (a *AmazonACPInitializer) buildRegionAmazonRequest(url string, regionConfig ACPRegionConfig, requestConfig map[string]interface{}) *models.Request {
	req := &models.Request{
		URL:     url,
		Method:  "GET",
		Headers: a.buildRequestHeaders(requestConfig),
		Metadata: map[string]interface{}{
			"spider_type": "amazon",
			"proxies":     regionConfig.Proxies, // 使用地区特定的代理
			"cookies":     regionConfig.Cookies, // 使用地区特定的cookies
		},
	}

	return req
}

// buildRequestHeaders 构建HTTP请求头
func (a *AmazonACPInitializer) buildRequestHeaders(requestConfig map[string]interface{}) map[string]string {
	headers := make(map[string]string)

	// 从配置中获取headers
	if defaultHeaders, ok := requestConfig["default_headers"].(map[string]interface{}); ok {
		for k, v := range defaultHeaders {
			if strVal, ok := v.(string); ok {
				headers[k] = strVal
			}
		}
	}

	// 如果没有配置headers，使用默认值
	if len(headers) == 0 {
		headers = getDefaultHeaders()
	}

	return headers
}

// extractACPData 从HTML中提取ACP参数和路径
func (a *AmazonACPInitializer) extractACPData(responseBody string, config map[string]interface{}) ACPData {
	// 首先尝试解析JSON响应
	htmlContent := a.extractHTMLFromResponse(responseBody)

	var acpData ACPData

	// 提取ACP参数
	if params := a.extractWithRegex(htmlContent, defaultACPParamsPattern); params != "" {
		acpData.Params = params
		a.logger.Info(fmt.Sprintf("成功提取ACP参数，长度：%d", len(params)))
	}

	// 提取ACP路径
	if path := a.extractWithRegex(htmlContent, defaultACPPathPattern); path != "" {
		acpData.Path = path
		a.logger.Info(fmt.Sprintf("成功提取ACP路径：%s", path))
	}

	// 如果没有提取到参数，尝试使用配置的模式
	if acpData.Params == "" {
		patterns := a.parseACPPatterns(config)
		for _, pattern := range patterns {
			if value := a.extractWithRegex(htmlContent, pattern); value != "" {
				acpData.Params = value
				a.logger.Info(fmt.Sprintf("通过配置模式成功提取ACP参数，长度：%d", len(value)))
				break
			}
		}
	}

	if acpData.Params == "" {
		a.logger.Error("未能从HTML中提取到ACP参数")
	}
	if acpData.Path == "" {
		a.logger.Warn("未能从HTML中提取到ACP路径")
	}

	return acpData
}

// extractHTMLFromResponse 从响应中提取HTML内容
func (a *AmazonACPInitializer) extractHTMLFromResponse(responseBody string) string {
	// 尝试解析为JSON
	var jsonResponse map[string]interface{}
	if err := json.Unmarshal([]byte(responseBody), &jsonResponse); err == nil {
		// 如果是JSON响应，提取html字段
		if htmlValue, exists := jsonResponse["html"]; exists {
			if htmlStr, ok := htmlValue.(string); ok {
				a.logger.Debug("从JSON响应中提取HTML内容")
				return htmlStr
			}
		}
	}

	// 如果不是JSON或没有html字段，直接返回原始内容（向后兼容）
	a.logger.Debug("直接使用响应体作为HTML内容")
	return responseBody
}

// parseACPPatterns 解析ACP提取模式配置
func (a *AmazonACPInitializer) parseACPPatterns(config map[string]interface{}) []string {
	var patterns []string

	// 将config转换为JSON字符串以使用GJSON
	configBytes, _ := json.Marshal(config)
	configJson := string(configBytes)

	// 使用GJSON解析ACP模式
	gjson.Get(configJson, configPathACPPatterns).ForEach(func(_, pattern gjson.Result) bool {
		patterns = append(patterns, pattern.String())
		return true
	})

	// 如果没有配置，使用默认值
	if len(patterns) == 0 {
		patterns = getDefaultACPPatterns()
	}

	return patterns
}

// extractWithRegex 使用正则表达式提取内容
func (a *AmazonACPInitializer) extractWithRegex(content, pattern string) string {
	regex := regexp.MustCompile(pattern)
	matches := regex.FindStringSubmatch(content)
	if len(matches) > 1 {
		return matches[1]
	}
	if len(matches) > 0 {
		return matches[0]
	}
	return ""
}

// storeACPData 存储ACP参数和路径到Redis
func (a *AmazonACPInitializer) storeACPData(ctx context.Context, acpData ACPData, config map[string]interface{}) error {
	if a.redisManager == nil {
		a.logger.Debug("Redis管理器不可用，跳过存储")
		return nil
	}

	// 使用版本化存储（Redis管理器已支持）
	// 存储ACP参数（版本化）
	if acpData.Params != "" {
		if err := a.redisManager.SetResourceVersioned(ctx, "x-amz-acp-params", "default", acpData.Params, a.ttl); err != nil {
			return fmt.Errorf("存储ACP参数到Redis（版本化）失败: %w", err)
		}
		a.logger.Info(fmt.Sprintf("ACP参数已存储到Redis（版本化），键：x-amz-acp-params，TTL：%s", a.ttl.String()))
	}

	// 存储ACP路径（版本化）
	if acpData.Path != "" {
		if err := a.redisManager.SetResourceVersioned(ctx, "x-amz-acp-path", "default", acpData.Path, a.ttl); err != nil {
			return fmt.Errorf("存储ACP路径到Redis（版本化）失败: %w", err)
		}
		a.logger.Info(fmt.Sprintf("ACP路径已存储到Redis（版本化），键：x-amz-acp-path，TTL：%s", a.ttl.String()))
	}

	return nil
}

// 实现剩余的接口方法
func (a *AmazonACPInitializer) Cleanup() error {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.initialized = false
	a.healthy = false
	return nil
}

func (a *AmazonACPInitializer) IsExpired() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	if !a.initialized {
		return true
	}
	return time.Since(a.lastRefresh) > a.ttl
}

func (a *AmazonACPInitializer) LastRefreshTime() time.Time {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.lastRefresh
}

func (a *AmazonACPInitializer) Close() error {
	return a.Cleanup()
}
