package initializers

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"go-monitor/internal/services/redis"
	"go-monitor/internal/services/resource"
	"go-monitor/pkg/logging"
)

// SignatureInfo 签名信息结构
type SignatureInfo struct {
	Algorithm string            `json:"algorithm"`
	Key       string            `json:"key"`
	Salt      string            `json:"salt"`
	Version   string            `json:"version"`
	ExpiresAt time.Time         `json:"expires_at"`
	Metadata  map[string]string `json:"metadata"`
	UpdatedAt time.Time         `json:"updated_at"`
}

// RemoteSignatureInitializer 远程签名信息初始化器
// 从远程端点获取签名信息并缓存到 Redis
type RemoteSignatureInitializer struct {
	// 基础信息
	name         string
	priority     int
	dependencies []string
	ttl          time.Duration

	// 配置
	endpoints   map[string]string // platform -> endpoint
	authToken   string
	cachePrefix string
	httpClient  *http.Client

	// Redis 管理器（用于缓存）
	redisManager *redis.RedisManager

	// 状态管理
	initialized   bool
	healthy       bool
	lastRefresh   time.Time
	initializedAt time.Time
	mu            sync.RWMutex

	// 缓存的签名数据
	signatureData map[string]*SignatureInfo // platform -> signature_info
	dataMu        sync.RWMutex

	// 统计信息
	stats struct {
		RefreshCount    int64     `json:"refresh_count"`
		ErrorCount      int64     `json:"error_count"`
		LastError       string    `json:"last_error"`
		CacheHitCount   int64     `json:"cache_hit_count"`
		CacheMissCount  int64     `json:"cache_miss_count"`
		LastSuccessTime time.Time `json:"last_success_time"`
		PlatformCount   int       `json:"platform_count"`
	}
	statsMu sync.RWMutex

	// 日志
	logger logging.Logger
}

// NewRemoteSignatureInitializer 创建新的远程签名初始化器
func NewRemoteSignatureInitializer(redisManager *redis.RedisManager) *RemoteSignatureInitializer {
	return &RemoteSignatureInitializer{
		name:         "signature_info",
		priority:     30,
		dependencies: []string{"remote_config"}, // 只依赖远程配置
		ttl:          120 * time.Minute,         // 默认 120 分钟过期
		redisManager: redisManager,              // 直接注入 Redis 管理器
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		endpoints:     make(map[string]string),
		signatureData: make(map[string]*SignatureInfo),
		logger:        logging.GetLogger("service.remote-signature-initializer"),
	}
}

// Name 返回资源名称
func (r *RemoteSignatureInitializer) Name() string {
	return r.name
}

// Priority 返回初始化优先级
func (r *RemoteSignatureInitializer) Priority() int {
	return r.priority
}

// Dependencies 返回依赖的资源列表
func (r *RemoteSignatureInitializer) Dependencies() []string {
	return r.dependencies
}

// Initialize 初始化远程签名资源
func (r *RemoteSignatureInitializer) Initialize(ctx context.Context, config map[string]interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.initialized {
		return fmt.Errorf("remote signature initializer already initialized")
	}

	r.logger.Info("初始化远程签名资源", nil)

	// 解析配置
	if err := r.parseConfig(config); err != nil {
		r.updateStats("error", 1, fmt.Sprintf("config parse error: %v", err))
		return fmt.Errorf("failed to parse config: %w", err)
	}

	// Redis 管理器已通过构造函数注入
	if r.redisManager == nil {
		r.logger.Warn("Redis 管理器不可用，将不使用缓存", nil)
	}

	// 首次加载签名信息
	if err := r.loadSignatures(ctx); err != nil {
		r.updateStats("error", 1, fmt.Sprintf("initial load error: %v", err))
		return fmt.Errorf("failed to load initial signatures: %w", err)
	}

	r.initialized = true
	r.healthy = true
	r.initializedAt = time.Now()
	r.lastRefresh = time.Now()

	r.logger.Info(fmt.Sprintf("远程签名资源初始化完成 平台数：%d，缓存前缀：%s，签名数量：%d",
		len(r.endpoints), r.cachePrefix, len(r.signatureData)))

	return nil
}

// Refresh 刷新远程签名资源
func (r *RemoteSignatureInitializer) Refresh(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.initialized {
		return fmt.Errorf("remote signature initializer not initialized")
	}

	r.logger.Debug("刷新远程签名资源", nil)

	if err := r.loadSignatures(ctx); err != nil {
		r.healthy = false
		r.updateStats("error", 1, fmt.Sprintf("refresh error: %v", err))
		return fmt.Errorf("failed to refresh signatures: %w", err)
	}

	r.healthy = true
	r.lastRefresh = time.Now()
	r.updateStats("refresh", 1, "")

	r.logger.Debug(fmt.Sprintf("远程签名资源刷新完成 签名数量：%d", len(r.signatureData)))

	return nil
}

// Close 关闭远程签名资源
func (r *RemoteSignatureInitializer) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.initialized {
		return nil
	}

	r.logger.Info("关闭远程签名资源", nil)

	// 清理签名数据
	r.dataMu.Lock()
	r.signatureData = make(map[string]*SignatureInfo)
	r.dataMu.Unlock()

	r.initialized = false
	r.healthy = false

	r.logger.Info("远程签名资源已关闭", nil)
	return nil
}

// TTL 返回资源生存时间
func (r *RemoteSignatureInitializer) TTL() time.Duration {
	return r.ttl
}

// IsExpired 检查是否过期
func (r *RemoteSignatureInitializer) IsExpired() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if r.ttl == 0 {
		return false
	}

	return time.Since(r.lastRefresh) > r.ttl
}

// LastRefreshTime 返回最后刷新时间
func (r *RemoteSignatureInitializer) LastRefreshTime() time.Time {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.lastRefresh
}

// IsHealthy 检查资源是否健康
func (r *RemoteSignatureInitializer) IsHealthy() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.initialized && r.healthy
}

// GetStatus 获取详细状态信息
func (r *RemoteSignatureInitializer) GetStatus() resource.ResourceStatus {
	r.mu.RLock()
	defer r.mu.RUnlock()

	r.statsMu.RLock()
	defer r.statsMu.RUnlock()

	nextRefresh := r.lastRefresh.Add(r.ttl)
	if r.ttl == 0 {
		nextRefresh = time.Time{}
	}

	platforms := make([]string, 0, len(r.endpoints))
	for platform := range r.endpoints {
		platforms = append(platforms, platform)
	}

	status := resource.ResourceStatus{
		Name:          r.name,
		Healthy:       r.healthy,
		LastRefresh:   r.lastRefresh,
		NextRefresh:   nextRefresh,
		RefreshCount:  r.stats.RefreshCount,
		ErrorCount:    r.stats.ErrorCount,
		LastError:     r.stats.LastError,
		Dependencies:  r.dependencies,
		TTL:           r.ttl,
		IsExpired:     r.IsExpired(),
		InitializedAt: r.initializedAt,
		Metadata:      make(map[string]string),
	}

	// 添加远程签名特定的元数据
	status.Metadata["platforms"] = strings.Join(platforms, ",")
	status.Metadata["cache_prefix"] = r.cachePrefix
	status.Metadata["signatures_count"] = fmt.Sprintf("%d", len(r.signatureData))
	status.Metadata["cache_hit_count"] = fmt.Sprintf("%d", r.stats.CacheHitCount)
	status.Metadata["cache_miss_count"] = fmt.Sprintf("%d", r.stats.CacheMissCount)
	status.Metadata["platform_count"] = fmt.Sprintf("%d", len(r.endpoints))

	return status
}

// GetSignature 获取指定平台的签名信息
func (r *RemoteSignatureInitializer) GetSignature(platform string) (*SignatureInfo, bool) {
	r.dataMu.RLock()
	defer r.dataMu.RUnlock()

	signature, exists := r.signatureData[platform]
	if !exists {
		return nil, false
	}

	// 检查签名是否过期
	if !signature.ExpiresAt.IsZero() && time.Now().After(signature.ExpiresAt) {
		return nil, false
	}

	// 返回副本以避免并发修改
	result := &SignatureInfo{
		Algorithm: signature.Algorithm,
		Key:       signature.Key,
		Salt:      signature.Salt,
		Version:   signature.Version,
		ExpiresAt: signature.ExpiresAt,
		UpdatedAt: signature.UpdatedAt,
		Metadata:  make(map[string]string),
	}

	for k, v := range signature.Metadata {
		result.Metadata[k] = v
	}

	return result, true
}

// GetAllSignatures 获取所有平台的签名信息
func (r *RemoteSignatureInitializer) GetAllSignatures() map[string]*SignatureInfo {
	r.dataMu.RLock()
	defer r.dataMu.RUnlock()

	result := make(map[string]*SignatureInfo)
	for platform, signature := range r.signatureData {
		// 检查签名是否过期
		if !signature.ExpiresAt.IsZero() && time.Now().After(signature.ExpiresAt) {
			continue
		}

		result[platform] = &SignatureInfo{
			Algorithm: signature.Algorithm,
			Key:       signature.Key,
			Salt:      signature.Salt,
			Version:   signature.Version,
			ExpiresAt: signature.ExpiresAt,
			UpdatedAt: signature.UpdatedAt,
			Metadata:  make(map[string]string),
		}

		for k, v := range signature.Metadata {
			result[platform].Metadata[k] = v
		}
	}

	return result
}

// parseConfig 解析配置
func (r *RemoteSignatureInitializer) parseConfig(config map[string]interface{}) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// 解析端点映射
	if endpoints, ok := config["endpoints"].(map[string]interface{}); ok {
		r.endpoints = make(map[string]string)
		for platform, endpoint := range endpoints {
			if endpointStr, ok := endpoint.(string); ok {
				r.endpoints[platform] = r.expandEnvVars(endpointStr)
			}
		}
	}

	if len(r.endpoints) == 0 {
		return fmt.Errorf("no endpoints configured")
	}

	// 解析认证令牌
	if authToken, ok := config["auth_token"].(string); ok {
		r.authToken = r.expandEnvVars(authToken)
	}

	// 解析缓存前缀
	if cachePrefix, ok := config["cache_prefix"].(string); ok {
		r.cachePrefix = cachePrefix
	} else {
		r.cachePrefix = "signature:"
	}

	// 解析 TTL
	if ttlStr, ok := config["ttl"].(string); ok {
		if duration, err := time.ParseDuration(ttlStr); err == nil {
			r.ttl = duration
		}
	}

	// 解析 HTTP 超时
	if timeoutStr, ok := config["timeout"].(string); ok {
		if duration, err := time.ParseDuration(timeoutStr); err == nil {
			r.httpClient.Timeout = duration
		}
	}

	return nil
}

// loadSignatures 加载所有平台的签名信息
func (r *RemoteSignatureInitializer) loadSignatures(ctx context.Context) error {
	newSignatureData := make(map[string]*SignatureInfo)

	for platform, endpoint := range r.endpoints {
		// 首先尝试从缓存加载
		cacheKey := r.cachePrefix + platform
		if r.redisManager != nil {
			if cachedSignature, err := r.loadFromCache(ctx, cacheKey); err == nil {
				newSignatureData[platform] = cachedSignature
				r.updateStats("cache_hit", 1, "")
				r.logger.Debug(fmt.Sprintf("从缓存加载签名信息成功 平台：%s，版本：%s",
					platform, cachedSignature.Version))
				continue
			}
			r.updateStats("cache_miss", 1, "")
		}

		// 从远程端点加载
		signature, err := r.loadFromRemote(ctx, platform, endpoint)
		if err != nil {
			r.logger.Error(fmt.Sprintf("从远程端点加载签名信息失败 平台：%s，端点：%s，错误：%s",
				platform, endpoint, err.Error()))
			continue
		}

		newSignatureData[platform] = signature

		// 保存到缓存
		if r.redisManager != nil {
			if err := r.saveToCache(ctx, cacheKey, signature); err != nil {
				r.logger.Warn(fmt.Sprintf("保存签名信息到缓存失败 平台：%s，错误：%s",
					platform, err.Error()))
			}
		}
	}

	// 更新本地数据
	r.dataMu.Lock()
	r.signatureData = newSignatureData
	r.dataMu.Unlock()

	r.updateStats("success", 1, "")
	return nil
}

// loadFromRemote 从远程端点加载指定平台的签名信息
func (r *RemoteSignatureInitializer) loadFromRemote(ctx context.Context, platform, endpoint string) (*SignatureInfo, error) {
	r.logger.Debug(fmt.Sprintf("从远程端点加载签名信息 平台：%s，端点：%s", platform, endpoint))

	req, err := http.NewRequestWithContext(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 添加认证头
	if r.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+r.authToken)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "go-monitor/1.0")

	resp, err := r.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var signatureInfo SignatureInfo
	if err := json.Unmarshal(body, &signatureInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal signature data: %w", err)
	}

	// 设置更新时间
	signatureInfo.UpdatedAt = time.Now()

	r.logger.Debug(fmt.Sprintf("从远程端点加载签名信息成功 平台：%s，算法：%s，版本：%s",
		platform, signatureInfo.Algorithm, signatureInfo.Version))

	return &signatureInfo, nil
}

// loadFromCache 从缓存加载签名信息
func (r *RemoteSignatureInitializer) loadFromCache(ctx context.Context, cacheKey string) (*SignatureInfo, error) {
	data, err := r.redisManager.Get(ctx, cacheKey)
	if err != nil {
		return nil, fmt.Errorf("failed to get from cache: %w", err)
	}

	var signatureInfo SignatureInfo
	if err := json.Unmarshal(data, &signatureInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cached data: %w", err)
	}

	return &signatureInfo, nil
}

// saveToCache 保存签名信息到缓存
func (r *RemoteSignatureInitializer) saveToCache(ctx context.Context, cacheKey string, signature *SignatureInfo) error {
	data, err := json.Marshal(signature)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %w", err)
	}

	if err := r.redisManager.Set(ctx, cacheKey, data, r.ttl); err != nil {
		return fmt.Errorf("failed to save to cache: %w", err)
	}

	return nil
}

// expandEnvVars 展开环境变量
func (r *RemoteSignatureInitializer) expandEnvVars(s string) string {
	return os.ExpandEnv(s)
}

// updateStats 更新统计信息
func (r *RemoteSignatureInitializer) updateStats(field string, value int64, errorMsg string) {
	r.statsMu.Lock()
	defer r.statsMu.Unlock()

	switch field {
	case "refresh":
		r.stats.RefreshCount += value
	case "error":
		r.stats.ErrorCount += value
		if errorMsg != "" {
			r.stats.LastError = errorMsg
		}
	case "cache_hit":
		r.stats.CacheHitCount += value
	case "cache_miss":
		r.stats.CacheMissCount += value
	case "success":
		r.stats.LastSuccessTime = time.Now()
		r.stats.LastError = "" // 清除错误信息
	}
}
