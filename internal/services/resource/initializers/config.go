package initializers

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"go-monitor/internal/services/redis"
	"go-monitor/internal/services/resource"
	"go-monitor/pkg/logging"

	"github.com/tidwall/gjson"
)

// RemoteConfigInitializer 远程配置初始化器
// 从远程端点获取配置信息并缓存到 Redis
type RemoteConfigInitializer struct {
	// 基础信息
	name         string
	priority     int
	dependencies []string
	ttl          time.Duration

	// 配置
	endpoints  []string
	authToken  string
	cacheKey   string
	httpClient *http.Client

	// Redis 管理器（用于缓存）
	redisManager *redis.RedisManager

	// 状态管理
	initialized   bool
	healthy       bool
	lastRefresh   time.Time
	initializedAt time.Time
	mu            sync.RWMutex

	// 缓存的配置数据
	configData map[string]interface{}
	dataMu     sync.RWMutex

	// 统计信息
	stats struct {
		RefreshCount    int64     `json:"refresh_count"`
		ErrorCount      int64     `json:"error_count"`
		LastError       string    `json:"last_error"`
		CacheHitCount   int64     `json:"cache_hit_count"`
		CacheMissCount  int64     `json:"cache_miss_count"`
		LastSuccessTime time.Time `json:"last_success_time"`
	}
	statsMu sync.RWMutex

	// 日志
	logger logging.Logger
}

// NewRemoteConfigInitializer 创建新的远程配置初始化器
func NewRemoteConfigInitializer(redisManager *redis.RedisManager) *RemoteConfigInitializer {
	return &RemoteConfigInitializer{
		name:         "remote_config",
		priority:     10,
		dependencies: []string{},       // 不再依赖 Redis 初始化器
		ttl:          30 * time.Minute, // 默认 30 分钟过期
		redisManager: redisManager,     // 直接注入 Redis 管理器
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		configData: make(map[string]interface{}),
		logger:     logging.GetLogger("service.remote-config-initializer"),
	}
}

// Name 返回资源名称
func (r *RemoteConfigInitializer) Name() string {
	return r.name
}

// Priority 返回初始化优先级
func (r *RemoteConfigInitializer) Priority() int {
	return r.priority
}

// Dependencies 返回依赖的资源列表
func (r *RemoteConfigInitializer) Dependencies() []string {
	return r.dependencies
}

// Initialize 初始化远程配置资源
func (r *RemoteConfigInitializer) Initialize(ctx context.Context, config map[string]interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.initialized {
		return fmt.Errorf("remote config initializer already initialized")
	}

	r.logger.Info("初始化远程配置资源")

	// 解析配置
	if err := r.parseConfig(config); err != nil {
		r.updateStats("error", 1, fmt.Sprintf("config parse error: %v", err))
		return fmt.Errorf("failed to parse config: %w", err)
	}

	// Redis 管理器已通过构造函数注入
	if r.redisManager == nil {
		r.logger.Warn("Redis 管理器不可用，将不使用缓存")
	}

	// 首次加载配置
	if err := r.loadConfig(ctx); err != nil {
		r.updateStats("error", 1, fmt.Sprintf("initial load error: %v", err))
		return fmt.Errorf("failed to load initial config: %w", err)
	}

	r.initialized = true
	r.healthy = true
	r.initializedAt = time.Now()
	r.lastRefresh = time.Now()

	r.logger.Info(fmt.Sprintf("远程配置资源初始化完成 端点：%v，缓存键：%s，配置数量：%d",
		r.endpoints, r.cacheKey, len(r.configData)))

	return nil
}

// Refresh 刷新远程配置资源
func (r *RemoteConfigInitializer) Refresh(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.initialized {
		return fmt.Errorf("remote config initializer not initialized")
	}

	r.logger.Debug("刷新远程配置资源")

	if err := r.loadConfig(ctx); err != nil {
		r.healthy = false
		r.updateStats("error", 1, fmt.Sprintf("refresh error: %v", err))
		return fmt.Errorf("failed to refresh config: %w", err)
	}

	r.healthy = true
	r.lastRefresh = time.Now()
	r.updateStats("refresh", 1, "")

	r.logger.Debug(fmt.Sprintf("远程配置资源刷新完成 配置数量：%d", len(r.configData)))

	return nil
}

// Close 关闭远程配置资源
func (r *RemoteConfigInitializer) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.initialized {
		return nil
	}

	r.logger.Info("关闭远程配置资源")

	// 清理配置数据
	r.dataMu.Lock()
	r.configData = make(map[string]interface{})
	r.dataMu.Unlock()

	r.initialized = false
	r.healthy = false

	r.logger.Info("远程配置资源已关闭")
	return nil
}

// TTL 返回资源生存时间
func (r *RemoteConfigInitializer) TTL() time.Duration {
	return r.ttl
}

// IsExpired 检查是否过期
func (r *RemoteConfigInitializer) IsExpired() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if r.ttl == 0 {
		return false
	}

	return time.Since(r.lastRefresh) > r.ttl
}

// LastRefreshTime 返回最后刷新时间
func (r *RemoteConfigInitializer) LastRefreshTime() time.Time {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.lastRefresh
}

// IsHealthy 检查资源是否健康
func (r *RemoteConfigInitializer) IsHealthy() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.initialized && r.healthy
}

// GetStatus 获取详细状态信息
func (r *RemoteConfigInitializer) GetStatus() resource.ResourceStatus {
	r.mu.RLock()
	defer r.mu.RUnlock()

	r.statsMu.RLock()
	defer r.statsMu.RUnlock()

	nextRefresh := r.lastRefresh.Add(r.ttl)
	if r.ttl == 0 {
		nextRefresh = time.Time{}
	}

	status := resource.ResourceStatus{
		Name:          r.name,
		Healthy:       r.healthy,
		LastRefresh:   r.lastRefresh,
		NextRefresh:   nextRefresh,
		RefreshCount:  r.stats.RefreshCount,
		ErrorCount:    r.stats.ErrorCount,
		LastError:     r.stats.LastError,
		Dependencies:  r.dependencies,
		TTL:           r.ttl,
		IsExpired:     r.IsExpired(),
		InitializedAt: r.initializedAt,
		Metadata:      make(map[string]string),
	}

	// 添加远程配置特定的元数据
	status.Metadata["endpoints"] = strings.Join(r.endpoints, ",")
	status.Metadata["cache_key"] = r.cacheKey
	status.Metadata["config_count"] = fmt.Sprintf("%d", len(r.configData))
	status.Metadata["cache_hit_count"] = fmt.Sprintf("%d", r.stats.CacheHitCount)
	status.Metadata["cache_miss_count"] = fmt.Sprintf("%d", r.stats.CacheMissCount)

	return status
}

// GetConfig 获取配置数据
func (r *RemoteConfigInitializer) GetConfig(key string) (interface{}, bool) {
	r.dataMu.RLock()
	defer r.dataMu.RUnlock()

	value, exists := r.configData[key]
	return value, exists
}

// GetAllConfig 获取所有配置数据
func (r *RemoteConfigInitializer) GetAllConfig() map[string]interface{} {
	r.dataMu.RLock()
	defer r.dataMu.RUnlock()

	result := make(map[string]interface{})
	for k, v := range r.configData {
		result[k] = v
	}

	return result
}

// parseConfig 解析配置
func (r *RemoteConfigInitializer) parseConfig(config map[string]interface{}) error {
	// 如果配置为空，使用默认配置（禁用远程配置功能）
	if config == nil {
		r.logger.Info("远程配置初始化器未提供配置，将禁用远程配置功能")
		r.endpoints = []string{} // 空端点列表，表示禁用
		return nil
	}

	// 解析端点列表
	if endpoints, ok := config["endpoints"].([]interface{}); ok {
		r.endpoints = make([]string, len(endpoints))
		for i, ep := range endpoints {
			if epStr, ok := ep.(string); ok {
				r.endpoints[i] = r.expandEnvVars(epStr)
			}
		}
	} else if endpoint, ok := config["endpoint"].(string); ok {
		r.endpoints = []string{r.expandEnvVars(endpoint)}
	}

	// 如果没有配置端点，记录警告但不失败
	if len(r.endpoints) == 0 {
		r.logger.Warn("未配置远程配置端点，远程配置功能将被禁用")
		return nil
	}

	// 解析认证令牌
	if authToken, ok := config["auth_token"].(string); ok {
		r.authToken = r.expandEnvVars(authToken)
	}

	// 解析缓存键
	if cacheKey, ok := config["cache_key"].(string); ok {
		r.cacheKey = cacheKey
	} else {
		r.cacheKey = "remote_config"
	}

	// 解析 TTL
	if ttlStr, ok := config["ttl"].(string); ok {
		if duration, err := time.ParseDuration(ttlStr); err == nil {
			r.ttl = duration
		}
	}

	// 解析 HTTP 超时
	if timeoutStr, ok := config["timeout"].(string); ok {
		if duration, err := time.ParseDuration(timeoutStr); err == nil {
			r.httpClient.Timeout = duration
		}
	}

	return nil
}

// loadConfig 加载配置
func (r *RemoteConfigInitializer) loadConfig(ctx context.Context) error {
	// 如果没有配置端点，跳过加载
	if len(r.endpoints) == 0 {
		r.logger.Info("没有配置远程端点，跳过远程配置加载")
		r.dataMu.Lock()
		r.configData = make(map[string]interface{}) // 初始化为空配置
		r.dataMu.Unlock()
		return nil
	}

	// 首先尝试从缓存加载
	if r.redisManager != nil {
		if cachedData, err := r.loadFromCache(ctx); err == nil {
			r.dataMu.Lock()
			r.configData = cachedData
			r.dataMu.Unlock()
			r.updateStats("cache_hit", 1, "")
			r.logger.Debug(fmt.Sprintf("从缓存加载配置成功 配置数量：%d", len(cachedData)))
			return nil
		}
		r.updateStats("cache_miss", 1, "")
	}

	// 从远程端点加载
	configData, err := r.loadFromRemote(ctx)
	if err != nil {
		return fmt.Errorf("failed to load from remote: %w", err)
	}

	// 更新本地数据
	r.dataMu.Lock()
	r.configData = configData
	r.dataMu.Unlock()

	// 保存到缓存
	if r.redisManager != nil {
		if err := r.saveToCache(ctx, configData); err != nil {
			r.logger.Warn(fmt.Sprintf("保存配置到缓存失败：%s", err.Error()))
		}
	}

	r.updateStats("success", 1, "")
	return nil
}

// loadFromRemote 从远程端点加载配置
func (r *RemoteConfigInitializer) loadFromRemote(ctx context.Context) (map[string]interface{}, error) {
	var lastErr error

	for _, endpoint := range r.endpoints {
		r.logger.Debug(fmt.Sprintf("从远程端点加载配置 端点：%s", endpoint))

		req, err := http.NewRequestWithContext(ctx, "GET", endpoint, nil)
		if err != nil {
			lastErr = err
			continue
		}

		// 添加认证头
		if r.authToken != "" {
			req.Header.Set("Authorization", "Bearer "+r.authToken)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "go-monitor/1.0")

		resp, err := r.httpClient.Do(req)
		if err != nil {
			lastErr = err
			continue
		}

		if resp.StatusCode != http.StatusOK {
			resp.Body.Close()
			lastErr = fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			lastErr = err
			continue
		}

		// 使用GJSON验证JSON格式
		jsonStr := string(body)
		if !gjson.Valid(jsonStr) {
			lastErr = fmt.Errorf("invalid JSON response from endpoint")
			continue
		}

		// 仍然需要返回map以保持接口兼容性
		var configData map[string]interface{}
		if err := json.Unmarshal(body, &configData); err != nil {
			lastErr = err
			continue
		}

		r.logger.Debug(fmt.Sprintf("从远程端点加载配置成功 端点：%s，配置数量：%d",
			endpoint, len(configData)))

		return configData, nil
	}

	return nil, fmt.Errorf("failed to load from all endpoints, last error: %w", lastErr)
}

// loadFromCache 从缓存加载配置
func (r *RemoteConfigInitializer) loadFromCache(ctx context.Context) (map[string]interface{}, error) {
	data, err := r.redisManager.Get(ctx, r.cacheKey)
	if err != nil {
		return nil, fmt.Errorf("failed to get from cache: %w", err)
	}

	var configData map[string]interface{}
	if err := json.Unmarshal(data, &configData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cached data: %w", err)
	}

	return configData, nil
}

// saveToCache 保存配置到缓存
func (r *RemoteConfigInitializer) saveToCache(ctx context.Context, configData map[string]interface{}) error {
	data, err := json.Marshal(configData)
	if err != nil {
		return fmt.Errorf("failed to marshal config data: %w", err)
	}

	if err := r.redisManager.Set(ctx, r.cacheKey, data, r.ttl); err != nil {
		return fmt.Errorf("failed to save to cache: %w", err)
	}

	return nil
}

// expandEnvVars 展开环境变量
func (r *RemoteConfigInitializer) expandEnvVars(s string) string {
	return os.ExpandEnv(s)
}

// updateStats 更新统计信息
func (r *RemoteConfigInitializer) updateStats(field string, value int64, errorMsg string) {
	r.statsMu.Lock()
	defer r.statsMu.Unlock()

	switch field {
	case "refresh":
		r.stats.RefreshCount += value
	case "error":
		r.stats.ErrorCount += value
		if errorMsg != "" {
			r.stats.LastError = errorMsg
		}
	case "cache_hit":
		r.stats.CacheHitCount += value
	case "cache_miss":
		r.stats.CacheMissCount += value
	case "success":
		r.stats.LastSuccessTime = time.Now()
		r.stats.LastError = "" // 清除错误信息
	}
}
