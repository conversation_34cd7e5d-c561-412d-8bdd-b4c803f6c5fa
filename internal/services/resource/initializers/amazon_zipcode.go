package initializers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/request"
	"go-monitor/pkg/logging"

	"github.com/tidwall/gjson"
)

// ============================================================================
// ZipCode处理器实现
// ============================================================================

// ZipCodeProcessor 实现RegionProcessor接口，处理zipCode相关的地区初始化
type ZipCodeProcessor struct {
	requestService *request.Service
	logger         logging.Logger
	commonHeaders  map[string]string
}

// NewZipCodeProcessor 创建ZipCode处理器
func NewZipCodeProcessor(requestService *request.Service) *ZipCodeProcessor {
	return &ZipCodeProcessor{
		requestService: requestService,
		logger:         logging.GetLogger("amazon-zipcode-processor"),
		commonHeaders: map[string]string{
			"Accept":          "*/*",
			"Accept-Encoding": "gzip, deflate, br",
			"Accept-Language": "en-US,en;q=0.9",
			"User-Agent":      "Amazon/761286.0 CFNetwork/3826.500.131 Darwin/24.5.0",
		},
	}
}

// ProcessRegion 实现RegionProcessor接口
func (z *ZipCodeProcessor) ProcessRegion(ctx context.Context, regionConfig AmazonRegionConfig) error {
	return z.performZipCodeFlow(ctx, regionConfig)
}

// performZipCodeFlow 执行zipCode设置流程
func (z *ZipCodeProcessor) performZipCodeFlow(ctx context.Context, regionConfig AmazonRegionConfig) error {
	// 使用第一个cookie组（如果有多个）
	if len(regionConfig.Cookies) == 0 {
		return fmt.Errorf("未配置cookie组")
	}
	cookieGroup := regionConfig.Cookies[0]

	// 步骤1: 获取validationToken
	validationToken, err := z.getValidationToken(ctx, regionConfig)
	if err != nil {
		return fmt.Errorf("获取validationToken失败: %w", err)
	}

	// 检查是否因国家代码匹配而跳过后续步骤
	if validationToken == "COUNTRY_MATCHED" {
		z.logger.Info(fmt.Sprintf("地区 %s 国家代码匹配，跳过zipCode设置和验证步骤", regionConfig.Country))
		return nil
	}

	// 步骤2: 获取csrfToken
	csrfToken, err := z.getCSRFToken(ctx, regionConfig, validationToken)
	if err != nil {
		return fmt.Errorf("获取csrfToken失败: %w", err)
	}

	// 步骤3: 设置zipCode
	if err := z.setZipCode(ctx, regionConfig, csrfToken); err != nil {
		return fmt.Errorf("设置zipCode失败: %w", err)
	}

	// 步骤4: 验证cookies有效性
	if err := z.validateCookies(ctx, regionConfig); err != nil {
		return fmt.Errorf("验证cookies失败: %w", err)
	}

	domain := extractDomainFromURL(regionConfig.URL)
	z.logger.Info(fmt.Sprintf("地区 %s 的cookies初始化成功，域名：%s，邮编：%s，cookie组：%s",
		regionConfig.Country, domain, regionConfig.ZipCode, cookieGroup))

	return nil
}

// buildHeaders 构建请求头，合并通用头和特定头
func (z *ZipCodeProcessor) buildHeaders(specificHeaders map[string]string) map[string]string {
	headers := make(map[string]string)

	// 先添加通用请求头
	for k, v := range z.commonHeaders {
		headers[k] = v
	}

	// 再添加特定请求头（会覆盖通用头中的同名项）
	for k, v := range specificHeaders {
		headers[k] = v
	}

	return headers
}

// buildHTTPRequest 构建HTTP请求，使用地区特定的配置
func (z *ZipCodeProcessor) buildHTTPRequest(method, url string, headers map[string]string,
	body []byte, params map[string]string, regionConfig AmazonRegionConfig) *models.Request {

	req := &models.Request{
		URL:     url,
		Method:  method,
		Headers: z.buildHeaders(headers),
		Body:    body,
		Metadata: map[string]interface{}{
			"spider_type": "amazon",
			"proxies":     regionConfig.Proxies,
			"cookies":     regionConfig.Cookies,
		},
	}

	if params != nil {
		req.Params = params
	}

	return req
}

// getValidationToken 获取validationToken，使用cookie组
func (z *ZipCodeProcessor) getValidationToken(ctx context.Context, regionConfig AmazonRegionConfig) (string, error) {
	// 步骤1: 先访问主页获取初始session
	if err := z.getInitialSession(ctx, regionConfig); err != nil {
		z.logger.Debug(fmt.Sprintf("获取初始session失败，继续尝试直接获取validationToken，错误：%s", err.Error()))
	}

	// 步骤2: 获取location-label
	baseURL := fmt.Sprintf("%s/portal-migration/hz/glow/get-location-label", regionConfig.URL)
	params := map[string]string{
		"pageType":   "gateway",
		"deviceType": "mobile",
		"osType":     "ios",
	}

	headers := map[string]string{
		"Accept":           "application/json, text/plain, */*",
		"Accept-Language":  "en-US,en;q=0.9",
		"Cache-Control":    "no-cache",
		"Connection":       "keep-alive",
		"Pragma":           "no-cache",
		"X-Requested-With": "XMLHttpRequest",
	}

	req := z.buildHTTPRequest("GET", baseURL, headers, nil, params, regionConfig)
	resp, err := z.requestService.SendRequest(ctx, req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %w", err)
	}

	if err := validateHTTPResponse(resp, "获取validationToken"); err != nil {
		return "", err
	}

	// 解析响应获取validationToken
	responseBody := string(resp.Body)

	// 检查国家代码是否匹配
	if !regionConfig.IgnoreCountry && checkCountryCodeMatch(regionConfig.Country, responseBody) {
		z.logger.Info(fmt.Sprintf("地区 %s 的国家代码与当前网站匹配，跳过后续zipCode设置步骤", regionConfig.Country))
		return "COUNTRY_MATCHED", nil
	}

	validationToken, found := parseJSONResponse(responseBody, "validationToken")
	if !found {
		z.logger.Debug(fmt.Sprintf("validationToken获取失败，状态码：%d，响应长度：%d，包含validationToken：%t",
			resp.StatusCode, len(responseBody), gjson.Get(responseBody, "validationToken").Exists()))
		return "", fmt.Errorf("未找到validationToken")
	}

	z.logger.Debug(fmt.Sprintf("获取validationToken成功，url：%s，cookie组：%s", baseURL, regionConfig.Cookies[0]))

	return validationToken, nil
}

// getInitialSession 访问主页获取初始session cookies
func (z *ZipCodeProcessor) getInitialSession(ctx context.Context, regionConfig AmazonRegionConfig) error {
	homeURL := regionConfig.URL

	headers := map[string]string{
		"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
		"Accept-Language":           "en-US,en;q=0.9",
		"Cache-Control":             "no-cache",
		"Connection":                "keep-alive",
		"Pragma":                    "no-cache",
		"Sec-Fetch-Dest":            "document",
		"Sec-Fetch-Mode":            "navigate",
		"Sec-Fetch-Site":            "none",
		"Upgrade-Insecure-Requests": "1",
	}

	req := z.buildHTTPRequest("GET", homeURL, headers, nil, nil, regionConfig)
	resp, err := z.requestService.SendRequest(ctx, req)
	if err != nil {
		return fmt.Errorf("访问主页失败: %w", err)
	}

	if err := validateHTTPResponse(resp, "访问主页"); err != nil {
		return err
	}

	z.logger.Debug(fmt.Sprintf("获取初始session成功 url: %s，cookie组：%s", homeURL, regionConfig.Cookies[0]))

	return nil
}

// getCSRFToken 获取CSRF token，使用cookie组
func (z *ZipCodeProcessor) getCSRFToken(ctx context.Context, regionConfig AmazonRegionConfig, validationToken string) (string, error) {
	// 构建获取address-selections的URL
	domain := extractDomainFromURL(regionConfig.URL)
	baseURL := fmt.Sprintf("https://%s/portal-migration/hz/glow/get-address-selections-view-model", domain)
	requestURL := baseURL + "?deviceType=reactnative&osType=ios&storeContext=&actionSource=ios-react-native-bottomsheet&showProgramSpecificSheet=false&pageType=gateway"

	headers := map[string]string{
		"Content-Type":       "application/json",
		"Anti-Csrftoken-A2z": validationToken,
	}

	req := z.buildHTTPRequest("GET", requestURL, headers, nil, nil, regionConfig)
	resp, err := z.requestService.SendRequest(ctx, req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %w", err)
	}

	if err := validateHTTPResponse(resp, "获取csrfToken"); err != nil {
		return "", err
	}

	// 解析响应获取csrfToken
	responseBody := string(resp.Body)

	// 尝试从features.AddressList路径获取csrfToken
	addressListStr, found := parseJSONResponse(responseBody, "features.AddressList")
	if !found {
		z.logger.Debug(fmt.Sprintf("csrfToken获取失败，状态码：%d，响应长度：%d，包含features：%t，包含addressList：%t",
			resp.StatusCode, len(responseBody), gjson.Get(responseBody, "features").Exists(),
			gjson.Get(responseBody, "features.AddressList").Exists()))
		return "", fmt.Errorf("未找到csrfToken，响应结构可能已变化")
	}

	csrfToken, found := parseJSONResponse(addressListStr, "csrfToken")
	if !found {
		return "", fmt.Errorf("未找到csrfToken，响应结构可能已变化")
	}

	return csrfToken, nil
}

// setZipCode 设置zipCode，使用cookie组
func (z *ZipCodeProcessor) setZipCode(ctx context.Context, regionConfig AmazonRegionConfig, csrfToken string) error {
	// 构建设置zipCode的URL
	baseURL := fmt.Sprintf("%s/portal-migration/hz/glow/address-change", regionConfig.URL)

	// 构建URL参数
	params := url.Values{}
	params.Set("locationType", "LOCATION_INPUT")
	params.Set("zipCode", regionConfig.ZipCode)
	params.Set("storeContext", "generic")
	params.Set("pageType", "gateway")
	params.Set("deviceType", "Native")
	params.Set("actionSource", "glow")
	params.Set("osType", "ios")

	requestURL := baseURL + "?" + params.Encode()

	// 构建请求体
	requestBody := map[string]interface{}{
		"locationType": "LOCATION_INPUT",
		"zipCode":      regionConfig.ZipCode,
		"storeContext": "generic",
		"csrfToken":    csrfToken,
		"pageType":     "gateway",
		"deviceType":   "Native",
		"actionSource": "glow",
		"osType":       "ios",
	}

	bodyBytes, _ := json.Marshal(requestBody)

	headers := map[string]string{
		"Accept":             "application/json",
		"Content-Type":       "application/json",
		"Anti-Csrftoken-A2z": csrfToken,
		"Accept-Language":    "en-US,en;q=0.9",
		"Accept-Encoding":    "gzip, deflate, br",
	}

	req := z.buildHTTPRequest("POST", requestURL, headers, bodyBytes, nil, regionConfig)
	resp, err := z.requestService.SendRequest(ctx, req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}

	if err := validateHTTPResponse(resp, "设置zipCode"); err != nil {
		return err
	}

	// 验证设置结果
	responseBody := string(resp.Body)
	isValid := gjson.Get(responseBody, "isValidAddress").Int()
	isUpdated := gjson.Get(responseBody, "isAddressUpdated").Int()

	if isValid != 1 || isUpdated != 1 {
		return fmt.Errorf("zipCode设置失败: isValid=%d, isUpdated=%d", isValid, isUpdated)
	}

	return nil
}

// validateCookies 验证cookies有效性
func (z *ZipCodeProcessor) validateCookies(ctx context.Context, regionConfig AmazonRegionConfig) error {
	// 构建验证URL
	baseURL := fmt.Sprintf("%s/portal-migration/hz/glow/get-location-label", regionConfig.URL)
	requestURL := baseURL + "?pageType=gateway&deviceType=mobile&osType=ios"

	req := z.buildHTTPRequest("GET", requestURL, nil, nil, nil, regionConfig)
	resp, err := z.requestService.SendRequest(ctx, req)
	if err != nil {
		return fmt.Errorf("发送验证请求失败: %w", err)
	}

	if err := validateHTTPResponse(resp, "验证cookies"); err != nil {
		return err
	}

	// 检查响应中的zipCode信息
	responseBody := string(resp.Body)
	actualZipCode, found := parseJSONResponse(responseBody, "customerIntent.zipCode")
	if !found {
		return fmt.Errorf("验证响应中未找到zipCode信息")
	}

	// 验证zipCode匹配
	if err := z.validateZipCodeMatch(regionConfig.ZipCode, actualZipCode); err != nil {
		return err
	}

	return nil
}

// validateZipCodeMatch 验证zipCode是否匹配
func (z *ZipCodeProcessor) validateZipCodeMatch(expected, actual string) error {
	// 宽松的zipCode验证：移除空格和特殊字符后比较
	expectedZip := strings.ReplaceAll(strings.ToUpper(expected), " ", "")
	actualZip := strings.ReplaceAll(strings.ToUpper(actual), " ", "")

	if actualZip == expectedZip {
		return nil
	}

	// 尝试前缀匹配（处理英国邮编格式）
	if strings.HasPrefix(expectedZip, actualZip) || strings.HasPrefix(actualZip, expectedZip) {
		z.logger.Debug(fmt.Sprintf("zipCode格式略有差异但可接受，期望：%s，实际：%s", expected, actual))
		return nil
	}

	return fmt.Errorf("cookies验证失败，zipCode不匹配: 期望=%s, 实际=%s", expected, actual)
}

// GetProcessorName 实现RegionProcessor接口
func (z *ZipCodeProcessor) GetProcessorName() string {
	return "zipcode"
}

// ============================================================================
// ZipCode特定的工具函数
// ============================================================================

// extractDomainFromURL 从URL中提取域名
func extractDomainFromURL(urlStr string) string {
	if parsedURL, err := url.Parse(urlStr); err == nil {
		return parsedURL.Host
	}
	// 如果解析失败，尝试简单的字符串处理
	if strings.HasPrefix(urlStr, "https://") {
		urlStr = strings.TrimPrefix(urlStr, "https://")
	} else if strings.HasPrefix(urlStr, "http://") {
		urlStr = strings.TrimPrefix(urlStr, "http://")
	}

	// 移除路径部分
	if idx := strings.Index(urlStr, "/"); idx != -1 {
		urlStr = urlStr[:idx]
	}

	return urlStr
}

// checkCountryCodeMatch 检查响应中的国家代码是否与区域组名匹配
func checkCountryCodeMatch(regionName, responseBody string) bool {
	// 从响应中提取国家代码
	countryCode, found := parseJSONResponse(responseBody, "customerIntent.countryCode")
	if !found {
		return false
	}

	// 大小写不敏感的匹配比较
	return strings.EqualFold(regionName, countryCode)
}

// ============================================================================
// 类型定义
// ============================================================================

// AmazonZipCodeInitializer Amazon zipCode资源初始化器
type AmazonZipCodeInitializer struct {
	*AmazonInitializerBase
	processor *ZipCodeProcessor
}

// RequestConfig 请求配置（保留用于向后兼容）
type RequestConfig struct {
	Timeout    time.Duration `yaml:"timeout"`
	MaxRetries int           `yaml:"max_retries"`
	Proxies    []string      `yaml:"proxies"`
}

// ============================================================================
// 构造函数和配置
// ============================================================================

// NewAmazonZipCodeInitializer 创建Amazon zipCode资源初始化器
func NewAmazonZipCodeInitializer(requestService *request.Service) *AmazonZipCodeInitializer {
	processor := NewZipCodeProcessor(requestService)
	base := NewAmazonInitializerBase(
		"amazon_zipcode_cookies",
		16, // 默认优先级，将从配置中覆盖
		[]string{},
		30*time.Minute, // 默认TTL，将从配置中覆盖
		requestService,
		processor,
	)

	return &AmazonZipCodeInitializer{
		AmazonInitializerBase: base,
		processor:             processor,
	}
}

// ResourceInitializer接口由AmazonInitializerBase实现
// Initialize, Refresh等方法由基础结构体提供

// 配置解析和并发处理由AmazonInitializerBase提供
// 所有ZipCode特定的方法都在ZipCodeProcessor中实现

// 实现剩余的接口方法
func (a *AmazonZipCodeInitializer) Cleanup() error {
	return a.AmazonInitializerBase.Cleanup()
}

func (a *AmazonZipCodeInitializer) Close() error {
	return a.AmazonInitializerBase.Close()
}

// ============================================================================
// 工具函数
// ============================================================================

// checkCountryCodeMatch 检查国家代码是否匹配
