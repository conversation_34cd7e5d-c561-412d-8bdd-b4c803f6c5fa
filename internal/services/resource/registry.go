package resource

import (
	"fmt"
	"sort"
	"sync"

	"go-monitor/pkg/logging"
)

// Registry 资源注册器实现
type Registry struct {
	// 存储注册的初始化器
	initializers map[string]ResourceInitializer

	// 依赖关系图
	dependencies map[string][]string // 资源名 -> 依赖的资源列表
	dependents   map[string][]string // 资源名 -> 依赖此资源的资源列表

	// 并发控制
	mu sync.RWMutex

	// 日志
	logger logging.Logger
}

// NewRegistry 创建新的资源注册器
func NewRegistry(logger logging.Logger) ResourceRegistry {
	return &Registry{
		initializers: make(map[string]ResourceInitializer),
		dependencies: make(map[string][]string),
		dependents:   make(map[string][]string),
		logger:       logger,
	}
}

// Register 注册资源初始化器
func (r *Registry) Register(name string, initializer ResourceInitializer) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if name == "" {
		return fmt.Errorf("resource name cannot be empty")
	}

	if initializer == nil {
		return fmt.Errorf("initializer cannot be nil")
	}

	// 检查是否已存在
	if _, exists := r.initializers[name]; exists {
		return fmt.Errorf("resource %s already registered", name)
	}

	// 验证初始化器名称一致性
	if initializer.Name() != name {
		return fmt.Errorf("initializer name %s does not match registration name %s",
			initializer.Name(), name)
	}

	// 注册初始化器
	r.initializers[name] = initializer

	// 构建依赖关系
	dependencies := initializer.Dependencies()
	r.dependencies[name] = make([]string, len(dependencies))
	copy(r.dependencies[name], dependencies)

	// 更新反向依赖关系
	for _, dep := range dependencies {
		if r.dependents[dep] == nil {
			r.dependents[dep] = make([]string, 0)
		}
		r.dependents[dep] = append(r.dependents[dep], name)
	}

	r.logger.Debug("资源注册成功",
		"name", name,
		"priority", initializer.Priority(),
		"dependencies", dependencies,
	)

	return nil
}

// Unregister 注销资源初始化器
func (r *Registry) Unregister(name string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 检查是否存在
	initializer, exists := r.initializers[name]
	if !exists {
		return fmt.Errorf("resource %s not found", name)
	}

	// 检查是否有其他资源依赖此资源
	if dependents := r.dependents[name]; len(dependents) > 0 {
		return fmt.Errorf("cannot unregister resource %s: still has dependents %v",
			name, dependents)
	}

	// 移除依赖关系
	dependencies := initializer.Dependencies()
	for _, dep := range dependencies {
		if deps := r.dependents[dep]; deps != nil {
			// 从依赖列表中移除当前资源
			newDeps := make([]string, 0, len(deps))
			for _, d := range deps {
				if d != name {
					newDeps = append(newDeps, d)
				}
			}
			r.dependents[dep] = newDeps
		}
	}

	// 删除注册信息
	delete(r.initializers, name)
	delete(r.dependencies, name)
	delete(r.dependents, name)

	r.logger.Debug("资源注销成功",
		"name", name,
	)

	return nil
}

// Get 获取资源初始化器
func (r *Registry) Get(name string) (ResourceInitializer, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	initializer, exists := r.initializers[name]
	return initializer, exists
}

// List 列出所有注册的资源名称
func (r *Registry) List() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	names := make([]string, 0, len(r.initializers))
	for name := range r.initializers {
		names = append(names, name)
	}

	// 按名称排序以保证一致性
	sort.Strings(names)
	return names
}

// GetDependencies 获取指定资源的依赖列表
func (r *Registry) GetDependencies(name string) []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if deps, exists := r.dependencies[name]; exists {
		result := make([]string, len(deps))
		copy(result, deps)
		return result
	}

	return []string{}
}

// GetDependents 获取依赖指定资源的资源列表
func (r *Registry) GetDependents(name string) []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if deps, exists := r.dependents[name]; exists {
		result := make([]string, len(deps))
		copy(result, deps)
		return result
	}

	return []string{}
}

// ResolveDependencyOrder 解析依赖顺序，返回初始化顺序
func (r *Registry) ResolveDependencyOrder() ([]string, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 使用拓扑排序算法
	return r.topologicalSort()
}

// ValidateDependencies 验证依赖关系的有效性
func (r *Registry) ValidateDependencies() error {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 检查循环依赖
	if err := r.detectCycles(); err != nil {
		return err
	}

	// 检查依赖的资源是否存在
	for name, deps := range r.dependencies {
		for _, dep := range deps {
			if _, exists := r.initializers[dep]; !exists {
				return fmt.Errorf("resource %s depends on non-existent resource %s", name, dep)
			}
		}
	}

	return nil
}

// topologicalSort 拓扑排序实现
func (r *Registry) topologicalSort() ([]string, error) {
	// 计算入度
	inDegree := make(map[string]int)
	for name := range r.initializers {
		inDegree[name] = 0
	}

	// 正确计算入度：依赖其他节点的节点入度增加
	for name, deps := range r.dependencies {
		inDegree[name] = len(deps)
	}

	// 创建优先级队列（使用切片模拟）
	type item struct {
		name     string
		priority int
	}

	queue := make([]item, 0)

	// 将入度为0的节点加入队列
	for name, degree := range inDegree {
		if degree == 0 {
			if initializer, exists := r.initializers[name]; exists {
				queue = append(queue, item{
					name:     name,
					priority: initializer.Priority(),
				})
			}
		}
	}

	// 按优先级排序队列
	sort.Slice(queue, func(i, j int) bool {
		return queue[i].priority < queue[j].priority
	})

	result := make([]string, 0, len(r.initializers))

	for len(queue) > 0 {
		// 取出优先级最高的节点
		current := queue[0]
		queue = queue[1:]
		result = append(result, current.name)

		// 更新依赖此节点的其他节点的入度
		if dependents, exists := r.dependents[current.name]; exists {
			newQueue := make([]item, 0)

			for _, dependent := range dependents {
				inDegree[dependent]--
				if inDegree[dependent] == 0 {
					if initializer, exists := r.initializers[dependent]; exists {
						newQueue = append(newQueue, item{
							name:     dependent,
							priority: initializer.Priority(),
						})
					}
				}
			}

			// 将新的零入度节点加入队列并排序
			queue = append(queue, newQueue...)
			sort.Slice(queue, func(i, j int) bool {
				return queue[i].priority < queue[j].priority
			})
		}
	}

	// 检查是否所有节点都被处理
	if len(result) != len(r.initializers) {
		return nil, fmt.Errorf("circular dependency detected")
	}

	return result, nil
}

// detectCycles 检测循环依赖
func (r *Registry) detectCycles() error {
	// 使用DFS检测循环
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	for name := range r.initializers {
		if !visited[name] {
			if r.hasCycleDFS(name, visited, recStack) {
				return fmt.Errorf("circular dependency detected involving resource %s", name)
			}
		}
	}

	return nil
}

// hasCycleDFS DFS检测循环依赖
func (r *Registry) hasCycleDFS(name string, visited, recStack map[string]bool) bool {
	visited[name] = true
	recStack[name] = true

	// 检查所有依赖
	if deps, exists := r.dependencies[name]; exists {
		for _, dep := range deps {
			if !visited[dep] {
				if r.hasCycleDFS(dep, visited, recStack) {
					return true
				}
			} else if recStack[dep] {
				return true
			}
		}
	}

	recStack[name] = false
	return false
}
