package resource

import (
	"context"
	"fmt"
	"time"
)

// ResourceStatus 资源状态
type ResourceStatus struct {
	Name          string            `json:"name"`
	Healthy       bool              `json:"healthy"`
	LastRefresh   time.Time         `json:"last_refresh"`
	NextRefresh   time.Time         `json:"next_refresh"`
	RefreshCount  int64             `json:"refresh_count"`
	ErrorCount    int64             `json:"error_count"`
	LastError     string            `json:"last_error,omitempty"`
	Metadata      map[string]string `json:"metadata,omitempty"`
	Dependencies  []string          `json:"dependencies"`
	TTL           time.Duration     `json:"ttl"`
	IsExpired     bool              `json:"is_expired"`
	InitializedAt time.Time         `json:"initialized_at"`
}

// ResourceInitializer 资源初始化器接口
// 定义了所有资源初始化器必须实现的方法
type ResourceInitializer interface {
	// 基础信息
	Name() string           // 资源名称
	Priority() int          // 初始化优先级（数字越小优先级越高）
	Dependencies() []string // 依赖的其他资源名称列表

	// 生命周期管理
	Initialize(ctx context.Context, config map[string]interface{}) error // 初始化资源
	Refresh(ctx context.Context) error                                   // 刷新资源
	Close() error                                                        // 关闭资源

	// 时效性管理
	TTL() time.Duration         // 资源生存时间
	IsExpired() bool            // 检查是否过期
	LastRefreshTime() time.Time // 最后刷新时间

	// 状态查询
	IsHealthy() bool           // 检查资源是否健康
	GetStatus() ResourceStatus // 获取详细状态信息
}

// ResourceManager 资源管理器接口
type ResourceManager interface {
	// 注册和管理
	Register(initializer ResourceInitializer) error
	Unregister(name string) error
	GetInitializer(name string) (ResourceInitializer, bool)
	ListInitializers() []string

	// 生命周期管理
	Initialize(ctx context.Context) error
	Start(ctx context.Context) error
	Stop() error
	Restart(ctx context.Context) error

	// 状态查询
	GetStatus(name string) (ResourceStatus, bool)
	GetAllStatuses() map[string]ResourceStatus
	IsHealthy() bool

	// 刷新管理
	RefreshResource(ctx context.Context, name string) error
	RefreshAll(ctx context.Context) error
}

// ResourceRegistry 资源注册器接口
type ResourceRegistry interface {
	// 注册管理
	Register(name string, initializer ResourceInitializer) error
	Unregister(name string) error
	Get(name string) (ResourceInitializer, bool)
	List() []string

	// 依赖管理
	GetDependencies(name string) []string
	GetDependents(name string) []string
	ResolveDependencyOrder() ([]string, error)
	ValidateDependencies() error
}

// ResourceScheduler 资源调度器接口
type ResourceScheduler interface {
	// 调度管理
	Start(ctx context.Context) error
	Stop() error
	IsRunning() bool

	// 刷新调度
	ScheduleRefresh(name string, interval time.Duration) error
	UnscheduleRefresh(name string) error
	TriggerRefresh(ctx context.Context, name string) error

	// 状态监控
	GetScheduleStatus() map[string]time.Time
	GetNextRefreshTime(name string) (time.Time, bool)
}

// ResourceConfig 资源配置结构
type ResourceConfig struct {
	Global struct {
		Enabled          bool          `yaml:"enabled"`
		RefreshInterval  time.Duration `yaml:"refresh_interval"`
		MaxRetryAttempts int           `yaml:"max_retry_attempts"`
		RetryDelay       time.Duration `yaml:"retry_delay"`
	} `yaml:"global"`

	Initializers map[string]InitializerConfig `yaml:"initializers"`
}

// InitializerConfig 单个初始化器配置
type InitializerConfig struct {
	Enabled      bool                   `yaml:"enabled"`
	Priority     int                    `yaml:"priority"`
	Dependencies []string               `yaml:"dependencies"`
	TTL          time.Duration          `yaml:"ttl"`
	Config       map[string]interface{} `yaml:"config"`
}

// ResourceError 资源错误类型
type ResourceError struct {
	ResourceName string
	Operation    string
	Err          error
}

func (e *ResourceError) Error() string {
	return fmt.Sprintf("resource %s %s failed: %v", e.ResourceName, e.Operation, e.Err)
}

// NewResourceError 创建资源错误
func NewResourceError(resourceName, operation string, err error) *ResourceError {
	return &ResourceError{
		ResourceName: resourceName,
		Operation:    operation,
		Err:          err,
	}
}
