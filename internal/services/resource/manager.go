package resource

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go-monitor/internal/config"
	"go-monitor/pkg/logging"
)

// Manager 资源管理器实现
type Manager struct {
	// 核心组件
	registry  ResourceRegistry
	scheduler ResourceScheduler
	logger    logging.Logger

	// 状态管理
	initialized bool
	running     bool
	mu          sync.RWMutex

	// 配置
	config *ResourceConfig

	// 统计信息
	stats struct {
		InitializedCount int64     `json:"initialized_count"`
		FailedCount      int64     `json:"failed_count"`
		RefreshCount     int64     `json:"refresh_count"`
		LastInitTime     time.Time `json:"last_init_time"`
	}
	statsMu sync.RWMutex
}

// NewManager 创建新的资源管理器
func NewManager(config *ResourceConfig) *Manager {
	// 获取全局日志工厂
	var logger = logging.GetLogger("service.resource-manager")

	manager := &Manager{
		config: config,
		logger: logger,
	}

	// 创建注册器和调度器
	manager.registry = NewRegistry(logger)
	manager.scheduler = NewScheduler(manager.registry, logger, config)

	return manager
}

// NewManagerFromConfig 从配置系统创建资源管理器 - 开箱即用
func NewManagerFromConfig(resourceConfig *config.ResourceConfig) *Manager {
	// 转换为内部配置格式
	internalConfig := &ResourceConfig{
		Global: struct {
			Enabled          bool          `yaml:"enabled"`
			RefreshInterval  time.Duration `yaml:"refresh_interval"`
			MaxRetryAttempts int           `yaml:"max_retry_attempts"`
			RetryDelay       time.Duration `yaml:"retry_delay"`
		}{
			Enabled:          resourceConfig.Global.Enabled,
			RefreshInterval:  resourceConfig.Global.RefreshInterval,
			MaxRetryAttempts: resourceConfig.Global.MaxRetryAttempts,
			RetryDelay:       resourceConfig.Global.RetryDelay,
		},
		Initializers: make(map[string]InitializerConfig),
	}

	// 转换初始化器配置
	for name, initConfig := range resourceConfig.Initializers {
		internalConfig.Initializers[name] = InitializerConfig{
			Enabled:      initConfig.Enabled,
			Priority:     initConfig.Priority,
			Dependencies: initConfig.Dependencies,
			TTL:          initConfig.TTL,
			Config:       initConfig.Config,
		}
	}

	return NewManager(internalConfig)
}

// Register 注册资源初始化器
func (m *Manager) Register(initializer ResourceInitializer) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.initialized {
		return fmt.Errorf("cannot register initializer after manager is initialized")
	}

	name := initializer.Name()
	if name == "" {
		return fmt.Errorf("initializer name cannot be empty")
	}

	// 注册到注册器
	if err := m.registry.Register(name, initializer); err != nil {
		return fmt.Errorf("failed to register initializer %s: %w", name, err)
	}

	m.logger.Info(fmt.Sprintf("资源初始化器注册成功：%s，优先级：%d，依赖：%v",
		name, initializer.Priority(), initializer.Dependencies()))

	return nil
}

// Unregister 注销资源初始化器
func (m *Manager) Unregister(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("cannot unregister initializer while manager is running")
	}

	if err := m.registry.Unregister(name); err != nil {
		return fmt.Errorf("failed to unregister initializer %s: %w", name, err)
	}

	m.logger.Info(fmt.Sprintf("资源初始化器注销成功：%s", name))

	return nil
}

// GetInitializer 获取资源初始化器
func (m *Manager) GetInitializer(name string) (ResourceInitializer, bool) {
	return m.registry.Get(name)
}

// ListInitializers 列出所有注册的初始化器
func (m *Manager) ListInitializers() []string {
	return m.registry.List()
}

// Initialize 初始化所有资源
func (m *Manager) Initialize(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.initialized {
		return fmt.Errorf("resource manager already initialized")
	}

	m.logger.Info(fmt.Sprintf("开始初始化资源管理器，初始化器数量：%d", len(m.registry.List())))

	// 验证依赖关系
	if err := m.registry.ValidateDependencies(); err != nil {
		return fmt.Errorf("dependency validation failed: %w", err)
	}

	// 解析初始化顺序
	initOrder, err := m.registry.ResolveDependencyOrder()
	if err != nil {
		return fmt.Errorf("failed to resolve dependency order: %w", err)
	}

	m.logger.Info(fmt.Sprintf("资源初始化顺序已确定：%v", initOrder))

	// 按顺序初始化资源
	for _, name := range initOrder {
		// 检查资源是否启用
		if !m.isResourceEnabled(name) {
			m.logger.Info(fmt.Sprintf("跳过禁用的资源：%s", name))
			continue
		}

		if err := m.initializeResource(ctx, name); err != nil {
			m.updateStats("failed", 1)
			return fmt.Errorf("failed to initialize resource %s: %w", name, err)
		}
		m.updateStats("initialized", 1)
	}

	m.initialized = true
	m.updateStats("last_init_time", time.Now())

	m.logger.Info(fmt.Sprintf("资源管理器初始化完成，初始化数量：%d，总时间：%s",
		len(initOrder), time.Since(m.stats.LastInitTime).String()))

	return nil
}

// Start 启动资源管理器
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.initialized {
		return fmt.Errorf("resource manager not initialized")
	}

	if m.running {
		return fmt.Errorf("resource manager already running")
	}

	m.logger.Info("启动资源管理器")

	// 启动调度器
	if err := m.scheduler.Start(ctx); err != nil {
		return fmt.Errorf("failed to start scheduler: %w", err)
	}

	// 为有TTL的资源安排刷新任务
	if err := m.scheduleRefreshTasks(); err != nil {
		m.scheduler.Stop()
		return fmt.Errorf("failed to schedule refresh tasks: %w", err)
	}

	m.running = true
	m.logger.Info("资源管理器启动完成")

	return nil
}

// Stop 停止资源管理器
func (m *Manager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return fmt.Errorf("resource manager not running")
	}

	m.logger.Info("停止资源管理器")

	// 停止调度器
	if err := m.scheduler.Stop(); err != nil {
		m.logger.Error(fmt.Sprintf("停止调度器失败：%s", err.Error()))
	}

	// 关闭所有资源
	var errors []error
	for _, name := range m.registry.List() {
		if initializer, exists := m.registry.Get(name); exists {
			if err := initializer.Close(); err != nil {
				errors = append(errors, fmt.Errorf("failed to close resource %s: %w", name, err))
				m.logger.Error(fmt.Sprintf("关闭资源失败：%s，错误：%s", name, err.Error()))
			}
		}
	}

	m.running = false
	m.logger.Info("资源管理器已停止")

	// 如果有错误，返回第一个错误
	if len(errors) > 0 {
		return errors[0]
	}

	return nil
}

// Restart 重启资源管理器
func (m *Manager) Restart(ctx context.Context) error {
	m.logger.Info("重启资源管理器")

	// 先停止
	if err := m.Stop(); err != nil {
		return fmt.Errorf("failed to stop manager: %w", err)
	}

	// 重置状态
	m.mu.Lock()
	m.initialized = false
	m.running = false
	m.mu.Unlock()

	// 重新初始化和启动
	if err := m.Initialize(ctx); err != nil {
		return fmt.Errorf("failed to reinitialize manager: %w", err)
	}

	if err := m.Start(ctx); err != nil {
		return fmt.Errorf("failed to restart manager: %w", err)
	}

	m.logger.Info("资源管理器重启完成")
	return nil
}

// GetStatus 获取指定资源的状态
func (m *Manager) GetStatus(name string) (ResourceStatus, bool) {
	initializer, exists := m.registry.Get(name)
	if !exists {
		return ResourceStatus{}, false
	}

	return initializer.GetStatus(), true
}

// GetAllStatuses 获取所有资源的状态
func (m *Manager) GetAllStatuses() map[string]ResourceStatus {
	statuses := make(map[string]ResourceStatus)

	for _, name := range m.registry.List() {
		if initializer, exists := m.registry.Get(name); exists {
			statuses[name] = initializer.GetStatus()
		}
	}

	return statuses
}

// IsHealthy 检查所有资源是否健康
func (m *Manager) IsHealthy() bool {
	for _, name := range m.registry.List() {
		if initializer, exists := m.registry.Get(name); exists {
			if !initializer.IsHealthy() {
				return false
			}
		}
	}
	return true
}

// RefreshResource 刷新指定资源
func (m *Manager) RefreshResource(ctx context.Context, name string) error {
	initializer, exists := m.registry.Get(name)
	if !exists {
		return fmt.Errorf("resource %s not found", name)
	}

	m.logger.Info(fmt.Sprintf("刷新资源：%s", name))

	if err := initializer.Refresh(ctx); err != nil {
		m.logger.Error(fmt.Sprintf("刷新资源失败：%s，错误：%s", name, err.Error()))
		return fmt.Errorf("failed to refresh resource %s: %w", name, err)
	}

	m.updateStats("refresh", 1)
	m.logger.Info(fmt.Sprintf("资源刷新成功：%s", name))

	return nil
}

// CheckResourceHealth 检查资源健康状态
func (m *Manager) CheckResourceHealth(ctx context.Context, name string) error {
	initializer, exists := m.registry.Get(name)
	if !exists {
		return fmt.Errorf("resource %s not found", name)
	}

	// 检查资源是否过期
	if initializer.IsExpired() {
		return fmt.Errorf("resource %s is expired", name)
	}

	// 检查资源是否健康
	if healthChecker, ok := initializer.(interface {
		IsHealthy() bool
	}); ok {
		if !healthChecker.IsHealthy() {
			return fmt.Errorf("resource %s is unhealthy", name)
		}
	}

	return nil
}

// GetResourceStatus 获取资源状态信息
func (m *Manager) GetResourceStatus(ctx context.Context, name string) map[string]interface{} {
	initializer, exists := m.registry.Get(name)
	if !exists {
		return map[string]interface{}{
			"exists": false,
		}
	}

	status := map[string]interface{}{
		"exists":       true,
		"name":         initializer.Name(),
		"priority":     initializer.Priority(),
		"dependencies": initializer.Dependencies(),
		"ttl":          initializer.TTL().String(),
		"last_refresh": initializer.LastRefreshTime(),
		"is_expired":   initializer.IsExpired(),
	}

	// 检查健康状态
	if healthChecker, ok := initializer.(interface {
		IsHealthy() bool
	}); ok {
		status["is_healthy"] = healthChecker.IsHealthy()
	}

	return status
}

// RefreshAll 刷新所有资源
func (m *Manager) RefreshAll(ctx context.Context) error {
	m.logger.Info("刷新所有资源")

	var errors []error
	for _, name := range m.registry.List() {
		if err := m.RefreshResource(ctx, name); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to refresh some resources: %v", errors)
	}

	m.logger.Info("所有资源刷新完成")
	return nil
}

// isResourceEnabled 检查资源是否启用
func (m *Manager) isResourceEnabled(name string) bool {
	if m.config == nil || m.config.Initializers == nil {
		return true // 默认启用
	}

	if initConfig, exists := m.config.Initializers[name]; exists {
		return initConfig.Enabled
	}

	return true // 如果没有配置，默认启用
}

// initializeResource 初始化单个资源
func (m *Manager) initializeResource(ctx context.Context, name string) error {
	initializer, exists := m.registry.Get(name)
	if !exists {
		return fmt.Errorf("resource %s not found", name)
	}

	m.logger.Info(fmt.Sprintf("初始化资源：%s", name))

	// 获取资源配置
	var config map[string]interface{}
	if m.config != nil && m.config.Initializers != nil {
		if initConfig, exists := m.config.Initializers[name]; exists {
			config = initConfig.Config
			if config == nil {
				config = make(map[string]interface{})
			}
			// 将顶层配置项（如TTL、优先级等）也传递给初始化器
			config["ttl"] = initConfig.TTL
			config["priority"] = initConfig.Priority
			config["enabled"] = initConfig.Enabled
			config["dependencies"] = initConfig.Dependencies
		}
	}

	// 初始化资源
	if err := initializer.Initialize(ctx, config); err != nil {
		return fmt.Errorf("failed to initialize resource %s: %w", name, err)
	}

	m.logger.Info(fmt.Sprintf("资源初始化成功：%s", name))

	return nil
}

// scheduleRefreshTasks 为有TTL的资源安排刷新任务
func (m *Manager) scheduleRefreshTasks() error {
	for _, name := range m.registry.List() {
		// 跳过禁用的资源
		if !m.isResourceEnabled(name) {
			m.logger.Debug(fmt.Sprintf("跳过禁用资源的刷新任务安排：%s", name))
			continue
		}

		initializer, exists := m.registry.Get(name)
		if !exists {
			continue
		}

		ttl := initializer.TTL()
		if ttl > 0 {
			// 计算刷新间隔 - 使用分层策略确保足够的安全边际
			refreshInterval := m.calculateRefreshInterval(ttl)
			if refreshInterval < time.Minute {
				refreshInterval = time.Minute // 最小1分钟
			}

			if err := m.scheduler.ScheduleRefresh(name, refreshInterval); err != nil {
				return fmt.Errorf("failed to schedule refresh for %s: %w", name, err)
			}

			// 计算提前时间用于日志显示
			advance := ttl - refreshInterval
			m.logger.Info("已安排资源刷新任务",
				"resource", name,
				"ttl", ttl.String(),
				"refresh_interval", refreshInterval.String(),
				"advance_time", advance.String(),
				"strategy", m.getRefreshStrategy(ttl),
			)
		}
	}

	return nil
}

// calculateRefreshInterval 计算刷新间隔 - 分层策略确保缓存不过期
func (m *Manager) calculateRefreshInterval(ttl time.Duration) time.Duration {
	// 分层刷新策略：
	// 1. 短TTL（<30分钟）：提前20%刷新，但至少提前2分钟
	// 2. 中等TTL（30分钟-2小时）：提前5-10分钟刷新
	// 3. 长TTL（>2小时）：提前15-30分钟刷新

	switch {
	case ttl <= 30*time.Minute:
		// 短TTL：提前20%，但至少提前2分钟，确保刷新间隔为正数
		advance := time.Duration(float64(ttl) * 0.2)
		if advance < 2*time.Minute {
			advance = 2 * time.Minute
		}
		// 确保刷新间隔不为负数，最小为TTL的一半
		refreshInterval := ttl - advance
		if refreshInterval <= 0 {
			refreshInterval = ttl / 2
			advance = ttl - refreshInterval
		}
		m.logger.Debug("计算短TTL刷新间隔",
			"ttl", ttl.String(),
			"advance", advance.String(),
			"refresh_interval", refreshInterval.String(),
		)
		return refreshInterval

	case ttl <= 2*time.Hour:
		// 中等TTL：提前5-10分钟刷新
		advance := 10 * time.Minute
		if ttl <= time.Hour {
			advance = 5 * time.Minute // 1小时以内提前5分钟
		}
		refreshInterval := ttl - advance
		m.logger.Debug("计算中等TTL刷新间隔",
			"ttl", ttl.String(),
			"advance", advance.String(),
			"refresh_interval", refreshInterval.String(),
		)
		return refreshInterval

	default:
		// 长TTL：提前15-30分钟刷新
		advance := 30 * time.Minute
		if ttl <= 6*time.Hour {
			advance = 15 * time.Minute // 6小时以内提前15分钟
		}
		refreshInterval := ttl - advance
		m.logger.Debug("计算长TTL刷新间隔",
			"ttl", ttl.String(),
			"advance", advance.String(),
			"refresh_interval", refreshInterval.String(),
		)
		return refreshInterval
	}
}

// getRefreshStrategy 获取刷新策略描述
func (m *Manager) getRefreshStrategy(ttl time.Duration) string {
	switch {
	case ttl <= 30*time.Minute:
		return "短TTL策略"
	case ttl <= 2*time.Hour:
		return "中等TTL策略"
	default:
		return "长TTL策略"
	}
}

// updateStats 更新统计信息
func (m *Manager) updateStats(field string, value interface{}) {
	m.statsMu.Lock()
	defer m.statsMu.Unlock()

	switch field {
	case "initialized":
		if v, ok := value.(int64); ok {
			m.stats.InitializedCount += v
		}
	case "failed":
		if v, ok := value.(int64); ok {
			m.stats.FailedCount += v
		}
	case "refresh":
		if v, ok := value.(int64); ok {
			m.stats.RefreshCount += v
		}
	case "last_init_time":
		if v, ok := value.(time.Time); ok {
			m.stats.LastInitTime = v
		}
	}
}

// GetStats 获取统计信息
func (m *Manager) GetStats() map[string]interface{} {
	m.statsMu.RLock()
	defer m.statsMu.RUnlock()

	return map[string]interface{}{
		"initialized_count": m.stats.InitializedCount,
		"failed_count":      m.stats.FailedCount,
		"refresh_count":     m.stats.RefreshCount,
		"last_init_time":    m.stats.LastInitTime,
		"is_initialized":    m.initialized,
		"is_running":        m.running,
		"resource_count":    len(m.registry.List()),
	}
}
