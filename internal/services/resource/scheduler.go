package resource

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go-monitor/pkg/logging"
)

// Scheduler 资源调度器实现
type Scheduler struct {
	// 核心组件
	registry ResourceRegistry
	logger   logging.Logger
	config   *ResourceConfig // 添加配置访问

	// 调度状态
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
	mu      sync.RWMutex

	// 刷新任务管理
	refreshTasks map[string]*refreshTask
	tasksMu      sync.RWMutex

	// 统计信息
	stats struct {
		RefreshCount    int64     `json:"refresh_count"`
		FailedCount     int64     `json:"failed_count"`
		LastRefreshTime time.Time `json:"last_refresh_time"`
	}
	statsMu sync.RWMutex
}

// refreshTask 刷新任务
type refreshTask struct {
	name        string
	interval    time.Duration
	nextRefresh time.Time
	ticker      *time.Ticker
	stopChan    chan struct{}
	running     bool
}

// NewScheduler 创建新的资源调度器
func NewScheduler(registry ResourceRegistry, logger logging.Logger, config *ResourceConfig) ResourceScheduler {

	return &Scheduler{
		registry:     registry,
		logger:       logging.GetLogger("service.resource-scheduler"),
		config:       config,
		refreshTasks: make(map[string]*refreshTask),
	}
}

// Start 启动调度器
func (s *Scheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("scheduler already running")
	}

	s.logger.Info("启动资源调度器")

	// 创建上下文
	s.ctx, s.cancel = context.WithCancel(ctx)
	s.running = true

	// 启动监控协程
	s.wg.Add(1)
	go s.monitorLoop()

	s.logger.Info("资源调度器启动完成")
	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return fmt.Errorf("scheduler not running")
	}

	s.logger.Info("停止资源调度器")

	// 停止所有刷新任务
	s.tasksMu.Lock()
	for name, task := range s.refreshTasks {
		s.stopRefreshTask(task)
		delete(s.refreshTasks, name)
	}
	s.tasksMu.Unlock()

	// 取消上下文并等待协程结束
	s.cancel()
	s.wg.Wait()

	s.running = false
	s.logger.Info("资源调度器已停止")

	return nil
}

// IsRunning 检查调度器是否运行中
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// ScheduleRefresh 安排资源刷新任务
func (s *Scheduler) ScheduleRefresh(name string, interval time.Duration) error {
	s.tasksMu.Lock()
	defer s.tasksMu.Unlock()

	// 检查资源是否存在
	if _, exists := s.registry.Get(name); !exists {
		return fmt.Errorf("resource %s not found", name)
	}

	// 检查是否已存在任务
	if existingTask, exists := s.refreshTasks[name]; exists {
		s.stopRefreshTask(existingTask)
	}

	// 创建新的刷新任务
	task := &refreshTask{
		name:        name,
		interval:    interval,
		nextRefresh: time.Now().Add(interval),
		ticker:      time.NewTicker(interval),
		stopChan:    make(chan struct{}),
		running:     true,
	}

	s.refreshTasks[name] = task

	// 启动刷新协程
	s.wg.Add(1)
	go s.refreshTaskLoop(task)

	s.logger.Info("已安排资源刷新任务",
		"resource", name,
		"interval", interval.String(),
	)

	return nil
}

// UnscheduleRefresh 取消资源刷新任务
func (s *Scheduler) UnscheduleRefresh(name string) error {
	s.tasksMu.Lock()
	defer s.tasksMu.Unlock()

	task, exists := s.refreshTasks[name]
	if !exists {
		return fmt.Errorf("no refresh task found for resource %s", name)
	}

	s.stopRefreshTask(task)
	delete(s.refreshTasks, name)

	s.logger.Info("已取消资源刷新任务",
		"resource", name,
	)

	return nil
}

// TriggerRefresh 立即触发资源刷新
func (s *Scheduler) TriggerRefresh(ctx context.Context, name string) error {
	initializer, exists := s.registry.Get(name)
	if !exists {
		return fmt.Errorf("resource %s not found", name)
	}

	s.logger.Info("触发资源刷新",
		"resource", name,
	)

	if err := initializer.Refresh(ctx); err != nil {
		s.updateStats("failed", 1)
		return fmt.Errorf("failed to refresh resource %s: %w", name, err)
	}

	s.updateStats("refresh", 1)
	s.updateStats("last_refresh_time", time.Now())

	// 更新下次刷新时间
	s.tasksMu.Lock()
	if task, exists := s.refreshTasks[name]; exists {
		task.nextRefresh = time.Now().Add(task.interval)
	}
	s.tasksMu.Unlock()

	s.logger.Info("资源刷新完成",
		"resource", name,
	)

	return nil
}

// GetScheduleStatus 获取调度状态
func (s *Scheduler) GetScheduleStatus() map[string]time.Time {
	s.tasksMu.RLock()
	defer s.tasksMu.RUnlock()

	status := make(map[string]time.Time)
	for name, task := range s.refreshTasks {
		status[name] = task.nextRefresh
	}

	return status
}

// GetNextRefreshTime 获取指定资源的下次刷新时间
func (s *Scheduler) GetNextRefreshTime(name string) (time.Time, bool) {
	s.tasksMu.RLock()
	defer s.tasksMu.RUnlock()

	if task, exists := s.refreshTasks[name]; exists {
		return task.nextRefresh, true
	}

	return time.Time{}, false
}

// monitorLoop 监控循环
func (s *Scheduler) monitorLoop() {
	defer s.wg.Done()

	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.checkExpiredResources()
		}
	}
}

// checkExpiredResources 检查过期资源
func (s *Scheduler) checkExpiredResources() {
	for _, name := range s.registry.List() {
		// 跳过禁用的资源
		if !s.isResourceEnabled(name) {
			continue
		}

		initializer, exists := s.registry.Get(name)
		if !exists {
			continue
		}

		if initializer.IsExpired() {
			s.logger.Warn("检测到过期资源",
				"resource", name,
				"last_refresh", initializer.LastRefreshTime(),
				"ttl", initializer.TTL().String(),
			)

			// 立即刷新过期资源，无论是否有定时任务
			go func(resourceName string) {
				ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
				defer cancel()

				s.logger.Info("触发过期资源自动刷新",
					"resource", resourceName,
				)

				if err := s.TriggerRefresh(ctx, resourceName); err != nil {
					s.logger.Error("自动刷新过期资源失败",
						"resource", resourceName,
						"error", err.Error(),
					)
				} else {
					s.logger.Info("过期资源自动刷新成功",
						"resource", resourceName,
					)
				}
			}(name)
		}
	}
}

// refreshTaskLoop 刷新任务循环
func (s *Scheduler) refreshTaskLoop(task *refreshTask) {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-task.stopChan:
			return
		case <-task.ticker.C:
			// 执行刷新
			ctx, cancel := context.WithTimeout(s.ctx, 30*time.Second)
			if err := s.TriggerRefresh(ctx, task.name); err != nil {
				s.logger.Error("定时刷新资源失败",
					"resource", task.name,
					"error", err.Error(),
				)
			}
			cancel()
		}
	}
}

// stopRefreshTask 停止刷新任务
func (s *Scheduler) stopRefreshTask(task *refreshTask) {
	if task.running {
		task.ticker.Stop()
		close(task.stopChan)
		task.running = false
	}
}

// isResourceEnabled 检查资源是否启用
func (s *Scheduler) isResourceEnabled(name string) bool {
	if s.config == nil || s.config.Initializers == nil {
		return true // 默认启用
	}

	if initConfig, exists := s.config.Initializers[name]; exists {
		return initConfig.Enabled
	}

	return true // 如果没有配置，默认启用
}

// updateStats 更新统计信息
func (s *Scheduler) updateStats(field string, value interface{}) {
	s.statsMu.Lock()
	defer s.statsMu.Unlock()

	switch field {
	case "refresh":
		if v, ok := value.(int64); ok {
			s.stats.RefreshCount += v
		}
	case "failed":
		if v, ok := value.(int64); ok {
			s.stats.FailedCount += v
		}
	case "last_refresh_time":
		if v, ok := value.(time.Time); ok {
			s.stats.LastRefreshTime = v
		}
	}
}

// GetStats 获取统计信息
func (s *Scheduler) GetStats() map[string]interface{} {
	s.statsMu.RLock()
	defer s.statsMu.RUnlock()

	return map[string]interface{}{
		"refresh_count":     s.stats.RefreshCount,
		"failed_count":      s.stats.FailedCount,
		"last_refresh_time": s.stats.LastRefreshTime,
		"is_running":        s.running,
		"active_tasks":      len(s.refreshTasks),
	}
}
