package notification

import (
	"go-monitor/internal/models"
	"time"
)

// NotificationType 通知类型枚举 - 对应Python版本的NotificationType
type NotificationType string

const (
	// NotificationTypeStockChange 库存变化通知 - 对应Python的"instock"
	NotificationTypeStockChange NotificationType = "instock"
	// NotificationTypeError 错误通知
	NotificationTypeError NotificationType = "error"
	// NotificationTypeSystem 系统通知
	NotificationTypeSystem NotificationType = "system"
)

// NotificationPriority 通知优先级枚举 - 对应Python版本的NotificationPriority
type NotificationPriority string

const (
	// PriorityLow 低优先级
	PriorityLow NotificationPriority = "low"
	// PriorityNormal 普通优先级
	PriorityNormal NotificationPriority = "normal"
	// PriorityHigh 高优先级
	PriorityHigh NotificationPriority = "high"
)

// NotificationGroup 通知组配置 - 对应Python版本的group_info结构
type NotificationGroup struct {
	Name       string                 `yaml:"name" json:"name"`
	ID         string                 `yaml:"id" json:"id,omitempty"`
	WebhookURL string                 `yaml:"webhook_url" json:"webhook_url,omitempty"`
	Info       map[string]interface{} `yaml:"info" json:"info,omitempty"`
	Delay      int                    `yaml:"delay" json:"delay,omitempty"` // 延迟发送时间（毫秒）
}

// NotificationTypeConfig 通知类型配置 - 对应Python版本的notification_type_config
type NotificationTypeConfig struct {
	Exchange   string               `json:"exchange"`
	RoutingKey string               `json:"routing_key"`
	Priority   NotificationPriority `json:"priority"`
}

// SendOptions 发送选项 - 对应Python版本的options参数
type SendOptions struct {
	Exchange  string                 `json:"exchange,omitempty"`
	Priority  NotificationPriority   `json:"priority,omitempty"`
	Delay     int                    `json:"delay,omitempty"` // 延迟时间（毫秒）
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Timestamp time.Time              `json:"timestamp,omitempty"`
}

// 使用标准的models包中的类型，避免重复定义
type ProductItem = models.ProductItem
type NotificationItem = models.NotificationItem
type ErrorLogItem = models.ErrorLogItem

// String 返回优先级的字符串表示
func (p NotificationPriority) String() string {
	return string(p)
}

// String 返回通知类型的字符串表示
func (nt NotificationType) String() string {
	return string(nt)
}

// Value 返回优先级的数值表示（用于排序）
func (p NotificationPriority) Value() int {
	switch p {
	case PriorityLow:
		return 0
	case PriorityNormal:
		return 1
	case PriorityHigh:
		return 2
	default:
		return 1
	}
}
