package notification

import (
	"context"
	"fmt"
	"go-monitor/internal/models"
	"go-monitor/internal/services/notification/handlers"
)

// Service 通知服务 - 简化版本，只支持Discord
type Service struct {
	// 配置
	config map[string]interface{}

	// 通知处理器
	discordHandler *handlers.DiscordNotificationHandler

	// 通知组配置
	notificationGroups map[string]interface{}

	// 状态标志
	enabled         bool
	discordEnabled  bool
	rabbitmqEnabled bool
}

// NewService 创建新的通知服务
func NewService(config map[string]interface{}) *Service {
	service := &Service{
		config: config,
	}

	// 解析基础配置
	if enabled, exists := config["enabled"]; exists {
		if enabledBool, ok := enabled.(bool); ok {
			service.enabled = enabledBool
		}
	} else {
		service.enabled = true // 默认启用
	}

	if discordConfig, exists := config["discord"].(map[string]interface{}); exists {
		if notificationGroups, exists := discordConfig["notification_groups"]; exists {
			if groupsMap, ok := notificationGroups.(map[string]interface{}); ok {
				service.notificationGroups = groupsMap
			}
		}
		if service.notificationGroups == nil {
			service.notificationGroups = make(map[string]interface{})
		}
		service.discordHandler = handlers.NewDiscordNotificationHandler(discordConfig)
	}

	return service
}

// SendNotification 发送通知的统一接口 - 简化版本，直接调用Discord
func (s *Service) SendNotification(ctx context.Context, notificationType NotificationType, data interface{}, groups []string, options *SendOptions) (string, error) {
	// 直接使用Discord发送通知
	if s.discordHandler == nil {
		return "", fmt.Errorf("Discord处理器未初始化")
	}

	// 优化：避免不必要的map创建
	var optionsMap map[string]interface{}
	if options != nil && options.Metadata != nil {
		optionsMap = options.Metadata
	}

	result, err := s.discordHandler.SendNotification(ctx, data, groups, optionsMap)
	if err != nil {
		return "", err
	}

	if result.Success && len(result.MessageIDs) > 0 {
		return result.MessageIDs[0], nil
	}

	return "", fmt.Errorf("通知发送失败")
}

// SendDiscordNotification 发送Discord通知的便捷方法
func (s *Service) SendDiscordNotification(ctx context.Context, data interface{}, groups []string, options *SendOptions) (*handlers.SendResult, error) {
	if s.discordHandler == nil {
		return &handlers.SendResult{
			Success: false,
			Errors:  []string{"Discord处理器未初始化"},
		}, fmt.Errorf("Discord处理器未初始化")
	}

	// 优化：只在需要时创建map
	var optionsMap map[string]interface{}
	if options != nil && (options.Metadata != nil || options.Delay > 0) {
		optionsMap = make(map[string]interface{}, 2) // 预分配容量
		if options.Metadata != nil {
			optionsMap["metadata"] = options.Metadata
		}
		if options.Delay > 0 {
			optionsMap["delay"] = options.Delay
		}
	}

	return s.discordHandler.SendNotification(ctx, data, groups, optionsMap)
}

// SendDiscordProductNotification 发送Discord产品通知的便捷方法
func (s *Service) SendDiscordProductNotification(ctx context.Context, product *models.ProductItem, groups []string, options *SendOptions) (*handlers.SendResult, error) {
	return s.SendDiscordNotification(ctx, product, groups, options)
}

// SendDiscordErrorNotification 发送Discord错误通知的便捷方法
func (s *Service) SendDiscordErrorNotification(ctx context.Context, errorItem *models.ErrorLogItem, groups []string, options *SendOptions) (*handlers.SendResult, error) {
	return s.SendDiscordNotification(ctx, errorItem, groups, options)
}

// SendDiscordSystemNotification 发送Discord系统通知的便捷方法
func (s *Service) SendDiscordSystemNotification(ctx context.Context, title, message, level string, groups []string, options *SendOptions) (*handlers.SendResult, error) {
	data := map[string]interface{}{
		"title":   title,
		"message": message,
		"level":   level,
	}
	return s.SendDiscordNotification(ctx, data, groups, options)
}

// Close 关闭通知服务
func (s *Service) Close() error {
	// 关闭Discord处理器
	if s.discordHandler != nil {
		return s.discordHandler.Close()
	}
	return nil
}

// ServiceStatus 服务状态枚举
type ServiceStatus int

const (
	ServiceStatusStopped ServiceStatus = iota
	ServiceStatusStarting
	ServiceStatusRunning
	ServiceStatusStopping
	ServiceStatusError
)

// String 返回服务状态的字符串表示
func (s ServiceStatus) String() string {
	switch s {
	case ServiceStatusStopped:
		return "stopped"
	case ServiceStatusStarting:
		return "starting"
	case ServiceStatusRunning:
		return "running"
	case ServiceStatusStopping:
		return "stopping"
	case ServiceStatusError:
		return "error"
	default:
		return "unknown"
	}
}

// GetServiceStatus 获取服务状态
func (s *Service) GetServiceStatus() ServiceStatus {
	// 简化：如果Discord处理器可用就认为是运行状态
	if s.discordEnabled && s.discordHandler != nil {
		return ServiceStatusRunning
	}
	return ServiceStatusStopped
}

// IsHealthy 检查服务是否健康
func (s *Service) IsHealthy() bool {
	// 简化：检查Discord处理器是否可用
	return s.discordEnabled && s.discordHandler != nil
}

// GetStats 获取服务统计信息
func (s *Service) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"service_status": s.GetServiceStatus().String(),
		"healthy":        s.IsHealthy(),
		"config": map[string]interface{}{
			"discord_enabled":  s.discordEnabled,
			"rabbitmq_enabled": s.rabbitmqEnabled,
		},
	}
}

// SendNotificationLegacy 兼容旧接口的通知发送方法
func (s *Service) SendNotificationLegacy(ctx context.Context, item interface{}, groups []string) error {
	// 根据item类型确定通知类型
	var notificationType NotificationType

	switch v := item.(type) {
	case *models.NotificationItem:
		// 根据NotificationItem的内容确定类型
		if v.ProductData.InStock {
			notificationType = NotificationTypeStockChange
		} else {
			notificationType = NotificationTypeSystem
		}
		// 使用ProductData作为实际数据
		_, err := s.SendNotification(ctx, notificationType, &v.ProductData, groups, nil)
		return err
	case *models.ProductItem:
		notificationType = NotificationTypeStockChange
		_, err := s.SendNotification(ctx, notificationType, v, groups, nil)
		return err
	case *models.ErrorLogItem:
		notificationType = NotificationTypeError
		_, err := s.SendNotification(ctx, notificationType, v, groups, nil)
		return err
	default:
		notificationType = NotificationTypeSystem
		_, err := s.SendNotification(ctx, notificationType, v, groups, nil)
		return err
	}
}
