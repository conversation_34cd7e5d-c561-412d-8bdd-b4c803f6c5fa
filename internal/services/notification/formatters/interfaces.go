package formatters

import (
	"go-monitor/internal/models"
)

// MessageFormatter 消息格式化器接口
type MessageFormatter interface {
	// FormatProductNotification 格式化产品通知
	FormatProductNotification(item interface{}, groupConfig map[string]interface{}) (map[string]interface{}, error)
	
	// FormatErrorNotification 格式化错误通知
	FormatErrorNotification(errorItem *models.ErrorLogItem, groupConfig map[string]interface{}) (map[string]interface{}, error)
	
	// FormatSystemNotification 格式化系统通知
	FormatSystemNotification(title, message, level string, groupConfig map[string]interface{}) (map[string]interface{}, error)
	
	// FormatSimpleMessage 格式化简单消息
	FormatSimpleMessage(message string, groupConfig map[string]interface{}) (map[string]interface{}, error)
	
	// ValidateMessage 验证消息格式
	ValidateMessage(message map[string]interface{}) bool
}

// DiscordMessageData Discord消息数据结构
type DiscordMessageData struct {
	Content   string                   `json:"content,omitempty"`
	Embeds    []map[string]interface{} `json:"embeds,omitempty"`
	Username  string                   `json:"username,omitempty"`
	AvatarURL string                   `json:"avatar_url,omitempty"`
}

// DiscordEmbed Discord嵌入消息结构
type DiscordEmbed struct {
	Title       string                   `json:"title,omitempty"`
	Description string                   `json:"description,omitempty"`
	Color       int                      `json:"color,omitempty"`
	Timestamp   string                   `json:"timestamp,omitempty"`
	Footer      map[string]interface{}   `json:"footer,omitempty"`
	Fields      []map[string]interface{} `json:"fields,omitempty"`
	Image       map[string]interface{}   `json:"image,omitempty"`
	Thumbnail   map[string]interface{}   `json:"thumbnail,omitempty"`
}
