package formatters

import (
	"fmt"
	"go-monitor/internal/models"
	"regexp"
	"strings"
	"time"
)

// DiscordMessageFormatter Discord消息格式化器 - 对应Python版本的DiscordMessageFormatter
type DiscordMessageFormatter struct {
	config    map[string]interface{}
	templates map[string]interface{}
	colors    map[string]int
}

// NewDiscordMessageFormatter 创建新的Discord消息格式化器
func NewDiscordMessageFormatter(config map[string]interface{}) *DiscordMessageFormatter {
	templates, _ := config["templates"].(map[string]interface{})

	return &DiscordMessageFormatter{
		config:    config,
		templates: templates,
		colors: map[string]int{
			"success": 0x00dd00,
			"error":   0xff0000,
			"warning": 0xffff00,
			"info":    0x0099ff,
		},
	}
}

// FormatProductNotification 格式化产品通知 - 对应Python版本的format_product_notification
func (f *DiscordMessageFormatter) FormatProductNotification(item interface{}, groupConfig map[string]interface{}) (map[string]interface{}, error) {
	var product *models.ProductItem

	// 处理不同类型的输入
	switch v := item.(type) {
	case *models.ProductItem:
		product = v
	case models.ProductItem:
		product = &v
	case *models.NotificationItem:
		product = &v.ProductData
	case models.NotificationItem:
		product = &v.ProductData
	default:
		return nil, fmt.Errorf("不支持的产品数据类型: %T", item)
	}

	// 获取模板
	spiderType := strings.ToLower(product.Platform)
	template := f.getTemplate(spiderType)

	// 准备变量
	variables := f.prepareProductVariables(product, groupConfig)

	// 模板替换
	result := f.replaceVariables(template, variables)

	// 动态颜色设置
	if embeds, ok := result["embeds"].([]map[string]interface{}); ok && len(embeds) > 0 {
		color := 0x00dd00 // 绿色 - 有库存
		if !product.InStock {
			color = 0xff4444 // 红色 - 无库存
		}
		embeds[0]["color"] = color
	}

	return result, nil
}

// FormatErrorNotification 格式化错误通知 - 对应Python版本的format_error_notification
func (f *DiscordMessageFormatter) FormatErrorNotification(errorItem *models.ErrorLogItem, groupConfig map[string]interface{}) (map[string]interface{}, error) {
	levelColors := map[string]int{
		"error":   f.colors["error"],
		"warning": f.colors["warning"],
		"info":    f.colors["info"],
	}
	levelEmojis := map[string]string{
		"error":   "🚨",
		"warning": "⚠️",
		"info":    "ℹ️",
	}

	color := levelColors[strings.ToLower(errorItem.ErrorLevel)]
	if color == 0 {
		color = f.colors["error"]
	}

	emoji := levelEmojis[strings.ToLower(errorItem.ErrorLevel)]
	if emoji == "" {
		emoji = "🚨"
	}

	// 限制描述长度（Discord限制）
	description := errorItem.ErrorMessage
	if len(description) > 2048 {
		description = description[:2048]
	}

	embed := map[string]interface{}{
		"title":       fmt.Sprintf("%s 系统错误", emoji),
		"description": description,
		"color":       color,
		"timestamp":   time.Now().UTC().Format(time.RFC3339),
		"fields": []map[string]interface{}{
			{
				"name":   "🔴 错误级别",
				"value":  strings.ToUpper(errorItem.ErrorLevel),
				"inline": true,
			},
			{
				"name":   "🏷️ 错误类型",
				"value":  errorItem.ErrorType,
				"inline": true,
			},
			{
				"name":   "⏰ 发生时间",
				"value":  errorItem.Timestamp.Format("2006-01-02 15:04:05"),
				"inline": true,
			},
		},
		"footer": map[string]interface{}{
			"text": fmt.Sprintf("监控系统 • 来源: %s", errorItem.Source),
		},
	}

	return map[string]interface{}{
		"embeds": []map[string]interface{}{embed},
	}, nil
}

// FormatSystemNotification 格式化系统通知
func (f *DiscordMessageFormatter) FormatSystemNotification(title, message, level string, groupConfig map[string]interface{}) (map[string]interface{}, error) {
	levelColors := map[string]int{
		"error":   f.colors["error"],
		"warning": f.colors["warning"],
		"info":    f.colors["info"],
		"success": f.colors["success"],
	}
	levelEmojis := map[string]string{
		"error":   "🚨",
		"warning": "⚠️",
		"info":    "ℹ️",
		"success": "✅",
	}

	color := levelColors[strings.ToLower(level)]
	if color == 0 {
		color = f.colors["info"]
	}

	emoji := levelEmojis[strings.ToLower(level)]
	if emoji == "" {
		emoji = "ℹ️"
	}

	embed := map[string]interface{}{
		"title":       fmt.Sprintf("%s %s", emoji, title),
		"description": message,
		"color":       color,
		"timestamp":   time.Now().UTC().Format(time.RFC3339),
		"footer": map[string]interface{}{
			"text": "监控系统通知",
		},
	}

	return map[string]interface{}{
		"embeds": []map[string]interface{}{embed},
	}, nil
}

// FormatSimpleMessage 格式化简单消息
func (f *DiscordMessageFormatter) FormatSimpleMessage(message string, groupConfig map[string]interface{}) (map[string]interface{}, error) {
	return map[string]interface{}{
		"content": message,
	}, nil
}

// ValidateMessage 验证消息格式
func (f *DiscordMessageFormatter) ValidateMessage(message map[string]interface{}) bool {
	// 检查是否有文本内容
	if content, ok := message["content"].(string); ok && content != "" {
		return true
	}

	// 检查是否有有效的嵌入消息
	if embedsInterface, exists := message["embeds"]; exists {
		// 处理不同类型的embeds
		var embeds []interface{}

		switch v := embedsInterface.(type) {
		case []interface{}:
			embeds = v
		case []map[string]interface{}:
			// 转换为[]interface{}
			for _, embed := range v {
				embeds = append(embeds, embed)
			}
		default:
			return false
		}

		// 检查是否有至少一个有效的嵌入消息
		for _, embedInterface := range embeds {
			if embedMap, ok := embedInterface.(map[string]interface{}); ok {
				if f.isValidEmbed(embedMap) {
					return true
				}
			}
		}
	}

	return false
}

// isValidEmbed 检查嵌入消息是否有效
func (f *DiscordMessageFormatter) isValidEmbed(embed map[string]interface{}) bool {
	// 检查是否有任何有效内容
	if title, ok := embed["title"].(string); ok && title != "" {
		return true
	}

	if description, ok := embed["description"].(string); ok && description != "" {
		return true
	}

	if fields, ok := embed["fields"].([]interface{}); ok && len(fields) > 0 {
		// 检查是否有至少一个有效字段
		for _, fieldInterface := range fields {
			if fieldMap, ok := fieldInterface.(map[string]interface{}); ok {
				if name, nameOk := fieldMap["name"].(string); nameOk && name != "" {
					if value, valueOk := fieldMap["value"].(string); valueOk && value != "" {
						return true
					}
				}
			}
		}
	}

	if footer, ok := embed["footer"].(map[string]interface{}); ok {
		if text, textOk := footer["text"].(string); textOk && text != "" {
			return true
		}
	}

	if author, ok := embed["author"].(map[string]interface{}); ok {
		if name, nameOk := author["name"].(string); nameOk && name != "" {
			return true
		}
	}

	// 如果有图片或缩略图，也认为是有效的
	if _, ok := embed["image"]; ok {
		return true
	}

	if _, ok := embed["thumbnail"]; ok {
		return true
	}

	return false
}

// getTemplate 获取模板
func (f *DiscordMessageFormatter) getTemplate(spiderType string) map[string]interface{} {
	if f.templates == nil {
		return f.getDefaultTemplate()
	}

	if template, ok := f.templates[spiderType].(map[string]interface{}); ok {
		return f.normalizeTemplate(template)
	}

	if defaultTemplate, ok := f.templates["default"].(map[string]interface{}); ok {
		return f.normalizeTemplate(defaultTemplate)
	}

	return f.getDefaultTemplate()
}

// normalizeTemplate 标准化模板格式，将embed转换为embeds
func (f *DiscordMessageFormatter) normalizeTemplate(template map[string]interface{}) map[string]interface{} {
	// 如果已经有embeds字段，直接返回
	if _, hasEmbeds := template["embeds"]; hasEmbeds {
		return template
	}

	// 如果有embed字段（单数），转换为embeds（复数）
	if embed, hasEmbed := template["embed"]; hasEmbed {
		if embedMap, ok := embed.(map[string]interface{}); ok {
			// 创建新的模板，将单个embed包装成embeds数组
			normalizedTemplate := make(map[string]interface{})

			// 复制其他字段（如content等）
			for k, v := range template {
				if k != "embed" {
					normalizedTemplate[k] = v
				}
			}

			// 将embed转换为embeds数组
			normalizedTemplate["embeds"] = []map[string]interface{}{embedMap}

			return normalizedTemplate
		}
	}

	// 如果既没有embeds也没有embed，返回原模板
	return template
}

// getDefaultTemplate 获取默认模板
func (f *DiscordMessageFormatter) getDefaultTemplate() map[string]interface{} {
	return map[string]interface{}{
		"embeds": []map[string]interface{}{
			{
				"title":       "{{title}}",
				"url":         "{{url}}",
				"description": "平台: {{platform}} | 国家: {{country}}",
				"color":       0x00dd00,
				"timestamp":   "{{timestamp}}",
				"fields": []map[string]interface{}{
					{
						"name":   "💰 价格",
						"value":  "{{price}} {{currency}}",
						"inline": true,
					},
					{
						"name":   "📦 库存",
						"value":  "{{stock}}",
						"inline": true,
					},
					{
						"name":   "✅ 状态",
						"value":  "{{availability}}",
						"inline": true,
					},
				},
				"footer": map[string]interface{}{
					"text": "监控系统 • {{name}}",
				},
			},
		},
	}
}

// prepareProductVariables 准备产品变量 - 对应Python版本的变量准备逻辑
func (f *DiscordMessageFormatter) prepareProductVariables(product *models.ProductItem, groupConfig map[string]interface{}) map[string]string {
	variables := map[string]string{
		"title":        cleanTitle(product.Title),
		"url":          product.URL,
		"imageUrl":     getStringValue(product.ImageURL),
		"productId":    product.ProductID,
		"skuId":        getStringValue(product.SkuID),
		"platform":     strings.ToUpper(product.Platform),
		"country":      strings.ToUpper(product.Country),
		"siteUrl":      product.SiteURL,
		"atcLink":      getStringValue(product.AtcLink),
		"releaseDate":  getStringValue(product.ReleaseDate),
		"availability": product.Availability,
		"price":        fmt.Sprintf("%.2f", product.Price),
		"currency":     product.Currency,
		"stock":        fmt.Sprintf("%d", product.Stock),
		"inStock":      fmt.Sprintf("%t", product.InStock),
		"timestamp":    time.Now().UTC().Format(time.RFC3339),
		"name":         product.Name,
	}

	// URL替换逻辑 - 根据实际情况替换
	if groupConfig != nil {
		// 直接从 groupConfig 获取 url 和 name 字段（extractGroupConfig 已经提取了 info 内容）
		if newURL, ok := groupConfig["url"].(string); ok && newURL != "" {
			if product.SiteURL != "" {
				variables["url"] = strings.Replace(product.URL, product.SiteURL, newURL, 1)
			}
			variables["siteUrl"] = newURL
		}

		// 从 groupConfig 获取组名称
		if name, ok := groupConfig["name"].(string); ok && name != "" {
			variables["name"] = name
		}

		// 添加组配置中的其他字段
		excludeKeys := map[string]bool{"webhook_url": true, "name": true, "url": true}
		for key, value := range groupConfig {
			if !excludeKeys[key] && value != nil {
				variables[key] = fmt.Sprintf("%v", value)
			}
		}
	}

	return variables
}

// replaceVariables 替换模板变量 - 对应Python版本的模板替换逻辑
func (f *DiscordMessageFormatter) replaceVariables(template map[string]interface{}, variables map[string]string) map[string]interface{} {
	result := make(map[string]interface{})

	for key, value := range template {
		result[key] = f.replaceValue(value, variables)
	}

	return result
}

// replaceValue 递归替换值中的变量
func (f *DiscordMessageFormatter) replaceValue(value interface{}, variables map[string]string) interface{} {
	switch v := value.(type) {
	case string:
		return f.replaceStringVariables(v, variables)
	case map[string]interface{}:
		result := make(map[string]interface{})
		for k, val := range v {
			result[k] = f.replaceValue(val, variables)
		}
		return result
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, val := range v {
			result[i] = f.replaceValue(val, variables)
		}
		return result
	case []map[string]interface{}:
		result := make([]map[string]interface{}, len(v))
		for i, val := range v {
			if replaced := f.replaceValue(val, variables); replaced != nil {
				if replacedMap, ok := replaced.(map[string]interface{}); ok {
					result[i] = replacedMap
				}
			}
		}
		return result
	default:
		return value
	}
}

// replaceStringVariables 替换字符串中的变量
func (f *DiscordMessageFormatter) replaceStringVariables(str string, variables map[string]string) string {
	result := str
	for key, value := range variables {
		// 支持两种格式：{variable} 和 {{variable}}
		// 优先处理单花括号格式（Python兼容）
		singleBracePlaceholder := fmt.Sprintf("{%s}", key)
		doubleBracePlaceholder := fmt.Sprintf("{{%s}}", key)

		// 先替换单花括号格式
		result = strings.ReplaceAll(result, singleBracePlaceholder, value)
		// 再替换双花括号格式
		result = strings.ReplaceAll(result, doubleBracePlaceholder, value)
	}
	return result
}

// cleanTitle 清理标题 - 对应Python版本的标题清理逻辑
func cleanTitle(title string) string {
	// 清理多余的空白字符
	re := regexp.MustCompile(`\s+`)
	return strings.TrimSpace(re.ReplaceAllString(title, " "))
}

// getStringValue 安全获取字符串指针的值
func getStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}
