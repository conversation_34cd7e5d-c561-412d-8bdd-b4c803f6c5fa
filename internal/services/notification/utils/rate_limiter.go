package utils

import (
	"sync"
	"time"
)

// RateLimiter 速率限制器
type RateLimiter struct {
	requestsPerMinute int
	requestTimes      []time.Time
	mutex             sync.Mutex
}

// NewRateLimiter 创建新的速率限制器
func NewRateLimiter(requestsPerMinute int) *RateLimiter {
	return &RateLimiter{
		requestsPerMinute: requestsPerMinute,
		requestTimes:      make([]time.Time, 0),
	}
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow() bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	// 清理1分钟前的记录
	cutoff := now.Add(-time.Minute)
	newTimes := make([]time.Time, 0)
	for _, t := range rl.requestTimes {
		if t.After(cutoff) {
			newTimes = append(newTimes, t)
		}
	}
	rl.requestTimes = newTimes

	if len(rl.requestTimes) >= rl.requestsPerMinute {
		return false
	}

	rl.requestTimes = append(rl.requestTimes, now)
	return true
}

// WaitForNext 等待下一个可用的请求时间
func (rl *RateLimiter) WaitForNext() time.Duration {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	if len(rl.requestTimes) < rl.requestsPerMinute {
		return 0
	}

	// 找到最早的请求时间
	oldest := rl.requestTimes[0]
	for _, t := range rl.requestTimes {
		if t.Before(oldest) {
			oldest = t
		}
	}

	// 计算需要等待的时间
	waitUntil := oldest.Add(time.Minute)
	now := time.Now()
	if waitUntil.After(now) {
		return waitUntil.Sub(now)
	}

	return 0
}
