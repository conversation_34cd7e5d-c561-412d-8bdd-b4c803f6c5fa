package utils

import (
	"context"
	"fmt"
	"time"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries        int
	InitialDelay      time.Duration
	BackoffMultiplier float64
	MaxDelay          time.Duration
}

// DefaultRetryConfig 默认重试配置
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxRetries:        3,
		InitialDelay:      1 * time.Second,
		BackoffMultiplier: 2.0,
		MaxDelay:          30 * time.Second,
	}
}

// RetryFunc 重试函数类型
type RetryFunc func() error

// RetryWithBackoff 带退避的重试
func RetryWithBackoff(ctx context.Context, config RetryConfig, fn RetryFunc) error {
	var lastErr error

	for attempt := 0; attempt < config.MaxRetries; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 执行函数
		err := fn()
		if err == nil {
			return nil // 成功
		}

		lastErr = err

		// 如果是最后一次尝试，不需要等待
		if attempt == config.MaxRetries-1 {
			break
		}

		// 计算延迟时间
		delay := time.Duration(float64(config.InitialDelay) *
			float64(attempt+1) * config.BackoffMultiplier)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}

		// 等待
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续下一次尝试
		}
	}

	return fmt.Errorf("重试失败，最后错误: %w", lastErr)
}

// IsRetryableError 判断错误是否可重试 - 增强版本
func IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// 网络相关错误通常可重试
	networkPatterns := []string{
		"connection refused",
		"connection reset",
		"connection timeout",
		"timeout",
		"network",
		"temporary failure",
		"no such host",
		"i/o timeout",
		"context deadline exceeded",
		"EOF",
		"broken pipe",
	}

	for _, pattern := range networkPatterns {
		if contains(errStr, pattern) {
			return true
		}
	}

	// HTTP 5xx 错误通常可重试
	if contains(errStr, "HTTP 5") {
		return true
	}

	// 速率限制错误可重试
	if contains(errStr, "429") || contains(errStr, "rate limit") {
		return true
	}

	// HTTP 408 请求超时可重试
	if contains(errStr, "HTTP 408") {
		return true
	}

	return false
}

// IsRetryableHTTPStatus 判断HTTP状态码是否可重试
func IsRetryableHTTPStatus(statusCode int) bool {
	switch statusCode {
	case 429: // 速率限制
		return true
	case 408: // 请求超时
		return true
	case 500, 502, 503, 504: // 服务器错误
		return true
	default:
		// 4xx 客户端错误通常不可重试（除了429和408）
		if statusCode >= 400 && statusCode < 500 {
			return false
		}
		// 5xx 服务器错误可重试
		if statusCode >= 500 {
			return true
		}
		return false
	}
}

// contains 检查字符串是否包含子字符串（简单实现）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr || (len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				containsInMiddle(s, substr))))
}

// containsInMiddle 检查字符串中间是否包含子字符串
func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
