package handlers

import (
	"context"
	"go-monitor/internal/models"
)

// NotificationHandler 通知处理器接口
type NotificationHandler interface {
	// SendNotification 发送通知
	SendNotification(ctx context.Context, data interface{}, groups []string, options map[string]interface{}) (*SendResult, error)
}

// ProductNotificationHandler 产品通知处理器接口
type ProductNotificationHandler interface {
	// SendProductNotification 发送产品通知
	SendProductNotification(ctx context.Context, product *models.ProductItem, groups []string, options map[string]interface{}) (*SendResult, error)
}

// ErrorNotificationHandler 错误通知处理器接口
type ErrorNotificationHandler interface {
	// SendErrorNotification 发送错误通知
	SendErrorNotification(ctx context.Context, errorItem *models.ErrorLogItem, groups []string, options map[string]interface{}) (*SendResult, error)
}

// SystemNotificationHandler 系统通知处理器接口
type SystemNotificationHandler interface {
	// SendSystemNotification 发送系统通知
	SendSystemNotification(ctx context.Context, title, message, level string, groups []string, options map[string]interface{}) (*SendResult, error)
}

// SendResult 发送结果
type SendResult struct {
	Success    bool                   `json:"success"`
	MessageIDs []string               `json:"message_ids,omitempty"`
	Errors     []string               `json:"errors,omitempty"`
	Results    []GroupResult          `json:"results,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// GroupResult 组发送结果
type GroupResult struct {
	Group     string `json:"group"`
	ChannelID string `json:"channel_id"`
	Status    string `json:"status"` // success, failed, error
	MessageID string `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
	WebhookURL string `json:"webhook_url,omitempty"`
}
