package handlers

import (
	"context"
	"fmt"
	"go-monitor/internal/models"
	"go-monitor/internal/services/notification/clients"
	"go-monitor/internal/services/notification/formatters"
	"go-monitor/pkg/logging"
	"regexp"
	"strings"
	"time"
)

// DiscordNotificationHandler Discord通知处理器 - 对应Python版本的discord_webhook.py
type DiscordNotificationHandler struct {
	client    *clients.DiscordWebhookClient
	formatter *formatters.DiscordMessageFormatter
	config    map[string]interface{}
	logger    logging.Logger
}

// NewDiscordNotificationHandler 创建新的Discord通知处理器
func NewDiscordNotificationHandler(config map[string]interface{}) *DiscordNotificationHandler {
	discordConfig := extractDiscordConfig(config)

	return &DiscordNotificationHandler{
		client:    clients.NewDiscordWebhookClient(discordConfig),
		formatter: formatters.NewDiscordMessageFormatter(config),
		config:    config,
		logger:    logging.GetLogger("通知"),
	}
}

// SendNotification 发送通知 - 对应Python版本的send_discord_notification
func (h *DiscordNotificationHandler) SendNotification(ctx context.Context, data interface{}, groups []string, options map[string]interface{}) (*SendResult, error) {
	// 检查Discord是否启用
	if enabled, ok := h.config["enabled"].(bool); !ok || !enabled {
		return &SendResult{
			Success: false,
			Errors:  []string{"Discord通知未启用"},
		}, fmt.Errorf("Discord通知未启用")
	}

	if len(groups) == 0 {
		groups = []string{"default"}
	}

	results := make([]GroupResult, 0, len(groups))
	messageIDs := make([]string, 0)
	errors := make([]string, 0)

	// 处理每个组
	for _, group := range groups {
		result := GroupResult{
			Group:     group,
			ChannelID: group,
			Status:    "failed",
		}

		// 获取组信息和webhook URL
		groupInfo := h.getGroupInfo(group)
		if groupInfo == nil {
			result.Error = fmt.Sprintf("无效的Discord通知组: %s (组配置不存在)", group)
			results = append(results, result)
			errors = append(errors, result.Error)
			continue
		}

		webhookURL := h.extractWebhookURL(groupInfo)
		if webhookURL == "" {
			result.Error = fmt.Sprintf("Discord通知组 %s 缺少webhook_url配置 (groupInfo: %+v)", group, groupInfo)
			results = append(results, result)
			errors = append(errors, result.Error)
			continue
		}

		result.WebhookURL = webhookURL

		// 格式化消息
		groupConfig := h.extractGroupConfig(groupInfo)
		messageData, err := h.formatMessage(data, groupConfig)
		if err != nil {
			result.Error = fmt.Sprintf("无法格式化Discord消息: %v (groupConfig: %+v)", err, groupConfig)
			results = append(results, result)
			errors = append(errors, result.Error)
			continue
		}

		// 验证并发送
		if !h.formatter.ValidateMessage(messageData) {
			result.Error = fmt.Sprintf("Discord消息格式验证失败 (messageData: %+v)", messageData)
			results = append(results, result)
			errors = append(errors, result.Error)
			continue
		}

		sendOptions := &clients.SendOptions{}
		if embeds, ok := messageData["embeds"].([]map[string]interface{}); ok {
			sendOptions.Embeds = embeds
		}
		if username, ok := messageData["username"].(string); ok {
			sendOptions.Username = username
		}
		if avatarURL, ok := messageData["avatar_url"].(string); ok {
			sendOptions.AvatarURL = avatarURL
		}

		content, _ := messageData["content"].(string)
		sendResult, err := h.client.SendMessage(ctx, content, webhookURL, sendOptions)
		if err != nil {
			// 增强错误日志，包含更多诊断信息
			result.Error = fmt.Sprintf("HTTP请求失败: %v (webhookURL: %s)", err, webhookURL)

			// 记录详细的错误信息用于诊断
			h.logDetailedError(group, webhookURL, err, messageData)

			results = append(results, result)
			errors = append(errors, result.Error)
			continue
		}

		if sendResult.Success {
			result.Status = "success"
			result.MessageID = sendResult.MessageID
			messageIDs = append(messageIDs, sendResult.MessageID)

			// 提取消息标题用于日志
			title := h.extractMessageTitle(messageData)
			cleanTitle := h.cleanTitleForLog(title)

			// 记录成功发送的日志
			h.logger.Debug(fmt.Sprintf("Discord通知发送成功 组：%s，标题：%s，消息ID：%s",
				group, cleanTitle, sendResult.MessageID))
		} else {
			result.Error = fmt.Sprintf("Discord API错误: %s (webhookURL: %s)", sendResult.Error, webhookURL)

			// 记录API错误的详细信息
			h.logDetailedError(group, webhookURL, fmt.Errorf("API错误: %s", sendResult.Error), messageData)

			errors = append(errors, result.Error)
		}

		results = append(results, result)
	}

	return &SendResult{
		Success:    len(messageIDs) > 0,
		MessageIDs: messageIDs,
		Errors:     errors,
		Results:    results,
	}, nil
}

// SendProductNotification 发送产品通知
func (h *DiscordNotificationHandler) SendProductNotification(ctx context.Context, product *models.ProductItem, groups []string, options map[string]interface{}) (*SendResult, error) {
	return h.SendNotification(ctx, product, groups, options)
}

// SendErrorNotification 发送错误通知
func (h *DiscordNotificationHandler) SendErrorNotification(ctx context.Context, errorItem *models.ErrorLogItem, groups []string, options map[string]interface{}) (*SendResult, error) {
	return h.SendNotification(ctx, errorItem, groups, options)
}

// SendSystemNotification 发送系统通知
func (h *DiscordNotificationHandler) SendSystemNotification(ctx context.Context, title, message, level string, groups []string, options map[string]interface{}) (*SendResult, error) {
	data := map[string]interface{}{
		"title":   title,
		"message": message,
		"level":   level,
	}
	return h.SendNotification(ctx, data, groups, options)
}

// Close 关闭处理器
func (h *DiscordNotificationHandler) Close() error {
	return h.client.Close()
}

// getGroupInfo 获取组信息 - 对应Python版本的_get_group_info
func (h *DiscordNotificationHandler) getGroupInfo(groupName string) map[string]interface{} {
	notificationGroups, ok := h.config["notification_groups"].(map[string]interface{})
	if !ok {
		return nil
	}

	groupConfig, exists := notificationGroups[groupName]
	if !exists {
		return nil
	}

	// 处理不同格式的配置
	result := map[string]interface{}{"name": groupName}

	switch v := groupConfig.(type) {
	case string:
		// 简单字符串格式
		if strings.HasPrefix(v, "http") {
			result["webhook_url"] = v
		} else {
			result["id"] = v
		}
		return result
	case map[string]interface{}:
		// 复制所有配置
		for key, value := range v {
			result[key] = value
		}

		// 检查是否有有效的标识符
		hasValidIdentifier := false
		if _, ok := v["id"]; ok {
			hasValidIdentifier = true
		}
		if _, ok := v["webhook_url"]; ok {
			hasValidIdentifier = true
		}
		if info, ok := v["info"].(map[string]interface{}); ok {
			if _, ok := info["webhook_url"]; ok {
				hasValidIdentifier = true
			}
		}

		if !hasValidIdentifier {
			return nil
		}

		return result
	default:
		return nil
	}
}

// extractWebhookURL 提取webhook URL
func (h *DiscordNotificationHandler) extractWebhookURL(groupInfo map[string]interface{}) string {
	if webhookURL, ok := groupInfo["webhook_url"].(string); ok && webhookURL != "" {
		return webhookURL
	}

	if info, ok := groupInfo["info"].(map[string]interface{}); ok {
		if webhookURL, ok := info["webhook_url"].(string); ok && webhookURL != "" {
			return webhookURL
		}
	}

	return ""
}

// extractGroupConfig 提取组配置
func (h *DiscordNotificationHandler) extractGroupConfig(groupInfo map[string]interface{}) map[string]interface{} {
	if info, ok := groupInfo["info"].(map[string]interface{}); ok {
		return info
	}
	return nil
}

// formatMessage 格式化消息 - 对应Python版本的_format_message
func (h *DiscordNotificationHandler) formatMessage(data interface{}, groupConfig map[string]interface{}) (map[string]interface{}, error) {
	switch v := data.(type) {
	case *models.ProductItem:
		return h.formatter.FormatProductNotification(v, groupConfig)
	case models.ProductItem:
		return h.formatter.FormatProductNotification(&v, groupConfig)
	case *models.NotificationItem:
		return h.formatter.FormatProductNotification(v, groupConfig)
	case models.NotificationItem:
		return h.formatter.FormatProductNotification(&v, groupConfig)
	case *models.ErrorLogItem:
		return h.formatter.FormatErrorNotification(v, groupConfig)
	case models.ErrorLogItem:
		return h.formatter.FormatErrorNotification(&v, groupConfig)
	case map[string]interface{}:
		if _, ok := v["productData"]; ok {
			return h.formatter.FormatProductNotification(v, groupConfig)
		} else if _, ok := v["error_type"]; ok {
			// 转换为ErrorLogItem
			errorItem := &models.ErrorLogItem{
				ErrorType:    getString(v, "error_type"),
				ErrorLevel:   getString(v, "error_level"),
				ErrorMessage: getString(v, "error_message"),
				Source:       getString(v, "source"),
			}
			return h.formatter.FormatErrorNotification(errorItem, groupConfig)
		} else {
			title := getString(v, "title")
			if title == "" {
				title = "系统通知"
			}
			message := getString(v, "message")
			if message == "" {
				message = fmt.Sprintf("%v", v)
			}
			level := getString(v, "level")
			if level == "" {
				level = "info"
			}
			return h.formatter.FormatSystemNotification(title, message, level, groupConfig)
		}
	case string:
		return h.formatter.FormatSimpleMessage(v, groupConfig)
	default:
		return h.formatter.FormatSimpleMessage(fmt.Sprintf("%v", v), groupConfig)
	}
}

// extractMessageTitle 提取消息标题
func (h *DiscordNotificationHandler) extractMessageTitle(messageData map[string]interface{}) string {
	if embeds, ok := messageData["embeds"].([]map[string]interface{}); ok && len(embeds) > 0 {
		if title, ok := embeds[0]["title"].(string); ok {
			return title
		}
	}
	if content, ok := messageData["content"].(string); ok {
		if len(content) > 50 {
			return content[:50]
		}
		return content
	}
	return "未知"
}

// cleanTitleForLog 清理标题用于日志
func (h *DiscordNotificationHandler) cleanTitleForLog(title string) string {
	// 清理标题中的emoji和特殊字符，只保留主要内容
	re := regexp.MustCompile(`[^\w\s\-]`)
	cleanTitle := strings.TrimSpace(re.ReplaceAllString(title, ""))
	if len(cleanTitle) > 30 {
		cleanTitle = cleanTitle[:30] + "..."
	}
	return cleanTitle
}

// extractDiscordConfig 提取Discord配置
func extractDiscordConfig(config map[string]interface{}) clients.DiscordConfig {

	enabled, _ := config["enabled"].(bool)
	defaultWebhookURL, _ := config["default_webhook_url"].(string)

	rateLimitConfig := clients.RateLimitConfig{RequestsPerMinute: 30}
	if rateLimit, ok := config["rate_limit"].(map[string]interface{}); ok {
		if rpm, ok := rateLimit["requests_per_minute"].(int); ok {
			rateLimitConfig.RequestsPerMinute = rpm
		}
	}

	retryConfig := clients.RetryConfig{
		MaxRetries:        5,          // 增加重试次数
		RetryDelay:        2000000000, // 2 seconds in nanoseconds
		BackoffMultiplier: 2.0,
	}
	if retry, ok := config["retry"].(map[string]interface{}); ok {
		if maxRetries, ok := retry["max_retries"].(int); ok {
			retryConfig.MaxRetries = maxRetries
		}
		if retryDelay, ok := retry["retry_delay"].(int); ok {
			retryConfig.RetryDelay = time.Duration(retryDelay) * time.Millisecond
		}
		if backoffMultiplier, ok := retry["backoff_multiplier"].(float64); ok {
			retryConfig.BackoffMultiplier = backoffMultiplier
		}
	}

	timeoutConfig := clients.TimeoutConfig{
		ConnectTimeout: 30 * time.Second,
		ReadTimeout:    60 * time.Second, // 增加读取超时
	}
	if timeout, ok := config["timeout"].(map[string]interface{}); ok {
		if connectTimeout, ok := timeout["connect_timeout"].(int); ok {
			timeoutConfig.ConnectTimeout = time.Duration(connectTimeout) * time.Second
		}
		if readTimeout, ok := timeout["read_timeout"].(int); ok {
			timeoutConfig.ReadTimeout = time.Duration(readTimeout) * time.Second
		}
	}

	return clients.DiscordConfig{
		Enabled:           enabled,
		DefaultWebhookURL: defaultWebhookURL,
		RateLimit:         rateLimitConfig,
		Retry:             retryConfig,
		Timeout:           timeoutConfig,
	}
}

// getString 安全获取字符串值
func getString(m map[string]interface{}, key string) string {
	if value, ok := m[key].(string); ok {
		return value
	}
	return ""
}

// logDetailedError 记录详细的错误信息用于诊断
func (h *DiscordNotificationHandler) logDetailedError(group, webhookURL string, err error, messageData map[string]interface{}) {
	// 提取消息标题用于日志
	title := h.extractMessageTitle(messageData)
	cleanTitle := h.cleanTitleForLog(title)

	// 记录详细的错误信息
	h.logger.Error(fmt.Sprintf("Discord通知发送失败 组：%s，标题：%s，错误：%v，WebhookURL：%s",
		group, cleanTitle, err, webhookURL))

	// 如果是网络错误，记录额外的诊断信息
	if err != nil {
		errStr := err.Error()
		if strings.Contains(errStr, "timeout") {
			h.logger.Warn(fmt.Sprintf("检测到超时错误，建议检查网络连接和Discord服务状态 组：%s", group))
		} else if strings.Contains(errStr, "connection") {
			h.logger.Warn(fmt.Sprintf("检测到连接错误，建议检查网络配置和防火墙设置 组：%s", group))
		} else if strings.Contains(errStr, "429") {
			h.logger.Warn(fmt.Sprintf("检测到速率限制，Discord API调用过于频繁 组：%s", group))
		}
	}
}
