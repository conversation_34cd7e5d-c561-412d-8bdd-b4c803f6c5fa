package clients

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"go-monitor/pkg/logging"
)

// DiscordWebhookClient Discord webhook客户端 - 对应Python版本的DiscordWebhookClient
type DiscordWebhookClient struct {
	// 配置
	enabled           bool
	defaultWebhookURL string
	requestsPerMinute int
	maxRetries        int
	retryDelay        time.Duration
	backoffMultiplier float64

	// HTTP客户端
	httpClient *http.Client

	// 速率限制
	requestTimes []time.Time
	rateMutex    sync.Mutex

	// 日志器
	logger logging.Logger
}

// NewDiscordWebhookClient 创建新的Discord webhook客户端
func NewDiscordWebhookClient(config DiscordConfig) *DiscordWebhookClient {
	// 设置更长的超时时间以提高可靠性
	timeout := 60 * time.Second
	if config.Timeout.ReadTimeout > 0 {
		timeout = config.Timeout.ReadTimeout
	}

	return &DiscordWebhookClient{
		enabled:           config.Enabled,
		defaultWebhookURL: config.DefaultWebhookURL,
		requestsPerMinute: config.RateLimit.RequestsPerMinute,
		maxRetries:        config.Retry.MaxRetries,
		retryDelay:        config.Retry.RetryDelay,
		backoffMultiplier: config.Retry.BackoffMultiplier,
		httpClient: &http.Client{
			Timeout: timeout,
		},
		requestTimes: make([]time.Time, 0),
		logger:       logging.GetLogger("service.notification.discord"),
	}
}

// DiscordConfig Discord配置结构
type DiscordConfig struct {
	Enabled           bool
	DefaultWebhookURL string
	RateLimit         RateLimitConfig
	Retry             RetryConfig
	Timeout           TimeoutConfig
}

// TimeoutConfig 超时配置
type TimeoutConfig struct {
	ConnectTimeout time.Duration
	ReadTimeout    time.Duration
}

// RateLimitConfig 速率限制配置
type RateLimitConfig struct {
	RequestsPerMinute int
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries        int
	RetryDelay        time.Duration
	BackoffMultiplier float64
}

// checkRateLimit 检查速率限制 - 对应Python版本的_check_rate_limit
func (c *DiscordWebhookClient) checkRateLimit() bool {
	c.rateMutex.Lock()
	defer c.rateMutex.Unlock()

	now := time.Now()
	// 清理1分钟前的记录
	cutoff := now.Add(-time.Minute)
	newTimes := make([]time.Time, 0)
	for _, t := range c.requestTimes {
		if t.After(cutoff) {
			newTimes = append(newTimes, t)
		}
	}
	c.requestTimes = newTimes

	if len(c.requestTimes) >= c.requestsPerMinute {
		return false
	}

	c.requestTimes = append(c.requestTimes, now)
	return true
}

// sendWithRetry 带重试的发送 - 修复版本，解决速率限制和重试逻辑问题
func (c *DiscordWebhookClient) sendWithRetry(ctx context.Context, webhookURL string, payload map[string]interface{}) (*SendResult, error) {
	var lastErr error
	var lastStatusCode int

	for attempt := 0; attempt < c.maxRetries; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return &SendResult{Success: false, Error: "请求被取消"}, ctx.Err()
		default:
		}

		// 检查速率限制 - 修复：速率限制等待不应消耗重试次数
		for !c.checkRateLimit() {
			c.logger.Debug(fmt.Sprintf("Discord速率限制，等待1秒后继续 (当前尝试 %d/%d)", attempt+1, c.maxRetries))
			select {
			case <-time.After(1 * time.Second):
				// 继续检查速率限制
			case <-ctx.Done():
				return &SendResult{Success: false, Error: "请求被取消"}, ctx.Err()
			}
		}

		// 准备请求
		jsonData, err := json.Marshal(payload)
		if err != nil {
			// JSON编码错误是不可重试的错误
			errorMsg := fmt.Sprintf("JSON编码失败: %v", err)
			c.logger.Error(errorMsg)
			return &SendResult{Success: false, Error: errorMsg}, err
		}

		req, err := http.NewRequestWithContext(ctx, "POST", webhookURL, bytes.NewBuffer(jsonData))
		if err != nil {
			// 请求创建错误是不可重试的错误
			errorMsg := fmt.Sprintf("创建请求失败: %v", err)
			c.logger.Error(errorMsg)
			return &SendResult{Success: false, Error: errorMsg}, err
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "Discord-Webhook-Client/1.0")

		// 记录尝试信息
		c.logger.Debug(fmt.Sprintf("Discord发送尝试 %d/%d，URL: %s", attempt+1, c.maxRetries, webhookURL))

		// 发送请求
		resp, err := c.httpClient.Do(req)
		if err != nil {
			lastErr = err
			// 增强错误日志，包含网络诊断信息
			c.logger.Warn(fmt.Sprintf("Discord HTTP请求失败 (尝试 %d/%d): %v，错误类型：%T",
				attempt+1, c.maxRetries, err, err))

			// 检查是否为可重试错误
			if !c.isRetryableError(err) {
				errorMsg := fmt.Sprintf("不可重试的网络错误: %v", err)
				c.logger.Error(errorMsg)
				return &SendResult{Success: false, Error: errorMsg}, fmt.Errorf("不可重试错误: %w", err)
			}

			if attempt < c.maxRetries-1 {
				delay := c.calculateBackoffDelay(attempt)
				c.logger.Info(fmt.Sprintf("Discord网络错误，等待 %v 后重试 (尝试 %d/%d)",
					delay, attempt+1, c.maxRetries))

				select {
				case <-time.After(delay):
					continue
				case <-ctx.Done():
					return &SendResult{Success: false, Error: "请求被取消"}, ctx.Err()
				}
			}
			continue
		}
		defer resp.Body.Close()

		lastStatusCode = resp.StatusCode

		// 处理响应
		if resp.StatusCode == 204 { // Discord成功响应
			c.logger.Debug(fmt.Sprintf("Discord消息发送成功 (尝试 %d/%d)", attempt+1, c.maxRetries))
			return &SendResult{Success: true, MessageID: "sent"}, nil
		} else if resp.StatusCode == 429 { // 速率限制 - 修复：不消耗重试次数
			retryAfter := resp.Header.Get("Retry-After")
			c.logger.Warn(fmt.Sprintf("Discord API速率限制 429，Retry-After: %s (尝试 %d/%d)",
				retryAfter, attempt+1, c.maxRetries))

			var waitDuration time.Duration = 2 * time.Second // 默认等待时间
			if retryAfter != "" {
				if duration, err := time.ParseDuration(retryAfter + "s"); err == nil {
					waitDuration = duration
				}
			}

			select {
			case <-time.After(waitDuration):
				attempt-- // 速率限制不消耗重试次数
				continue
			case <-ctx.Done():
				return &SendResult{Success: false, Error: "请求被取消"}, ctx.Err()
			}
		} else {
			// 读取响应体以获取更多错误信息
			body := make([]byte, 1024)
			n, _ := resp.Body.Read(body)
			responseBody := string(body[:n])

			errorMsg := fmt.Sprintf("HTTP %d: %s", resp.StatusCode, responseBody)
			c.logger.Warn(fmt.Sprintf("Discord HTTP错误 (尝试 %d/%d): %s", attempt+1, c.maxRetries, errorMsg))

			// 检查是否为可重试的HTTP错误
			if !c.isRetryableHTTPStatus(resp.StatusCode) {
				c.logger.Error(fmt.Sprintf("不可重试的HTTP错误: %s", errorMsg))
				return &SendResult{Success: false, Error: errorMsg}, fmt.Errorf("不可重试HTTP错误: %s", errorMsg)
			}

			if attempt < c.maxRetries-1 {
				delay := c.calculateBackoffDelay(attempt)
				c.logger.Info(fmt.Sprintf("Discord HTTP错误，等待 %v 后重试 (尝试 %d/%d)",
					delay, attempt+1, c.maxRetries))

				select {
				case <-time.After(delay):
					continue
				case <-ctx.Done():
					return &SendResult{Success: false, Error: "请求被取消"}, ctx.Err()
				}
			}
		}
	}

	finalError := fmt.Sprintf("Discord请求处理失败，所有重试尝试已耗尽 (最后错误: %v, 状态码: %d)", lastErr, lastStatusCode)
	c.logger.Error(finalError)
	return &SendResult{Success: false, Error: finalError}, fmt.Errorf("%s", finalError)
}

// isRetryableError 判断网络错误是否可重试
func (c *DiscordWebhookClient) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// 网络相关错误通常可重试
	retryablePatterns := []string{
		"connection refused",
		"connection reset",
		"connection timeout",
		"timeout",
		"network",
		"temporary failure",
		"no such host",
		"i/o timeout",
		"context deadline exceeded",
		"EOF",
	}

	for _, pattern := range retryablePatterns {
		if contains(errStr, pattern) {
			return true
		}
	}

	return false
}

// isRetryableHTTPStatus 判断HTTP状态码是否可重试
func (c *DiscordWebhookClient) isRetryableHTTPStatus(statusCode int) bool {
	switch statusCode {
	case 429: // 速率限制，可重试
		return true
	case 500, 502, 503, 504: // 服务器错误，可重试
		return true
	case 408: // 请求超时，可重试
		return true
	default:
		// 4xx 客户端错误通常不可重试（除了429和408）
		if statusCode >= 400 && statusCode < 500 {
			return false
		}
		// 5xx 服务器错误可重试
		if statusCode >= 500 {
			return true
		}
		return false
	}
}

// calculateBackoffDelay 计算指数退避延迟时间
func (c *DiscordWebhookClient) calculateBackoffDelay(attempt int) time.Duration {
	// 基础延迟时间
	baseDelay := c.retryDelay

	// 指数退避：delay = baseDelay * (backoffMultiplier ^ attempt)
	multiplier := 1.0
	for i := 0; i < attempt; i++ {
		multiplier *= c.backoffMultiplier
	}

	delay := time.Duration(float64(baseDelay) * multiplier)

	// 限制最大延迟时间为30秒
	maxDelay := 30 * time.Second
	if delay > maxDelay {
		delay = maxDelay
	}

	// 添加随机抖动，避免惊群效应
	jitter := time.Duration(float64(delay) * 0.1 * (0.5 - float64(time.Now().UnixNano()%1000)/1000.0))
	delay += jitter

	return delay
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr || (len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				containsInMiddle(s, substr))))
}

// containsInMiddle 检查字符串中间是否包含子字符串
func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// HealthCheck 检查Discord服务的健康状态
func (c *DiscordWebhookClient) HealthCheck(ctx context.Context) error {
	if !c.enabled {
		return fmt.Errorf("Discord客户端未启用")
	}

	// 使用一个简单的测试webhook来检查连接
	testPayload := map[string]interface{}{
		"content": "健康检查测试（此消息可忽略）",
	}

	// 创建一个较短超时的上下文用于健康检查
	healthCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 尝试发送测试消息到默认webhook
	if c.defaultWebhookURL != "" {
		result, err := c.sendWithRetry(healthCtx, c.defaultWebhookURL, testPayload)
		if err != nil {
			return fmt.Errorf("健康检查失败: %w", err)
		}
		if !result.Success {
			return fmt.Errorf("健康检查失败: %s", result.Error)
		}
	}

	return nil
}

// GetConnectionStats 获取连接统计信息
func (c *DiscordWebhookClient) GetConnectionStats() map[string]interface{} {
	c.rateMutex.Lock()
	defer c.rateMutex.Unlock()

	stats := map[string]interface{}{
		"enabled":             c.enabled,
		"requests_per_minute": c.requestsPerMinute,
		"current_requests":    len(c.requestTimes),
		"max_retries":         c.maxRetries,
		"retry_delay_ms":      c.retryDelay.Milliseconds(),
		"backoff_multiplier":  c.backoffMultiplier,
	}

	return stats
}

// SendMessage 发送消息到Discord - 对应Python版本的send_message
func (c *DiscordWebhookClient) SendMessage(ctx context.Context, content string, webhookURL string, options *SendOptions) (*SendResult, error) {
	if !c.enabled {
		return &SendResult{Success: false, Error: "Discord通知未启用"}, fmt.Errorf("Discord通知未启用")
	}

	if webhookURL == "" {
		webhookURL = c.defaultWebhookURL
	}
	if webhookURL == "" {
		return &SendResult{Success: false, Error: "缺少webhook URL"}, fmt.Errorf("缺少webhook URL")
	}

	// 构建payload
	payload := make(map[string]interface{})
	if content != "" {
		payload["content"] = content
	}
	if options != nil {
		if options.Embeds != nil {
			payload["embeds"] = options.Embeds
		}
		if options.Username != "" {
			payload["username"] = options.Username
		}
		if options.AvatarURL != "" {
			payload["avatar_url"] = options.AvatarURL
		}
	}

	if len(payload) == 0 {
		return &SendResult{Success: false, Error: "消息内容为空"}, fmt.Errorf("消息内容为空")
	}

	return c.sendWithRetry(ctx, webhookURL, payload)
}

// Close 关闭客户端
func (c *DiscordWebhookClient) Close() error {
	// HTTP客户端不需要显式关闭
	return nil
}
