package clients

import (
	"context"
)

// WebhookClient 通用webhook客户端接口
type WebhookClient interface {
	// SendMessage 发送消息
	SendMessage(ctx context.Context, content string, webhookURL string, options *SendOptions) (*SendResult, error)
	
	// Close 关闭客户端
	Close() error
}

// SendOptions 发送选项
type SendOptions struct {
	Embeds    []map[string]interface{} `json:"embeds,omitempty"`
	Username  string                   `json:"username,omitempty"`
	AvatarURL string                   `json:"avatar_url,omitempty"`
}

// SendResult 发送结果
type SendResult struct {
	Success   bool   `json:"success"`
	MessageID string `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
}
