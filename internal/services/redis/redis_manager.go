package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"go-monitor/internal/config"
	"go-monitor/pkg/logging"

	"github.com/go-redis/redis/v8"
	"github.com/tidwall/gjson"
)

// RedisManager Redis管理器 - 对应Python版本的redis_manager.py
type RedisManager struct {
	client  *redis.Client
	logger  logging.Logger
	config  *config.RedisConfig
	mu      sync.RWMutex
	started bool
}

// DefaultConfig 返回默认配置
func DefaultConfig() *config.RedisConfig {
	return &config.RedisConfig{
		Host:     "localhost",
		Port:     6379,
		Password: "",
		DB:       0,

		PoolSize:     10,
		MinIdleConns: 5,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,

		MaxRetries:      3,
		MinRetryBackoff: 8 * time.Millisecond,
		MaxRetryBackoff: 512 * time.Millisecond,

		DefaultTTL: 24 * time.Hour,
		KeyPrefix:  "monitor:",
	}
}

// NewRedisManager 创建Redis管理器
func NewRedisManager(cfg *config.RedisConfig) *RedisManager {
	if cfg == nil {
		cfg = DefaultConfig()
	}

	return &RedisManager{
		config: cfg,
		logger: logging.GetLogger("service.redis_manager"),
	}
}

// Start 启动Redis连接 - 对应Python版本的connect方法
func (r *RedisManager) Start() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.started {
		return nil
	}

	// 创建Redis客户端
	r.client = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", r.config.Host, r.config.Port),
		Password: r.config.Password,
		DB:       r.config.DB,

		PoolSize:     r.config.PoolSize,
		MinIdleConns: r.config.MinIdleConns,
		DialTimeout:  r.config.DialTimeout,
		ReadTimeout:  r.config.ReadTimeout,
		WriteTimeout: r.config.WriteTimeout,
		PoolTimeout:  r.config.PoolTimeout,

		MaxRetries:      r.config.MaxRetries,
		MinRetryBackoff: r.config.MinRetryBackoff,
		MaxRetryBackoff: r.config.MaxRetryBackoff,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := r.client.Ping(ctx).Err(); err != nil {
		r.logger.Error(fmt.Sprintf("Redis连接失败 %s:%d，错误：%s",
			r.config.Host, r.config.Port, err.Error()))
		return fmt.Errorf("redis connection failed: %w", err)
	}

	r.started = true
	r.logger.Info(fmt.Sprintf("Redis连接已建立 %s:%d，连接池大小：%d",
		r.config.Host, r.config.Port, r.config.PoolSize))

	return nil
}

// Stop 停止Redis连接
func (r *RedisManager) Stop() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.started || r.client == nil {
		return nil
	}

	err := r.client.Close()
	r.client = nil
	r.started = false

	r.logger.Info("Redis连接已关闭")
	return err
}

// buildKey 构建完整的Redis键名
func (r *RedisManager) buildKey(key string) string {
	return r.config.KeyPrefix + key
}

// Set 设置缓存值 - 对应Python版本的set方法
func (r *RedisManager) Set(ctx context.Context, key string, value interface{}, ttl ...time.Duration) error {
	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return fmt.Errorf("redis client not started")
	}

	// 序列化值
	var data []byte
	var err error

	switch v := value.(type) {
	case string:
		data = []byte(v)
	case []byte:
		data = v
	default:
		data, err = json.Marshal(v)
		if err != nil {
			return fmt.Errorf("failed to marshal value: %w", err)
		}
	}

	// 确定TTL
	expiration := r.config.DefaultTTL
	if len(ttl) > 0 && ttl[0] > 0 {
		expiration = ttl[0]
	}

	fullKey := r.buildKey(key)

	err = client.Set(ctx, fullKey, data, expiration).Err()
	if err != nil {
		r.logger.Error(fmt.Sprintf("Redis设置值失败，键：%s，错误：%s", fullKey, err.Error()))
		return fmt.Errorf("redis set failed: %w", err)
	}

	r.logger.Debug(fmt.Sprintf("Redis设置值成功，键：%s，TTL：%s", fullKey, expiration.String()))

	return nil
}

// Get 获取缓存值 - 对应Python版本的get方法
func (r *RedisManager) Get(ctx context.Context, key string) ([]byte, error) {
	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("redis client not started")
	}

	fullKey := r.buildKey(key)

	data, err := client.Get(ctx, fullKey).Bytes()
	if err != nil {
		if err == redis.Nil {
			r.logger.Debug(fmt.Sprintf("Redis键不存在：%s", fullKey))
			return nil, ErrKeyNotFound
		}

		r.logger.Error(fmt.Sprintf("Redis获取值失败，键：%s，错误：%s", fullKey, err.Error()))
		return nil, fmt.Errorf("redis get failed: %w", err)
	}

	return data, nil
}

// GetString 获取字符串缓存值
func (r *RedisManager) GetString(ctx context.Context, key string) (string, error) {
	data, err := r.Get(ctx, key)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// GetJSON 获取JSON缓存值并反序列化 - 使用GJSON验证
func (r *RedisManager) GetJSON(ctx context.Context, key string, dest interface{}) error {
	data, err := r.Get(ctx, key)
	if err != nil {
		return err
	}

	// 使用GJSON验证JSON格式
	if !gjson.Valid(string(data)) {
		return fmt.Errorf("invalid JSON data in cache for key: %s", key)
	}

	return json.Unmarshal(data, dest)
}

// Exists 检查键是否存在 - 对应Python版本的exists方法
func (r *RedisManager) Exists(ctx context.Context, key string) (bool, error) {
	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return false, fmt.Errorf("redis client not started")
	}

	fullKey := r.buildKey(key)

	result, err := client.Exists(ctx, fullKey).Result()
	if err != nil {
		r.logger.Error(fmt.Sprintf("Redis检查键存在失败，键：%s，错误：%s", fullKey, err.Error()))
		return false, fmt.Errorf("redis exists failed: %w", err)
	}

	return result > 0, nil
}

// Delete 删除缓存键 - 对应Python版本的delete方法
func (r *RedisManager) Delete(ctx context.Context, key string) error {
	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return fmt.Errorf("redis client not started")
	}

	fullKey := r.buildKey(key)

	_, err := client.Del(ctx, fullKey).Result()
	if err != nil {
		r.logger.Error(fmt.Sprintf("Redis删除键失败，键：%s，错误：%s", fullKey, err.Error()))
		return fmt.Errorf("redis delete failed: %w", err)
	}

	r.logger.Debug(fmt.Sprintf("Redis删除键成功：%s", fullKey))

	return nil
}

// Expire 设置键的过期时间
func (r *RedisManager) Expire(ctx context.Context, key string, ttl time.Duration) error {
	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return fmt.Errorf("redis client not started")
	}

	fullKey := r.buildKey(key)

	_, err := client.Expire(ctx, fullKey, ttl).Result()
	if err != nil {
		r.logger.Error(fmt.Sprintf("Redis设置过期时间失败，键：%s，TTL：%s，错误：%s",
			fullKey, ttl.String(), err.Error()))
		return fmt.Errorf("redis expire failed: %w", err)
	}

	return nil
}

// TTL 获取键的剩余生存时间
func (r *RedisManager) TTL(ctx context.Context, key string) (time.Duration, error) {
	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return 0, fmt.Errorf("redis client not started")
	}

	fullKey := r.buildKey(key)

	ttl, err := client.TTL(ctx, fullKey).Result()
	if err != nil {
		r.logger.Error(fmt.Sprintf("Redis获取TTL失败，键：%s，错误：%s", fullKey, err.Error()))
		return 0, fmt.Errorf("redis ttl failed: %w", err)
	}

	return ttl, nil
}

// Pipeline 创建管道操作
func (r *RedisManager) Pipeline() redis.Pipeliner {
	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return nil
	}

	return client.Pipeline()
}

// IsHealthy 检查Redis连接健康状态
func (r *RedisManager) IsHealthy(ctx context.Context) bool {
	r.mu.RLock()
	client := r.client
	started := r.started
	r.mu.RUnlock()

	if !started || client == nil {
		return false
	}

	err := client.Ping(ctx).Err()
	return err == nil
}

// GetStats 获取Redis连接统计信息
func (r *RedisManager) GetStats(ctx context.Context) map[string]interface{} {
	r.mu.RLock()
	client := r.client
	started := r.started
	r.mu.RUnlock()

	stats := map[string]interface{}{
		"started":    started,
		"host":       r.config.Host,
		"port":       r.config.Port,
		"pool_size":  r.config.PoolSize,
		"key_prefix": r.config.KeyPrefix,
	}

	if client != nil {
		poolStats := client.PoolStats()
		stats["pool_stats"] = map[string]interface{}{
			"hits":        poolStats.Hits,
			"misses":      poolStats.Misses,
			"timeouts":    poolStats.Timeouts,
			"total_conns": poolStats.TotalConns,
			"idle_conns":  poolStats.IdleConns,
			"stale_conns": poolStats.StaleConns,
		}
	}

	return stats
}

// ErrKeyNotFound 键不存在错误
var ErrKeyNotFound = fmt.Errorf("key not found")

// ResourceCache 资源缓存相关方法

// SetResource 设置资源缓存
func (r *RedisManager) SetResource(ctx context.Context, resourceType, key string, value interface{}, ttl time.Duration) error {
	fullKey := r.buildResourceKey(resourceType, key)
	return r.Set(ctx, fullKey, value, ttl)
}

// SetResourceVersioned 设置版本化资源缓存（双缓存策略）
func (r *RedisManager) SetResourceVersioned(ctx context.Context, resourceType, key string, value interface{}, ttl time.Duration) error {
	// 先更新backup版本
	backupKey := r.buildResourceKey(resourceType, key+"_backup")
	if err := r.Set(ctx, backupKey, value, ttl); err != nil {
		return fmt.Errorf("failed to set backup resource: %w", err)
	}

	// 再更新current版本
	currentKey := r.buildResourceKey(resourceType, key+"_current")
	if err := r.Set(ctx, currentKey, value, ttl); err != nil {
		return fmt.Errorf("failed to set current resource: %w", err)
	}

	// 同时更新默认版本以保持向后兼容
	defaultKey := r.buildResourceKey(resourceType, key)
	return r.Set(ctx, defaultKey, value, ttl)
}

// GetResource 获取资源缓存
func (r *RedisManager) GetResource(ctx context.Context, resourceType, key string) ([]byte, error) {
	fullKey := r.buildResourceKey(resourceType, key)
	return r.Get(ctx, fullKey)
}

// GetResourceVersioned 获取版本化资源缓存（双缓存策略）
func (r *RedisManager) GetResourceVersioned(ctx context.Context, resourceType, key string) ([]byte, error) {
	// 优先尝试获取current版本
	currentKey := r.buildResourceKey(resourceType, key+"_current")
	if data, err := r.Get(ctx, currentKey); err == nil {
		return data, nil
	}

	// 如果current版本不可用，尝试backup版本
	backupKey := r.buildResourceKey(resourceType, key+"_backup")
	if data, err := r.Get(ctx, backupKey); err == nil {
		return data, nil
	}

	// 最后尝试默认版本（向后兼容）
	return r.GetResource(ctx, resourceType, key)
}

// DeleteResource 删除资源缓存
func (r *RedisManager) DeleteResource(ctx context.Context, resourceType, key string) error {
	fullKey := r.buildResourceKey(resourceType, key)
	return r.Delete(ctx, fullKey)
}

// ExistsResource 检查资源缓存是否存在
func (r *RedisManager) ExistsResource(ctx context.Context, resourceType, key string) (bool, error) {
	fullKey := r.buildResourceKey(resourceType, key)
	return r.Exists(ctx, fullKey)
}

// ListResourceKeys 列出指定类型的所有资源键
func (r *RedisManager) ListResourceKeys(ctx context.Context, resourceType string) ([]string, error) {
	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("redis client not started")
	}

	pattern := r.buildResourceKey(resourceType, "*")
	keys, err := client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to list resource keys: %w", err)
	}

	// 移除前缀，只返回实际的键名
	prefix := r.buildResourceKey(resourceType, "")
	result := make([]string, len(keys))
	for i, key := range keys {
		result[i] = strings.TrimPrefix(key, prefix)
	}

	return result, nil
}

// ClearResourceType 清除指定类型的所有资源缓存
func (r *RedisManager) ClearResourceType(ctx context.Context, resourceType string) error {
	keys, err := r.ListResourceKeys(ctx, resourceType)
	if err != nil {
		return fmt.Errorf("failed to list resource keys: %w", err)
	}

	if len(keys) == 0 {
		return nil
	}

	// 构建完整键名
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = r.buildResourceKey(resourceType, key)
	}

	r.mu.RLock()
	client := r.client
	r.mu.RUnlock()

	if client == nil {
		return fmt.Errorf("redis client not started")
	}

	_, err = client.Del(ctx, fullKeys...).Result()
	if err != nil {
		return fmt.Errorf("failed to clear resource type %s: %w", resourceType, err)
	}

	r.logger.Info(fmt.Sprintf("清除资源类型缓存：%s，键数量：%d", resourceType, len(fullKeys)))

	return nil
}

// buildResourceKey 构建资源缓存键
func (r *RedisManager) buildResourceKey(resourceType, key string) string {
	return r.config.KeyPrefix + "resource:" + resourceType + ":" + key
}

// HealthCheck 执行健康检查并返回详细信息
func (r *RedisManager) HealthCheck(ctx context.Context) map[string]interface{} {
	result := map[string]interface{}{
		"healthy":    false,
		"error":      "",
		"latency_ms": 0,
		"timestamp":  time.Now(),
	}

	r.mu.RLock()
	client := r.client
	started := r.started
	r.mu.RUnlock()

	if !started || client == nil {
		result["error"] = "redis client not started"
		return result
	}

	start := time.Now()
	err := client.Ping(ctx).Err()
	latency := time.Since(start)

	result["latency_ms"] = latency.Milliseconds()

	if err != nil {
		result["error"] = err.Error()
		return result
	}

	result["healthy"] = true
	return result
}

// GetDetailedStats 获取详细的统计信息
func (r *RedisManager) GetDetailedStats(ctx context.Context) map[string]interface{} {
	stats := r.GetStats(ctx)

	// 添加健康检查信息
	healthInfo := r.HealthCheck(ctx)
	stats["health"] = healthInfo

	// 添加配置信息
	stats["config"] = map[string]interface{}{
		"host":        r.config.Host,
		"port":        r.config.Port,
		"db":          r.config.DB,
		"pool_size":   r.config.PoolSize,
		"key_prefix":  r.config.KeyPrefix,
		"default_ttl": r.config.DefaultTTL.String(),
	}

	return stats
}
