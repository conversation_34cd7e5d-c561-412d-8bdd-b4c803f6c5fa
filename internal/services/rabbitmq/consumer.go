package rabbitmq

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go-monitor/pkg/logging"

	"github.com/streadway/amqp"
)

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	QueueName     string                 `json:"queue_name"`
	ConsumerTag   string                 `json:"consumer_tag"`
	AutoAck       bool                   `json:"auto_ack"`
	Exclusive     bool                   `json:"exclusive"`
	NoLocal       bool                   `json:"no_local"`
	NoWait        bool                   `json:"no_wait"`
	PrefetchCount int                    `json:"prefetch_count"`
	PrefetchSize  int                    `json:"prefetch_size"`
	Global        bool                   `json:"global"`
	Args          map[string]interface{} `json:"args"`
}

// DefaultConsumerConfig 默认消费者配置
func DefaultConsumerConfig() *ConsumerConfig {
	return &ConsumerConfig{
		QueueName:     "monitor_queue",
		ConsumerTag:   "",
		AutoAck:       false,
		Exclusive:     false,
		NoLocal:       false,
		NoWait:        false,
		PrefetchCount: 10,
		PrefetchSize:  0,
		Global:        false,
		Args:          make(map[string]interface{}),
	}
}

// MessageHandler 消息处理函数类型
type MessageHandler func(ctx context.Context, delivery amqp.Delivery) error

// Consumer RabbitMQ消费者
type Consumer struct {
	name      string
	manager   *Manager
	config    *ConsumerConfig
	logger    logging.Logger
	mutex     sync.RWMutex
	isRunning bool

	// 消息处理
	handler     MessageHandler
	deliveries  <-chan amqp.Delivery
	workerCount int

	// 上下文控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// 统计信息
	stats ConsumerStats
}

// ConsumerStats 消费者统计
type ConsumerStats struct {
	TotalReceived    int64     `json:"total_received"`
	TotalProcessed   int64     `json:"total_processed"`
	TotalErrors      int64     `json:"total_errors"`
	TotalAcked       int64     `json:"total_acked"`
	TotalNacked      int64     `json:"total_nacked"`
	TotalRejecter    int64     `json:"total_rejected"`
	LastReceivedTime time.Time `json:"last_received_time"`
	LastError        string    `json:"last_error"`
	LastErrorTime    time.Time `json:"last_error_time"`
	WorkerCount      int       `json:"worker_count"`
}

// NewConsumer 创建新的消费者
func NewConsumer(name string, manager *Manager, config *ConsumerConfig) *Consumer {
	if config == nil {
		config = DefaultConsumerConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &Consumer{
		name:        name,
		manager:     manager,
		config:      config,
		logger:      manager.logger.Named(fmt.Sprintf("consumer.%s", name)),
		ctx:         ctx,
		cancel:      cancel,
		workerCount: 5, // 默认5个工作者
		stats:       ConsumerStats{},
	}
}

// SetHandler 设置消息处理函数
func (c *Consumer) SetHandler(handler MessageHandler) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.handler = handler
}

// SetWorkerCount 设置工作者数量
func (c *Consumer) SetWorkerCount(count int) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	if count > 0 {
		c.workerCount = count
	}
}

// Start 开始消费
func (c *Consumer) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isRunning {
		return fmt.Errorf("consumer is already running")
	}

	if c.handler == nil {
		return fmt.Errorf("message handler not set")
	}

	if !c.manager.IsConnected() {
		return fmt.Errorf("RabbitMQ not connected")
	}

	// 设置QoS
	if err := c.manager.channel.Qos(
		c.config.PrefetchCount,
		c.config.PrefetchSize,
		c.config.Global,
	); err != nil {
		return fmt.Errorf("failed to set QoS: %w", err)
	}

	// 开始消费
	deliveries, err := c.manager.channel.Consume(
		c.config.QueueName,   // queue
		c.config.ConsumerTag, // consumer
		c.config.AutoAck,     // auto-ack
		c.config.Exclusive,   // exclusive
		c.config.NoLocal,     // no-local
		c.config.NoWait,      // no-wait
		c.config.Args,        // args
	)
	if err != nil {
		return fmt.Errorf("failed to start consuming: %w", err)
	}

	c.deliveries = deliveries
	c.isRunning = true
	c.stats.WorkerCount = c.workerCount

	// 启动工作者
	for i := 0; i < c.workerCount; i++ {
		c.wg.Add(1)
		go c.worker(i)
	}

	c.logger.Info(fmt.Sprintf("消费者已启动 名称：%s，队列：%s，工作者数量：%d，预取数量：%d",
		c.name, c.config.QueueName, c.workerCount, c.config.PrefetchCount))

	return nil
}

// worker 工作者goroutine
func (c *Consumer) worker(id int) {
	defer c.wg.Done()

	workerLogger := c.logger.Named(fmt.Sprintf("worker.%d", id))

	for {
		select {
		case delivery, ok := <-c.deliveries:
			if !ok {
				workerLogger.Info("消息通道已关闭")
				return
			}

			c.stats.TotalReceived++
			c.stats.LastReceivedTime = time.Now()

			// 处理消息
			if err := c.processMessage(delivery); err != nil {
				c.stats.TotalErrors++
				c.stats.LastError = err.Error()
				c.stats.LastErrorTime = time.Now()
				workerLogger.Error("处理消息失败",
					"error", err.Error(),
					"message_id", delivery.MessageId,
					"routing_key", delivery.RoutingKey,
					"delivery_tag", delivery.DeliveryTag,
				)
			} else {
				c.stats.TotalProcessed++
				workerLogger.Debug("消息处理成功",
					"message_id", delivery.MessageId,
					"routing_key", delivery.RoutingKey,
					"delivery_tag", delivery.DeliveryTag,
				)
			}

		case <-c.ctx.Done():
			workerLogger.Info("工作者停止")
			return
		}
	}
}

// processMessage 处理单个消息
func (c *Consumer) processMessage(delivery amqp.Delivery) error {
	// 创建消息处理的上下文
	ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
	defer cancel()

	// 调用处理函数
	err := c.handler(ctx, delivery)

	// 根据处理结果确认或拒绝消息
	if !c.config.AutoAck {
		if err != nil {
			// 拒绝消息并重新排队
			if nackErr := delivery.Nack(false, true); nackErr != nil {
				c.logger.Error(fmt.Sprintf("Nack消息失败 错误：%s，投递标签：%d",
					nackErr.Error(), delivery.DeliveryTag))
			} else {
				c.stats.TotalNacked++
			}
		} else {
			// 确认消息
			if ackErr := delivery.Ack(false); ackErr != nil {
				c.logger.Error(fmt.Sprintf("Ack消息失败 错误：%s，投递标签：%d",
					ackErr.Error(), delivery.DeliveryTag))
			} else {
				c.stats.TotalAcked++
			}
		}
	}

	return err
}

// Stop 停止消费
func (c *Consumer) Stop() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.isRunning {
		return nil
	}

	// 取消上下文，停止工作者
	c.cancel()

	// 取消消费
	if c.config.ConsumerTag != "" && c.manager.IsConnected() {
		if err := c.manager.channel.Cancel(c.config.ConsumerTag, false); err != nil {
			c.logger.Error(fmt.Sprintf("取消消费失败：%s", err.Error()))
		}
	}

	// 等待所有工作者完成
	c.wg.Wait()

	c.isRunning = false

	c.logger.Debug(fmt.Sprintf("消费者已停止 名称：%s，总接收：%d，总处理：%d，总错误：%d",
		c.name, c.stats.TotalReceived, c.stats.TotalProcessed, c.stats.TotalErrors))

	return nil
}

// IsRunning 检查运行状态
func (c *Consumer) IsRunning() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.isRunning
}

// GetStats 获取统计信息
func (c *Consumer) GetStats() ConsumerStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.stats
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	return c.Stop()
}

// SetConfig 设置配置
func (c *Consumer) SetConfig(config *ConsumerConfig) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.config = config
}

// GetConfig 获取配置
func (c *Consumer) GetConfig() *ConsumerConfig {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.config
}
