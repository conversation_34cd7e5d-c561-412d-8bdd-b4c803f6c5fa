package rabbitmq

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"go-monitor/pkg/logging"
	"sync"
	"time"

	"github.com/streadway/amqp"
)

// Config RabbitMQ配置
type Config struct {
	URL           string        `json:"url"`
	Exchange      string        `json:"exchange"`
	ExchangeType  string        `json:"exchange_type"`
	RoutingKey    string        `json:"routing_key"`
	QueueName     string        `json:"queue_name"`
	Durable       bool          `json:"durable"`
	AutoDelete    bool          `json:"auto_delete"`
	Exclusive     bool          `json:"exclusive"`
	NoWait        bool          `json:"no_wait"`
	PrefetchCount int           `json:"prefetch_count"`
	Timeout       time.Duration `json:"timeout"`
	RetryCount    int           `json:"retry_count"`
	RetryDelay    time.Duration `json:"retry_delay"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		URL:           "amqp://guest:guest@localhost:5672/",
		Exchange:      "monitor",
		ExchangeType:  "topic",
		Routing<PERSON>ey:    "monitor.notifications",
		QueueName:     "monitor_queue",
		Durable:       true,
		AutoDelete:    false,
		Exclusive:     false,
		NoWait:        false,
		PrefetchCount: 10,
		Timeout:       30 * time.Second,
		RetryCount:    3,
		RetryDelay:    5 * time.Second,
	}
}

// Manager RabbitMQ管理器
type Manager struct {
	config      *Config
	connection  *amqp.Connection
	channel     *amqp.Channel
	logger      logging.Logger
	isConnected bool
	mutex       sync.RWMutex

	// 监控信息
	stats *Stats

	// 生产者和消费者
	producers map[string]*Producer
	consumers map[string]*Consumer

	// 错误处理
	errorChan    chan *amqp.Error
	closeChan    chan bool
	reconnectMux sync.Mutex

	// 上下文控制
	ctx    context.Context
	cancel context.CancelFunc
}

// Stats 统计信息
type Stats struct {
	ConnectedAt     time.Time `json:"connected_at"`
	TotalSent       int64     `json:"total_sent"`
	TotalReceived   int64     `json:"total_received"`
	TotalErrors     int64     `json:"total_errors"`
	ConnectionCount int64     `json:"connection_count"`
	LastError       string    `json:"last_error"`
	LastErrorTime   time.Time `json:"last_error_time"`
}

// NewManager 创建新的RabbitMQ管理器
func NewManager(config *Config) *Manager {
	if config == nil {
		config = DefaultConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &Manager{
		config:    config,
		logger:    logging.GetLogger("rabbitmq"),
		stats:     &Stats{},
		producers: make(map[string]*Producer),
		consumers: make(map[string]*Consumer),
		errorChan: make(chan *amqp.Error),
		closeChan: make(chan bool),
		ctx:       ctx,
		cancel:    cancel,
	}
}

// Connect 连接到RabbitMQ
func (m *Manager) Connect() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isConnected {
		return nil
	}

	// 连接到RabbitMQ
	conn, err := amqp.Dial(m.config.URL)
	if err != nil {
		m.stats.TotalErrors++
		m.stats.LastError = err.Error()
		m.stats.LastErrorTime = time.Now()
		return fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	// 创建通道
	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		m.stats.TotalErrors++
		m.stats.LastError = err.Error()
		m.stats.LastErrorTime = time.Now()
		return fmt.Errorf("failed to open channel: %w", err)
	}

	// 设置QoS
	if err := ch.Qos(m.config.PrefetchCount, 0, false); err != nil {
		ch.Close()
		conn.Close()
		m.stats.TotalErrors++
		m.stats.LastError = err.Error()
		m.stats.LastErrorTime = time.Now()
		return fmt.Errorf("failed to set QoS: %w", err)
	}

	m.connection = conn
	m.channel = ch
	m.isConnected = true
	m.stats.ConnectedAt = time.Now()
	m.stats.ConnectionCount++

	// 监听连接错误
	go m.handleConnectionErrors()

	// 声明Exchange和Queue
	if err := m.setupInfrastructure(); err != nil {
		return err
	}

	m.logger.Debug(fmt.Sprintf("成功连接到RabbitMQ：%s，交换机：%s，队列：%s",
		m.config.URL, m.config.Exchange, m.config.QueueName))

	return nil
}

// setupInfrastructure 设置RabbitMQ基础设施
func (m *Manager) setupInfrastructure() error {
	// 声明Exchange
	err := m.channel.ExchangeDeclare(
		m.config.Exchange,     // 名称
		m.config.ExchangeType, // 类型
		m.config.Durable,      // 持久化
		m.config.AutoDelete,   // 自动删除
		false,                 // internal
		m.config.NoWait,       // no-wait
		nil,                   // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to declare exchange: %w", err)
	}

	// 声明Queue
	queue, err := m.channel.QueueDeclare(
		m.config.QueueName,  // 名称
		m.config.Durable,    // 持久化
		m.config.AutoDelete, // 自动删除
		m.config.Exclusive,  // 独占
		m.config.NoWait,     // no-wait
		nil,                 // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to declare queue: %w", err)
	}

	// 绑定Queue到Exchange
	err = m.channel.QueueBind(
		queue.Name,          // queue name
		m.config.RoutingKey, // routing key
		m.config.Exchange,   // exchange
		m.config.NoWait,     // no-wait
		nil,                 // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to bind queue: %w", err)
	}

	return nil
}

// handleConnectionErrors 处理连接错误
func (m *Manager) handleConnectionErrors() {
	for {
		select {
		case err := <-m.connection.NotifyClose(make(chan *amqp.Error)):
			if err != nil {
				m.logger.Error(fmt.Sprintf("RabbitMQ连接丢失：%s", err.Error()))
				m.stats.TotalErrors++
				m.stats.LastError = err.Error()
				m.stats.LastErrorTime = time.Now()
				m.handleReconnect()
			}
		case <-m.ctx.Done():
			return
		}
	}
}

// handleReconnect 处理重连
func (m *Manager) handleReconnect() {
	m.reconnectMux.Lock()
	defer m.reconnectMux.Unlock()

	m.mutex.Lock()
	m.isConnected = false
	m.mutex.Unlock()

	// 等待并重连
	for i := 0; i < m.config.RetryCount; i++ {
		time.Sleep(m.config.RetryDelay)

		m.logger.Info(fmt.Sprintf("尝试重连RabbitMQ，第%d次，最大%d次，延迟：%.1fs",
			i+1, m.config.RetryCount, m.config.RetryDelay.Seconds()))

		if err := m.Connect(); err == nil {
			m.logger.Info("RabbitMQ重连成功")
			return
		}
	}

	m.logger.Error(fmt.Sprintf("RabbitMQ重连失败，已达到最大重试次数：%d", m.config.RetryCount))
}

// Disconnect 断开连接
func (m *Manager) Disconnect() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isConnected {
		return nil
	}

	// 取消上下文
	m.cancel()

	// 关闭生产者和消费者
	for _, producer := range m.producers {
		producer.Close()
	}
	for _, consumer := range m.consumers {
		consumer.Close()
	}

	// 关闭通道和连接
	if m.channel != nil {
		m.channel.Close()
	}
	if m.connection != nil {
		m.connection.Close()
	}

	m.isConnected = false
	m.logger.Debug("已断开RabbitMQ连接")

	return nil
}

// IsConnected 检查连接状态
func (m *Manager) IsConnected() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.isConnected
}

// GetStats 获取统计信息
func (m *Manager) GetStats() *Stats {
	return m.stats
}

// Publish 发布消息
func (m *Manager) Publish(routingKey string, body []byte, headers map[string]interface{}) error {
	if !m.IsConnected() {
		return errors.New("RabbitMQ未连接")
	}

	publishing := amqp.Publishing{
		ContentType:  "application/json",
		DeliveryMode: amqp.Persistent, // 持久化消息
		Timestamp:    time.Now(),
		Body:         body,
		Headers:      headers,
	}

	err := m.channel.Publish(
		m.config.Exchange, // exchange
		routingKey,        // routing key
		false,             // mandatory
		false,             // immediate
		publishing,        // publishing
	)

	if err != nil {
		m.stats.TotalErrors++
		m.stats.LastError = err.Error()
		m.stats.LastErrorTime = time.Now()
		return fmt.Errorf("failed to publish message: %w", err)
	}

	m.stats.TotalSent++
	return nil
}

// PublishJSON 发布JSON消息
func (m *Manager) PublishJSON(routingKey string, data interface{}, headers map[string]interface{}) error {
	body, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}

	return m.Publish(routingKey, body, headers)
}

// CreateProducer 创建生产者
func (m *Manager) CreateProducer(name string, config *ProducerConfig) (*Producer, error) {
	if !m.IsConnected() {
		return nil, errors.New("RabbitMQ未连接")
	}

	producer := NewProducer(name, m, config)
	m.producers[name] = producer

	return producer, nil
}

// CreateConsumer 创建消费者
func (m *Manager) CreateConsumer(name string, config *ConsumerConfig) (*Consumer, error) {
	if !m.IsConnected() {
		return nil, errors.New("RabbitMQ未连接")
	}

	consumer := NewConsumer(name, m, config)
	m.consumers[name] = consumer

	return consumer, nil
}

// GetProducer 获取生产者
func (m *Manager) GetProducer(name string) *Producer {
	return m.producers[name]
}

// GetConsumer 获取消费者
func (m *Manager) GetConsumer(name string) *Consumer {
	return m.consumers[name]
}

// DefaultManager 默认管理器实例
var DefaultManager *Manager

func init() {
	DefaultManager = NewManager(DefaultConfig())
}

// GlobalConnect 全局连接
func GlobalConnect(config *Config) error {
	if config != nil {
		DefaultManager.config = config
	}
	return DefaultManager.Connect()
}

// GlobalDisconnect 全局断开连接
func GlobalDisconnect() error {
	return DefaultManager.Disconnect()
}

// GlobalPublish 全局发布消息
func GlobalPublish(routingKey string, body []byte, headers map[string]interface{}) error {
	return DefaultManager.Publish(routingKey, body, headers)
}

// GlobalPublishJSON 全局发布JSON消息
func GlobalPublishJSON(routingKey string, data interface{}, headers map[string]interface{}) error {
	return DefaultManager.PublishJSON(routingKey, data, headers)
}
