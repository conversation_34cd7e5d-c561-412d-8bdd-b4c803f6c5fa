package rabbitmq

import (
	"encoding/json"
	"fmt"
	"go-monitor/pkg/logging"
	"sync"
	"time"

	"github.com/streadway/amqp"
)

// ProducerConfig 生产者配置
type ProducerConfig struct {
	RoutingKey    string                 `json:"routing_key"`
	Exchange      string                 `json:"exchange"`
	Mandatory     bool                   `json:"mandatory"`
	Immediate     bool                   `json:"immediate"`
	Headers       map[string]interface{} `json:"headers"`
	ContentType   string                 `json:"content_type"`
	DeliveryMode  uint8                  `json:"delivery_mode"`
	Expiration    string                 `json:"expiration"`
	Priority      uint8                  `json:"priority"`
	CorrelationID string                 `json:"correlation_id"`
	ReplyTo       string                 `json:"reply_to"`
	MessageID     string                 `json:"message_id"`
	Timestamp     bool                   `json:"timestamp"`
	Type          string                 `json:"type"`
	UserID        string                 `json:"user_id"`
	AppID         string                 `json:"app_id"`
}

// DefaultProducerConfig 默认生产者配置
func DefaultProducerConfig() *ProducerConfig {
	return &ProducerConfig{
		RoutingKey:   "monitor.notifications",
		Exchange:     "monitor",
		Mandatory:    false,
		Immediate:    false,
		Headers:      make(map[string]interface{}),
		ContentType:  "application/json",
		DeliveryMode: amqp.Persistent,
		Priority:     0,
		Timestamp:    true,
	}
}

// Producer RabbitMQ生产者
type Producer struct {
	name    string
	manager *Manager
	config  *ProducerConfig
	logger  logging.Logger
	mutex   sync.RWMutex

	// 统计信息
	stats ProducerStats
}

// ProducerStats 生产者统计
type ProducerStats struct {
	TotalSent     int64     `json:"total_sent"`
	TotalErrors   int64     `json:"total_errors"`
	LastSentTime  time.Time `json:"last_sent_time"`
	LastError     string    `json:"last_error"`
	LastErrorTime time.Time `json:"last_error_time"`
}

// NewProducer 创建新的生产者
func NewProducer(name string, manager *Manager, config *ProducerConfig) *Producer {
	if config == nil {
		config = DefaultProducerConfig()
	}

	return &Producer{
		name:    name,
		manager: manager,
		config:  config,
		logger:  manager.logger.Named(fmt.Sprintf("producer.%s", name)),
		stats:   ProducerStats{},
	}
}

// Publish 发布消息
func (p *Producer) Publish(routingKey string, body []byte, headers map[string]interface{}) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.manager.IsConnected() {
		err := fmt.Errorf("RabbitMQ未连接")
		p.stats.TotalErrors++
		p.stats.LastError = err.Error()
		p.stats.LastErrorTime = time.Now()
		return err
	}

	// 合并headers
	finalHeaders := make(map[string]interface{})
	for k, v := range p.config.Headers {
		finalHeaders[k] = v
	}
	for k, v := range headers {
		finalHeaders[k] = v
	}

	// 使用配置的routing key如果没有指定
	if routingKey == "" {
		routingKey = p.config.RoutingKey
	}

	// 构建消息
	publishing := amqp.Publishing{
		Headers:       finalHeaders,
		ContentType:   p.config.ContentType,
		DeliveryMode:  p.config.DeliveryMode,
		Priority:      p.config.Priority,
		CorrelationId: p.config.CorrelationID,
		ReplyTo:       p.config.ReplyTo,
		MessageId:     p.config.MessageID,
		Type:          p.config.Type,
		UserId:        p.config.UserID,
		AppId:         p.config.AppID,
		Body:          body,
	}

	// 设置时间戳
	if p.config.Timestamp {
		publishing.Timestamp = time.Now()
	}

	// 设置过期时间
	if p.config.Expiration != "" {
		publishing.Expiration = p.config.Expiration
	}

	// 发布消息
	err := p.manager.channel.Publish(
		p.config.Exchange,  // exchange
		routingKey,         // routing key
		p.config.Mandatory, // mandatory
		p.config.Immediate, // immediate
		publishing,         // publishing
	)

	if err != nil {
		p.stats.TotalErrors++
		p.stats.LastError = err.Error()
		p.stats.LastErrorTime = time.Now()
		p.logger.Error(fmt.Sprintf("发布消息失败：%s，路由键：%s", err.Error(), routingKey))
		return fmt.Errorf("failed to publish message: %w", err)
	}

	p.stats.TotalSent++
	p.stats.LastSentTime = time.Now()

	p.logger.Debug(fmt.Sprintf("成功发布消息，路由键：%s，大小：%d", routingKey, len(body)))

	return nil
}

// PublishJSON 发布JSON消息
func (p *Producer) PublishJSON(routingKey string, data interface{}, headers map[string]interface{}) error {
	body, err := json.Marshal(data)
	if err != nil {
		p.stats.TotalErrors++
		p.stats.LastError = err.Error()
		p.stats.LastErrorTime = time.Now()
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}

	return p.Publish(routingKey, body, headers)
}

// PublishWithConfirm 发布消息并等待确认
func (p *Producer) PublishWithConfirm(routingKey string, body []byte, headers map[string]interface{}) error {
	if !p.manager.IsConnected() {
		return fmt.Errorf("RabbitMQ未连接")
	}

	// 启用发布确认
	if err := p.manager.channel.Confirm(false); err != nil {
		return fmt.Errorf("failed to enable confirm mode: %w", err)
	}

	confirms := p.manager.channel.NotifyPublish(make(chan amqp.Confirmation, 1))

	// 发布消息
	if err := p.Publish(routingKey, body, headers); err != nil {
		return err
	}

	// 等待确认
	select {
	case confirm := <-confirms:
		if !confirm.Ack {
			err := fmt.Errorf("message not acknowledged")
			p.stats.TotalErrors++
			p.stats.LastError = err.Error()
			p.stats.LastErrorTime = time.Now()
			return err
		}
		return nil
	case <-time.After(30 * time.Second):
		err := fmt.Errorf("confirmation timeout")
		p.stats.TotalErrors++
		p.stats.LastError = err.Error()
		p.stats.LastErrorTime = time.Now()
		return err
	}
}

// GetStats 获取统计信息
func (p *Producer) GetStats() ProducerStats {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.stats
}

// Close 关闭生产者
func (p *Producer) Close() error {
	p.logger.Debug(fmt.Sprintf("关闭生产者：%s，总发送：%d", p.name, p.stats.TotalSent))
	return nil
}

// SetConfig 设置配置
func (p *Producer) SetConfig(config *ProducerConfig) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.config = config
}

// GetConfig 获取配置
func (p *Producer) GetConfig() *ProducerConfig {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.config
}
