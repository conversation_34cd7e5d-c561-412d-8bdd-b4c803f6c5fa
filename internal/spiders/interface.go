package spiders

import (
	"context"

	"go-monitor/internal/models"
	"go-monitor/internal/types"
)

/*
配置参数使用指南 (Config Parameter Usage Guide)

Go版本的spider接口现在完全对齐Python版本，所有方法都支持config参数传递。
每个阶段(GetProductIDs, PrepareRequest, ParseResponse)都能接收统一的监控配置。

=== 标准配置参数 ===

必需参数 (Required Parameters):
• url (string): 站点URL，用于构建产品URL和ATC链接
  - 示例: "https://www.popmart.com", "https://www.amazon.com"

• name (string): 监控名称，用于日志记录和国家信息
  - 示例: "US", "UK", "PopMart_Monitor"

可选参数 (Optional Parameters):
• notifications ([]string): 通知组列表
  - 示例: ["group1", "group2"]

• currency (string): 货币符号
  - 示例: "$", "€", "£", "¥"
  - 默认值: 各爬虫自定义

• country (string): 国家代码
  - 示例: "US", "UK", "CN", "JP"
  - 默认值: "US"

爬虫特定参数 (Spider-specific Parameters):
• spider_type (string): 爬虫类型标识
• proxies ([]string): 代理组配置
• cookies ([]string): Cookie组配置

=== 配置传递机制 ===

1. 配置优先级 (Priority Order):
   新传入的config > config.Parameters > response.Request.Metadata

2. 配置合并策略:
   使用MergeConfigs()函数智能合并多层配置

3. 类型安全访问:
   • GetConfigString(config, key, defaultValue)
   • GetConfigStringArray(config, key)
   • GetConfigInt(config, key, defaultValue)
   • GetConfigBool(config, key, defaultValue)

=== 使用示例 ===

config := map[string]interface{}{
    "url": "https://www.popmart.com",
    "name": "PopMart_US_Monitor",
    "currency": "$",
    "country": "US",
    "notifications": []string{"webhook1", "discord1"},
    "spider_type": "popmart",
}

// 所有方法现在都支持config参数
productIDs := spider.GetProductIDs(config)
request, err := spider.PrepareRequest(ctx, config)
products, err := spider.ParseResponse(ctx, response, config)

=== 与Python版本对齐 ===

Go版本现在完全实现了Python版本中的配置使用模式：
• parse_response方法中的config['url']用于构建产品URL
• config['notifications']传递到ProductItem
• config['currency']用于价格显示
• config['name']用作country信息

这确保了两个版本行为的完全一致性。
*/

// Spider 爬虫接口 - 定义所有爬虫必须实现的方法
//
// 配置传递机制：
// 每个方法都接收 config map[string]interface{} 参数，用于传递监控配置。
// 配置内容包括但不限于：
// - product_ids: 产品ID列表
// - spider_type: 爬虫类型
// - proxies: 代理配置
// - cookies: Cookie配置
// - country: 国家代码
// - currency: 货币代码
// - name: 监控名称
// - 其他平台特定配置
//
// 示例配置：
//
//	config := map[string]interface{}{
//	    "product_ids": []string{"12345", "67890"},
//	    "spider_type": "amazon",
//	    "proxies": "default",
//	    "country": "US",
//	    "currency": "USD",
//	    "name": "我的监控",
//	}
type Spider interface {
	// GetProductIDs 获取产品ID集合 - 对应Python版本的get_product_ids
	//
	// Args:
	//   config: 爬虫配置，类型安全的配置结构
	//
	// Returns:
	//   产品ID字符串切片
	//
	// 示例：
	//   config := &SpiderConfig{
	//       SpiderSettings: map[string]interface{}{
	//           "product_ids": []interface{}{"12345", "67890"},
	//       },
	//   }
	//   ids := spider.GetProductIDs(config)
	GetProductIDs(config *types.SpiderConfig) []string

	// PrepareRequest 准备请求 - 对应Python版本的prepare_request
	//
	// Args:
	//   ctx: 上下文
	//   config: 爬虫配置，类型安全的配置结构
	//   productID: 产品ID，避免修改共享配置对象
	//
	// Returns:
	//   构建好的HTTP请求对象
	//
	// 示例：
	//   config := &SpiderConfig{
	//       Platform: "amazon",
	//       Country: "US",
	//       SpiderSettings: map[string]interface{}{
	//           "proxies": "residential",
	//       },
	//   }
	//   req, err := spider.PrepareRequest(ctx, config, "12345")
	PrepareRequest(ctx context.Context, productID string, config *types.SpiderConfig) (*models.Request, error)

	// ParseResponse 解析响应 - 对应Python版本的parse_response
	//
	// Args:
	//   ctx: 上下文
	//   resp: HTTP响应对象
	//   config: 爬虫配置，类型安全的配置结构
	//
	// Returns:
	//   解析后的产品信息列表
	//
	// 示例：
	//   config := &SpiderConfig{
	//       Name: "我的监控",
	//       Country: "US",
	//       SpiderSettings: map[string]interface{}{
	//           "filters": map[string]interface{}{
	//               "min_price": 10.0,
	//               "max_price": 100.0,
	//           },
	//       },
	//   }
	//   products, err := spider.ParseResponse(ctx, resp, config)
	ParseResponse(ctx context.Context, resp *models.Response, config *types.SpiderConfig) ([]*models.ProductItem, error)

	// GetName 获取爬虫名称
	GetName() string

	// GetVersion 获取爬虫版本
	GetVersion() string

	// GetDescription 获取爬虫描述
	GetDescription() string

	// 配置验证
	ValidateConfig(config *types.SpiderConfig) error

	// 获取默认配置
	GetDefaultConfig() *types.SpiderConfig

	// ========== 批量处理能力接口 ==========

	// SupportsBatchProcessing 返回爬虫是否支持批量处理
	//
	// Returns:
	//   true: 支持批量处理多个产品ID
	//   false: 只支持单个产品ID处理
	//
	// 示例：
	//   if spider.SupportsBatchProcessing() {
	//       // 使用批量处理
	//   } else {
	//       // 使用单个处理
	//   }
	SupportsBatchProcessing() bool

	// GetMaxBatchSize 返回最大批量处理大小
	//
	// Returns:
	//   最大批量大小，如果不支持批量处理则返回1
	//
	// 示例：
	//   maxSize := spider.GetMaxBatchSize()
	//   if len(productIDs) > maxSize {
	//       // 需要分批处理
	//   }
	GetMaxBatchSize() int

	// PrepareBatchRequest 准备批量请求 - 可选实现
	//
	// Args:
	//   ctx: 上下文
	//   config: 爬虫配置
	//   productIDs: 产品ID列表
	//
	// Returns:
	//   构建好的HTTP请求对象
	//
	// 注意：
	//   只有SupportsBatchProcessing()返回true的爬虫需要实现此方法
	//   其他爬虫可以返回错误或使用默认实现
	//
	// 示例：
	//   if spider.SupportsBatchProcessing() {
	//       req, err := spider.PrepareBatchRequest(ctx, config, []string{"id1", "id2"})
	//   }
	PrepareBatchRequest(ctx context.Context, productIDs []string, config *SpiderConfig) (*models.Request, error)
}

// BatchProcessingConfig 类型别名 - 使用统一的配置类型
type BatchProcessingConfig = types.BatchProcessingConfig

// SpiderConfig 类型别名 - 使用统一的配置类型
type SpiderConfig = types.SpiderConfig

// ParseDurations 解析时间配置 - 已移除，使用GetIntervalDuration和GetTimeoutDuration方法代替

// 配置处理已统一到 internal/config 包中
