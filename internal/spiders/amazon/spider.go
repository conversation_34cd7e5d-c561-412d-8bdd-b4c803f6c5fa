package amazon

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/internal/spiders/shared"

	"github.com/antchfx/htmlquery"
	"golang.org/x/net/html"
)

// ========== 常量定义 ==========

// HTML选择器常量 - 使用分层回退策略
const (
	// 产品信息选择器
	selectorProductTitle = `//h5[@id='aod-asin-title-text']/text()`
	selectorProductImage = `//img[@id='aod-asin-image-id']/@src`
)

// 分层选择器配置 - 从最具体到最通用
var (
	// Pinned Offer选择器（优先级从高到低）
	pinnedOfferSelectors = []string{
		`//div[@id='aod-pinned-offer']`,                 // 主要ID选择器
		`//div[contains(@class, 'aod-pinned-offer')]`,   // Class选择器
		`//div[contains(@class, 'pinned-offer-block')]`, // 备选class选择器
	}

	// Offer List容器选择器
	offerListSelectors = []string{
		`//div[@id='aod-offer-list']`,               // 主要ID选择器
		`//div[contains(@class, 'aod-offer-list')]`, // Class选择器备选
	}

	// 单个Offer节点选择器
	offerNodeSelectors = []string{
		`.//div[contains(@class, 'aod-information-block')]`, // 最稳定的class选择器
		`.//div[@role='listitem']`,                          // 语义化选择器
		`.//div[@id='aod-offer']`,                           // ID选择器（不稳定）
	}

	// 价格选择器（优先级从高到低）
	priceSelectors = []string{
		`.//span[@class='aok-offscreen']`,         // 最可靠的价格文本
		`.//span[starts-with(@id, 'aod-price-')]`, // 动态价格ID
		`.//span[contains(@class, 'a-price')]`,    // 通用价格容器
	}

	// 数据提取选择器
	asinSelectors = []string{
		`.//input[@name='items[0.base][asin]']`,
		`.//input[contains(@name, 'asin')]`,
	}

	offerListingIDSelectors = []string{
		`.//input[@name='items[0.base][offerListingId]']`,
		`.//input[contains(@name, 'offerListingId')]`,
	}

	// 商户信息选择器
	soldBySelectors = []string{
		`.//div[@id='aod-offer-soldBy']`,
		`.//div[contains(@class, 'aod-offer-soldBy')]`,
		`.//div[contains(text(), 'Sold by') or contains(text(), 'Ships from')]/../..`,
	}

	merchantLinkSelectors = []string{
		`.//a[contains(@href, 'seller=')]`,
		`.//a[contains(@href, '&amp;seller=')]`,
		`.//a[contains(@href, '/gp/aag/main')]`,
	}

	// 库存按钮选择器
	stockButtonSelectors = []string{
		`.//input[@name='submit.addToCart' and @type='submit' and not(@disabled)]`,
		`.//input[@name='submit.add-to-cart' and @type='submit' and not(@disabled)]`,
		`.//input[@value='Add to Cart' and @type='submit' and not(@disabled)]`,
		`.//input[contains(@aria-label, 'Add to basket') and @type='submit' and not(@disabled)]`,
		`.//button[contains(@class, 'a-button-primary') and not(@disabled)]`, // 备选按钮选择器
	}

	// 货币符号数组
	currencySymbols = []string{"€", "$", "£", "¥", "￥"}
)

// ========== 工具函数 ==========

// findNodeWithFallback 使用回退机制查找节点
func findNodeWithFallback(doc *html.Node, selectors []string) *html.Node {
	for _, selector := range selectors {
		if node := htmlquery.FindOne(doc, selector); node != nil {
			return node
		}
	}
	return nil
}

// findNodesWithFallback 使用回退机制查找多个节点
func findNodesWithFallback(doc *html.Node, selectors []string) []*html.Node {
	for _, selector := range selectors {
		if nodes := htmlquery.Find(doc, selector); len(nodes) > 0 {
			return nodes
		}
	}
	return nil
}

// extractAttributeWithFallback 使用回退机制提取属性值
func extractAttributeWithFallback(node *html.Node, selectors []string, attribute string) string {
	for _, selector := range selectors {
		if attrNode := htmlquery.FindOne(node, selector); attrNode != nil {
			value := strings.TrimSpace(htmlquery.SelectAttr(attrNode, attribute))
			if value != "" {
				return value
			}
		}
	}
	return ""
}

// buildProductURL 构建产品URL - 使用shared包函数
func buildProductURL(siteURL, asin string) string {
	return shared.BuildProductURL(siteURL, shared.AmazonProductPagePath, asin)
}

// buildATCLink 构建ATC (Add to Cart) 链接
func buildATCLink(siteURL, offerListingId, asin string) *string {
	if offerListingId == "" || asin == "" {
		return nil
	}

	baseURL := fmt.Sprintf("%s%s?offerListingID=%s&asin=%s&submit.buy-now=1",
		siteURL, shared.AmazonATCBasePath, offerListingId, asin)

	// 验证构建的URL包含当前ASIN
	if !strings.Contains(baseURL, asin) {
		return nil
	}

	link := fmt.Sprintf("[x1](%s&quantity.1=1) | [x2](%s&quantity.1=2)", baseURL, baseURL)
	return &link
}

// createOfferIDPointer 创建OfferID指针 - 使用shared包函数
func createOfferIDPointer(offerListingId string) *string {
	return shared.CreateStringPointer(offerListingId)
}

// ========== 类型定义 ==========

// DataExtractor 统一数据提取器
type DataExtractor struct {
	spider *Spider
}

// ExtractorConfig 提取器配置
type ExtractorConfig struct {
	NodeType  string            // "pinned" 或 "offer"
	PriceID   string            // 价格元素ID
	Selectors map[string]string // 选择器映射
}

// Spider Amazon商品监控爬虫
type Spider struct {
	*spiders.BaseSpider
	defaultHeaders map[string]string
	extractor      *DataExtractor
}

// ========== 构造函数 ==========

// NewSpider 创建Amazon爬虫实例
func NewSpider() spiders.Spider {
	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider(shared.PlatformAmazon, shared.SpiderVersion, "Amazon商品监控爬虫"),
		defaultHeaders: map[string]string{
			"Accept":           "application/json, text/plain, */*",
			"Accept-Language":  "en-US,en;q=0.9",
			"Accept-Encoding":  "gzip, deflate, br, zstd",
			"Cache-Control":    "no-cache",
			"Pragma":           "no-cache",
			"X-Requested-With": "XMLHttpRequest",
			"Sec-Fetch-Dest":   "empty",
			"Sec-Fetch-Mode":   "cors",
			"Sec-Fetch-Site":   "same-origin",
			"Priority":         "u=1, i",
		},
	}
	spider.extractor = &DataExtractor{spider: spider}
	return spider
}

// ========== 接口实现方法 ==========

// GetProductIDs 获取产品ID集合 - 对应Python版本的get_product_ids
func (a *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// PrepareRequest 准备请求 - 实现Spider接口
func (s *Spider) PrepareRequest(ctx context.Context, productID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	// 获取站点URL
	siteURL := fmt.Sprintf("%s%s", spiderConfig.SiteURL, shared.AmazonProductAjaxPath)

	params := map[string]string{
		"asin":         productID,
		"filters":      url.QueryEscape(shared.AmazonFilterParam),
		"pc":           shared.AmazonPCParam,
		"experienceId": shared.AmazonExperienceID,
	}

	builder := shared.CreateGenericSpiderRequest(shared.PlatformAmazon, siteURL, spiderConfig, productID)

	for key, value := range params {
		builder.AddParam(key, value)
	}
	builder.SetMethod("POST")

	httpRequest := builder.Build()

	return &models.Request{
		SpiderType:  shared.PlatformAmazon,
		URL:         httpRequest.URL,
		Method:      httpRequest.Method,
		Headers:     httpRequest.Headers,
		Body:        httpRequest.Body,
		Params:      httpRequest.Params,
		Metadata:    httpRequest.Metadata,
		MonitorName: spiderConfig.Name,
		ProductID:   productID,
	}, nil
}

// ParseResponse 解析响应 - 实现Spider接口
func (a *Spider) ParseResponse(ctx context.Context, response *models.Response, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	if response.StatusCode != shared.HTTPStatusOK {
		return nil, fmt.Errorf("HTTP状态码错误: %d", response.StatusCode)
	}

	responseStr := string(response.Body)

	productID := ""
	if response.Request != nil {
		productID = response.Request.GetMetadataString("product_id", "")
		if productID == "" {
			productID = response.Request.ProductID
		}
	}

	products, err := a.parseHTMLResponse(responseStr, config, productID)
	if err != nil {
		return nil, fmt.Errorf("解析HTML响应失败: %w", err)
	}
	a.LogDebug("成功解析Amazon HTML响应，产品数量：%d", len(products))
	return products, nil

}

// parseHTMLResponse 解析HTML格式的Amazon All Offers Display响应
func (a *Spider) parseHTMLResponse(htmlStr string, spiderConfig *spiders.SpiderConfig, productID string) ([]*models.ProductItem, error) {
	// 提取HTML产品数据
	data, err := a.extractHTMLProductData(htmlStr, productID, spiderConfig)
	if err != nil {
		return nil, fmt.Errorf("提取HTML产品数据失败: %w", err)
	}

	// 构建产品项（商户ID验证已在extractHTMLProductData中完成）
	product := a.buildProductFromHTML(data, htmlStr, spiderConfig)

	return []*models.ProductItem{product}, nil
}

// ========== HTML解析核心方法 ==========

// extractHTMLProductData 从HTML中提取产品数据
func (a *Spider) extractHTMLProductData(htmlStr string, productID string, spiderConfig *spiders.SpiderConfig) (*HTMLProductData, error) {
	// 解析HTML文档
	doc, err := htmlquery.Parse(strings.NewReader(htmlStr))
	if err != nil {
		return nil, fmt.Errorf("解析HTML文档失败: %w", err)
	}

	// 提取产品标题
	title := a.extractTitleFromHTML(doc)
	if title == "" {
		return nil, fmt.Errorf("无法提取产品标题，页面结构异常或非产品页面")
	}

	// 创建默认数据的辅助函数
	createDefaultData := func(reason string) *HTMLProductData {
		a.LogDebug("返回默认数据：%s", reason)
		return &HTMLProductData{
			ASIN:           productID,
			Title:          title,
			Price:          0,
			OfferListingID: "",
			InStock:        false,
		}
	}

	// 1. 首先尝试从pinned offer提取数据
	if pinnedNode := findNodeWithFallback(doc, pinnedOfferSelectors); pinnedNode != nil {
		a.LogDebug("找到pinned offer节点，开始提取数据")
		if data, err := a.extractDataFromNode(pinnedNode, "pinned", doc, productID, title, spiderConfig, htmlStr); err == nil {
			a.LogDebug("从pinned offer成功提取数据")
			return data, nil
		} else {
			a.LogDebug("pinned offer数据提取失败: %v，尝试offer list", err)
		}
	} else {
		a.LogDebug("未找到pinned offer节点，尝试offer list")
	}

	// 2. 回退到offer list中遍历提取
	if data, err := a.extractFromOfferListNode(doc, productID, spiderConfig); err == nil && data != nil {
		a.LogDebug("从offer list成功提取数据")
		return data, nil
	} else {
		a.LogDebug("从offer list提取数据失败: %v", err)
	}

	// 所有方法都失败，返回默认数据
	return createDefaultData("未找到匹配的商户数据"), nil
}

// extractDataFromNode 从指定节点提取完整产品数据 - 简化版本
func (a *Spider) extractDataFromNode(node *html.Node, nodeType string, doc *html.Node, productID string, title string, spiderConfig *spiders.SpiderConfig, _ string) (*HTMLProductData, error) {
	if node == nil {
		return nil, fmt.Errorf("节点为空")
	}

	// 提取并验证ASIN（从input的value属性中提取）
	asin := extractAttributeWithFallback(node, asinSelectors, "value")
	if asin == "" {
		return nil, fmt.Errorf("无法从节点中提取ASIN，节点可能不包含有效的产品数据")
	}
	if asin != productID {
		return nil, fmt.Errorf("ASIN不匹配：提取到%s，期望%s", asin, productID)
	}

	// 验证商户ID
	if !a.validateMerchantID(node, nodeType, spiderConfig) {
		return nil, fmt.Errorf("商户ID不匹配目标商户")
	}

	// 创建简化的配置
	config := newExtractorConfig(nodeType)

	// 提取完整产品信息
	return &HTMLProductData{
		ASIN:           asin,
		Title:          title,
		Price:          a.extractor.extractPrice(node, config),
		OfferListingID: a.extractor.extractOfferListingID(node),
		InStock:        a.extractor.extractStockStatus(node),
		ImageURL:       a.extractImageURLFromHTML(doc),
	}, nil
}

// extractFromOfferListNode 遍历aod-offer-list中的所有aod-offer节点，寻找匹配的商户
func (a *Spider) extractFromOfferListNode(doc *html.Node, productID string, spiderConfig *spiders.SpiderConfig) (*HTMLProductData, error) {
	// 使用回退机制查找offer list容器
	offerListNode := findNodeWithFallback(doc, offerListSelectors)
	if offerListNode == nil {
		return nil, fmt.Errorf("未找到offer list容器节点")
	}

	// 使用回退机制查找所有offer节点（在offer list容器内查找）
	offerNodes := findNodesWithFallback(offerListNode, offerNodeSelectors)
	if len(offerNodes) == 0 {
		// offer list为空时返回nil，让主流程继续处理pinned offer
		return nil, nil
	}

	a.LogDebug("找到%d个offer节点，开始遍历", len(offerNodes))

	// 遍历所有offer节点，寻找匹配的商户ID
	for i, offerNode := range offerNodes {
		a.LogDebug("检查第%d个offer节点", i+1)

		// 尝试从当前offer节点提取数据
		if data, err := a.extractDataFromNode(offerNode, "offer", doc, productID, a.extractTitleFromHTML(doc), spiderConfig, ""); err == nil {
			a.LogDebug("第%d个offer节点匹配成功，提取数据", i+1)
			return data, nil
		} else {
			a.LogDebug("第%d个offer节点不匹配: %v，继续下一个", i+1, err)
		}
	}

	// 所有offer节点都不匹配
	return nil, fmt.Errorf("遍历了%d个offer节点，未找到匹配的商户ID", len(offerNodes))
}

// ========== 基础数据提取方法 ==========

// extractTitleFromHTML 从HTML中提取产品标题
func (a *Spider) extractTitleFromHTML(doc *html.Node) string {
	// 从aod-asin-title-text元素中提取标题
	if node := htmlquery.FindOne(doc, selectorProductTitle); node != nil {
		return strings.TrimSpace(htmlquery.InnerText(node))
	}
	return ""
}

// ========== 商户验证方法 ==========

// validateMerchantID 验证指定节点中的商户ID是否匹配配置 - 简化版本
func (a *Spider) validateMerchantID(node *html.Node, _ string, spiderConfig *spiders.SpiderConfig) bool {
	var targetMerchantID string

	if spiderConfig != nil && spiderConfig.SpiderSettings != nil {
		if merchantID, ok := spiderConfig.SpiderSettings["merchantID"].(string); ok {
			targetMerchantID = merchantID
		}
	}

	// 如果没有配置merchantID，则认为验证通过
	if targetMerchantID == "" {
		return true
	}

	if node == nil {
		return false
	}

	// 直接提取商户ID，不需要复杂的配置
	pageMerchantID := a.extractor.extractMerchantID(node)

	// 验证商户ID是否匹配
	if pageMerchantID == "" {
		// 如果配置了merchantID但页面中提取不到，说明不匹配
		return targetMerchantID == ""
	}

	return pageMerchantID == targetMerchantID
}

// ========== 辅助方法 ==========

// extractImageURLFromHTML 从HTML中提取产品图片URL
func (a *Spider) extractImageURLFromHTML(doc *html.Node) string {
	// 从img标签中提取图片URL
	if node := htmlquery.FindOne(doc, selectorProductImage); node != nil {
		return strings.TrimSpace(htmlquery.InnerText(node))
	}

	return ""
}

// buildProductFromHTML 从HTML数据构建产品项
func (a *Spider) buildProductFromHTML(data *HTMLProductData, htmlStr string, spiderConfig *spiders.SpiderConfig) *models.ProductItem {
	// 计算可用性 - 使用shared包函数
	availability := shared.GetAvailabilityText(data.InStock)

	// 处理各种无库存情况的可用性描述
	if !data.InStock {
		if strings.Contains(htmlStr, "No featured offers available") {
			availability = "No featured offers available"
		} else if strings.Contains(htmlStr, "Currently, there are no other sellers") {
			availability = "No other sellers available"
		}
	}

	// 构建产品URL
	siteURL := spiderConfig.SiteURL
	if siteURL == "" {
		siteURL = shared.AmazonDefaultSiteURL
	}
	productURL := buildProductURL(siteURL, data.ASIN)

	// 构建 ATC (Add to Cart) 链接
	var atcLink *string
	if data.OfferListingID != "" && data.InStock {
		atcLink = buildATCLink(siteURL, data.OfferListingID, data.ASIN)
	}

	// 处理图片URL
	var imageURL *string
	if data.ImageURL != "" {
		imageURL = &data.ImageURL
	}

	// 创建基础产品 - 使用shared包函数
	product := shared.CreateBaseProduct(spiderConfig.Name, data.ASIN, data.Title, productURL, shared.PlatformAmazon, siteURL, spiderConfig.Notifications)

	// 设置价格和库存信息 - 使用shared包函数
	currency := config.GetConfigString(spiderConfig, "currency", "€")
	shared.SetProductPriceAndStock(product, data.Price, currency, data.InStock, availability, createOfferIDPointer(data.OfferListingID))

	// 设置Amazon特定字段
	product.ImageURL = imageURL
	product.AtcLink = atcLink

	return product
}

// ========== 批量处理接口实现 ==========

// SupportsBatchProcessing Amazon爬虫支持批量处理
func (a *Spider) SupportsBatchProcessing() bool {
	return true
}

// GetMaxBatchSize Amazon爬虫的最大批量大小
func (a *Spider) GetMaxBatchSize() int {
	return shared.DefaultBatchSize
}

// ========== 统一数据提取器方法 ==========

// newExtractorConfig 创建提取器配置 - 简化版本
func newExtractorConfig(nodeType string) *ExtractorConfig {
	return &ExtractorConfig{
		NodeType: nodeType,
		PriceID:  "", // 不再依赖固定的价格ID，使用回退机制
		Selectors: map[string]string{
			// 这些选择器现在主要用于向后兼容，实际使用回退机制
			"priceWhole":     `.//span[@class='a-price-whole']/text()`,
			"priceFraction":  `.//span[@class='a-price-fraction']/text()`,
			"priceOffscreen": `.//span[@class='aok-offscreen']/text()`,
		},
	}
}

// extractPrice 提取价格 - 使用回退机制
func (e *DataExtractor) extractPrice(node *html.Node, _ *ExtractorConfig) float64 {
	if node == nil {
		return 0
	}

	// 使用回退机制查找价格节点
	for _, selector := range priceSelectors {
		if priceNode := htmlquery.FindOne(node, selector); priceNode != nil {
			if price := e.extractPriceFromNode(priceNode); price > 0 {
				return price
			}
		}
	}

	return 0
}

// extractPriceFromNode 从价格节点中提取价格值 - 简化版本
func (e *DataExtractor) extractPriceFromNode(priceNode *html.Node) float64 {
	// 方法1：从aok-offscreen元素提取（最可靠）
	if offscreenNode := htmlquery.FindOne(priceNode, `.//span[@class='aok-offscreen']`); offscreenNode != nil {
		text := strings.TrimSpace(htmlquery.InnerText(offscreenNode))
		if text != "" {
			return e.parsePrice(text)
		}
	}

	// 方法2：从价格整数部分和小数部分组合
	wholeNode := htmlquery.FindOne(priceNode, `.//span[@class='a-price-whole']`)
	fractionNode := htmlquery.FindOne(priceNode, `.//span[@class='a-price-fraction']`)

	if wholeNode != nil && fractionNode != nil {
		whole := strings.TrimSpace(htmlquery.InnerText(wholeNode))
		fraction := strings.TrimSpace(htmlquery.InnerText(fractionNode))

		if whole != "" && fraction != "" {
			priceStr := whole + "." + fraction
			return e.parsePrice(priceStr)
		}
	}

	return 0
}

// parsePrice 解析价格字符串
func (e *DataExtractor) parsePrice(text string) float64 {
	// 移除货币符号和逗号
	priceText := text
	for _, symbol := range currencySymbols {
		priceText = strings.ReplaceAll(priceText, symbol, "")
	}
	priceText = strings.ReplaceAll(priceText, ",", "")
	priceText = strings.TrimSpace(priceText)

	if price, err := strconv.ParseFloat(priceText, 64); err == nil {
		return price
	}
	return 0
}

// extractMerchantID 从节点中提取商户ID - 使用回退机制
func (e *DataExtractor) extractMerchantID(node *html.Node) string {
	// 首先尝试在soldBy区域查找
	if soldByDiv := findNodeWithFallback(node, soldBySelectors); soldByDiv != nil {
		if merchantID := e.extractMerchantIDFromNode(soldByDiv); merchantID != "" {
			return merchantID
		}
	}

	// 如果没有找到soldBy div，直接在节点区域查找
	return e.extractMerchantIDFromNode(node)
}

// extractMerchantIDFromNode 从指定节点中提取商家ID的通用方法
func (e *DataExtractor) extractMerchantIDFromNode(node *html.Node) string {
	for _, selector := range merchantLinkSelectors {
		if linkNode := htmlquery.FindOne(node, selector); linkNode != nil {
			href := htmlquery.SelectAttr(linkNode, "href")
			if href != "" && strings.Contains(href, "seller=") {
				// 处理HTML编码的&amp;
				href = strings.ReplaceAll(href, "&amp;", "&")

				// 使用URL解析从href中提取seller参数
				if parsedURL, err := url.Parse(href); err == nil {
					if seller := parsedURL.Query().Get("seller"); seller != "" {
						return seller
					}
				}
			}
		}
	}
	return ""
}

// extractOfferListingID 从节点中提取OfferListingID - 使用回退机制
func (e *DataExtractor) extractOfferListingID(node *html.Node) string {
	value := extractAttributeWithFallback(node, offerListingIDSelectors, "value")
	if value != "" {
		// URL解码offerListingId
		if decoded, err := url.QueryUnescape(value); err == nil {
			return decoded
		}
		return value
	}
	return ""
}

// extractStockStatus 从节点中提取库存状态 - 使用回退机制
func (e *DataExtractor) extractStockStatus(node *html.Node) bool {
	for _, selector := range stockButtonSelectors {
		if htmlquery.FindOne(node, selector) != nil {
			return true
		}
	}
	return false
}

// ========== 数据结构定义 ==========

// HTMLProductData HTML产品数据结构
type HTMLProductData struct {
	ASIN           string
	Title          string
	Price          float64
	OfferListingID string
	InStock        bool
	ImageURL       string
}
