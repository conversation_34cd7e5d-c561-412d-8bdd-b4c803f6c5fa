package wishlist

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/internal/spiders/shared"

	"github.com/antchfx/htmlquery"
	"golang.org/x/net/html"
)

// ========== 工具函数 ==========

// buildWishlistURL 构建心愿单URL
func buildWishlistURL(siteURL, wishlistID string) string {
	return shared.BuildProductURL(siteURL, shared.AmazonWishlistAPIPath, wishlistID)
}

// buildATCLink 构建ATC (Add to Cart) 链接
func buildATCLink(siteURL, offerListingId, asin string) *string {
	if offerListingId == "" || asin == "" {
		return nil
	}

	baseURL := fmt.Sprintf("%s%s?offerListingID=%s&asin=%s&submit.buy-now=1",
		siteURL, shared.AmazonATCBasePath, offerListingId, asin)

	// 验证构建的URL包含当前ASIN
	if !strings.Contains(baseURL, asin) {
		return nil
	}

	link := fmt.Sprintf("[x1](%s&quantity.1=1) | [x2](%s&quantity.1=2)", baseURL, baseURL)
	return &link
}

// createOfferIDPointer 创建OfferID指针
func createOfferIDPointer(offerListingId string) *string {
	if offerListingId != "" {
		return &offerListingId
	}
	return nil
}

// ========== 常量定义 ==========

const (
	// 心愿单特定常量
	WishlistItemSelector       = "//li[contains(@class, 'awl-item-wrapper')]"
	WishlistTitleSelector      = ".//h3[contains(@class, 'awl-item-title')]/text()"
	WishlistImageSelector      = ".//img[@class='wl-img-size-adjust']/@src"
	WishlistASINSelector       = ".//div[@data-csa-c-item-type='asin']/@data-csa-c-item-id"
	WishlistPaginationSelector = "//input[@class='lastEvaluatedKey']/@value"

	// 心愿单默认值
	DefaultItemsPerPage = 25
)

// ========== 类型定义 ==========

// WishlistProductData 心愿单产品数据结构
type WishlistProductData struct {
	ASIN           string
	Title          string
	Price          float64
	OfferListingID string
	InStock        bool
	ImageURL       string
	MerchantID     string
}

// Spider Amazon心愿单监控爬虫
type Spider struct {
	*spiders.BaseSpider
	defaultHeaders map[string]string
}

// ========== 构造函数 ==========

// NewWishlistSpider 创建Amazon心愿单爬虫实例
func NewWishlistSpider() spiders.Spider {
	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider(shared.PlatformAmazonWishlist, shared.SpiderVersion, "Amazon心愿单监控爬虫"),
		defaultHeaders: map[string]string{
			"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
			"Accept-Language":           "en-US,en;q=0.5",
			"Accept-Encoding":           "gzip, deflate",
			"Cache-Control":             "no-cache",
			"Pragma":                    "no-cache",
			"X-Requested-With":          "XMLHttpRequest",
			"Upgrade-Insecure-Requests": "1",
			"Sec-Fetch-Dest":            "document",
			"Sec-Fetch-Mode":            "navigate",
			"Sec-Fetch-Site":            "none",
			"Sec-Fetch-User":            "?1",
		},
	}
	return spider
}

// ========== 核心方法 ==========

// GetProductIDs 获取心愿单ID集合 - 对应Python版本的get_product_ids
func (w *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	// 使用通用产品ID获取方法作为后备
	return config.GetProductIDsFromConfig(spiderConfig)
}

// PrepareRequest 准备心愿单请求 - 实现Spider接口
func (w *Spider) PrepareRequest(ctx context.Context, wishlistID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	// 构建心愿单URL
	siteURL := buildWishlistURL(spiderConfig.SiteURL, wishlistID)

	// 构建查询参数
	params := map[string]string{
		"type":     shared.AmazonWishlistTypeParam,
		"filter":   shared.AmazonWishlistFilterParam,
		"sort":     shared.AmazonWishlistSortParam,
		"viewType": shared.AmazonWishlistViewTypeParam,
		"language": shared.AmazonWishlistLanguageParam,
	}

	// 添加时间戳防止缓存
	params["_t"] = strconv.FormatInt(time.Now().UnixMilli(), 10)

	// 创建请求构建器
	builder := shared.CreateGenericSpiderRequest(shared.PlatformAmazonWishlist, siteURL, spiderConfig, wishlistID)

	// 添加参数
	for key, value := range params {
		builder.AddParam(key, value)
	}

	// 添加默认请求头
	for key, value := range w.defaultHeaders {
		builder.AddHeader(key, value)
	}

	// 设置心愿单特定的元数据
	builder.SetMetadata("wishlist_id", wishlistID)
	builder.SetMetadata("is_pagination", false)
	builder.SetMetadata("items_rendered", 0)

	httpRequest := builder.Build()

	// 转换为 models.Request
	request := &models.Request{
		SpiderType:  shared.PlatformAmazonWishlist,
		URL:         httpRequest.URL,
		Method:      httpRequest.Method,
		Headers:     httpRequest.Headers,
		Body:        httpRequest.Body,
		Params:      httpRequest.Params,
		Metadata:    httpRequest.Metadata,
		MonitorName: spiderConfig.Name,
		ProductID:   wishlistID,
		RequestTime: time.Now(),
	}

	w.LogDebug("准备心愿单请求成功，心愿单ID：%s，URL：%s", wishlistID, request.URL)
	return request, nil
}

// ParseResponse 解析心愿单响应 - 实现Spider接口
func (w *Spider) ParseResponse(ctx context.Context, response *models.Response, spiderConfig *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	if response.StatusCode != shared.HTTPStatusOK {
		return nil, fmt.Errorf("HTTP状态码错误: %d", response.StatusCode)
	}

	responseStr := string(response.Body)

	// 获取心愿单ID
	wishlistID := ""
	if response.Request != nil {
		wishlistID = response.Request.GetMetadataString("wishlist_id", "")
		if wishlistID == "" {
			wishlistID = response.Request.ProductID
		}
	}

	products, err := w.parseWishlistHTML(responseStr, spiderConfig, wishlistID)
	if err != nil {
		return nil, fmt.Errorf("解析心愿单HTML响应失败: %w", err)
	}

	w.LogDebug("成功解析Amazon心愿单响应，产品数量：%d，心愿单ID：%s", len(products), wishlistID)
	return products, nil
}

// ========== HTML解析核心方法 ==========

// parseWishlistHTML 解析心愿单HTML响应
func (w *Spider) parseWishlistHTML(htmlStr string, spiderConfig *spiders.SpiderConfig, wishlistID string) ([]*models.ProductItem, error) {
	// 解析HTML文档
	doc, err := htmlquery.Parse(strings.NewReader(htmlStr))
	if err != nil {
		return nil, fmt.Errorf("解析HTML文档失败: %w", err)
	}

	// 提取所有商品元素
	productItems := w.extractAllWishlistProducts(doc, wishlistID)
	if len(productItems) == 0 {
		w.LogWarn("未找到心愿单商品，心愿单ID：%s", wishlistID)
		return []*models.ProductItem{}, nil
	}

	// 处理每个商品 - 遵循Python版本的逻辑
	products := make([]*models.ProductItem, 0, len(productItems))
	for _, item := range productItems {
		// 提取商品数据
		productInfo := w.extractWishlistProductData(item)

		// 提取产品ID
		asin := ""
		if asinValue, ok := productInfo["asin"]; ok {
			if asinStr, ok := asinValue.(string); ok {
				asin = asinStr
			}
		}
		if asin == "" {
			w.LogWarn("跳过无效商品：缺少ASIN")
			continue
		}

		// 检查商家ID - 遵循Python版本的逻辑
		inStock := false
		offerID := ""
		price := "0.00"

		if w.checkMerchantID(productInfo, spiderConfig) {
			// 提取价格
			if priceValue, ok := productInfo["price"]; ok {
				if priceStr, ok := priceValue.(string); ok {
					price = priceStr
				}
			}
			// 提取offer_id
			if offerValue, ok := productInfo["offerID"]; ok {
				if offerStr, ok := offerValue.(string); ok {
					offerID = offerStr
				}
			}
			inStock = true
		}

		// 构建产品项
		product := w.buildWishlistProductFromInfo(productInfo, asin, price, offerID, inStock, spiderConfig, wishlistID)
		products = append(products, product)
	}

	// 处理分页（如果需要）
	// TODO: 实现分页处理逻辑

	return products, nil
}

// ========== 商品信息提取方法 ==========

// extractAllWishlistProducts 提取所有心愿单商品元素 - 遵循Python版本的_extract_all_products
func (w *Spider) extractAllWishlistProducts(doc *html.Node, wishlistID string) []*html.Node {
	// 使用XPath选择器 - 完全按照Python版本
	productItems := htmlquery.Find(doc, "//li[contains(@class, 'awl-item-wrapper')]")
	if len(productItems) > 0 {
		return productItems
	}

	// 备用选择器 - 完全按照Python版本
	productItems = htmlquery.Find(doc, fmt.Sprintf("//li[@data-id='%s']", wishlistID))
	if len(productItems) > 0 {
		return productItems
	}

	return []*html.Node{}
}

// extractWishlistProductData 从心愿单商品元素中提取产品数据 - 遵循Python版本的逻辑
func (w *Spider) extractWishlistProductData(item *html.Node) map[string]interface{} {
	// 提取产品基本信息 - 遵循Python版本的_extract_product_info
	productInfo := w.extractProductInfo(item)

	// 提取产品ID - 遵循Python版本的逻辑
	asin := ""
	if asinValue, ok := productInfo["asin"]; ok {
		if asinStr, ok := asinValue.(string); ok {
			asin = asinStr
		}
	}
	if asin == "" {
		asin = w.extractASIN(item)
		if asin != "" {
			productInfo["asin"] = asin
		}
	}

	// 提取标题和图片URL - 遵循Python版本
	if title := w.extractTitle(item); title != "" {
		productInfo["title"] = title
	} else {
		productInfo["title"] = "未知商品"
	}

	if imageURL := w.extractImageURL(item); imageURL != "" {
		productInfo["image_url"] = imageURL
	} else {
		productInfo["image_url"] = ""
	}

	return productInfo
}

// extractProductInfo 提取商品基本信息 - 遵循Python版本的_extract_product_info
func (w *Spider) extractProductInfo(item *html.Node) map[string]interface{} {
	cartData := make(map[string]interface{})

	// 查找data-action="grid-add-to-cart"的span元素
	addCartSpan := htmlquery.FindOne(item, ".//span[@data-action='grid-add-to-cart']")
	if addCartSpan != nil {
		cartDataStr := htmlquery.SelectAttr(addCartSpan, "data-grid-add-to-cart")
		if cartDataStr == "" {
			cartDataStr = "{}"
		}

		// 尝试解析JSON
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(cartDataStr), &data); err == nil {
			cartData = data
		}
	} else {
		// 如果没有找到，尝试提取ASIN
		if asin := w.extractASIN(item); asin != "" {
			cartData["asin"] = asin
		}
	}

	return cartData
}

// extractASIN 提取商品ASIN - 完全遵循Python版本的_extract_asin
func (w *Spider) extractASIN(item *html.Node) string {
	// 尝试从data-csa-c-item-id获取
	asinNodes := htmlquery.Find(item, ".//div[@data-csa-c-item-type='asin']/@data-csa-c-item-id")
	if len(asinNodes) > 0 {
		if asin := htmlquery.SelectAttr(asinNodes[0], "data-csa-c-item-id"); asin != "" {
			return strings.TrimSpace(asin)
		}
	}

	// 尝试从data-itemid获取
	itemNodes := htmlquery.Find(item, ".//*[@data-reposition-action-params]/@data-reposition-action-params")
	if len(itemNodes) > 0 {
		if paramStr := htmlquery.SelectAttr(itemNodes[0], "data-reposition-action-params"); paramStr != "" {
			return strings.TrimSpace(paramStr)
		}
	}

	// 新增：尝试从 data-reposition-action-params 属性获取
	if paramStr := htmlquery.SelectAttr(item, "data-reposition-action-params"); paramStr != "" {
		var params map[string]interface{}
		if err := json.Unmarshal([]byte(paramStr), &params); err == nil {
			if itemExternalID, ok := params["itemExternalId"].(string); ok {
				// 4. 提取 ASIN
				parts := strings.Split(itemExternalID, ":")
				if len(parts) > 1 {
					asinPart := strings.Split(parts[1], "|")[0]
					if asinPart != "" {
						return strings.TrimSpace(asinPart)
					}
				}
			}
		}
	}

	// 新增：尝试从链接中提取ASIN
	productLinks := htmlquery.Find(item, ".//a[contains(@href, '/dp/')]/@href")
	for _, linkNode := range productLinks {
		if href := htmlquery.SelectAttr(linkNode, "href"); href != "" {
			// 尝试提取/dp/后的ASIN - 使用正则表达式
			if strings.Contains(href, "/dp/") {
				parts := strings.Split(href, "/dp/")
				if len(parts) > 1 {
					asinPart := strings.Split(parts[1], "/")[0]
					asinPart = strings.Split(asinPart, "?")[0]
					// 检查ASIN格式 (通常是10位字母数字)
					if len(asinPart) == 10 {
						return strings.TrimSpace(asinPart)
					}
				}
			}
		}
	}

	return ""
}

// extractTitle 提取商品标题 - 遵循Python版本的_extract_title
func (w *Spider) extractTitle(item *html.Node) string {
	// 尝试不同的选择器
	titleNodes := htmlquery.Find(item, ".//h3[contains(@class, 'awl-item-title')]/text()")
	if len(titleNodes) > 0 {
		if title := htmlquery.InnerText(titleNodes[0]); title != "" {
			return strings.TrimSpace(title)
		}
	}

	titleNodes = htmlquery.Find(item, ".//a[contains(@class, 'a-link-normal')]/@title")
	if len(titleNodes) > 0 {
		if title := htmlquery.SelectAttr(titleNodes[0], "title"); title != "" {
			return strings.TrimSpace(title)
		}
	}

	return ""
}

// extractImageURL 提取商品图片URL - 遵循Python版本的_extract_image_url
func (w *Spider) extractImageURL(item *html.Node) string {
	// 优先尝试匹配商品图片特定类名
	imgNodes := htmlquery.Find(item, ".//img[@class='wl-img-size-adjust']/@src")
	if len(imgNodes) > 0 {
		if src := htmlquery.SelectAttr(imgNodes[0], "src"); src != "" {
			return strings.TrimSpace(src)
		}
	}

	// 如果没找到，尝试通过图片容器定位
	imgNodes = htmlquery.Find(item, ".//div[contains(@class, 'wl-grid-item-middle-section')]//img/@src")
	if len(imgNodes) > 0 {
		if src := htmlquery.SelectAttr(imgNodes[0], "src"); src != "" {
			return strings.TrimSpace(src)
		}
	}

	// 如果以上都失败，再尝试查找所有图片，并过滤掉明显的小图标
	allImgs := htmlquery.Find(item, ".//img/@src")
	var filteredImgs []string
	for _, imgNode := range allImgs {
		if src := htmlquery.SelectAttr(imgNode, "src"); src != "" {
			// 过滤可能的图标链接（通常包含特定关键词）
			srcLower := strings.ToLower(src)
			if !strings.Contains(srcLower, "icon") &&
				!strings.Contains(srcLower, "button") &&
				!strings.Contains(srcLower, "ellipsis") {
				filteredImgs = append(filteredImgs, src)
			}
		}
	}

	if len(filteredImgs) > 0 {
		return strings.TrimSpace(filteredImgs[0])
	}

	return ""
}

// checkMerchantID 检查商家ID是否匹配 - 遵循Python版本的_check_merchant_id
func (w *Spider) checkMerchantID(productInfo map[string]interface{}, spiderConfig *spiders.SpiderConfig) bool {
	// 如果配置中没有指定merchantID，默认为True
	merchantIDConfig := config.GetConfigString(spiderConfig, "merchantID", "")
	if merchantIDConfig == "" {
		return true
	}

	// 如果产品信息中没有merchantID，返回False
	merchantIDValue, exists := productInfo["merchantID"]
	if !exists {
		return false
	}

	merchantID, ok := merchantIDValue.(string)
	if !ok {
		return false
	}

	// 检查merchantID是否匹配
	return merchantID == merchantIDConfig
}

// ========== 产品构建方法 ==========

// buildWishlistProductFromInfo 构建心愿单产品项 - 遵循Python版本的逻辑
func (w *Spider) buildWishlistProductFromInfo(productInfo map[string]interface{}, asin, price, offerID string, inStock bool, spiderConfig *spiders.SpiderConfig, wishlistID string) *models.ProductItem {
	// 构建商品URL - 遵循Python版本
	productURL := fmt.Sprintf("%s/dp/%s", spiderConfig.SiteURL, asin)

	// 获取通知组配置
	notifications := spiderConfig.Notifications

	// 获取国家信息
	country := spiderConfig.Name

	// 提取价格和货币 - 遵循Python版本
	priceStr := strings.ReplaceAll(price, "$", "")
	priceStr = strings.ReplaceAll(priceStr, ",", "")

	var priceValue float64
	var currency string
	if parsedPrice, err := strconv.ParseFloat(priceStr, 64); err == nil {
		priceValue = parsedPrice
		currency = config.GetConfigString(spiderConfig, "currency", "$") // Amazon默认使用USD
	} else {
		priceValue = 0.0
		currency = config.GetConfigString(spiderConfig, "currency", "$")
	}

	// 获取标题和图片URL
	title := "未知商品"
	if titleValue, ok := productInfo["title"]; ok {
		if titleStr, ok := titleValue.(string); ok {
			title = titleStr
		}
	}

	imageURL := ""
	if imageValue, ok := productInfo["image_url"]; ok {
		if imageStr, ok := imageValue.(string); ok {
			imageURL = imageStr
		}
	}

	// 构建 ATC (Add to Cart) 链接
	var atcLink *string
	if offerID != "" && inStock {
		atcLink = buildATCLink(spiderConfig.SiteURL, offerID, asin)
	}

	// 创建ProductItem - 遵循Python版本的逻辑
	product := &models.ProductItem{
		// 基础信息（必需参数）
		Name:      spiderConfig.Name,
		ProductID: asin,
		Title:     title,
		URL:       productURL,
		Platform:  shared.PlatformAmazonWishlist,

		// 价格信息
		Price:    priceValue,
		Currency: currency,

		// 库存信息
		Stock:        shared.GetStockFromBool(inStock), // Amazon不显示具体库存数量
		InStock:      inStock,
		Availability: shared.GetAvailabilityText(inStock),
		Country:      country,
		SiteURL:      spiderConfig.SiteURL,

		// 可选参数
		SkuID:       nil,
		OfferID:     createOfferIDPointer(offerID),
		AtcLink:     atcLink,
		ImageURL:    shared.CreateStringPointer(imageURL),
		Addition:    nil, // Amazon不提供额外的商品信息
		ReleaseDate: nil, // Amazon不提供发布日期

		// 通知配置
		Notifications: notifications,

		// 系统元数据
		CrawledAt: time.Now(),
		Metadata: map[string]interface{}{
			"spider_type":       shared.PlatformAmazonWishlist,
			"wishlist_id":       wishlistID,
			"merchant_id":       productInfo["merchantID"],
			"product_info":      productInfo,
			"extraction_method": "html_parsing",
		},
	}

	return product
}

// ========== 批量处理支持方法 ==========

// SupportsBatchProcessing Amazon心愿单爬虫不支持批量处理（每个心愿单需要单独请求）
func (w *Spider) SupportsBatchProcessing() bool {
	return false
}

// GetMaxBatchSize Amazon心愿单爬虫的批量大小为1
func (w *Spider) GetMaxBatchSize() int {
	return 1
}
