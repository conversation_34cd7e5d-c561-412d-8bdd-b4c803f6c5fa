package aliexpress

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/spiders"
	"go-monitor/internal/spiders/shared"

	"github.com/tidwall/gjson"
)

// ========== 常量定义 ==========
// 使用shared包中的统一常量，移除重复定义

// ========== 类型定义 ==========

// ResponseType 响应类型枚举
type ResponseType int

const (
	ResponseTypeAPI  ResponseType = iota // API响应格式
	ResponseTypePage                     // 页面JSON格式
)

// Spider AliExpress爬虫实现
type Spider struct {
	*spiders.BaseSpider
	// 默认请求头 - 对应Python版本的default_headers
	defaultHeaders map[string]string
}

// ========== 构造函数 ==========

// NewSpider 创建新的AliExpress爬虫实例
func NewSpider() spiders.Spider {
	spider := &Spider{
		BaseSpider: spiders.NewBaseSpider(shared.PlatformAliExpress, shared.SpiderVersion, "AliExpress商品监控爬虫"),
		defaultHeaders: map[string]string{
			"pragma":          "no-cache",
			"cache-control":   "no-cache",
			"accept":          "*/*",
			"sec-fetch-site":  "same-origin",
			"sec-fetch-mode":  "cors",
			"sec-fetch-dest":  "empty",
			"accept-encoding": "gzip, deflate, br, zstd",
			"accept-language": "en-US,en;q=0.9",
			"priority":        "u=1, i",
		},
	}
	return spider
}

// GetProductIDs 获取产品ID集合 - 对应Python版本的get_product_ids
func (s *Spider) GetProductIDs(spiderConfig *spiders.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// PrepareRequest 准备请求 - 根据tests/resp.json更新为新的API端点
func (s *Spider) PrepareRequest(ctx context.Context, productID string, spiderConfig *spiders.SpiderConfig) (*models.Request, error) {
	s.LogDebug("开始准备AliExpress请求 产品ID：%s，监控：%s", productID, spiderConfig.Name)

	// 验证产品ID
	if productID == "" {
		return nil, s.HandleError("产品ID验证", fmt.Errorf("监控 %s 缺少产品ID", spiderConfig.Name))
	}

	// 新的API端点URL - 基于tests/resp.json中的实际端点
	baseURL := "https://www.aliexpress.com/aeglodetailweb/api/msite/item"
	s.LogDebug("使用新API端点 URL：%s", baseURL)

	// 构建查询参数 - 基于tests/resp.json中的实际参数
	params := map[string]string{
		"productId":  productID,
		"sourceType": "1",
	}

	// 获取配置参数
	country := spiderConfig.Country
	currency := config.GetConfigString(spiderConfig, "currency", "USD")
	s.LogDebug("配置参数 国家：%s，货币：%s", country, currency)

	// 可选：添加SKU ID参数（如果配置中有指定）
	if skuID := config.GetConfigString(spiderConfig, "sku_id", ""); skuID != "" {
		// 构建pdp_ext_f参数，格式：{"sku_id":"12000047925960786"}
		pdpExtF := fmt.Sprintf(`{"sku_id":"%s"}`, skuID)
		params["pdp_ext_f"] = pdpExtF
		s.LogDebug("添加SKU参数 SKU ID：%s", skuID)
	}

	// 使用HTTP客户端构建器创建请求 - 注意：这里会自动设置为GET请求
	builder := shared.CreateAliExpressRequest(baseURL, spiderConfig, productID)

	// 添加查询参数
	for key, value := range params {
		builder.AddParam(key, value)
	}

	// 更新请求头以匹配新API要求 - 包含关键的Referer头
	updatedHeaders := map[string]string{
		"referer": fmt.Sprintf("https://www.aliexpress.com/item/%s.html", productID),
	}

	// 合并默认请求头和更新的请求头
	for key, value := range s.defaultHeaders {
		if _, exists := updatedHeaders[key]; !exists {
			updatedHeaders[key] = value
		}
	}

	builder.AddHeaders(updatedHeaders)

	// 设置为GET请求（新API使用GET而不是POST）
	builder.SetMethod("GET")

	// 构建请求
	httpRequest := builder.Build()

	// 转换为models.Request
	request := &models.Request{
		SpiderType:   "aliexpress",
		URL:          httpRequest.URL,
		Method:       httpRequest.Method,
		Headers:      httpRequest.Headers,
		Body:         httpRequest.Body,
		Params:       httpRequest.Params,
		ReverseProxy: httpRequest.ReverseProxy,
		Metadata:     httpRequest.Metadata,
	}

	s.LogDebug("成功准备AliExpress请求 监控：%s，产品ID：%s，方法：%s，URL：%s",
		spiderConfig.Name, productID, request.Method, request.URL)

	return request, nil
}

// detectResponseType 检测响应类型 - 优化版本，优先处理页面JSON格式
func (s *Spider) detectResponseType(jsonStr string) ResponseType {
	// 优先检查页面JSON格式（基于tests/resp.json的实际结构）
	// 页面JSON包含多个顶级组件：GLOBAL_DATA、SKU、PRICE、HEADER_IMAGE_PC等
	if gjson.Get(jsonStr, "GLOBAL_DATA").Exists() {
		s.LogDebug("检测到页面JSON格式响应（包含GLOBAL_DATA）")
		return ResponseTypePage
	}

	// 检查其他页面JSON标识字段
	if gjson.Get(jsonStr, "SKU.skuPaths").Exists() ||
		gjson.Get(jsonStr, "PRICE.skuIdStrPriceInfoMap").Exists() {
		s.LogDebug("检测到页面JSON格式响应（包含SKU或PRICE组件）")
		return ResponseTypePage
	}

	// 检查是否为旧版API响应格式（包含data字段）
	if gjson.Get(jsonStr, "data").Exists() {
		s.LogDebug("检测到旧版API响应格式")
		return ResponseTypeAPI
	}

	// 默认按页面JSON格式处理（因为新API主要返回页面JSON）
	s.LogWarn("无法确定响应类型，默认使用页面JSON格式解析")
	return ResponseTypePage
}

// ParseResponse 解析响应 - 优化版本，支持双模式解析
func (s *Spider) ParseResponse(ctx context.Context, resp *models.Response, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	// 获取产品ID
	productID := ""
	if resp.Request != nil {
		productID = resp.Request.GetMetadataString("product_id", "")
	}

	if productID == "" {
		return nil, fmt.Errorf("响应中缺少产品ID")
	}

	// 验证JSON格式
	jsonStr := string(resp.Body)
	if !gjson.Valid(jsonStr) {
		return nil, s.HandleError("验证JSON响应", fmt.Errorf("无效的JSON响应"))
	}

	// 检测响应类型并选择对应的解析策略
	responseType := s.detectResponseType(jsonStr)

	switch responseType {
	case ResponseTypePage:
		// 页面JSON格式解析
		s.LogDebug("使用页面JSON解析器处理产品ID：%s", productID)
		return s.parsePageResponse(productID, jsonStr, config)

	case ResponseTypeAPI:
		// API响应格式解析
		s.LogDebug("使用API响应解析器处理产品ID：%s", productID)
		return s.parseAPIResponse(productID, jsonStr, config)

	default:
		return nil, fmt.Errorf("不支持的响应类型")
	}
}

// parseAPIResponse 解析API响应格式 - 保持原有逻辑，增强兼容性检查
func (s *Spider) parseAPIResponse(productID, jsonStr string, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	s.LogDebug("解析API响应格式 产品ID：%s", productID)

	// 检查是否为JSONP格式响应
	if strings.Contains(jsonStr, "mtopjsonp") {
		s.LogDebug("检测到JSONP格式响应，进行预处理")
		jsonStr = s.cleanJSONPResponse(jsonStr)
	}

	// 使用GJSON直接检查API返回状态
	if !gjson.Get(jsonStr, "data").Exists() {
		s.LogDebug("响应中没有data字段，直接解析为产品数据")
		// 如果没有data字段，直接使用整个响应作为产品数据
		return s.extractProductInfo(productID, jsonStr, config)
	}

	// 检查success状态
	if success := gjson.Get(jsonStr, "data.success"); success.Exists() && !success.Bool() {
		errorMsg := gjson.Get(jsonStr, "data.errorMsg").String()
		if errorMsg == "" {
			errorMsg = "未知错误"
		}
		s.LogError("API返回错误，产品ID：%s，错误：%s", productID, errorMsg)
		return nil, fmt.Errorf("API返回错误: %s", errorMsg)
	}

	// 获取真正的产品数据
	if !gjson.Get(jsonStr, "data.data").Exists() {
		s.LogError("响应中缺少产品数据，产品ID：%s", productID)
		return nil, fmt.Errorf("响应中缺少产品数据")
	}

	// 提取商品信息
	s.LogDebug("从API响应提取产品数据 产品ID：%s", productID)
	return s.extractProductInfo(productID, gjson.Get(jsonStr, "data.data").Raw, config)
}

// cleanJSONPResponse 清理JSONP响应格式 - 新增辅助函数
func (s *Spider) cleanJSONPResponse(jsonStr string) string {
	// 移除JSONP包装，如 "mtopjsonp1({...})"
	start := strings.Index(jsonStr, "(")
	end := strings.LastIndex(jsonStr, ")")

	if start != -1 && end != -1 && start < end {
		return jsonStr[start+1 : end]
	}

	return jsonStr
}

// parsePageResponse 解析页面JSON格式 - 增强版本，改进错误处理
func (s *Spider) parsePageResponse(productID, jsonStr string, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	s.LogDebug("开始解析页面JSON响应 产品ID：%s", productID)

	// 验证关键页面组件的存在性
	hasGlobalData := gjson.Get(jsonStr, "GLOBAL_DATA").Exists()
	hasSKU := gjson.Get(jsonStr, "SKU").Exists()
	hasPrice := gjson.Get(jsonStr, "PRICE").Exists()

	s.LogDebug("页面组件检查 产品ID：%s，GLOBAL_DATA：%t，SKU：%t，PRICE：%t",
		productID, hasGlobalData, hasSKU, hasPrice)

	// 至少需要SKU或PRICE组件之一
	if !hasSKU && !hasPrice {
		return nil, fmt.Errorf("页面响应中缺少必要的SKU或PRICE数据，产品ID：%s", productID)
	}

	// 提取商品信息
	products, err := s.extractPageProductInfo(productID, jsonStr, config)
	if err != nil {
		return nil, fmt.Errorf("提取页面产品信息失败，产品ID：%s，错误：%w", productID, err)
	}

	s.LogDebug("页面JSON解析完成 产品ID：%s，提取产品数量：%d", productID, len(products))
	return products, nil
}

// extractPageProductInfo 提取页面JSON格式的商品信息 - 增强版本，改进数据提取逻辑
func (s *Spider) extractPageProductInfo(productID string, jsonStr string, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var result []*models.ProductItem

	// 从GLOBAL_DATA提取产品标题
	title := s.extractPageProductTitle(jsonStr)
	s.LogDebug("提取页面产品标题：%s", title)

	// 从SKU组件提取SKU路径数据
	skuPaths := gjson.Get(jsonStr, "SKU.skuPaths").Array()
	if len(skuPaths) == 0 {
		s.LogWarn("页面响应中没有SKU路径数据，尝试从PRICE组件提取，产品ID：%s", productID)

		// 如果没有SKU路径，尝试从PRICE组件直接构建产品项
		return s.extractProductFromPriceComponent(productID, title, jsonStr, config)
	}

	// 从PRICE组件提取价格数据
	priceData := s.getPagePriceData(jsonStr)
	s.LogDebug("提取到价格数据，SKU数量：%d", len(priceData))

	// 从SKU组件提取图片数据
	skuImages := s.getPageSKUImages(jsonStr)
	s.LogDebug("提取到SKU图片，数量：%d", len(skuImages))

	// 处理每个SKU路径
	var processErrors []string
	for i, skuPath := range skuPaths {
		productItem := s.buildPageSKUItem(productID, title, skuPath.Raw, skuImages, priceData, config)
		if productItem != nil {
			result = append(result, productItem)
		} else {
			processErrors = append(processErrors, fmt.Sprintf("SKU%d", i+1))
		}
	}

	// 记录处理统计信息
	s.LogDebug("页面产品信息提取完成 产品ID：%s，标题：%s，原始SKU：%d，有效SKU：%d，处理错误：%d",
		productID, title, len(skuPaths), len(result), len(processErrors))

	if len(processErrors) > 0 {
		s.LogWarn("部分SKU处理失败 产品ID：%s，失败项：%v", productID, processErrors)
	}

	if len(result) == 0 {
		return result, fmt.Errorf("产品 %s 没有有效的SKU数据", productID)
	}

	return result, nil
}

// extractProductFromPriceComponent 从PRICE组件直接提取产品信息 - 新增辅助函数
func (s *Spider) extractProductFromPriceComponent(productID, title, jsonStr string, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var result []*models.ProductItem

	// 从PRICE组件提取价格数据
	priceData := s.getPagePriceData(jsonStr)
	if len(priceData) == 0 {
		return result, fmt.Errorf("PRICE组件中没有价格数据，产品ID：%s", productID)
	}

	// 为每个价格项创建产品
	for skuID := range priceData {
		// 构建基本的SKU路径数据
		skuPathData := map[string]any{
			"skuIdStr": skuID,
			"skuAttr":  "",
			"skuVal":   "",
		}

		skuPathJSON, _ := json.Marshal(skuPathData)
		productItem := s.buildPageSKUItem(productID, title, string(skuPathJSON), make(map[string]string), priceData, config)
		if productItem != nil {
			result = append(result, productItem)
		}
	}

	s.LogDebug("从PRICE组件提取产品 产品ID：%s，SKU数量：%d", productID, len(result))
	return result, nil
}

// extractPageProductTitle 从页面JSON提取产品标题 - 新增函数
func (s *Spider) extractPageProductTitle(jsonStr string) string {
	// 优先从GLOBAL_DATA.globalData.subject获取
	if subject := gjson.Get(jsonStr, "GLOBAL_DATA.globalData.subject").String(); subject != "" {
		return shared.CleanTitle(subject)
	}

	// 备选从PRODUCT_TITLE获取
	if title := gjson.Get(jsonStr, "PRODUCT_TITLE.text").String(); title != "" {
		return shared.CleanTitle(title)
	}

	return "Unknown product"
}

// extractProductInfo 提取商品信息 - 优化版本，增强错误处理和日志记录
func (s *Spider) extractProductInfo(productID string, jsonStr string, config *spiders.SpiderConfig) ([]*models.ProductItem, error) {
	var result []*models.ProductItem

	s.LogDebug("开始提取产品信息 产品ID：%s，监控：%s", productID, config.Name)

	// 提取标题
	title := s.extractProductTitle(jsonStr)
	if title == "Unknown product" {
		s.LogWarn("无法提取产品标题 产品ID：%s", productID)
	}

	// 使用纯GJSON处理SKU数据
	var skuItems []gjson.Result
	gjson.Get(jsonStr, "priceComponent.skuPriceList").ForEach(func(_, item gjson.Result) bool {
		skuItems = append(skuItems, item)
		return true
	})

	// 检查是否有SKU数据
	if len(skuItems) == 0 {
		s.LogWarn("产品没有SKU数据 产品ID：%s，监控：%s", productID, config.Name)
		return result, nil
	}

	// 提取价格数据
	priceData := s.getPriceData(jsonStr)
	s.LogDebug("提取价格数据 产品ID：%s，价格项数量：%d", productID, len(priceData))

	// 提取SKU图片
	skuImages := s.getSKUImages(jsonStr)
	s.LogDebug("提取SKU图片 产品ID：%s，图片数量：%d", productID, len(skuImages))

	// 构建SKU项
	var processErrors []string
	for i, skuItem := range skuItems {
		productItem := s.buildSKUItem(productID, title, skuItem.Raw, skuImages, priceData, config)
		if productItem != nil {
			result = append(result, productItem)
		} else {
			processErrors = append(processErrors, fmt.Sprintf("SKU%d", i+1))
		}
	}

	// 记录处理统计信息
	s.LogInfo("产品信息提取完成 产品ID：%s，标题：%s，原始SKU：%d，有效SKU：%d，处理错误：%d",
		productID, title, len(skuItems), len(result), len(processErrors))

	if len(processErrors) > 0 {
		s.LogWarn("部分SKU处理失败 产品ID：%s，失败项：%v", productID, processErrors)
	}

	if len(result) == 0 {
		return result, fmt.Errorf("产品 %s 没有有效的SKU数据", productID)
	}

	return result, nil
}

// extractProductTitle 提取产品标题 - 优化版本，支持API和页面JSON两种格式
func (s *Spider) extractProductTitle(jsonStr string) string {
	// API响应格式路径（原有逻辑）
	if subject := gjson.Get(jsonStr, "productInfoComponent.subject").String(); subject != "" {
		return shared.CleanTitle(subject)
	}

	if seoTitle := gjson.Get(jsonStr, "extraComponent.seoTitle").String(); seoTitle != "" {
		return shared.CleanTitle(seoTitle)
	}

	// 备选API路径
	if title := gjson.Get(jsonStr, "priceComponent.subject").String(); title != "" {
		return shared.CleanTitle(title)
	}

	if title := gjson.Get(jsonStr, "skuComponent.subject").String(); title != "" {
		return shared.CleanTitle(title)
	}

	// 页面JSON格式路径（新增支持）
	if title := gjson.Get(jsonStr, "GLOBAL_DATA.globalData.subject").String(); title != "" {
		return shared.CleanTitle(title)
	}

	// 页面JSON备选路径
	if title := gjson.Get(jsonStr, "PRODUCT_TITLE.text").String(); title != "" {
		return shared.CleanTitle(title)
	}

	// 通用路径
	if title := gjson.Get(jsonStr, "subject").String(); title != "" {
		return shared.CleanTitle(title)
	}

	s.LogWarn("无法提取产品标题，使用默认值")
	return "Unknown product"
}

// getPriceData 提取SKU价格信息 - 优化版本，支持API和页面JSON两种格式
func (s *Spider) getPriceData(jsonStr string) map[string]map[string]any {
	priceData := make(map[string]map[string]any)

	// API响应格式：priceComponent.skuPriceList（原有逻辑）
	if gjson.Get(jsonStr, "priceComponent.skuPriceList").Exists() {
		gjson.Get(jsonStr, "priceComponent.skuPriceList").ForEach(func(_, priceItem gjson.Result) bool {
			skuIdStr := gjson.Get(priceItem.Raw, "skuIdStr").String()
			if skuIdStr != "" {
				priceData[skuIdStr] = map[string]any{
					"price":       gjson.Get(priceItem.Raw, "skuVal.skuAmount.value").Float(),
					"price_value": gjson.Get(priceItem.Raw, "skuVal.skuCalPrice").String(),
				}
			}
			return true
		})
		return priceData
	}

	// 页面JSON格式：PRICE.skuIdStrPriceInfoMap（新增支持）
	if gjson.Get(jsonStr, "PRICE.skuIdStrPriceInfoMap").Exists() {
		gjson.Get(jsonStr, "PRICE.skuIdStrPriceInfoMap").ForEach(func(skuId, priceInfo gjson.Result) bool {
			// 提取价格字符串，如"€21.71"
			salePriceString := gjson.Get(priceInfo.Raw, "salePriceString").String()

			// 解析价格值
			var priceValue float64
			if salePriceLocal := gjson.Get(priceInfo.Raw, "salePriceLocal").String(); salePriceLocal != "" {
				// 格式如"€21.71|21|71"，提取中间的数值部分
				parts := strings.Split(salePriceLocal, "|")
				if len(parts) >= 3 {
					if price, err := shared.ParsePrice(parts[1] + "." + parts[2]); err == nil {
						priceValue = price
					}
				}
			}

			priceData[skuId.String()] = map[string]any{
				"price":       priceValue,
				"price_value": salePriceString,
			}
			return true
		})
		return priceData
	}

	s.LogDebug("未找到价格数据，返回空映射")
	return priceData
}

// getSKUImages 获取SKU图片 - 优化版本，支持API和页面JSON两种格式
func (s *Spider) getSKUImages(jsonStr string) map[string]string {
	skuImages := make(map[string]string)

	// 页面JSON格式：直接从SKU.skuImagesMap获取（新增支持）
	if gjson.Get(jsonStr, "SKU.skuImagesMap").Exists() {
		gjson.Get(jsonStr, "SKU.skuImagesMap").ForEach(func(skuId, images gjson.Result) bool {
			// 获取第一张图片
			if images.IsArray() && len(images.Array()) > 0 {
				skuImages[skuId.String()] = images.Array()[0].String()
			}
			return true
		})

		// 如果直接映射为空，尝试从SKU属性中提取
		if len(skuImages) == 0 {
			s.extractPageSKUImages(jsonStr, skuImages)
		}
		return skuImages
	}

	// API响应格式：从SKU属性中提取（原有逻辑）
	hasSkuProperties := gjson.Get(jsonStr, "skuComponent.productSKUPropertyList").Exists() ||
		gjson.Get(jsonStr, "SKU.skuProperties").Exists()

	if !hasSkuProperties {
		// 如果没有SKU属性，尝试从价格组件中获取第一个SKU作为默认
		firstSKU := gjson.Get(jsonStr, "priceComponent.skuPriceList.0.skuIdStr").String()
		if firstSKU != "" {
			skuImages[firstSKU] = "default_image_placeholder"
		}
		return skuImages
	}

	// 使用GJSON处理SKU属性
	skuPropertiesPath := "skuComponent.productSKUPropertyList"
	if !gjson.Get(jsonStr, skuPropertiesPath).Exists() {
		skuPropertiesPath = "SKU.skuProperties"
	}

	gjson.Get(jsonStr, skuPropertiesPath).ForEach(func(_, prop gjson.Result) bool {
		gjson.Get(prop.Raw, "skuPropertyValues").ForEach(func(_, value gjson.Result) bool {
			// 提取属性ID
			propertyID := gjson.Get(value.Raw, "propertyValueIdLong").String()
			if propertyID == "" {
				if id := gjson.Get(value.Raw, "propertyValueIdLong").Float(); id > 0 {
					propertyID = fmt.Sprintf("%.0f", id)
				}
			}

			imageURL := gjson.Get(value.Raw, "skuPropertyImagePath").String()
			if propertyID == "" || imageURL == "" {
				return true
			}

			// 为包含此属性的SKU设置图片
			s.assignImageToMatchingSKUs(jsonStr, propertyID, imageURL, skuImages)
			return true
		})
		return true
	})

	return skuImages
}

// extractPageSKUImages 从页面JSON的SKU属性中提取图片 - 新增辅助函数
func (s *Spider) extractPageSKUImages(jsonStr string, skuImages map[string]string) {
	gjson.Get(jsonStr, "SKU.skuProperties").ForEach(func(_, prop gjson.Result) bool {
		gjson.Get(prop.Raw, "skuPropertyValues").ForEach(func(_, value gjson.Result) bool {
			propertyID := gjson.Get(value.Raw, "propertyValueIdLong").String()
			imageURL := gjson.Get(value.Raw, "skuPropertyImagePath").String()

			if propertyID != "" && imageURL != "" {
				// 为包含此属性的SKU设置图片
				gjson.Get(jsonStr, "SKU.skuPaths").ForEach(func(_, skuPath gjson.Result) bool {
					skuAttr := gjson.Get(skuPath.Raw, "skuAttr").String()
					if strings.Contains(skuAttr, propertyID) {
						skuIdStr := gjson.Get(skuPath.Raw, "skuIdStr").String()
						if skuIdStr != "" {
							skuImages[skuIdStr] = imageURL
						}
					}
					return true
				})
			}
			return true
		})
		return true
	})
}

// assignImageToMatchingSKUs 为匹配的SKU分配图片 - 新增辅助函数
func (s *Spider) assignImageToMatchingSKUs(jsonStr, propertyID, imageURL string, skuImages map[string]string) {
	gjson.Get(jsonStr, "priceComponent.skuPriceList").ForEach(func(_, skuItem gjson.Result) bool {
		skuAttr := gjson.Get(skuItem.Raw, "skuAttr").String()
		skuPropIds := gjson.Get(skuItem.Raw, "skuPropIds").String()

		if strings.Contains(skuAttr, propertyID) || strings.Contains(skuPropIds, propertyID) {
			skuID := gjson.Get(skuItem.Raw, "skuIdStr").String()
			if skuID == "" {
				skuID = gjson.Get(skuItem.Raw, "skuId").String()
			}
			if skuID != "" {
				skuImages[skuID] = imageURL
			}
		}
		return true
	})
}

// buildSKUItem 构建SKU项 - 优化版本，统一SKU数据处理逻辑
func (s *Spider) buildSKUItem(productID, title string, pathJson string,
	skuImages map[string]string, priceData map[string]map[string]any, spiderConfig *spiders.SpiderConfig) *models.ProductItem {

	// 提取SKU ID
	skuIDStr := s.extractSKUID(pathJson)
	if skuIDStr == "" {
		s.LogWarn("SKU数据缺少有效ID，产品ID：%s", productID)
		return nil
	}

	// 提取价格信息
	priceValue, currency := s.extractPriceInfo(skuIDStr, priceData, spiderConfig)

	// 提取库存信息
	stock, inStock := s.extractStockInfo(pathJson)

	// 构建ATC链接
	atcLink := s.buildATCLink(inStock, spiderConfig.SiteURL, productID, skuIDStr)

	// 提取图片URL
	imageURL := skuImages[skuIDStr]

	// 提取商品详情
	addition := s.extractSKUAddition(pathJson)

	// 构建产品URL
	productURL := fmt.Sprintf("%s/item/%s.html?pdp_ext_f={\"sku_id\":\"%s\"}",
		spiderConfig.SiteURL, productID, skuIDStr)

	// 构建完整标题
	fullTitle := s.buildSKUTitle(title, addition)

	// 创建基础产品
	product := s.CreateBaseProduct(spiderConfig.Name, productID, fullTitle, productURL, spiderConfig.SiteURL, spiderConfig.Notifications)

	// 设置价格和库存信息
	shared.SetProductPriceAndStock(product, priceValue, currency, inStock, shared.GetAvailabilityText(inStock), nil)
	product.Stock = stock

	// 设置AliExpress特定信息
	product.SkuID = shared.CreateStringPointer(skuIDStr)
	product.ImageURL = shared.CreateStringPointer(imageURL)
	product.Addition = shared.CreateStringPointer(addition)
	product.AtcLink = atcLink

	// 设置元数据
	shared.SetProductMetadata(product, shared.PlatformAliExpress, "api", map[string]any{
		"sku_path": pathJson,
	})

	s.LogDebug("构建SKU项 产品ID：%s，SKU：%s，价格：%.2f %s，库存：%t",
		productID, skuIDStr, priceValue, currency, inStock)

	return product
}

// extractSKUID 提取SKU ID - 新增辅助函数
func (s *Spider) extractSKUID(pathJson string) string {
	// 优先使用字符串格式的SKU ID
	if skuIDStr := gjson.Get(pathJson, "skuIdStr").String(); skuIDStr != "" {
		return skuIDStr
	}

	// 备选：处理数字类型的skuId
	if skuIDFloat := gjson.Get(pathJson, "skuId").Float(); skuIDFloat > 0 {
		return fmt.Sprintf("%.0f", skuIDFloat)
	}

	return ""
}

// extractPriceInfo 提取价格信息 - 新增辅助函数
func (s *Spider) extractPriceInfo(skuIDStr string, priceData map[string]map[string]any, spiderConfig *spiders.SpiderConfig) (float64, string) {
	// 获取价格数据
	priceInfo, exists := priceData[skuIDStr]
	if !exists {
		priceInfo = map[string]any{"price": 0.0, "price_value": "0"}
	}

	// 解析价格值
	priceValue := 0.0
	if price, ok := priceInfo["price"].(float64); ok {
		priceValue = price
	} else if priceVal, ok := priceInfo["price_value"].(string); ok {
		if parsedPrice, err := shared.ParsePrice(priceVal); err == nil {
			priceValue = parsedPrice
		}
	}

	// 获取货币配置
	currency := config.GetConfigString(spiderConfig, "currency", "€")

	return priceValue, currency
}

// extractStockInfo 提取库存信息 - 新增辅助函数
func (s *Spider) extractStockInfo(pathJson string) (int, bool) {
	stock := int(gjson.Get(pathJson, "skuVal.availQuantity").Int())
	inStock := stock > 0
	return stock, inStock
}

// buildATCLink 构建ATC链接 - 新增辅助函数
func (s *Spider) buildATCLink(inStock bool, siteURL, productID, skuIDStr string) *string {
	if !inStock {
		return nil
	}

	baseURL := fmt.Sprintf("%s/p/trade/confirm.html?productId=%s&skuId=%s", siteURL, productID, skuIDStr)
	atcLinkStr := fmt.Sprintf("[x1](%s&quantity=1) | [x2](%s&quantity=2)", baseURL, baseURL)
	return &atcLinkStr
}

// extractSKUAddition 提取SKU附加信息 - 新增辅助函数
func (s *Spider) extractSKUAddition(pathJson string) string {
	skuAttr := gjson.Get(pathJson, "skuAttr").String()
	if skuAttr == "" {
		return ""
	}

	// 从skuAttr中提取商品规格信息，格式如："14:691#1PC Single Box;200007763:201336100"
	parts := strings.Split(skuAttr, "#")
	if len(parts) > 1 {
		// 提取中间部分，如"1PC Single Box"
		specParts := strings.Split(parts[1], ";")
		return specParts[0]
	}

	return ""
}

// buildSKUTitle 构建SKU标题 - 新增辅助函数
func (s *Spider) buildSKUTitle(title, addition string) string {
	if addition == "" {
		return title
	}
	return fmt.Sprintf("%s - %s", title, addition)
}

// ========== 批量处理能力接口实现 ==========

// SupportsBatchProcessing AliExpress爬虫不支持批量处理
func (s *Spider) SupportsBatchProcessing() bool {
	return false
}

// GetMaxBatchSize AliExpress爬虫只支持单个处理
func (s *Spider) GetMaxBatchSize() int {
	return 1
}

// PrepareBatchRequest AliExpress爬虫不支持批量处理，使用默认实现
// 此方法继承自BaseSpider，会返回错误

// ========== 兼容性验证函数 ==========

// ValidateCompatibility 验证向后兼容性 - 新增函数
func (s *Spider) ValidateCompatibility() error {
	s.LogInfo("开始验证AliExpress spider向后兼容性")

	// 验证响应类型检测
	testAPIResponse := `{"data":{"success":true,"data":{"priceComponent":{}}}}`
	testPageResponse := `{"GLOBAL_DATA":{},"SKU":{},"PRICE":{}}`

	if s.detectResponseType(testAPIResponse) != ResponseTypeAPI {
		return fmt.Errorf("API响应类型检测失败")
	}

	if s.detectResponseType(testPageResponse) != ResponseTypePage {
		return fmt.Errorf("页面JSON响应类型检测失败")
	}

	// 验证标题提取功能
	testTitle := s.extractProductTitle(`{"productInfoComponent":{"subject":"Test Product"}}`)
	if testTitle != "Test Product" {
		return fmt.Errorf("标题提取功能验证失败")
	}

	// 验证JSONP清理功能
	testJSONP := `mtopjsonp1({"test":"value"})`
	cleaned := s.cleanJSONPResponse(testJSONP)
	if cleaned != `{"test":"value"}` {
		return fmt.Errorf("JSONP清理功能验证失败")
	}

	s.LogInfo("AliExpress spider向后兼容性验证通过")
	return nil
}

// ========== 页面JSON解析辅助函数 ==========

// getPagePriceData 从页面JSON提取价格数据 - 优化版本，改进价格解析逻辑
func (s *Spider) getPagePriceData(jsonStr string) map[string]map[string]any {
	priceData := make(map[string]map[string]any)

	// 从PRICE.skuIdStrPriceInfoMap提取价格信息
	priceInfoMap := gjson.Get(jsonStr, "PRICE.skuIdStrPriceInfoMap")
	if priceInfoMap.Exists() {
		priceInfoMap.ForEach(func(skuId, priceInfo gjson.Result) bool {
			// 提取价格字符串，如"€21.71"
			salePriceString := gjson.Get(priceInfo.Raw, "salePriceString").String()

			// 解析价格值 - 改进解析逻辑
			var priceValue float64
			if salePriceLocal := gjson.Get(priceInfo.Raw, "salePriceLocal").String(); salePriceLocal != "" {
				// 格式如"€21.71|21|71"，提取中间的数值部分
				parts := strings.Split(salePriceLocal, "|")
				if len(parts) >= 3 {
					// 组合整数和小数部分
					priceStr := parts[1] + "." + parts[2]
					if price, err := shared.ParsePrice(priceStr); err == nil {
						priceValue = price
					} else {
						s.LogWarn("价格解析失败，SKU：%s，价格字符串：%s，错误：%v", skuId.String(), priceStr, err)
					}
				} else if len(parts) >= 2 {
					// 只有整数部分
					if price, err := shared.ParsePrice(parts[1]); err == nil {
						priceValue = price
					}
				}
			}

			// 如果salePriceLocal解析失败，尝试直接解析salePriceString
			if priceValue == 0 && salePriceString != "" {
				if price, err := shared.ParsePrice(salePriceString); err == nil {
					priceValue = price
				}
			}

			priceData[skuId.String()] = map[string]any{
				"price":       priceValue,
				"price_value": salePriceString,
			}
			return true
		})
	}

	// 备选：从PRICE.skuPriceInfoMap提取（如果存在）
	if len(priceData) == 0 {
		skuPriceInfoMap := gjson.Get(jsonStr, "PRICE.skuPriceInfoMap")
		if skuPriceInfoMap.Exists() {
			skuPriceInfoMap.ForEach(func(skuId, priceInfo gjson.Result) bool {
				salePriceString := gjson.Get(priceInfo.Raw, "salePriceString").String()
				priceValue, _ := shared.ParsePrice(salePriceString)

				priceData[skuId.String()] = map[string]any{
					"price":       priceValue,
					"price_value": salePriceString,
				}
				return true
			})
		}
	}

	s.LogDebug("提取页面价格数据完成，SKU数量：%d", len(priceData))
	return priceData
}

// getPageSKUImages 从页面JSON提取SKU图片 - 优化版本，增强图片提取逻辑
func (s *Spider) getPageSKUImages(jsonStr string) map[string]string {
	skuImages := make(map[string]string)

	// 方法1：从SKU.skuImagesMap直接获取SKU图片映射
	skuImagesMap := gjson.Get(jsonStr, "SKU.skuImagesMap")
	if skuImagesMap.Exists() {
		skuImagesMap.ForEach(func(skuId, images gjson.Result) bool {
			// 获取第一张图片
			if images.IsArray() && len(images.Array()) > 0 {
				imageURL := images.Array()[0].String()
				if imageURL != "" {
					skuImages[skuId.String()] = imageURL
				}
			}
			return true
		})
	}

	// 方法2：如果没有直接的图片映射，尝试从SKU属性中提取
	if len(skuImages) == 0 {
		s.extractSKUImagesFromProperties(jsonStr, skuImages)
	}

	// 方法3：如果仍然没有图片，尝试从HEADER_IMAGE_PC获取默认图片
	if len(skuImages) == 0 {
		s.extractDefaultImagesFromHeader(jsonStr, skuImages)
	}

	s.LogDebug("提取页面SKU图片完成，图片数量：%d", len(skuImages))
	return skuImages
}

// extractDefaultImagesFromHeader 从HEADER_IMAGE_PC获取默认图片 - 新增辅助函数
func (s *Spider) extractDefaultImagesFromHeader(jsonStr string, skuImages map[string]string) {
	// 从HEADER_IMAGE_PC.imagePathList获取默认图片
	imagePathList := gjson.Get(jsonStr, "HEADER_IMAGE_PC.imagePathList").Array()
	if len(imagePathList) > 0 {
		defaultImage := imagePathList[0].String()
		if defaultImage != "" {
			// 为所有SKU设置默认图片
			gjson.Get(jsonStr, "SKU.skuPaths").ForEach(func(_, skuPath gjson.Result) bool {
				skuIdStr := gjson.Get(skuPath.Raw, "skuIdStr").String()
				if skuIdStr != "" {
					skuImages[skuIdStr] = defaultImage
				}
				return true
			})
		}
	}

	// 备选：从HEADER_IMAGE_PC.mainImages获取
	if len(skuImages) == 0 {
		mainImages := gjson.Get(jsonStr, "HEADER_IMAGE_PC.mainImages").Array()
		if len(mainImages) > 0 {
			defaultImage := gjson.Get(mainImages[0].Raw, "imageUrl").String()
			if defaultImage != "" {
				gjson.Get(jsonStr, "SKU.skuPaths").ForEach(func(_, skuPath gjson.Result) bool {
					skuIdStr := gjson.Get(skuPath.Raw, "skuIdStr").String()
					if skuIdStr != "" {
						skuImages[skuIdStr] = defaultImage
					}
					return true
				})
			}
		}
	}
}

// extractSKUImagesFromProperties 从SKU属性中提取图片 - 新增辅助函数
func (s *Spider) extractSKUImagesFromProperties(jsonStr string, skuImages map[string]string) {
	// 从SKU.skuProperties提取图片信息
	gjson.Get(jsonStr, "SKU.skuProperties").ForEach(func(_, prop gjson.Result) bool {
		gjson.Get(prop.Raw, "skuPropertyValues").ForEach(func(_, value gjson.Result) bool {
			propertyID := gjson.Get(value.Raw, "propertyValueIdLong").String()
			imageURL := gjson.Get(value.Raw, "skuPropertyImagePath").String()

			if propertyID != "" && imageURL != "" {
				// 为包含此属性的SKU设置图片
				gjson.Get(jsonStr, "SKU.skuPaths").ForEach(func(_, skuPath gjson.Result) bool {
					skuAttr := gjson.Get(skuPath.Raw, "skuAttr").String()
					if strings.Contains(skuAttr, propertyID) {
						skuIdStr := gjson.Get(skuPath.Raw, "skuIdStr").String()
						if skuIdStr != "" {
							skuImages[skuIdStr] = imageURL
						}
					}
					return true
				})
			}
			return true
		})
		return true
	})
}

// buildPageSKUItem 构建页面JSON格式的SKU项 - 优化版本，改进库存判断逻辑
func (s *Spider) buildPageSKUItem(productID, title, skuPathJson string, skuImages map[string]string, priceData map[string]map[string]any, spiderConfig *spiders.SpiderConfig) *models.ProductItem {
	// 提取SKU基本信息
	skuIdStr := gjson.Get(skuPathJson, "skuIdStr").String()
	if skuIdStr == "" {
		s.LogWarn("SKU路径缺少skuIdStr，产品ID：%s", productID)
		return nil
	}

	// 提取SKU属性
	skuAttr := gjson.Get(skuPathJson, "skuAttr").String()
	skuVal := gjson.Get(skuPathJson, "skuVal").String()

	// 构建完整标题
	fullTitle := s.buildPageSKUTitle(title, skuVal)

	// 获取价格信息
	var priceValue float64
	if priceInfo, exists := priceData[skuIdStr]; exists {
		priceValue, _ = priceInfo["price"].(float64)
	}

	// 获取图片URL
	imageURL := skuImages[skuIdStr]

	// 改进的库存判断逻辑
	inStock := s.determinePageSKUStock(skuPathJson, priceValue)

	// 提取库存数量（如果有）
	stock := int(gjson.Get(skuPathJson, "skuStock").Int())

	// 构建产品URL - 使用AliExpress标准格式
	siteURL := spiderConfig.SiteURL
	productURL := fmt.Sprintf("%s/item/%s.html?pdp_ext_f={\"sku_id\":\"%s\"}", siteURL, productID, skuIdStr)

	// 创建产品项
	product := s.CreateBaseProduct(spiderConfig.Name, productID, fullTitle, productURL, spiderConfig.SiteURL, spiderConfig.Notifications)

	// 设置价格和库存信息 - 使用配置中的货币
	currency := config.GetConfigString(spiderConfig, "currency", "EUR")
	shared.SetProductPriceAndStock(product, priceValue, currency, inStock, shared.GetAvailabilityText(inStock), nil)
	product.Stock = stock

	// 设置AliExpress特定信息
	product.SkuID = shared.CreateStringPointer(skuIdStr)
	product.ImageURL = shared.CreateStringPointer(imageURL)
	product.Addition = shared.CreateStringPointer(skuVal)

	// 构建ATC链接
	if inStock && priceValue > 0 {
		baseURL := fmt.Sprintf("%s/p/trade/confirm.html?productId=%s&skuId=%s", siteURL, productID, skuIdStr)
		atcLinkStr := fmt.Sprintf("[x1](%s&quantity=1) | [x2](%s&quantity=2)", baseURL, baseURL)
		product.AtcLink = &atcLinkStr
	}

	// 设置元数据
	shared.SetProductMetadata(product, shared.PlatformAliExpress, "page", map[string]any{
		"sku_attr": skuAttr,
		"sku_val":  skuVal,
		"stock":    stock,
	})

	s.LogDebug("构建页面SKU项 产品ID：%s，SKU：%s，价格：%.2f %s，库存：%t", productID, skuIdStr, priceValue, currency, inStock)
	return product
}

// determinePageSKUStock 判断页面SKU库存状态 - 新增辅助函数
func (s *Spider) determinePageSKUStock(skuPathJson string, priceValue float64) bool {
	// 方法1：检查salable字段
	if salable := gjson.Get(skuPathJson, "salable"); salable.Exists() {
		return salable.Bool()
	}

	// 方法2：检查skuStock字段
	if skuStock := gjson.Get(skuPathJson, "skuStock").Int(); skuStock > 0 {
		return true
	}

	// 方法3：基于价格判断（有价格通常表示有库存）
	if priceValue > 0 {
		return true
	}

	// 默认为无库存
	return false
}

// buildPageSKUTitle 构建页面SKU标题 - 新增辅助函数
func (s *Spider) buildPageSKUTitle(baseTitle, skuVal string) string {
	if skuVal == "" {
		return baseTitle
	}
	return fmt.Sprintf("%s - %s", baseTitle, skuVal)
}
