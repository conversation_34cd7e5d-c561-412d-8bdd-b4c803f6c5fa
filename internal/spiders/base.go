package spiders

import (
	"context"
	"fmt"

	"go-monitor/internal/models"
	"go-monitor/internal/spiders/shared"
	"go-monitor/pkg/logging"
)

// BaseSpider 基础爬虫实现 - 提供通用功能
type BaseSpider struct {
	name        string
	version     string
	description string
	logger      logging.Logger
}

// NewBaseSpider 创建基础爬虫实例
func NewBaseSpider(name, version, description string) *BaseSpider {
	return &BaseSpider{
		name:        name,
		version:     version,
		description: description,
		logger:      logging.GetLogger(fmt.Sprintf("spider.%s", name)),
	}
}

// GetProductIDs 获取产品ID集合 - 统一使用SpiderConfig
func (b *BaseSpider) GetProductIDs(config *SpiderConfig) []string {
	// 从SpiderSettings中获取product_ids
	if productIDs, ok := config.SpiderSettings["product_ids"].([]interface{}); ok {
		return convertToStringSlice(productIDs)
	}

	// 尝试从其他可能的字段获取
	if productIDs, ok := config.SpiderSettings["product_id"].(string); ok {
		return []string{productIDs}
	}

	return []string{}
}

// PrepareRequest 准备请求 - 统一使用SpiderConfig，添加productID参数
func (b *BaseSpider) PrepareRequest(ctx context.Context, productID string, config *SpiderConfig) (*models.Request, error) {
	return nil, fmt.Errorf("PrepareRequest not implemented for %s", b.name)
}

// ParseResponse 解析响应 - 统一使用SpiderConfig
func (b *BaseSpider) ParseResponse(ctx context.Context, resp *models.Response, config *SpiderConfig) ([]*models.ProductItem, error) {
	return nil, fmt.Errorf("ParseResponse not implemented for %s", b.name)
}

// GetName 获取爬虫名称
func (b *BaseSpider) GetName() string {
	return b.name
}

// GetVersion 获取爬虫版本
func (b *BaseSpider) GetVersion() string {
	return b.version
}

// GetDescription 获取爬虫描述
func (b *BaseSpider) GetDescription() string {
	return b.description
}

// GetDefaultConfig 获取默认配置 - 默认实现
func (b *BaseSpider) GetDefaultConfig() *SpiderConfig {
	return &SpiderConfig{
		Name:     b.name,
		Platform: b.name, // 使用name作为platform
		Enabled:  true,
		Interval: "30s",
		Country:  shared.DefaultCountry,
	}
}

// ========== 统一的辅助方法 ==========

// LogDebug 统一的调试日志记录
func (b *BaseSpider) LogDebug(message string, args ...interface{}) {
	if b.logger != nil {
		b.logger.Debug(fmt.Sprintf(message, args...), "platform", b.name)
	}
}

// LogInfo 统一的信息日志记录
func (b *BaseSpider) LogInfo(message string, args ...interface{}) {
	if b.logger != nil {
		b.logger.Info(fmt.Sprintf(message, args...), "platform", b.name)
	}
}

// LogWarn 统一的警告日志记录
func (b *BaseSpider) LogWarn(message string, args ...interface{}) {
	if b.logger != nil {
		b.logger.Warn(fmt.Sprintf(message, args...), "platform", b.name)
	}
}

// LogError 统一的错误日志记录
func (b *BaseSpider) LogError(message string, args ...interface{}) {
	if b.logger != nil {
		b.logger.Error(fmt.Sprintf(message, args...), "platform", b.name)
	}
}

// HandleError 统一的错误处理
func (b *BaseSpider) HandleError(operation string, err error) error {
	if err == nil {
		return nil
	}
	return shared.WrapSpiderError(b.name, operation, err)
}

// ValidateConfig 增强的配置验证
func (b *BaseSpider) ValidateConfig(config *SpiderConfig) error {
	if config == nil {
		return shared.NewConfigError("config", "", "配置不能为空")
	}

	// 使用共享验证函数
	return shared.ValidateSpiderConfig(config)
}

// BuildProductURL 统一的产品URL构建
func (b *BaseSpider) BuildProductURL(siteURL, productPath, productID string) string {
	return shared.BuildProductURL(siteURL, productPath, productID)
}

// CreateBaseProduct 统一的基础产品创建
func (b *BaseSpider) CreateBaseProduct(name, productID, title, productURL, siteURL string, notifications []string) *models.ProductItem {
	return shared.CreateBaseProduct(name, productID, title, productURL, b.name, siteURL, notifications)
}

// ParseResponseWithConfig 便捷方法：仅使用config解析响应（向后兼容）
func (b *BaseSpider) ParseResponseWithConfig(ctx context.Context, resp *models.Response, config map[string]interface{}) ([]*models.ProductItem, error) {
	// 转换map配置为SpiderConfig
	spiderConfig := mapToSpiderConfig(config)
	return b.ParseResponse(ctx, resp, spiderConfig)
}

// 辅助函数：转换interface{}切片为字符串切片
func convertToStringSlice(items []interface{}) []string {
	var result []string
	for _, item := range items {
		switch v := item.(type) {
		case string:
			result = append(result, v)
		case float64:
			result = append(result, fmt.Sprintf("%.0f", v))
		case int:
			result = append(result, fmt.Sprintf("%d", v))
		case map[string]interface{}:
			// 如果是对象，尝试提取id字段
			if id, exists := v["id"]; exists {
				if idStr, ok := id.(string); ok {
					result = append(result, idStr)
				} else if idNum, ok := id.(float64); ok {
					result = append(result, fmt.Sprintf("%.0f", idNum))
				} else if idInt, ok := id.(int); ok {
					result = append(result, fmt.Sprintf("%d", idInt))
				}
			} else {
				// 如果没有id字段，转换整个对象为字符串
				result = append(result, fmt.Sprintf("%v", v))
			}
		default:
			result = append(result, fmt.Sprintf("%v", v))
		}
	}
	return result
}

// 辅助函数：将map配置转换为SpiderConfig
func mapToSpiderConfig(config map[string]interface{}) *SpiderConfig {
	spiderConfig := &SpiderConfig{
		SpiderSettings: make(map[string]interface{}),
	}

	// 复制所有配置到SpiderSettings
	for k, v := range config {
		switch k {
		case "name":
			if name, ok := v.(string); ok {
				spiderConfig.Name = name
			}
		case "platform":
			if platform, ok := v.(string); ok {
				spiderConfig.Platform = platform
			}
		case "enabled":
			if enabled, ok := v.(bool); ok {
				spiderConfig.Enabled = enabled
			}
		case "country":
			if country, ok := v.(string); ok {
				spiderConfig.Country = country
			}
		case "site_url", "url":
			if url, ok := v.(string); ok {
				spiderConfig.SiteURL = url
			}
		case "notifications":
			if notifications, ok := v.([]string); ok {
				spiderConfig.Notifications = notifications
			}
		default:
			spiderConfig.SpiderSettings[k] = v
		}
	}

	return spiderConfig
}

// ========== 批量处理能力接口默认实现 ==========

// SupportsBatchProcessing 默认不支持批量处理
func (b *BaseSpider) SupportsBatchProcessing() bool {
	return false
}

// GetMaxBatchSize 默认批量大小为1（单个处理）
func (b *BaseSpider) GetMaxBatchSize() int {
	return 1
}

// PrepareBatchRequest 默认批量请求实现 - 不支持批量处理的爬虫会返回错误
func (b *BaseSpider) PrepareBatchRequest(ctx context.Context, productIDs []string, config *SpiderConfig) (*models.Request, error) {
	return nil, fmt.Errorf("爬虫 %s 不支持批量处理", b.name)
}

// ========== BaseSpider 请求构建方法 ==========

// NewRequestBuilder 为当前爬虫创建请求构建器
func (b *BaseSpider) NewRequestBuilder(baseURL string) *shared.SpiderRequestBuilder {
	return shared.NewSpiderRequestBuilder(b.name, baseURL)
}

// CreateRequest 创建适合当前爬虫的请求
func (b *BaseSpider) CreateRequest(baseURL string, config *SpiderConfig, productID string) *shared.SpiderRequestBuilder {
	return shared.NewSpiderRequestBuilder(b.name, baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}
