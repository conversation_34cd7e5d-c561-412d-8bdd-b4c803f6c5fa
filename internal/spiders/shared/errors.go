package shared

import (
	"fmt"
	"strings"
)

// ========== 错误类型定义 ==========

// SpiderError 爬虫错误基础类型
type SpiderError struct {
	Platform  string
	Operation string
	Message   string
	Cause     error
}

// Error 实现error接口
func (e *SpiderError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s] %s失败: %s - 原因: %s", e.Platform, e.Operation, e.Message, e.Cause.Error())
	}
	return fmt.Sprintf("[%s] %s失败: %s", e.Platform, e.Operation, e.Message)
}

// Unwrap 支持错误链
func (e *SpiderError) Unwrap() error {
	return e.Cause
}

// ValidationError 数据验证错误
type ValidationError struct {
	Field   string
	Value   string
	Reason  string
	Context string
}

// Error 实现error接口
func (e *ValidationError) Error() string {
	if e.Context != "" {
		return fmt.Sprintf("验证失败 [%s] - 字段: %s, 值: %s, 原因: %s", e.Context, e.Field, e.Value, e.Reason)
	}
	return fmt.Sprintf("验证失败 - 字段: %s, 值: %s, 原因: %s", e.Field, e.Value, e.Reason)
}

// ConfigError 配置错误
type ConfigError struct {
	Key     string
	Value   string
	Reason  string
	Section string
}

// Error 实现error接口
func (e *ConfigError) Error() string {
	if e.Section != "" {
		return fmt.Sprintf("配置错误 [%s] - 键: %s, 值: %s, 原因: %s", e.Section, e.Key, e.Value, e.Reason)
	}
	return fmt.Sprintf("配置错误 - 键: %s, 值: %s, 原因: %s", e.Key, e.Value, e.Reason)
}

// NetworkError 网络错误
type NetworkError struct {
	URL        string
	Method     string
	StatusCode int
	Message    string
	Cause      error
}

// Error 实现error接口
func (e *NetworkError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("网络请求失败 - URL: %s, 方法: %s, 状态码: %d, 消息: %s, 原因: %s", 
			e.URL, e.Method, e.StatusCode, e.Message, e.Cause.Error())
	}
	return fmt.Sprintf("网络请求失败 - URL: %s, 方法: %s, 状态码: %d, 消息: %s", 
		e.URL, e.Method, e.StatusCode, e.Message)
}

// Unwrap 支持错误链
func (e *NetworkError) Unwrap() error {
	return e.Cause
}

// ParseError 解析错误
type ParseError struct {
	DataType string
	Content  string
	Position string
	Reason   string
	Cause    error
}

// Error 实现error接口
func (e *ParseError) Error() string {
	msg := fmt.Sprintf("解析%s失败", e.DataType)
	if e.Position != "" {
		msg += fmt.Sprintf(" [位置: %s]", e.Position)
	}
	if e.Reason != "" {
		msg += fmt.Sprintf(" - 原因: %s", e.Reason)
	}
	if e.Cause != nil {
		msg += fmt.Sprintf(" - 详细错误: %s", e.Cause.Error())
	}
	return msg
}

// Unwrap 支持错误链
func (e *ParseError) Unwrap() error {
	return e.Cause
}

// ========== 错误创建函数 ==========

// NewSpiderError 创建爬虫错误
func NewSpiderError(platform, operation, message string, cause error) *SpiderError {
	return &SpiderError{
		Platform:  platform,
		Operation: operation,
		Message:   message,
		Cause:     cause,
	}
}

// NewValidationError 创建验证错误
func NewValidationError(field, value, reason string) *ValidationError {
	return &ValidationError{
		Field:  field,
		Value:  value,
		Reason: reason,
	}
}

// NewValidationErrorWithContext 创建带上下文的验证错误
func NewValidationErrorWithContext(field, value, reason, context string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Value:   value,
		Reason:  reason,
		Context: context,
	}
}

// NewConfigError 创建配置错误
func NewConfigError(key, value, reason string) *ConfigError {
	return &ConfigError{
		Key:    key,
		Value:  value,
		Reason: reason,
	}
}

// NewConfigErrorWithSection 创建带节的配置错误
func NewConfigErrorWithSection(key, value, reason, section string) *ConfigError {
	return &ConfigError{
		Key:     key,
		Value:   value,
		Reason:  reason,
		Section: section,
	}
}

// NewNetworkError 创建网络错误
func NewNetworkError(url, method string, statusCode int, message string, cause error) *NetworkError {
	return &NetworkError{
		URL:        url,
		Method:     method,
		StatusCode: statusCode,
		Message:    message,
		Cause:      cause,
	}
}

// NewParseError 创建解析错误
func NewParseError(dataType, content, position, reason string, cause error) *ParseError {
	// 截断过长的内容
	if len(content) > 100 {
		content = content[:100] + "..."
	}
	
	return &ParseError{
		DataType: dataType,
		Content:  content,
		Position: position,
		Reason:   reason,
		Cause:    cause,
	}
}

// ========== 错误处理工具函数 ==========

// WrapSpiderError 包装为爬虫错误
func WrapSpiderError(platform, operation string, err error) error {
	if err == nil {
		return nil
	}
	
	// 如果已经是SpiderError，直接返回
	if _, ok := err.(*SpiderError); ok {
		return err
	}
	
	return NewSpiderError(platform, operation, err.Error(), err)
}

// WrapValidationError 包装为验证错误
func WrapValidationError(field, value string, err error) error {
	if err == nil {
		return nil
	}
	
	return NewValidationError(field, value, err.Error())
}

// WrapNetworkError 包装为网络错误
func WrapNetworkError(url, method string, statusCode int, err error) error {
	if err == nil {
		return nil
	}
	
	return NewNetworkError(url, method, statusCode, err.Error(), err)
}

// WrapParseError 包装为解析错误
func WrapParseError(dataType, content string, err error) error {
	if err == nil {
		return nil
	}
	
	return NewParseError(dataType, content, "", err.Error(), err)
}

// ========== 错误检查函数 ==========

// IsSpiderError 检查是否为爬虫错误
func IsSpiderError(err error) bool {
	_, ok := err.(*SpiderError)
	return ok
}

// IsValidationError 检查是否为验证错误
func IsValidationError(err error) bool {
	_, ok := err.(*ValidationError)
	return ok
}

// IsConfigError 检查是否为配置错误
func IsConfigError(err error) bool {
	_, ok := err.(*ConfigError)
	return ok
}

// IsNetworkError 检查是否为网络错误
func IsNetworkError(err error) bool {
	_, ok := err.(*NetworkError)
	return ok
}

// IsParseError 检查是否为解析错误
func IsParseError(err error) bool {
	_, ok := err.(*ParseError)
	return ok
}

// ========== 错误聚合函数 ==========

// AggregateErrors 聚合多个错误
func AggregateErrors(errors []error) error {
	if len(errors) == 0 {
		return nil
	}
	
	// 过滤nil错误
	var validErrors []error
	for _, err := range errors {
		if err != nil {
			validErrors = append(validErrors, err)
		}
	}
	
	if len(validErrors) == 0 {
		return nil
	}
	
	if len(validErrors) == 1 {
		return validErrors[0]
	}
	
	// 构建聚合错误消息
	var messages []string
	for i, err := range validErrors {
		messages = append(messages, fmt.Sprintf("%d. %s", i+1, err.Error()))
	}
	
	return fmt.Errorf("发生多个错误:\n%s", strings.Join(messages, "\n"))
}

// ========== 标准化错误处理函数 ==========

// HandleError 标准化错误处理
func HandleError(operation string, err error) error {
	if err == nil {
		return nil
	}
	
	return fmt.Errorf("%s失败: %w", operation, err)
}

// HandleValidationError 标准化验证错误处理
func HandleValidationError(field, value string, err error) error {
	if err == nil {
		return nil
	}
	
	return NewValidationError(field, value, err.Error())
}

// HandleConfigError 标准化配置错误处理
func HandleConfigError(key, value string, err error) error {
	if err == nil {
		return nil
	}
	
	return NewConfigError(key, value, err.Error())
}
