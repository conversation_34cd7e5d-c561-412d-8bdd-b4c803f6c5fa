package shared

import (
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go-monitor/internal/config"
	"go-monitor/internal/models"
	"go-monitor/internal/types"
)

// ========== 工具函数 ==========

// BuildProductURL 构建产品页面URL
func BuildProductURL(siteURL, productPath, productID string) string {
	if siteURL == "" {
		siteURL = DefaultSiteURL
	}

	// 确保siteURL不以斜杠结尾
	siteURL = strings.TrimSuffix(siteURL, "/")

	// 确保productPath以斜杠开头
	if !strings.HasPrefix(productPath, "/") {
		productPath = "/" + productPath
	}

	return fmt.Sprintf("%s%s%s", siteURL, productPath, productID)
}

// BuildATCLink 构建加购链接
func BuildATCLink(siteURL, atcPath, offerListingID, productID string) *string {
	if siteURL == "" || atcPath == "" || offerListingID == "" {
		return nil
	}

	// 构建ATC链接
	siteURL = strings.TrimSuffix(siteURL, "/")
	if !strings.HasPrefix(atcPath, "/") {
		atcPath = "/" + atcPath
	}

	// 构建查询参数
	params := url.Values{}
	params.Set("offerListingId", offerListingID)
	if productID != "" {
		params.Set("asin", productID)
	}

	atcURL := fmt.Sprintf("%s%s?%s", siteURL, atcPath, params.Encode())
	return &atcURL
}

// ParsePrice 解析价格字符串
func ParsePrice(priceStr string) (float64, error) {
	if priceStr == "" {
		return 0, fmt.Errorf("价格字符串为空")
	}

	// 移除货币符号和空格
	cleanPrice := regexp.MustCompile(`[^\d.,]`).ReplaceAllString(priceStr, "")

	// 处理千位分隔符
	if strings.Contains(cleanPrice, ",") && strings.Contains(cleanPrice, ".") {
		// 如果同时包含逗号和点，假设逗号是千位分隔符
		parts := strings.Split(cleanPrice, ".")
		if len(parts) == 2 && len(parts[1]) <= 2 {
			// 最后一个点后面是小数部分
			cleanPrice = strings.ReplaceAll(parts[0], ",", "") + "." + parts[1]
		}
	} else if strings.Contains(cleanPrice, ",") {
		// 只有逗号，可能是千位分隔符或小数分隔符
		parts := strings.Split(cleanPrice, ",")
		if len(parts) == 2 && len(parts[1]) <= 2 {
			// 逗号作为小数分隔符
			cleanPrice = parts[0] + "." + parts[1]
		} else {
			// 逗号作为千位分隔符
			cleanPrice = strings.ReplaceAll(cleanPrice, ",", "")
		}
	}

	price, err := strconv.ParseFloat(cleanPrice, 64)
	if err != nil {
		return 0, fmt.Errorf("解析价格失败: %w", err)
	}

	// 验证价格范围
	if price < MinPriceValue || price > MaxPriceValue {
		return 0, fmt.Errorf("价格超出有效范围: %.2f", price)
	}

	return price, nil
}

// FormatPrice 格式化价格显示
func FormatPrice(price float64, currency string) string {
	if currency == "" {
		currency = DefaultCurrency
	}
	return fmt.Sprintf("%.2f %s", price, currency)
}

// GetStockFromBool 从布尔值获取库存数量
func GetStockFromBool(inStock bool) int {
	if inStock {
		return StockQuantity
	}
	return NoStockQuantity
}

// GetAvailabilityText 获取可用性文本
func GetAvailabilityText(inStock bool) string {
	if inStock {
		return AvailabilityInStock
	}
	return AvailabilityOutOfStock
}

// CreateStringPointer 创建字符串指针
func CreateStringPointer(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// SafeStringDeref 安全解引用字符串指针
func SafeStringDeref(s *string, defaultValue string) string {
	if s == nil {
		return defaultValue
	}
	return *s
}

// TruncateString 截断字符串到指定长度
func TruncateString(s string, maxLength int) string {
	if len(s) <= maxLength {
		return s
	}
	return s[:maxLength-3] + "..."
}

// CleanTitle 清理产品标题
func CleanTitle(title string) string {
	// 移除多余的空白字符
	title = strings.TrimSpace(title)
	title = regexp.MustCompile(`\s+`).ReplaceAllString(title, " ")

	// 截断过长的标题
	return TruncateString(title, MaxTitleLength)
}

// ValidateURL 验证URL格式
func ValidateURL(urlStr string) error {
	if urlStr == "" {
		return fmt.Errorf("URL不能为空")
	}

	if len(urlStr) > MaxURLLength {
		return fmt.Errorf("URL长度超出限制: %d", len(urlStr))
	}

	_, err := url.Parse(urlStr)
	if err != nil {
		return fmt.Errorf("无效的URL格式: %w", err)
	}

	return nil
}

// ConvertToStringSlice 转换interface{}切片为字符串切片
func ConvertToStringSlice(items []interface{}) []string {
	result := make([]string, len(items))
	for i, item := range items {
		result[i] = fmt.Sprintf("%v", item)
	}
	return result
}

// MergeStringSlices 合并多个字符串切片，去重
func MergeStringSlices(slices ...[]string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, slice := range slices {
		for _, item := range slice {
			if !seen[item] {
				seen[item] = true
				result = append(result, item)
			}
		}
	}

	return result
}

// ========== 产品数据处理函数 ==========

// CreateBaseProduct 创建基础产品项
func CreateBaseProduct(name, productID, title, productURL, platform, siteURL string, notifications []string) *models.ProductItem {
	return &models.ProductItem{
		Name:          name,
		ProductID:     productID,
		Title:         CleanTitle(title),
		URL:           productURL,
		Platform:      platform,
		SiteURL:       siteURL,
		Notifications: notifications,
		CrawledAt:     time.Now(),
		Metadata:      make(map[string]interface{}),
	}
}

// SetProductPriceAndStock 设置产品价格和库存信息
func SetProductPriceAndStock(product *models.ProductItem, price float64, currency string, inStock bool, availability string, offerID *string) {
	product.Price = price
	product.Currency = currency
	product.InStock = inStock
	product.Stock = GetStockFromBool(inStock)
	product.Availability = availability
	product.OfferID = offerID
}

// SetProductMetadata 设置产品元数据
func SetProductMetadata(product *models.ProductItem, spiderType, apiSource string, additionalData map[string]interface{}) {
	if product.Metadata == nil {
		product.Metadata = make(map[string]interface{})
	}

	product.Metadata["spider_type"] = spiderType
	product.Metadata["api_source"] = apiSource

	// 添加额外的元数据
	for key, value := range additionalData {
		product.Metadata[key] = value
	}
}

// ========== 配置处理函数 ==========

// GetConfigWithDefault 从配置中获取值，支持默认值
func GetConfigWithDefault(spiderConfig *types.SpiderConfig, key string, defaultValue interface{}) interface{} {
	if spiderConfig == nil || spiderConfig.SpiderSettings == nil {
		return defaultValue
	}

	if value, exists := spiderConfig.SpiderSettings[key]; exists {
		return value
	}

	return defaultValue
}

// ExtractProductIDsFromConfig 从配置中提取产品ID列表
func ExtractProductIDsFromConfig(spiderConfig *types.SpiderConfig) []string {
	return config.GetProductIDsFromConfig(spiderConfig)
}

// ========== 错误处理函数 ==========

// WrapError 包装错误信息
func WrapError(operation string, err error) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s失败: %w", operation, err)
}

// CreateValidationError 创建验证错误
func CreateValidationError(field, value, reason string) error {
	return fmt.Errorf("字段验证失败 - 字段: %s, 值: %s, 原因: %s", field, value, reason)
}

// ========== 请求构建器功能 ==========

// SpiderRequestBuilder 爬虫请求构建器
type SpiderRequestBuilder struct {
	platform string
	baseURL  string
	method   string
	headers  map[string]string
	params   map[string]string
	metadata map[string]interface{}
	body     []byte
}

// NewSpiderRequestBuilder 创建爬虫请求构建器
func NewSpiderRequestBuilder(platform, baseURL string) *SpiderRequestBuilder {
	return &SpiderRequestBuilder{
		platform: platform,
		baseURL:  baseURL,
		method:   "GET",
		headers:  make(map[string]string),
		params:   make(map[string]string),
		metadata: make(map[string]interface{}),
	}
}

// SetMethod 设置HTTP方法
func (b *SpiderRequestBuilder) SetMethod(method string) *SpiderRequestBuilder {
	b.method = method
	return b
}

// AddHeader 添加请求头
func (b *SpiderRequestBuilder) AddHeader(key, value string) *SpiderRequestBuilder {
	b.headers[key] = value
	return b
}

// AddHeaders 批量添加请求头
func (b *SpiderRequestBuilder) AddHeaders(headers map[string]string) *SpiderRequestBuilder {
	for k, v := range headers {
		b.headers[k] = v
	}
	return b
}

// AddParam 添加查询参数
func (b *SpiderRequestBuilder) AddParam(key, value string) *SpiderRequestBuilder {
	b.params[key] = value
	return b
}

// AddParams 批量添加查询参数
func (b *SpiderRequestBuilder) AddParams(params map[string]string) *SpiderRequestBuilder {
	for k, v := range params {
		b.params[k] = v
	}
	return b
}

// SetBody 设置请求体
func (b *SpiderRequestBuilder) SetBody(body []byte) *SpiderRequestBuilder {
	b.body = body
	return b
}

// SetJSONBody 设置JSON请求体
func (b *SpiderRequestBuilder) SetJSONBody(data interface{}) *SpiderRequestBuilder {
	if data == nil {
		return b
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		b.metadata["json_marshal_error"] = err.Error()
		return b
	}

	b.body = jsonData
	b.headers["Content-Type"] = "application/json; charset=utf-8"
	return b
}

// SetMetadata 设置元数据
func (b *SpiderRequestBuilder) SetMetadata(key string, value interface{}) *SpiderRequestBuilder {
	b.metadata[key] = value
	return b
}

// SetSpiderMetadata 设置爬虫标准元数据
func (b *SpiderRequestBuilder) SetSpiderMetadata(config *types.SpiderConfig, productID string) *SpiderRequestBuilder {
	// 使用构建器的平台参数，而不是配置中的平台
	b.metadata["spider_type"] = b.platform

	if config != nil {
		b.metadata["name"] = config.GetName()
		b.metadata["country"] = config.GetCountry()

		if settings := config.GetSpiderSettings(); settings != nil {
			if proxyGroup, ok := settings["proxies"].([]interface{}); ok {
				b.metadata["proxies"] = proxyGroup
			}
			if cookieGroup, ok := settings["cookies"].([]interface{}); ok {
				b.metadata["cookies"] = cookieGroup
			}
			if fingerprint, ok := settings["fingerprint"].(string); ok {
				b.metadata["fingerprint"] = fingerprint
			}
		}
	}

	if productID != "" {
		b.metadata["product_id"] = productID
	}

	return b
}

// SetCommonHeaders 设置常用的爬虫请求头
func (b *SpiderRequestBuilder) SetCommonHeaders() *SpiderRequestBuilder {
	b.headers["accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
	b.headers["accept-language"] = "en-US,en;q=0.5"
	b.headers["accept-encoding"] = "gzip, deflate"
	b.headers["connection"] = "keep-alive"
	b.headers["upgrade-Iinsecure-Requests"] = "1"
	b.headers["sec-fetch-dest"] = "document"
	b.headers["sec-fetch-mode"] = "navigate"
	b.headers["sec-fetch-site"] = "none"
	b.headers["sec-fetch-user"] = "?1"
	return b
}

// SetAPIHeaders 设置API请求头
func (b *SpiderRequestBuilder) SetAPIHeaders() *SpiderRequestBuilder {
	b.headers["accept"] = "application/json, text/plain, */*"
	b.headers["content-type"] = "application/json"
	return b
}

// SetFormHeaders 设置表单请求头
func (b *SpiderRequestBuilder) SetFormHeaders() *SpiderRequestBuilder {
	b.headers["content-type"] = "application/x-www-form-urlencoded; charset=UTF-8"
	return b
}

// Build 构建请求对象
func (b *SpiderRequestBuilder) Build() *models.Request {
	return &models.Request{
		URL:        b.baseURL,
		Method:     b.method,
		Headers:    b.headers,
		Params:     b.params,
		Body:       b.body,
		Metadata:   b.metadata,
		SpiderType: b.platform,
	}
}

// ========== 便捷的爬虫请求创建函数 ==========

// CreateAmazonRequest 创建Amazon爬虫请求
func CreateAmazonRequest(baseURL string, config *types.SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("amazon", baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}

// CreateAliExpressRequest 创建AliExpress爬虫请求
func CreateAliExpressRequest(baseURL string, config *types.SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("aliexpress", baseURL).
		SetMethod("POST").
		SetFormHeaders().
		SetSpiderMetadata(config, productID)
}

// CreatePopMartRequest 创建PopMart爬虫请求
func CreatePopMartRequest(baseURL string, config *types.SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder("popmart", baseURL).
		SetAPIHeaders().
		SetSpiderMetadata(config, productID)
}

// CreateGenericSpiderRequest 创建通用爬虫请求
func CreateGenericSpiderRequest(platform, baseURL string, config *types.SpiderConfig, productID string) *SpiderRequestBuilder {
	return NewSpiderRequestBuilder(platform, baseURL).
		SetCommonHeaders().
		SetSpiderMetadata(config, productID)
}
