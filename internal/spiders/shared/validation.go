package shared

import (
	"fmt"
	"regexp"
	"strings"

	"go-monitor/internal/types"
)

// ========== 数据验证函数 ==========

// ValidateSpiderConfig 验证爬虫配置
func ValidateSpiderConfig(config *types.SpiderConfig) error {
	if config == nil {
		return fmt.Errorf(ErrMissingConfig)
	}

	// 验证必需字段
	if config.Name == "" {
		return CreateValidationError("name", "", "监控名称不能为空")
	}

	if config.Platform == "" {
		return CreateValidationError("platform", "", "平台标识不能为空")
	}

	// 验证平台标识
	validPlatforms := []string{
		PlatformAmazon, PlatformAmazonWishlist, PlatformAmazonMain, PlatformAmazonACP,
		PlatformAliExpress, PlatformPopMart,
	}
	if !contains(validPlatforms, config.Platform) {
		return CreateValidationError("platform", config.Platform, "不支持的平台")
	}

	// 验证站点URL
	if config.SiteURL != "" {
		if err := ValidateURL(config.SiteURL); err != nil {
			return CreateValidationError("site_url", config.SiteURL, err.Error())
		}
	}

	// 验证国家代码
	if config.Country != "" && len(config.Country) != 2 {
		return CreateValidationError("country", config.Country, "国家代码应为2位字母")
	}

	return nil
}

// ValidatePrice 验证价格值
func ValidatePrice(price float64) error {
	if price < 0 {
		return CreateValidationError("price", fmt.Sprintf("%.2f", price), "价格不能为负数")
	}

	if price > MaxPriceValue {
		return CreateValidationError("price", fmt.Sprintf("%.2f", price), "价格超出最大限制")
	}

	return nil
}

// ValidateCurrency 验证货币代码
func ValidateCurrency(currency string) error {
	if currency == "" {
		return nil // 允许空值，使用默认货币
	}

	// 简单的货币代码验证（1-5个字符）
	if len(currency) < 1 || len(currency) > 5 {
		return CreateValidationError("currency", currency, "货币代码长度应为1-5个字符")
	}

	return nil
}

// ValidateTitle 验证产品标题
func ValidateTitle(title string) error {
	if title == "" {
		return CreateValidationError("title", "", "产品标题不能为空")
	}

	if len(title) > MaxTitleLength {
		return CreateValidationError("title", title, "标题长度超出限制")
	}

	return nil
}

// ValidateNotifications 验证通知配置
func ValidateNotifications(notifications []string) error {
	for i, notification := range notifications {
		if notification == "" {
			return CreateValidationError("notifications", "", fmt.Sprintf("第%d个通知配置不能为空", i+1))
		}

		if len(notification) > 100 {
			return CreateValidationError("notifications", notification, fmt.Sprintf("第%d个通知配置长度超出限制", i+1))
		}
	}

	return nil
}

// ========== 数据完整性验证 ==========

// ValidateHTTPResponse 验证HTTP响应
func ValidateHTTPResponse(statusCode int, body []byte) error {
	if statusCode != HTTPStatusOK {
		return fmt.Errorf("HTTP状态码错误: %d", statusCode)
	}

	if len(body) == 0 {
		return fmt.Errorf("响应体为空")
	}

	return nil
}

// ========== 辅助函数 ==========

// contains 检查字符串切片是否包含指定值
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// IsValidJSON 检查字符串是否为有效JSON
func IsValidJSON(data string) bool {
	return strings.HasPrefix(strings.TrimSpace(data), "{") || strings.HasPrefix(strings.TrimSpace(data), "[")
}

// IsValidHTML 检查字符串是否为有效HTML
func IsValidHTML(data string) bool {
	return strings.Contains(strings.ToLower(data), "<html") || strings.Contains(strings.ToLower(data), "<!doctype")
}

// SanitizeInput 清理输入数据
func SanitizeInput(input string) string {
	// 移除潜在的危险字符
	input = strings.TrimSpace(input)
	input = regexp.MustCompile(`[<>\"'&]`).ReplaceAllString(input, "")
	return input
}
