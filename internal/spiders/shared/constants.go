package shared

// ========== HTTP 相关常量 ==========

const (
	// HTTP 状态码
	HTTPStatusOK                  = 200
	HTTPStatusBadRequest          = 400
	HTTPStatusUnauthorized        = 401
	HTTPStatusForbidden           = 403
	HTTPStatusNotFound            = 404
	HTTPStatusTooManyRequests     = 429
	HTTPStatusInternalServerError = 500
	HTTPStatusBadGateway          = 502
	HTTPStatusServiceUnavailable  = 503

	// HTTP 内容类型
	ContentTypeJSON      = "application/json"
	ContentTypeHTML      = "text/html"
	ContentTypeFormData  = "application/x-www-form-urlencoded"
	ContentTypeMultipart = "multipart/form-data"
	ContentTypeTextPlain = "text/plain"

	// HTTP 方法
	MethodGET    = "GET"
	MethodPOST   = "POST"
	MethodPUT    = "PUT"
	MethodDELETE = "DELETE"
	MethodPATCH  = "PATCH"
	MethodHEAD   = "HEAD"
)

// ========== 平台相关常量 ==========

const (
	// 平台标识
	PlatformAmazon     = "amazon"
	PlatformAliExpress = "aliexpress"
	PlatformPopMart    = "popmart"

	// Amazon 子平台标识
	PlatformAmazonMain     = "amazon-main"
	PlatformAmazonWishlist = "amazon-wishlist"
	PlatformAmazonACP      = "amazon-acp"

	// 爬虫版本
	SpiderVersion = "1.0.0"

	// 默认值
	DefaultCountry   = "US"
	DefaultCurrency  = "USD"
	DefaultSiteURL   = "https://www.example.com"
	DefaultBatchSize = 50
	DefaultTimeout   = 30 // 秒
	DefaultRetries   = 3
	DefaultUserAgent = "Go-Monitor/1.0"
)

// ========== 库存和可用性常量 ==========

const (
	// 库存状态
	InStockText     = "In Stock"
	OutOfStockText  = "Out of Stock"
	StockQuantity   = 1
	NoStockQuantity = 0

	// 可用性状态
	AvailabilityInStock    = "In Stock"
	AvailabilityOutOfStock = "Out of Stock"
	AvailabilityUnknown    = "Unknown"
	AvailabilityPreOrder   = "Pre-order"
	AvailabilityBackOrder  = "Back Order"
)

// ========== 错误消息常量 ==========

const (
	// 通用错误消息
	ErrMissingProductID    = "缺少产品ID"
	ErrInvalidJSON         = "无效的JSON响应"
	ErrInvalidHTML         = "无效的HTML响应"
	ErrMissingConfig       = "缺少配置信息"
	ErrInvalidConfig       = "无效的配置信息"
	ErrNetworkTimeout      = "网络请求超时"
	ErrHTTPStatusError     = "HTTP状态码错误"
	ErrParseResponseFailed = "解析响应失败"
	ErrBuildRequestFailed  = "构建请求失败"
	ErrValidationFailed    = "数据验证失败"

	// 平台特定错误消息
	ErrAmazonASINInvalid      = "无效的Amazon ASIN"
	ErrAmazonMerchantMismatch = "Amazon商家ID不匹配"
	ErrAliExpressAPIError     = "AliExpress API错误"
	ErrPopMartProductNotFound = "PopMart产品未找到"
)

// ========== 数据格式常量 ==========

const (
	// 时间格式
	TimeFormatISO8601  = "2006-01-02T15:04:05Z07:00"
	TimeFormatRFC3339  = "2006-01-02T15:04:05Z07:00"
	TimeFormatDate     = "2006-01-02"
	TimeFormatDateTime = "2006-01-02 15:04:05"
	TimeFormatDebug    = "20060102_150405"

	// 数据长度限制
	MaxProductIDLength = 50
	MaxTitleLength     = 500
	MaxURLLength       = 2048
	MaxDescLength      = 1000
	MinPriceValue      = 0.01
	MaxPriceValue      = 999999.99

	// 调试相关
	MaxDebugFileSize = 5 * 1024 * 1024 // 5MB
	DebugFileMode    = 0755
	DebugFilePerms   = 0644
)

// ========== 配置键名常量 ==========

const (
	// 通用配置键
	ConfigKeyProductIDs    = "product_ids"
	ConfigKeyProductID     = "product_id"
	ConfigKeyCountry       = "country"
	ConfigKeyCurrency      = "currency"
	ConfigKeySiteURL       = "site_url"
	ConfigKeyNotifications = "notifications"
	ConfigKeyProxies       = "proxies"
	ConfigKeyCookies       = "cookies"
	ConfigKeyTimeout       = "timeout"
	ConfigKeyRetries       = "retries"
	ConfigKeyUserAgent     = "user_agent"

	// 平台特定配置键
	ConfigKeyAmazonMerchantID = "merchant_id"
	ConfigKeyAliExpressLang   = "lang"
	ConfigKeyPopMartAPI       = "api"
)

// ========== Amazon 特定常量 ==========

const (
	// Amazon API 路径
	AmazonProductAjaxPath = "/gp/product/ajax/ref=aod_f_new"
	AmazonProductPagePath = "/dp/"
	AmazonATCBasePath     = "/gp/product/handle-buy-box/ref=dp_start-bbf_1_glance"

	// Amazon 心愿单 API 路径
	AmazonWishlistAPIPath = "/hz/wishlist/ls/"
	AmazonWishlistRefPath = "?ref_=ast_sto_dp"

	// Amazon 参数
	AmazonFilterParam  = `{"new":true,"used":false}`
	AmazonExperienceID = "aodAjaxMain"
	AmazonPCParam      = "dp"

	// Amazon 心愿单参数
	AmazonWishlistTypeParam     = "wishlist"
	AmazonWishlistFilterParam   = "all"
	AmazonWishlistSortParam     = "priority"
	AmazonWishlistViewTypeParam = "grid"
	AmazonWishlistLanguageParam = "en_GB"

	// Amazon 默认值
	AmazonDefaultCurrency = "EUR"
	AmazonDefaultSiteURL  = "https://www.amazon.com"
)

// ========== AliExpress 特定常量 ==========

const (
	// AliExpress API 参数
	AliExpressJSV      = "2.5.1"
	AliExpressAppKey   = "12574478"
	AliExpressAPI      = "mtop.aliexpress.itemdetail.querySkuPanelDetail"
	AliExpressVersion  = "1.0"
	AliExpressType     = "originaljson"
	AliExpressDataType = "jsonp"

	// AliExpress 默认值
	AliExpressBaseURL     = "https://acs.aliexpress.com/h5/mtop.aliexpress.itemdetail.queryskupaneldetail/1.0/"
	AliExpressDefaultLang = "en_US"
	AliExpressClientType  = "pc"
	AliExpressSourceType  = 1
)

// ========== PopMart 特定常量 ==========

const (
	// PopMart API 路径
	PopMartProductDetailsPath     = "/shop/v1/shop/productDetails"      // 旧版单产品API（保留兼容）
	PopMartCollectionProductsPath = "/shop/v3/shop/productOnCollection" // 新版集合API

	// PopMart 参数
	PopMartSpuIDParam = "spuId" // 旧版参数（保留兼容）

	// PopMart 默认值
	PopMartDefaultSiteURL  = "https://www.popmart.com"
	PopMartDefaultPageSize = 1000 // 默认分页大小
)
