# HTTP客户端迁移指南

从 `pkg/httpclient_old` 迁移到新的 `pkg/httpclient`

## 概述

新版本HTTP客户端采用分层架构，解决了旧版本的内存泄漏问题，并提供了更好的资源管理和可选功能。

## 主要改进

### ✅ 已解决的问题

1. **内存泄漏修复**: 统一的资源管理器自动清理所有资源
2. **自动取消机制**: 基于context的请求取消和超时管理
3. **可选指纹功能**: 指纹功能可以完全禁用，降低复杂性
4. **统一监控**: 集中的连接和性能监控
5. **更好的错误处理**: 分类错误和重试判断

### 🚀 新增功能

1. **分层架构**: 基础层 → 功能层 → 应用层
2. **多种客户端**: 基础、高级、爬虫专用客户端
3. **请求构建器**: 链式API构建复杂请求
4. **配置管理**: 环境变量和配置文件支持
5. **便捷函数**: 快速使用的全局函数

## 快速迁移

### 1. 导入路径变更

```go
// 旧版本
import "go-monitor/pkg/httpclient_old"

// 新版本
import "go-monitor/pkg/httpclient"
```

### 2. 基础用法对比

#### 旧版本
```go
// 旧版本 - 复杂的初始化
client := httpclient_old.NewClient(&httpclient_old.Config{
    Timeout: 30 * time.Second,
    // 大量配置选项...
})

resp, err := client.Get("https://api.example.com")
```

#### 新版本
```go
// 新版本 - 简化的使用
resp, err := httpclient.Get(ctx, "https://api.example.com", nil)

// 或者创建客户端
client := httpclient.NewBasicClient(nil)
defer client.Close()
resp, err := client.Get(ctx, "https://api.example.com", nil)
```

## 详细迁移步骤

### 步骤1: 更新导入

```diff
- import "go-monitor/pkg/httpclient_old"
- import "go-monitor/pkg/httpclient_old/core"
+ import "go-monitor/pkg/httpclient"
```

### 步骤2: 客户端创建

#### 旧版本客户端创建
```go
// 旧版本
config := &httpclient_old.Config{
    Timeout:         30 * time.Second,
    MaxIdleConns:    100,
    MaxConnsPerHost: 10,
    UserAgent:       "MyApp/1.0",
    EnableMonitoring: true,
    // 复杂的指纹配置...
}

client := httpclient_old.NewClient(config)
```

#### 新版本客户端创建
```go
// 新版本 - 基础客户端
client := httpclient.NewBasicClient(nil)
defer client.Close()

// 新版本 - 高级客户端
config := &httpclient.Config{
    Timeout:           30 * time.Second,
    MaxIdleConns:      100,
    MaxConnsPerHost:   10,
    UserAgent:         "MyApp/1.0",
    EnableMonitoring:  true,
    EnableFingerprint: false, // 可选
}
client := httpclient.NewAdvancedClient(config)
defer client.Close()
```

### 步骤3: 请求方法迁移

#### GET请求
```go
// 旧版本
resp, err := client.Get("https://api.example.com")

// 新版本
ctx := context.Background()
resp, err := client.Get(ctx, "https://api.example.com", nil)
```

#### POST请求
```go
// 旧版本
body := []byte(`{"key": "value"}`)
resp, err := client.Post("https://api.example.com", body)

// 新版本
body := []byte(`{"key": "value"}`)
headers := map[string]string{"Content-Type": "application/json"}
resp, err := client.Post(ctx, "https://api.example.com", body, headers)
```

#### 复杂请求
```go
// 旧版本
req := &httpclient_old.Request{
    URL:    "https://api.example.com",
    Method: "POST",
    Headers: map[string]string{
        "Authorization": "Bearer token",
        "Content-Type":  "application/json",
    },
    Body: []byte(`{"data": "value"}`),
}
resp, err := client.Do(req)

// 新版本
req := &httpclient.Request{
    URL:    "https://api.example.com",
    Method: "POST",
    Headers: map[string]string{
        "Authorization": "Bearer token",
        "Content-Type":  "application/json",
    },
    Body: []byte(`{"data": "value"}`),
}
resp, err := client.Do(ctx, req)
```

### 步骤4: 爬虫客户端迁移

#### 旧版本爬虫
```go
// 旧版本
spiderClient := httpclient_old.NewSpiderClient(&httpclient_old.SpiderConfig{
    Platform: "amazon",
    Country:  "US",
    // 复杂配置...
})

req := spiderClient.BuildRequest("https://amazon.com/product/123")
resp, err := spiderClient.Do(req)
```

#### 新版本爬虫
```go
// 新版本 - 实现SpiderConfig接口
type MySpiderConfig struct{}
func (c *MySpiderConfig) GetPlatform() string { return "amazon" }
func (c *MySpiderConfig) GetName() string { return "product-scraper" }
func (c *MySpiderConfig) GetCountry() string { return "US" }
func (c *MySpiderConfig) GetSpiderSettings() map[string]interface{} {
    return map[string]interface{}{
        "fingerprint_pool": "amazon-us",
    }
}

config := httpclient.NewSpiderConfig()
spiderConfig := &MySpiderConfig{}
client := httpclient.NewSpiderClient(config, spiderConfig)
defer client.Close()

// 使用请求构建器
req := client.NewRequestBuilder("https://amazon.com/product/123").
    SetCommonHeaders().
    SetSpiderMetadata("product-123").
    Build()

resp, err := client.Do(ctx, req)
```

## 配置迁移

### 旧版本配置
```go
config := &httpclient_old.Config{
    Timeout:              30 * time.Second,
    MaxIdleConns:         100,
    MaxConnsPerHost:      10,
    UserAgent:            "MyApp/1.0",
    EnableMonitoring:     true,
    EnableFingerprint:    true,
    FingerprintPoolName:  "default",
    RetryCount:           3,
    RetryDelay:           1 * time.Second,
    // 更多复杂配置...
}
```

### 新版本配置
```go
config := &httpclient.Config{
    // 基础配置
    Timeout:         30 * time.Second,
    MaxIdleConns:    100,
    MaxConnsPerHost: 10,
    UserAgent:       "MyApp/1.0",
    
    // 功能开关（简化）
    EnableMonitoring:   true,
    EnableFingerprint:  true,
    EnableRetry:        true,
    
    // 重试配置
    MaxRetries:   3,
    RetryDelay:   1 * time.Second,
    RetryBackoff: 2.0,
    
    // 指纹配置（简化）
    FingerprintConfig: map[string]interface{}{
        "enabled": true,
        "pools":   []string{"default"},
    },
}
```

## 指纹功能迁移

### 旧版本指纹
```go
// 旧版本 - 复杂的指纹配置
fingerprintConfig := &httpclient_old.FingerprintConfig{
    Enabled:     true,
    PoolName:    "chrome",
    ProfileName: "chrome_latest",
    // 大量配置选项...
}

client := httpclient_old.NewClientWithFingerprint(config, fingerprintConfig)
```

### 新版本指纹
```go
// 新版本 - 简化的指纹配置
config := &httpclient.Config{
    EnableFingerprint: true,
    FingerprintConfig: map[string]interface{}{
        "enabled": true,
        "pools":   []string{"chrome"},
    },
}

client := httpclient.NewAdvancedClient(config)
defer client.Close()

// 在请求中指定指纹
req := &httpclient.Request{
    URL:    "https://target-site.com",
    Method: "GET",
    Meta: map[string]interface{}{
        "fingerprint_pool": "chrome",
        "fingerprint_name": "chrome_latest",
    },
}
```

## 监控功能迁移

### 旧版本监控
```go
// 旧版本 - 分散的监控
connectionMonitor := httpclient_old.GetConnectionMonitor()
stats := connectionMonitor.GetStats()

networkMonitor := httpclient_old.GetNetworkMonitor()
networkStats := networkMonitor.GetStats()
```

### 新版本监控
```go
// 新版本 - 统一的监控
config := &httpclient.Config{
    EnableMonitoring: true,
}

client := httpclient.NewAdvancedClient(config)
defer client.Close()

// 获取统一的统计信息
stats := client.GetStats()
fmt.Printf("连接统计: %+v\n", stats["connection"])
fmt.Printf("性能统计: %+v\n", stats["performance"])
```

## 错误处理迁移

### 旧版本错误处理
```go
// 旧版本
resp, err := client.Get("https://api.example.com")
if err != nil {
    if httpclient_old.IsTimeoutError(err) {
        // 处理超时
    } else if httpclient_old.IsConnectionError(err) {
        // 处理连接错误
    }
}
```

### 新版本错误处理
```go
// 新版本
import "go-monitor/pkg/httpclient/errors"

resp, err := client.Get(ctx, "https://api.example.com", nil)
if err != nil {
    if errors.IsTimeoutError(err) {
        // 处理超时
    } else if errors.IsConnectionError(err) {
        // 处理连接错误
    } else if errors.IsRetryableError(err) {
        // 处理可重试错误
    }
}
```

## 兼容性说明

### 完全兼容
- ✅ 基础HTTP方法 (GET, POST, PUT, DELETE)
- ✅ 请求/响应结构
- ✅ 超时控制
- ✅ 代理支持
- ✅ Cookie管理

### 需要调整
- ⚠️ 客户端创建方式
- ⚠️ 配置结构
- ⚠️ 指纹配置方式
- ⚠️ 监控接口
- ⚠️ 错误处理

### 不兼容
- ❌ 旧版本的复杂配置选项
- ❌ 旧版本的内部API
- ❌ 旧版本的监控接口

## 迁移检查清单

### 准备阶段
- [ ] 备份现有代码
- [ ] 了解新版本架构
- [ ] 确定使用的功能

### 代码迁移
- [ ] 更新导入路径
- [ ] 迁移客户端创建代码
- [ ] 更新请求方法调用
- [ ] 迁移配置结构
- [ ] 更新错误处理
- [ ] 迁移监控代码

### 测试验证
- [ ] 运行现有测试
- [ ] 验证功能正确性
- [ ] 检查性能表现
- [ ] 验证内存使用

### 部署
- [ ] 在测试环境验证
- [ ] 监控内存使用
- [ ] 检查错误日志
- [ ] 性能对比

## 常见问题

### Q: 新版本性能如何？
A: 新版本在内存使用上有显著改进，请求性能与旧版本相当或更好。

### Q: 指纹功能是否完全兼容？
A: 指纹功能的底层实现保持兼容，但配置方式有所简化。

### Q: 是否需要修改大量代码？
A: 基础用法的修改较少，主要是添加context参数和更新配置结构。

### Q: 如何验证迁移成功？
A: 运行测试套件，监控内存使用，检查功能正确性。

## 获取帮助

如果在迁移过程中遇到问题：

1. 查看 [API文档](API.md)
2. 运行示例代码
3. 检查测试用例
4. 提交Issue报告问题

## 示例项目

完整的迁移示例请参考 `examples/migration/` 目录。
