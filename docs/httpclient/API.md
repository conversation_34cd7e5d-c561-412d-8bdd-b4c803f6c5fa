# HTTP客户端 API 文档

## 概述

重新设计的HTTP客户端采用分层架构，提供无内存泄漏、自动取消机制和可选指纹功能。

## 快速开始

### 基础使用

```go
package main

import (
    "context"
    "fmt"
    "log"
    
    "go-monitor/pkg/httpclient"
)

func main() {
    ctx := context.Background()
    
    // 使用便捷函数
    resp, err := httpclient.Get(ctx, "https://api.example.com/data", nil)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("状态码: %d\n", resp.StatusCode)
    fmt.Printf("响应体: %s\n", string(resp.Body))
}
```

### 创建客户端

```go
// 基础客户端
client := httpclient.NewBasicClient()
defer client.Close()

// 高级客户端（带监控和重试）
config := httpclient.NewAdvancedConfig()
advancedClient := httpclient.NewAdvancedClient(config)
defer advancedClient.Close()

// 爬虫客户端
spiderConfig := httpclient.NewSpiderConfig()
spiderClient := httpclient.NewSpiderClient(spiderConfig, mySpiderConfig)
defer spiderClient.Close()
```

## 核心接口

### HTTPClient 接口

```go
type HTTPClient interface {
    // 基础HTTP方法
    Get(ctx context.Context, url string, headers map[string]string) (*Response, error)
    Post(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error)
    Put(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error)
    Delete(ctx context.Context, url string, headers map[string]string) (*Response, error)
    
    // 通用请求方法
    Do(ctx context.Context, req *Request) (*Response, error)
    
    // 配置管理
    GetConfig() *Config
    UpdateConfig(config *Config) error
    
    // 生命周期
    Close() error
}
```

### Request 结构

```go
type Request struct {
    URL     string                 // 请求URL
    Method  string                 // HTTP方法
    Headers map[string]string      // 请求头
    Body    []byte                 // 请求体
    Params  map[string]string      // 查询参数
    Cookies map[string]string      // Cookies
    Proxy   string                 // 代理URL
    Timeout time.Duration          // 超时时间
    Meta    map[string]interface{} // 元数据
}
```

### Response 结构

```go
type Response struct {
    StatusCode   int                    // HTTP状态码
    Headers      map[string][]string    // 响应头
    Body         []byte                 // 响应体
    URL          string                 // 实际请求URL
    ResponseTime time.Duration          // 响应时间
    Request      *Request               // 原始请求
}
```

## 配置选项

### Config 结构

```go
type Config struct {
    // 基础配置
    Timeout         time.Duration // 请求超时时间
    MaxIdleConns    int          // 最大空闲连接数
    MaxConnsPerHost int          // 每个主机的最大连接数
    UserAgent       string       // 用户代理
    
    // 功能开关
    EnableMonitoring   bool // 启用监控功能
    EnableFingerprint  bool // 启用指纹功能
    EnableRetry        bool // 启用重试功能
    
    // 重试配置
    MaxRetries    int           // 最大重试次数
    RetryDelay    time.Duration // 重试延迟
    RetryBackoff  float64       // 退避因子
    
    // 指纹配置
    FingerprintConfig map[string]interface{} // 指纹配置
}
```

### 预定义配置

```go
// 获取默认配置
config := httpclient.GetDefaultConfig()

// 基础配置（轻量级）
config := httpclient.NewBasicConfig()

// 高级配置（全功能）
config := httpclient.NewAdvancedConfig()

// 爬虫配置（爬虫优化）
config := httpclient.NewSpiderConfig()
```

## 客户端类型

### 1. 基础客户端 (BasicClient)

最简单的HTTP客户端，提供基础功能：

```go
client := httpclient.NewBasicClient(config)
defer client.Close()

// 发起请求
resp, err := client.Get(ctx, "https://api.example.com", headers)
```

**特性：**
- 连接池管理
- 自动资源清理
- 基础超时控制
- 无内存泄漏

### 2. 高级客户端 (AdvancedClient)

集成所有高级功能的客户端：

```go
config := &httpclient.Config{
    EnableMonitoring:   true,
    EnableRetry:        true,
    EnableFingerprint:  false,
    MaxRetries:         3,
}

client := httpclient.NewAdvancedClient(config)
defer client.Close()

// 获取统计信息
stats := client.GetStats()
fmt.Printf("监控统计: %+v\n", stats)
```

**特性：**
- 包含基础客户端所有功能
- 请求监控和统计
- 智能重试机制
- 可选TLS指纹
- 性能监控

### 3. 爬虫客户端 (SpiderClient)

专门为爬虫场景优化的客户端：

```go
// 实现SpiderConfig接口
type MySpiderConfig struct{}
func (c *MySpiderConfig) GetPlatform() string { return "amazon" }
func (c *MySpiderConfig) GetName() string { return "product-scraper" }
func (c *MySpiderConfig) GetCountry() string { return "US" }
func (c *MySpiderConfig) GetSpiderSettings() map[string]interface{} {
    return map[string]interface{}{
        "fingerprint_pool": "amazon-us",
    }
}

spiderConfig := &MySpiderConfig{}
client := httpclient.NewSpiderClient(config, spiderConfig)
defer client.Close()

// 使用请求构建器
req := client.NewRequestBuilder("https://amazon.com/product/123").
    SetCommonHeaders().
    SetSpiderMetadata("product-123").
    Build()

resp, err := client.Do(ctx, req)
```

**特性：**
- 包含高级客户端所有功能
- 请求构建器
- 爬虫元数据管理
- 指纹池自动选择
- 爬虫专用配置

## 功能模块

### 1. 监控功能

启用监控后可以获取详细的统计信息：

```go
config := &httpclient.Config{
    EnableMonitoring: true,
}

client := httpclient.NewAdvancedClient(config)
defer client.Close()

// 执行一些请求...

// 获取统计信息
stats := client.GetStats()
if connectionStats, ok := stats["connection"]; ok {
    fmt.Printf("连接统计: %+v\n", connectionStats)
}

if performanceStats, ok := stats["performance"]; ok {
    fmt.Printf("性能统计: %+v\n", performanceStats)
}
```

### 2. 重试机制

自动重试失败的请求：

```go
config := &httpclient.Config{
    EnableRetry:   true,
    MaxRetries:    3,
    RetryDelay:    1 * time.Second,
    RetryBackoff:  2.0,
}

client := httpclient.NewAdvancedClient(config)
defer client.Close()

// 重试会自动处理临时错误
resp, err := client.Get(ctx, "https://unreliable-api.com", nil)
```

**可重试的错误类型：**
- 网络超时
- 连接错误
- 5xx服务器错误

### 3. 指纹功能

可选的TLS指纹功能：

```go
config := &httpclient.Config{
    EnableFingerprint: true,
    FingerprintConfig: map[string]interface{}{
        "enabled": true,
        "pools":   []string{"default", "chrome"},
    },
}

client := httpclient.NewAdvancedClient(config)
defer client.Close()

// 在请求中指定指纹
req := &httpclient.Request{
    URL:    "https://target-site.com",
    Method: "GET",
    Meta: map[string]interface{}{
        "fingerprint_pool": "chrome",
        "fingerprint_name": "chrome_latest",
    },
}

resp, err := client.Do(ctx, req)
```

## 错误处理

### 错误类型

```go
import "go-monitor/pkg/httpclient/errors"

// 检查错误类型
if errors.IsTimeoutError(err) {
    fmt.Println("请求超时")
} else if errors.IsConnectionError(err) {
    fmt.Println("连接错误")
} else if errors.IsRetryableError(err) {
    fmt.Println("可重试错误")
}
```

### 错误分类

- **超时错误** (`ErrTypeTimeout`): 请求超时
- **连接错误** (`ErrTypeConnection`): 网络连接问题
- **请求错误** (`ErrTypeRequest`): 请求格式错误
- **响应错误** (`ErrTypeResponse`): 服务器响应错误
- **配置错误** (`ErrTypeConfig`): 配置问题
- **资源错误** (`ErrTypeResource`): 资源管理错误

## 配置管理

### 环境变量配置

```bash
# 基础配置
export HTTPCLIENT_TIMEOUT=30s
export HTTPCLIENT_MAX_IDLE_CONNS=100
export HTTPCLIENT_MAX_CONNS_PER_HOST=10
export HTTPCLIENT_USER_AGENT="MyApp/1.0"

# 功能开关
export HTTPCLIENT_ENABLE_MONITORING=true
export HTTPCLIENT_ENABLE_RETRY=true
export HTTPCLIENT_ENABLE_FINGERPRINT=false

# 重试配置
export HTTPCLIENT_MAX_RETRIES=3
export HTTPCLIENT_RETRY_DELAY=1s
export HTTPCLIENT_RETRY_BACKOFF=2.0
```

```go
// 从环境变量加载配置
config := httpclient.LoadFromEnv()
client := httpclient.NewBasicClient(config)
```

### 配置文件

支持YAML和JSON格式的配置文件：

```yaml
# config.yaml
timeout: 30s
maxIdleConns: 100
maxConnsPerHost: 10
userAgent: "MyApp/1.0"

enableMonitoring: true
enableRetry: true
enableFingerprint: false

maxRetries: 3
retryDelay: 1s
retryBackoff: 2.0
```

```go
// 从配置文件加载
config, err := httpclient.LoadConfig("config.yaml")
if err != nil {
    log.Fatal(err)
}

client := httpclient.NewBasicClient(config)
```

## 最佳实践

### 1. 资源管理

```go
// 总是关闭客户端
client := httpclient.NewBasicClient(nil)
defer client.Close() // 重要：防止资源泄漏

// 或使用便捷函数（自动管理资源）
resp, err := httpclient.Get(ctx, url, headers)
```

### 2. 超时控制

```go
// 使用context控制超时
ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
defer cancel()

resp, err := client.Get(ctx, url, headers)
```

### 3. 错误处理

```go
resp, err := client.Get(ctx, url, headers)
if err != nil {
    if errors.IsRetryableError(err) {
        // 可以考虑手动重试
        log.Printf("可重试错误: %v", err)
    } else {
        // 不可重试错误，直接处理
        log.Printf("请求失败: %v", err)
        return err
    }
}

// 检查HTTP状态码
if resp.StatusCode >= 400 {
    log.Printf("HTTP错误: %d", resp.StatusCode)
}
```

### 4. 性能优化

```go
// 复用客户端实例
var globalClient = httpclient.NewAdvancedClient(&httpclient.Config{
    MaxIdleConns:    200,
    MaxConnsPerHost: 20,
    EnableMonitoring: true,
})

// 在程序退出时关闭
defer globalClient.Close()

// 使用连接池
// 客户端会自动复用连接，无需手动管理
```

## 迁移指南

从旧版本迁移的详细指南请参考 [迁移指南](MIGRATION.md)。
