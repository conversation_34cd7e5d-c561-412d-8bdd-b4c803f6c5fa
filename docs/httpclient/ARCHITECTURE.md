# HTTP客户端架构设计

## 概述

新的HTTP客户端采用分层架构设计，从根本上解决了旧版本的内存泄漏问题，并提供了更好的可扩展性和可维护性。

## 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                     应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  BasicClient    │  AdvancedClient   │   SpiderClient        │
│  (基础功能)      │  (完整功能)        │   (爬虫专用)           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     功能层 (Feature Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  Monitor        │  Retry         │  Fingerprint            │
│  (监控模块)      │  (重试机制)     │  (指纹功能)              │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     基础层 (Foundation Layer)                │
├─────────────────────────────────────────────────────────────┤
│  ResourceManager │ ConnectionManager │ Types & Interfaces   │
│  (资源管理器)     │ (连接管理器)       │ (核心类型)            │
└─────────────────────────────────────────────────────────────┘
```

## 分层设计原则

### 1. 基础层 (Foundation Layer)

**职责**: 提供核心的资源管理和连接管理功能

**组件**:
- **ResourceManager**: 统一的资源生命周期管理
- **ConnectionManager**: HTTP连接池管理
- **Types & Interfaces**: 核心类型定义和接口

**特点**:
- 无内存泄漏的资源管理
- 自动清理机制
- 线程安全的操作

### 2. 功能层 (Feature Layer)

**职责**: 提供可选的高级功能模块

**组件**:
- **Monitor**: 连接和性能监控
- **Retry**: 智能重试机制
- **Fingerprint**: TLS指纹功能

**特点**:
- 模块化设计，可独立开启/关闭
- 插件式架构
- 功能间解耦

### 3. 应用层 (Application Layer)

**职责**: 提供面向用户的客户端接口

**组件**:
- **BasicClient**: 基础HTTP客户端
- **AdvancedClient**: 集成所有功能的客户端
- **SpiderClient**: 爬虫专用客户端

**特点**:
- 易用的API设计
- 渐进式功能增强
- 场景化优化

## 核心组件详解

### ResourceManager (资源管理器)

```go
type ResourceManager interface {
    RegisterResource(id string, resource Resource) error
    UnregisterResource(id string) error
    GetResource(id string) (Resource, bool)
    ListResources() []string
    Start(ctx context.Context) error
    Stop() error
    Cleanup() error
    ForceCleanup() error
}
```

**设计特点**:
- 统一管理所有HTTP相关资源
- 自动清理过期和无效资源
- 防止内存泄漏
- 支持优雅关闭

**实现机制**:
- 使用 `sync.Map` 存储资源
- 定时清理goroutine
- 原子操作保证线程安全
- 引用计数管理

### ConnectionManager (连接管理器)

```go
type ConnectionManager interface {
    CreateConnection(config *ConnectionConfig) (Connection, error)
    GetConnection(id string) (Connection, bool)
    CloseConnection(id string) error
    GetPoolStats() PoolStats
    CleanupIdleConnections() int
    Start(ctx context.Context) error
    Stop() error
}
```

**设计特点**:
- HTTP连接池管理
- 连接复用优化
- 自动超时和取消
- 连接健康检查

**实现机制**:
- 基于 `http.Transport` 的连接池
- 连接生命周期跟踪
- 空闲连接自动清理
- 连接统计和监控

### Monitor (监控模块)

```go
type Monitor interface {
    RecordRequest(req *Request, resp *Response, err error)
    RecordConnection(event ConnectionEvent)
    GetStats() MonitorStats
    GetConnectionStats() []ConnectionStat
    Start(ctx context.Context) error
    Stop() error
}
```

**设计特点**:
- 请求级别监控
- 连接级别监控
- 性能指标收集
- 实时统计信息

**监控指标**:
- 请求总数、成功率、错误率
- 平均响应时间、最大/最小响应时间
- 活跃连接数、连接池状态
- 内存使用情况

## 内存管理策略

### 问题分析

旧版本的内存泄漏主要来源于：
1. 连接对象未正确释放
2. 监控数据无限累积
3. 资源清理机制不完善
4. goroutine泄漏

### 解决方案

#### 1. 统一资源管理
```go
// 所有资源都通过ResourceManager管理
type Resource interface {
    ID() string
    Type() string
    IsActive() bool
    LastUsed() time.Time
    Close() error
    Stats() map[string]interface{}
}
```

#### 2. 自动清理机制
```go
// 定期清理过期资源
func (rm *DefaultResourceManager) cleanupLoop() {
    ticker := time.NewTicker(rm.cleanupInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            rm.Cleanup()
        case <-rm.ctx.Done():
            return
        }
    }
}
```

#### 3. 生命周期管理
```go
// 确保资源正确关闭
func (c *BasicClient) Close() error {
    if c.closed {
        return nil
    }
    c.closed = true
    
    // 停止所有管理器
    c.connectionManager.Stop()
    c.resourceManager.Stop()
    
    return nil
}
```

## 并发安全设计

### 1. 无锁设计
- 使用 `sync.Map` 替代 `map + mutex`
- 原子操作处理计数器
- 减少锁竞争

### 2. 读写分离
- 读操作无锁
- 写操作最小化锁范围
- 使用读写锁优化

### 3. goroutine管理
- 统一的goroutine生命周期管理
- 使用context控制goroutine退出
- 防止goroutine泄漏

## 可扩展性设计

### 1. 插件式架构
```go
// 功能模块可独立开启/关闭
type Config struct {
    EnableMonitoring   bool
    EnableFingerprint  bool
    EnableRetry        bool
}
```

### 2. 接口驱动
- 所有核心组件都定义接口
- 支持自定义实现
- 便于测试和扩展

### 3. 配置驱动
- 运行时配置更新
- 环境变量支持
- 配置文件支持

## 性能优化

### 1. 连接复用
- HTTP/1.1 Keep-Alive
- HTTP/2 多路复用
- 连接池优化

### 2. 内存优化
- 对象池复用
- 及时释放大对象
- 内存使用监控

### 3. CPU优化
- 减少不必要的拷贝
- 优化热点路径
- 异步处理

## 错误处理策略

### 1. 分层错误处理
```go
// 基础层：包装系统错误
func WrapError(err error) error

// 功能层：添加功能相关信息
func (rm *RetryManager) HandleError(err error) error

// 应用层：用户友好的错误信息
func (c *BasicClient) Do(ctx context.Context, req *Request) (*Response, error)
```

### 2. 错误分类
- 可重试错误 vs 不可重试错误
- 临时错误 vs 永久错误
- 用户错误 vs 系统错误

### 3. 错误恢复
- 自动重试机制
- 降级策略
- 熔断保护

## 测试策略

### 1. 单元测试
- 每个组件独立测试
- Mock外部依赖
- 覆盖率 > 90%

### 2. 集成测试
- 组件间协作测试
- 真实场景模拟
- 端到端测试

### 3. 性能测试
- 基准测试
- 内存泄漏测试
- 并发压力测试

## 部署和运维

### 1. 监控指标
- 请求成功率
- 响应时间分布
- 内存使用趋势
- 连接池状态

### 2. 日志记录
- 结构化日志
- 分级日志
- 性能日志

### 3. 配置管理
- 热更新配置
- 环境隔离
- 配置验证

## 未来扩展

### 1. 协议支持
- HTTP/3 支持
- WebSocket 支持
- gRPC 支持

### 2. 功能增强
- 缓存机制
- 限流功能
- 熔断器

### 3. 可观测性
- 分布式追踪
- 指标导出
- 健康检查

## 总结

新的HTTP客户端架构通过分层设计、统一资源管理和模块化功能，从根本上解决了内存泄漏问题，同时提供了更好的可扩展性和可维护性。这种设计为未来的功能扩展和性能优化奠定了坚实的基础。
