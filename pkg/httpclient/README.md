# HTTP Client 重构

这是重新设计的HTTP客户端模块，采用分层架构解决内存泄漏和资源管理问题。

## 架构设计

### 基础层 (Foundation)
- 资源管理器：统一的资源生命周期管理
- 连接管理器：连接池和自动清理
- 核心类型：Request、Response等基础类型

### 功能层 (Features)  
- 监控模块：连接和性能监控
- 重试机制：智能重试和错误处理
- 指纹模块：可选的TLS指纹功能

### 应用层 (Client)
- 基础客户端：简单HTTP功能
- 高级客户端：完整功能集成
- 爬虫客户端：爬虫专用优化

## 使用方式

```go
// 基础使用
client := httpclient.NewBasicClient()
resp, err := client.Get(ctx, "https://example.com")

// 高级功能
client := httpclient.NewAdvancedClient(&httpclient.Config{
    EnableMonitoring: true,
    EnableFingerprint: false,
})
```

## 核心特性

- ✅ 无内存泄漏：自动资源管理
- ✅ 自动取消：基于context的超时管理  
- ✅ 可选指纹：TLS指纹功能可开启/关闭
- ✅ 统一监控：集中的连接和性能监控
- ✅ 测试友好：分层架构便于单元测试
