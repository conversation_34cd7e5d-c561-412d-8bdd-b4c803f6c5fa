# HTTP Client 重构

这是重新设计的HTTP客户端模块，采用分层架构解决内存泄漏和资源管理问题。

## 架构设计

### 基础层 (Foundation)
- 资源管理器：统一的资源生命周期管理
- 连接管理器：连接池和自动清理
- 核心类型：Request、Response等基础类型

### 功能层 (Features)  
- 监控模块：连接和性能监控
- 重试机制：智能重试和错误处理
- 指纹模块：可选的TLS指纹功能

### 应用层 (Client)
- 基础客户端：简单HTTP功能
- 高级客户端：完整功能集成
- 爬虫客户端：爬虫专用优化

## 使用方式

```go
// 基础使用
client := httpclient.NewBasicClient()
resp, err := client.Get(ctx, "https://example.com")

// 高级功能
client := httpclient.NewAdvancedClient(&httpclient.Config{
    EnableMonitoring: true,
    EnableFingerprint: false,
})

// HTTP/2 优化
client := httpclient.NewAdvancedClient(httpclient.WithHTTP2())

// SOCKS5 代理
client := httpclient.NewAdvancedClient(httpclient.WithSOCKS5("socks5://127.0.0.1:1080", "user", "pass"))

// HTTP 代理
client := httpclient.NewAdvancedClient(httpclient.WithHTTPProxy("http://proxy.example.com:8080"))
```

## 核心特性

- ✅ 无内存泄漏：自动资源管理
- ✅ 自动取消：基于context的超时管理
- ✅ 可选指纹：TLS指纹功能可开启/关闭
- ✅ 统一监控：集中的连接和性能监控
- ✅ 测试友好：分层架构便于单元测试
- ✅ HTTP/2 支持：完整的HTTP/2协议支持和配置
- ✅ 多种代理：支持HTTP、HTTPS、SOCKS5代理
- ✅ 代理认证：支持用户名密码认证

## 新增功能详解

### HTTP/2 支持

```go
// 创建HTTP/2优化配置
config := httpclient.WithHTTP2()
client := httpclient.NewAdvancedClient(config)

// 手动配置HTTP/2
config := &httpclient.Config{
    HTTPVersion: httpclient.HTTPVersionHTTP2Preferred,
    EnableHTTP2Push: true,
    HTTP2Settings: map[string]uint32{
        "MAX_CONCURRENT_STREAMS": 100,
        "INITIAL_WINDOW_SIZE": 65535,
    },
}
```

### 代理支持

```go
// SOCKS5 代理（带认证）
config := httpclient.WithSOCKS5("socks5://127.0.0.1:1080", "username", "password")

// HTTP 代理
config := httpclient.WithHTTPProxy("http://proxy.example.com:8080")

// HTTPS 代理
config := httpclient.WithHTTPSProxy("https://proxy.example.com:8080")

// 手动配置代理
config := &httpclient.Config{
    ProxyType: httpclient.ProxyTypeSOCKS5,
    ProxyURL: "socks5://127.0.0.1:1080",
    ProxyAuth: &httpclient.ProxyAuth{
        Username: "user",
        Password: "pass",
    },
}
```

### 支持的代理类型

- **HTTP 代理**: `http://proxy.example.com:8080`
- **HTTPS 代理**: `https://proxy.example.com:8080`
- **SOCKS5 代理**: `socks5://127.0.0.1:1080`
- **SOCKS4/4A 代理**: `socks4://127.0.0.1:1080` (自动转换为SOCKS5)

### HTTP 版本控制

- **HTTPVersionAuto**: 自动协商（默认）
- **HTTPVersionHTTP1Only**: 强制使用HTTP/1.1
- **HTTPVersionHTTP2Only**: 强制使用HTTP/2
- **HTTPVersionHTTP2Preferred**: 优先使用HTTP/2，失败时降级
