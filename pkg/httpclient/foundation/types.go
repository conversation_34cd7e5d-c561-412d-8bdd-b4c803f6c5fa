// Package foundation 提供HTTP客户端的基础类型和接口
package foundation

import (
	"context"
	"net/http"
	"time"
)

// Request HTTP请求结构
type Request struct {
	// 基础信息
	URL     string
	Method  string
	Headers map[string]string
	Body    []byte

	// 查询参数和Cookies
	Params  map[string]string
	Cookies map[string]string

	// 网络配置
	Proxy   string
	Timeout time.Duration

	// 元数据
	Meta map[string]interface{}
}

// Response HTTP响应结构
type Response struct {
	StatusCode   int
	Headers      map[string][]string
	Body         []byte
	URL          string
	ResponseTime time.Duration
	Request      *Request
}

// Config HTTP客户端配置
type Config struct {
	// 基础配置
	Timeout         time.Duration
	MaxIdleConns    int
	MaxConnsPerHost int
	UserAgent       string

	// 功能开关
	EnableMonitoring  bool
	EnableFingerprint bool
	EnableRetry       bool

	// 重试配置
	MaxRetries   int
	RetryDelay   time.Duration
	RetryBackoff float64

	// 指纹配置
	FingerprintConfig map[string]interface{}
}

// ResourceManager 资源管理器接口
type ResourceManager interface {
	// 资源注册和注销
	RegisterResource(id string, resource Resource) error
	UnregisterResource(id string) error

	// 资源查询
	GetResource(id string) (Resource, bool)
	ListResources() []string

	// 生命周期管理
	Start(ctx context.Context) error
	Stop() error

	// 清理操作
	Cleanup() error
	ForceCleanup() error
}

// Resource 可管理的资源接口
type Resource interface {
	// 资源标识
	ID() string
	Type() string

	// 生命周期
	IsActive() bool
	LastUsed() time.Time
	Close() error

	// 统计信息
	Stats() map[string]interface{}
}

// ConnectionManager 连接管理器接口
type ConnectionManager interface {
	// 连接管理
	CreateConnection(config *ConnectionConfig) (Connection, error)
	GetConnection(id string) (Connection, bool)
	GetConnectionByTarget(target string) (Connection, bool)
	CloseConnection(id string) error

	// 连接池管理
	GetPoolStats() PoolStats
	CleanupIdleConnections() int

	// 生命周期
	Start(ctx context.Context) error
	Stop() error
}

// Connection 连接接口
type Connection interface {
	Resource

	// 连接操作
	Do(req *http.Request) (*http.Response, error)

	// 连接信息
	RemoteAddr() string
	LocalAddr() string
	CreatedAt() time.Time
}

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	Target      string
	Timeout     time.Duration
	KeepAlive   time.Duration
	MaxIdleTime time.Duration
	TLSConfig   interface{} // 可以是 *tls.Config 或指纹配置
	ProxyURL    string
}

// PoolStats 连接池统计
type PoolStats struct {
	TotalConnections  int64
	ActiveConnections int
	IdleConnections   int
	CreatedCount      int64
	ClosedCount       int64
	ErrorCount        int64
}

// Monitor 监控器接口
type Monitor interface {
	// 事件记录
	RecordRequest(req *Request, resp *Response, err error)
	RecordConnection(event ConnectionEvent)

	// 统计信息
	GetStats() MonitorStats
	GetConnectionStats() []ConnectionStat

	// 生命周期
	Start(ctx context.Context) error
	Stop() error
}

// ConnectionEvent 连接事件
type ConnectionEvent struct {
	Type      string // created, closed, error, etc.
	ConnID    string
	Timestamp time.Time
	Details   map[string]interface{}
}

// MonitorStats 监控统计
type MonitorStats struct {
	TotalRequests     int64
	SuccessRequests   int64
	FailedRequests    int64
	AvgResponseTime   time.Duration
	ActiveConnections int
	TotalConnections  int64
}

// ConnectionStat 连接统计
type ConnectionStat struct {
	ID           string
	RemoteAddr   string
	CreatedAt    time.Time
	LastUsed     time.Time
	RequestCount int64
	IsActive     bool
}

// HTTPClient HTTP客户端接口
type HTTPClient interface {
	// 基础HTTP方法
	Get(ctx context.Context, url string, headers map[string]string) (*Response, error)
	Post(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error)
	Put(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error)
	Delete(ctx context.Context, url string, headers map[string]string) (*Response, error)

	// 通用请求方法
	Do(ctx context.Context, req *Request) (*Response, error)

	// 配置管理
	GetConfig() *Config
	UpdateConfig(config *Config) error

	// 生命周期
	Close() error
}
