// Package foundation 提供HTTP客户端的基础类型和接口
package foundation

import (
	"context"
	"net/http"
	"time"
)

// ProxyType 代理类型
type ProxyType int

const (
	// ProxyTypeNone 无代理
	ProxyTypeNone ProxyType = iota
	// ProxyTypeHTTP HTTP代理
	ProxyTypeHTTP
	// ProxyTypeHTTPS HTTPS代理
	ProxyTypeHTTPS
	// ProxyTypeSOCKS4 SOCKS4代理
	ProxyTypeSOCKS4
	// ProxyTypeSOCKS4A SOCKS4A代理
	ProxyTypeSOCKS4A
	// ProxyTypeSOCKS5 SOCKS5代理
	ProxyTypeSOCKS5
)

// String 返回代理类型的字符串表示
func (pt ProxyType) String() string {
	switch pt {
	case ProxyTypeNone:
		return "none"
	case ProxyTypeHTTP:
		return "http"
	case ProxyTypeHTTPS:
		return "https"
	case ProxyTypeSOCKS4:
		return "socks4"
	case ProxyTypeSOCKS4A:
		return "socks4a"
	case ProxyTypeSOCKS5:
		return "socks5"
	default:
		return "unknown"
	}
}

// HTTPVersion HTTP版本
type HTTPVersion int

const (
	// HTTPVersionAuto 自动协商（默认）
	HTTPVersionAuto HTTPVersion = iota
	// HTTPVersionHTTP1Only 仅使用HTTP/1.1
	HTTPVersionHTTP1Only
	// HTTPVersionHTTP2Only 仅使用HTTP/2
	HTTPVersionHTTP2Only
	// HTTPVersionHTTP2Preferred 优先使用HTTP/2
	HTTPVersionHTTP2Preferred
)

// String 返回HTTP版本的字符串表示
func (hv HTTPVersion) String() string {
	switch hv {
	case HTTPVersionAuto:
		return "auto"
	case HTTPVersionHTTP1Only:
		return "http1"
	case HTTPVersionHTTP2Only:
		return "http2"
	case HTTPVersionHTTP2Preferred:
		return "http2-preferred"
	default:
		return "unknown"
	}
}

// ProxyAuth 代理认证信息
type ProxyAuth struct {
	Username string
	Password string
}

// Request HTTP请求结构
type Request struct {
	// 基础信息
	URL     string
	Method  string
	Headers map[string]string
	Body    []byte

	// 查询参数和Cookies
	Params  map[string]string
	Cookies map[string]string

	// 网络配置
	Proxy   string
	Timeout time.Duration

	// 元数据
	Meta map[string]interface{}
}

// Response HTTP响应结构
type Response struct {
	StatusCode   int
	Headers      map[string][]string
	Body         []byte
	URL          string
	ResponseTime time.Duration
	Request      *Request
}

// Config HTTP客户端配置
type Config struct {
	// 基础配置
	Timeout         time.Duration
	MaxIdleConns    int
	MaxConnsPerHost int
	UserAgent       string

	// 功能开关
	EnableMonitoring  bool
	EnableFingerprint bool
	EnableRetry       bool

	// 重试配置
	MaxRetries   int
	RetryDelay   time.Duration
	RetryBackoff float64

	// 指纹配置
	FingerprintConfig map[string]interface{}

	// 代理配置
	ProxyType ProxyType
	ProxyURL  string
	ProxyAuth *ProxyAuth

	// HTTP版本配置
	HTTPVersion HTTPVersion

	// HTTP/2 特定配置
	EnableHTTP2Push bool
	HTTP2Settings   map[string]uint32
}

// ResourceManager 资源管理器接口
type ResourceManager interface {
	// 资源注册和注销
	RegisterResource(id string, resource Resource) error
	UnregisterResource(id string) error

	// 资源查询
	GetResource(id string) (Resource, bool)
	ListResources() []string

	// 生命周期管理
	Start(ctx context.Context) error
	Stop() error

	// 清理操作
	Cleanup() error
	ForceCleanup() error
}

// Resource 可管理的资源接口
type Resource interface {
	// 资源标识
	ID() string
	Type() string

	// 生命周期
	IsActive() bool
	LastUsed() time.Time
	Close() error

	// 统计信息
	Stats() map[string]interface{}
}

// ConnectionManager 连接管理器接口
type ConnectionManager interface {
	// 连接管理
	CreateConnection(config *ConnectionConfig) (Connection, error)
	GetConnection(id string) (Connection, bool)
	GetConnectionByTarget(target string) (Connection, bool)
	CloseConnection(id string) error

	// 连接池管理
	GetPoolStats() PoolStats
	CleanupIdleConnections() int

	// 生命周期
	Start(ctx context.Context) error
	Stop() error
}

// Connection 连接接口
type Connection interface {
	Resource

	// 连接操作
	Do(req *http.Request) (*http.Response, error)

	// 连接信息
	RemoteAddr() string
	LocalAddr() string
	CreatedAt() time.Time
}

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	Target      string
	Timeout     time.Duration
	KeepAlive   time.Duration
	MaxIdleTime time.Duration
	TLSConfig   interface{} // 可以是 *tls.Config 或指纹配置

	// 代理配置
	ProxyType ProxyType
	ProxyURL  string
	ProxyAuth *ProxyAuth

	// HTTP版本配置
	HTTPVersion     HTTPVersion
	EnableHTTP2Push bool
	HTTP2Settings   map[string]uint32
}

// PoolStats 连接池统计
type PoolStats struct {
	TotalConnections  int64
	ActiveConnections int
	IdleConnections   int
	CreatedCount      int64
	ClosedCount       int64
	ErrorCount        int64
}

// Monitor 监控器接口
type Monitor interface {
	// 事件记录
	RecordRequest(req *Request, resp *Response, err error)
	RecordConnection(event ConnectionEvent)

	// 统计信息
	GetStats() MonitorStats
	GetConnectionStats() []ConnectionStat

	// 生命周期
	Start(ctx context.Context) error
	Stop() error
}

// ConnectionEvent 连接事件
type ConnectionEvent struct {
	Type      string // created, closed, error, etc.
	ConnID    string
	Timestamp time.Time
	Details   map[string]interface{}
}

// MonitorStats 监控统计
type MonitorStats struct {
	TotalRequests     int64
	SuccessRequests   int64
	FailedRequests    int64
	AvgResponseTime   time.Duration
	ActiveConnections int
	TotalConnections  int64
}

// ConnectionStat 连接统计
type ConnectionStat struct {
	ID           string
	RemoteAddr   string
	CreatedAt    time.Time
	LastUsed     time.Time
	RequestCount int64
	IsActive     bool
}

// HTTPClient HTTP客户端接口
type HTTPClient interface {
	// 基础HTTP方法
	Get(ctx context.Context, url string, headers map[string]string) (*Response, error)
	Post(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error)
	Put(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error)
	Delete(ctx context.Context, url string, headers map[string]string) (*Response, error)

	// 通用请求方法
	Do(ctx context.Context, req *Request) (*Response, error)

	// 配置管理
	GetConfig() *Config
	UpdateConfig(config *Config) error

	// 生命周期
	Close() error
}
