// Package foundation 连接管理器实现
package foundation

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"sync"
	"sync/atomic"
	"time"

	"go-monitor/pkg/logging"

	"github.com/google/uuid"
)

// DefaultConnectionManager 默认连接管理器实现
type DefaultConnectionManager struct {
	// 连接存储
	connections sync.Map // map[string]*ManagedConnection

	// 统计信息
	stats PoolStats

	// 配置
	maxConnections  int
	maxIdleTime     time.Duration
	cleanupInterval time.Duration

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	logger logging.Logger

	// 状态
	running int32
}

// ManagedConnection 管理的连接
type ManagedConnection struct {
	id           string
	client       *http.Client
	transport    *http.Transport
	target       string
	createdAt    time.Time
	lastUsed     time.Time
	requestCount int64
	active       int32

	// 生命周期控制
	ctx    context.Context
	cancel context.CancelFunc

	mu sync.RWMutex
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(maxConnections int, maxIdleTime, cleanupInterval time.Duration) *DefaultConnectionManager {
	return &DefaultConnectionManager{
		maxConnections:  maxConnections,
		maxIdleTime:     maxIdleTime,
		cleanupInterval: cleanupInterval,
		logger:          logging.GetLogger("httpclient.connection_manager"),
	}
}

// CreateConnection 创建新连接
func (cm *DefaultConnectionManager) CreateConnection(config *ConnectionConfig) (Connection, error) {
	if config == nil {
		return nil, fmt.Errorf("连接配置不能为空")
	}

	// 生成唯一连接ID（使用UUID确保全局唯一性）
	connID := fmt.Sprintf("conn_%s", uuid.New().String())

	// 创建HTTP传输
	transport := &http.Transport{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		MaxConnsPerHost:     20,
		IdleConnTimeout:     config.MaxIdleTime,
		TLSHandshakeTimeout: 10 * time.Second,

		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			dialer := &net.Dialer{
				Timeout:   config.Timeout,
				KeepAlive: config.KeepAlive,
			}
			return dialer.DialContext(ctx, network, addr)
		},
	}

	// 配置代理
	if config.ProxyURL != "" {
		proxyURL, err := url.Parse(config.ProxyURL)
		if err != nil {
			return nil, fmt.Errorf("解析代理URL失败: %w", err)
		}
		transport.Proxy = http.ProxyURL(proxyURL)
	}

	// 配置TLS
	if config.TLSConfig != nil {
		if tlsConfig, ok := config.TLSConfig.(*tls.Config); ok {
			transport.TLSClientConfig = tlsConfig
		}
	}

	// 创建HTTP客户端
	client := &http.Client{
		Transport: transport,
		Timeout:   config.Timeout,
	}

	// 创建连接上下文
	connCtx, connCancel := context.WithCancel(context.Background())

	// 创建管理连接
	conn := &ManagedConnection{
		id:        connID,
		client:    client,
		transport: transport,
		target:    config.Target,
		createdAt: time.Now(),
		lastUsed:  time.Now(),
		active:    1,
		ctx:       connCtx,
		cancel:    connCancel,
	}

	// 注册连接
	cm.connections.Store(connID, conn)
	atomic.AddInt64(&cm.stats.TotalConnections, 1)
	atomic.AddInt64(&cm.stats.CreatedCount, 1)

	cm.logger.Debug("创建连接", "id", connID, "target", config.Target,
		"total", atomic.LoadInt64(&cm.stats.TotalConnections))

	return conn, nil
}

// GetConnection 获取连接
func (cm *DefaultConnectionManager) GetConnection(id string) (Connection, bool) {
	value, exists := cm.connections.Load(id)
	if !exists {
		return nil, false
	}

	conn := value.(*ManagedConnection)
	return conn, true
}

// GetConnectionByTarget 根据目标地址获取活跃连接
func (cm *DefaultConnectionManager) GetConnectionByTarget(target string) (Connection, bool) {
	var foundConn *ManagedConnection

	cm.connections.Range(func(key, value interface{}) bool {
		conn := value.(*ManagedConnection)
		if conn.target == target && conn.IsActive() {
			foundConn = conn
			return false // 停止遍历
		}
		return true // 继续遍历
	})

	if foundConn != nil {
		return foundConn, true
	}
	return nil, false
}

// CloseConnection 关闭连接
func (cm *DefaultConnectionManager) CloseConnection(id string) error {
	value, exists := cm.connections.LoadAndDelete(id)
	if !exists {
		return fmt.Errorf("连接不存在: %s", id)
	}

	conn := value.(*ManagedConnection)

	// 关闭连接
	if err := conn.Close(); err != nil {
		cm.logger.Warn("关闭连接失败", "id", id, "error", err.Error())
	}

	atomic.AddInt64(&cm.stats.TotalConnections, -1)
	atomic.AddInt64(&cm.stats.ClosedCount, 1)

	cm.logger.Debug("关闭连接", "id", id,
		"total", atomic.LoadInt64(&cm.stats.TotalConnections))

	return nil
}

// GetPoolStats 获取连接池统计
func (cm *DefaultConnectionManager) GetPoolStats() PoolStats {
	stats := cm.stats

	// 计算活跃和空闲连接数
	var activeCount, idleCount int
	now := time.Now()

	cm.connections.Range(func(key, value interface{}) bool {
		conn := value.(*ManagedConnection)
		if conn.IsActive() {
			activeCount++
		} else if now.Sub(conn.LastUsed()) < cm.maxIdleTime {
			idleCount++
		}
		return true
	})

	stats.ActiveConnections = activeCount
	stats.IdleConnections = idleCount
	stats.TotalConnections = int64(activeCount + idleCount)

	return stats
}

// GetConnectionStats 获取所有连接的详细统计
func (cm *DefaultConnectionManager) GetConnectionStats() []ConnectionStat {
	var stats []ConnectionStat

	cm.connections.Range(func(key, value interface{}) bool {
		conn := value.(*ManagedConnection)
		conn.mu.RLock()
		stat := ConnectionStat{
			ID:           conn.id,
			RemoteAddr:   conn.target,
			CreatedAt:    conn.createdAt,
			LastUsed:     conn.lastUsed,
			RequestCount: atomic.LoadInt64(&conn.requestCount),
			IsActive:     conn.IsActive(),
		}
		conn.mu.RUnlock()
		stats = append(stats, stat)
		return true
	})

	return stats
}

// ForceCleanupAll 强制清理所有连接
func (cm *DefaultConnectionManager) ForceCleanupAll() error {
	var allIDs []string

	// 收集所有连接ID
	cm.connections.Range(func(key, value interface{}) bool {
		allIDs = append(allIDs, key.(string))
		return true
	})

	// 关闭所有连接
	for _, id := range allIDs {
		if err := cm.CloseConnection(id); err != nil {
			cm.logger.Warn("强制关闭连接失败", "id", id, "error", err.Error())
		}
	}

	cm.logger.Info("强制清理所有连接完成", "closed", len(allIDs))
	return nil
}

// CleanupIdleConnections 清理空闲连接
func (cm *DefaultConnectionManager) CleanupIdleConnections() int {
	now := time.Now()
	var toRemove []string
	cleanupCount := 0

	// 查找需要清理的连接
	cm.connections.Range(func(key, value interface{}) bool {
		id := key.(string)
		conn := value.(*ManagedConnection)

		// 检查是否应该清理
		shouldCleanup := false

		// 检查是否不活跃且超过空闲时间
		if !conn.IsActive() && now.Sub(conn.LastUsed()) > cm.maxIdleTime {
			shouldCleanup = true
		}

		// 检查context是否已取消
		select {
		case <-conn.ctx.Done():
			shouldCleanup = true
		default:
		}

		if shouldCleanup {
			toRemove = append(toRemove, id)
			cleanupCount++
		}

		return true
	})

	// 清理标记的连接
	for _, id := range toRemove {
		if err := cm.CloseConnection(id); err != nil {
			cm.logger.Warn("清理连接失败", "id", id, "error", err.Error())
		}
	}

	if cleanupCount > 0 {
		cm.logger.Debug("清理空闲连接", "cleaned", cleanupCount)
	}

	return cleanupCount
}

// Start 启动连接管理器
func (cm *DefaultConnectionManager) Start(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&cm.running, 0, 1) {
		return fmt.Errorf("连接管理器已在运行")
	}

	cm.ctx, cm.cancel = context.WithCancel(ctx)

	// 启动清理goroutine
	cm.wg.Add(1)
	go cm.cleanupLoop()

	cm.logger.Info("连接管理器已启动", "max_connections", cm.maxConnections)
	return nil
}

// Stop 停止连接管理器
func (cm *DefaultConnectionManager) Stop() error {
	if !atomic.CompareAndSwapInt32(&cm.running, 1, 0) {
		return fmt.Errorf("连接管理器未运行")
	}

	// 取消context
	if cm.cancel != nil {
		cm.cancel()
	}

	// 等待goroutine结束
	cm.wg.Wait()

	// 关闭所有连接
	var allIDs []string
	cm.connections.Range(func(key, value interface{}) bool {
		allIDs = append(allIDs, key.(string))
		return true
	})

	for _, id := range allIDs {
		if err := cm.CloseConnection(id); err != nil {
			cm.logger.Warn("关闭连接失败", "id", id, "error", err.Error())
		}
	}

	cm.logger.Info("连接管理器已停止", "closed_connections", len(allIDs))
	return nil
}

// cleanupLoop 清理循环
func (cm *DefaultConnectionManager) cleanupLoop() {
	defer cm.wg.Done()

	ticker := time.NewTicker(cm.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cm.CleanupIdleConnections()
		case <-cm.ctx.Done():
			cm.logger.Debug("连接清理循环退出")
			return
		}
	}
}

// ManagedConnection 方法实现

// ID 返回连接ID
func (mc *ManagedConnection) ID() string {
	return mc.id
}

// Type 返回资源类型
func (mc *ManagedConnection) Type() string {
	return "http_connection"
}

// GetID 返回连接ID
func (mc *ManagedConnection) GetID() string {
	return mc.id
}

// IsActive 检查连接是否活跃
func (mc *ManagedConnection) IsActive() bool {
	return atomic.LoadInt32(&mc.active) == 1
}

// LastUsed 返回最后使用时间
func (mc *ManagedConnection) LastUsed() time.Time {
	mc.mu.RLock()
	defer mc.mu.RUnlock()
	return mc.lastUsed
}

// Close 关闭连接
func (mc *ManagedConnection) Close() error {
	if !atomic.CompareAndSwapInt32(&mc.active, 1, 0) {
		return fmt.Errorf("连接已关闭")
	}

	// 取消context
	if mc.cancel != nil {
		mc.cancel()
	}

	// 关闭传输
	if mc.transport != nil {
		mc.transport.CloseIdleConnections()
	}

	return nil
}

// Stats 返回连接统计
func (mc *ManagedConnection) Stats() map[string]interface{} {
	mc.mu.RLock()
	defer mc.mu.RUnlock()

	return map[string]interface{}{
		"id":            mc.id,
		"target":        mc.target,
		"created_at":    mc.createdAt,
		"last_used":     mc.lastUsed,
		"request_count": atomic.LoadInt64(&mc.requestCount),
		"active":        mc.IsActive(),
	}
}

// Do 执行HTTP请求
func (mc *ManagedConnection) Do(req *http.Request) (*http.Response, error) {
	if !mc.IsActive() {
		return nil, fmt.Errorf("连接已关闭")
	}

	// 更新最后使用时间
	mc.mu.Lock()
	mc.lastUsed = time.Now()
	mc.mu.Unlock()

	// 增加请求计数
	atomic.AddInt64(&mc.requestCount, 1)

	// 执行请求
	return mc.client.Do(req)
}

// RemoteAddr 返回远程地址
func (mc *ManagedConnection) RemoteAddr() string {
	return mc.target
}

// LocalAddr 返回本地地址
func (mc *ManagedConnection) LocalAddr() string {
	return "local"
}

// CreatedAt 返回创建时间
func (mc *ManagedConnection) CreatedAt() time.Time {
	return mc.createdAt
}
