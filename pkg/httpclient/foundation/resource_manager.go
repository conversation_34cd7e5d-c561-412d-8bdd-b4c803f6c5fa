// Package foundation 资源管理器实现
package foundation

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"go-monitor/pkg/logging"
)

// DefaultResourceManager 默认资源管理器实现
type DefaultResourceManager struct {
	// 资源存储
	resources sync.Map // map[string]Resource

	// 统计信息
	totalResources  int64
	activeResources int64

	// 配置
	cleanupInterval time.Duration
	maxIdleTime     time.Duration

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	logger logging.Logger

	// 状态
	running int32
}

// NewResourceManager 创建新的资源管理器
func NewResourceManager(cleanupInterval, maxIdleTime time.Duration) *DefaultResourceManager {
	return &DefaultResourceManager{
		cleanupInterval: cleanupInterval,
		maxIdleTime:     maxIdleTime,
		logger:          logging.GetLogger("httpclient.resource_manager"),
	}
}

// RegisterResource 注册资源
func (rm *DefaultResourceManager) RegisterResource(id string, resource Resource) error {
	if id == "" {
		return fmt.Errorf("资源ID不能为空")
	}

	if resource == nil {
		return fmt.Errorf("资源不能为nil")
	}

	// 检查是否已存在
	if _, exists := rm.resources.Load(id); exists {
		return fmt.Errorf("资源ID已存在: %s", id)
	}

	// 注册资源
	rm.resources.Store(id, resource)
	atomic.AddInt64(&rm.totalResources, 1)
	atomic.AddInt64(&rm.activeResources, 1)

	rm.logger.Debug("注册资源", "id", id, "type", resource.Type(),
		"total", atomic.LoadInt64(&rm.totalResources))

	return nil
}

// UnregisterResource 注销资源
func (rm *DefaultResourceManager) UnregisterResource(id string) error {
	value, exists := rm.resources.LoadAndDelete(id)
	if !exists {
		return fmt.Errorf("资源不存在: %s", id)
	}

	resource := value.(Resource)

	// 关闭资源
	if err := resource.Close(); err != nil {
		// 如果资源已经关闭，使用Debug级别，否则使用Warn级别
		if err.Error() == "连接已关闭" {
			rm.logger.Debug("资源已关闭", "id", id, "type", resource.Type())
		} else {
			rm.logger.Warn("关闭资源失败", "id", id, "error", err.Error())
		}
	}

	atomic.AddInt64(&rm.activeResources, -1)

	rm.logger.Debug("注销资源", "id", id, "type", resource.Type(),
		"active", atomic.LoadInt64(&rm.activeResources))

	return nil
}

// GetResource 获取资源
func (rm *DefaultResourceManager) GetResource(id string) (Resource, bool) {
	value, exists := rm.resources.Load(id)
	if !exists {
		return nil, false
	}

	resource := value.(Resource)
	return resource, true
}

// ListResources 列出所有资源ID
func (rm *DefaultResourceManager) ListResources() []string {
	var ids []string

	rm.resources.Range(func(key, value interface{}) bool {
		ids = append(ids, key.(string))
		return true
	})

	return ids
}

// Start 启动资源管理器
func (rm *DefaultResourceManager) Start(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&rm.running, 0, 1) {
		return fmt.Errorf("资源管理器已在运行")
	}

	rm.ctx, rm.cancel = context.WithCancel(ctx)

	// 启动清理goroutine
	rm.wg.Add(1)
	go rm.cleanupLoop()

	rm.logger.Info("资源管理器已启动", "cleanup_interval", rm.cleanupInterval)
	return nil
}

// Stop 停止资源管理器
func (rm *DefaultResourceManager) Stop() error {
	if !atomic.CompareAndSwapInt32(&rm.running, 1, 0) {
		return fmt.Errorf("资源管理器未运行")
	}

	// 取消context
	if rm.cancel != nil {
		rm.cancel()
	}

	// 等待goroutine结束
	rm.wg.Wait()

	// 清理所有资源
	if err := rm.ForceCleanup(); err != nil {
		rm.logger.Warn("强制清理失败", "error", err.Error())
	}

	rm.logger.Info("资源管理器已停止")
	return nil
}

// Cleanup 清理过期资源
func (rm *DefaultResourceManager) Cleanup() error {
	now := time.Now()
	var toRemove []string
	cleanupCount := 0

	// 查找需要清理的资源
	rm.resources.Range(func(key, value interface{}) bool {
		id := key.(string)
		resource := value.(Resource)

		// 检查资源是否应该被清理
		shouldCleanup := false

		// 检查是否不活跃
		if !resource.IsActive() {
			shouldCleanup = true
		}

		// 检查是否超过最大空闲时间
		if now.Sub(resource.LastUsed()) > rm.maxIdleTime {
			shouldCleanup = true
		}

		if shouldCleanup {
			toRemove = append(toRemove, id)
			cleanupCount++
		}

		return true
	})

	// 清理标记的资源
	for _, id := range toRemove {
		if err := rm.UnregisterResource(id); err != nil {
			rm.logger.Warn("清理资源失败", "id", id, "error", err.Error())
		}
	}

	if cleanupCount > 0 {
		rm.logger.Debug("清理完成", "cleaned", cleanupCount,
			"active", atomic.LoadInt64(&rm.activeResources))
	}

	return nil
}

// ForceCleanup 强制清理所有资源
func (rm *DefaultResourceManager) ForceCleanup() error {
	var allIDs []string

	// 收集所有资源ID
	rm.resources.Range(func(key, value interface{}) bool {
		allIDs = append(allIDs, key.(string))
		return true
	})

	// 清理所有资源
	for _, id := range allIDs {
		if err := rm.UnregisterResource(id); err != nil {
			rm.logger.Warn("强制清理资源失败", "id", id, "error", err.Error())
		}
	}

	rm.logger.Info("强制清理完成", "cleaned", len(allIDs))
	return nil
}

// cleanupLoop 清理循环
func (rm *DefaultResourceManager) cleanupLoop() {
	defer rm.wg.Done()

	ticker := time.NewTicker(rm.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := rm.Cleanup(); err != nil {
				rm.logger.Error("定期清理失败", "error", err.Error())
			}
		case <-rm.ctx.Done():
			rm.logger.Debug("清理循环退出")
			return
		}
	}
}

// GetStats 获取统计信息
func (rm *DefaultResourceManager) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_resources":  atomic.LoadInt64(&rm.totalResources),
		"active_resources": atomic.LoadInt64(&rm.activeResources),
		"cleanup_interval": rm.cleanupInterval.String(),
		"max_idle_time":    rm.maxIdleTime.String(),
		"running":          atomic.LoadInt32(&rm.running) == 1,
	}
}
