// Package errors 定义HTTP客户端错误类型
package errors

import (
	"fmt"
	"net"
	"net/url"
	"strings"
	"time"
)

// 错误类型常量
const (
	ErrTypeTimeout     = "timeout"
	ErrTypeConnection  = "connection"
	ErrTypeRequest     = "request"
	ErrTypeResponse    = "response"
	ErrTypeConfig      = "config"
	ErrTypeResource    = "resource"
	ErrTypeFingerprint = "fingerprint"
)

// HTTPClientError HTTP客户端错误
type HTTPClientError struct {
	Type    string
	Message string
	Cause   error
	Details map[string]interface{}
}

// Error 实现error接口
func (e *HTTPClientError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap 支持错误链
func (e *HTTPClientError) Unwrap() error {
	return e.Cause
}

// Is 支持错误比较
func (e *HTTPClientError) Is(target error) bool {
	if t, ok := target.(*HTTPClientError); ok {
		return e.Type == t.Type
	}
	return false
}

// NewError 创建新错误
func NewError(errType, message string, cause error) *HTTPClientError {
	return &HTTPClientError{
		Type:    errType,
		Message: message,
		Cause:   cause,
		Details: make(map[string]interface{}),
	}
}

// WithDetail 添加错误详情
func (e *HTTPClientError) WithDetail(key string, value interface{}) *HTTPClientError {
	if e.Details == nil {
		e.Details = make(map[string]interface{})
	}
	e.Details[key] = value
	return e
}

// 预定义错误

// NewTimeoutError 创建超时错误
func NewTimeoutError(message string, timeout time.Duration) *HTTPClientError {
	return NewError(ErrTypeTimeout, message, nil).
		WithDetail("timeout", timeout)
}

// NewConnectionError 创建连接错误
func NewConnectionError(message string, cause error) *HTTPClientError {
	return NewError(ErrTypeConnection, message, cause)
}

// NewRequestError 创建请求错误
func NewRequestError(message string, cause error) *HTTPClientError {
	return NewError(ErrTypeRequest, message, cause)
}

// NewResponseError 创建响应错误
func NewResponseError(message string, statusCode int) *HTTPClientError {
	return NewError(ErrTypeResponse, message, nil).
		WithDetail("status_code", statusCode)
}

// NewConfigError 创建配置错误
func NewConfigError(message string) *HTTPClientError {
	return NewError(ErrTypeConfig, message, nil)
}

// NewResourceError 创建资源错误
func NewResourceError(message string, resourceID string) *HTTPClientError {
	return NewError(ErrTypeResource, message, nil).
		WithDetail("resource_id", resourceID)
}

// NewFingerprintError 创建指纹错误
func NewFingerprintError(message string, cause error) *HTTPClientError {
	return NewError(ErrTypeFingerprint, message, cause)
}

// 错误检查函数

// IsTimeoutError 检查是否为超时错误
func IsTimeoutError(err error) bool {
	if e, ok := err.(*HTTPClientError); ok {
		return e.Type == ErrTypeTimeout
	}

	// 检查标准库超时错误
	if netErr, ok := err.(net.Error); ok {
		return netErr.Timeout()
	}

	return false
}

// IsConnectionError 检查是否为连接错误
func IsConnectionError(err error) bool {
	if e, ok := err.(*HTTPClientError); ok {
		return e.Type == ErrTypeConnection
	}

	// 检查网络错误
	if _, ok := err.(*net.OpError); ok {
		return true
	}

	if _, ok := err.(*url.Error); ok {
		return true
	}

	return false
}

// IsRequestError 检查是否为请求错误
func IsRequestError(err error) bool {
	if e, ok := err.(*HTTPClientError); ok {
		return e.Type == ErrTypeRequest
	}
	return false
}

// IsResponseError 检查是否为响应错误
func IsResponseError(err error) bool {
	if e, ok := err.(*HTTPClientError); ok {
		return e.Type == ErrTypeResponse
	}
	return false
}

// IsConfigError 检查是否为配置错误
func IsConfigError(err error) bool {
	if e, ok := err.(*HTTPClientError); ok {
		return e.Type == ErrTypeConfig
	}
	return false
}

// IsResourceError 检查是否为资源错误
func IsResourceError(err error) bool {
	if e, ok := err.(*HTTPClientError); ok {
		return e.Type == ErrTypeResource
	}
	return false
}

// IsFingerprintError 检查是否为指纹错误
func IsFingerprintError(err error) bool {
	if e, ok := err.(*HTTPClientError); ok {
		return e.Type == ErrTypeFingerprint
	}
	return false
}

// IsRetryableError 检查错误是否可重试
func IsRetryableError(err error) bool {
	// 超时错误可重试
	if IsTimeoutError(err) {
		return true
	}

	// 某些连接错误可重试
	if IsConnectionError(err) {
		// 检查具体的连接错误类型
		if netErr, ok := err.(*net.OpError); ok {
			// 连接被拒绝可重试
			if netErr.Op == "dial" {
				return true
			}
		}
		return true
	}

	// 某些响应错误可重试
	if e, ok := err.(*HTTPClientError); ok && e.Type == ErrTypeResponse {
		if statusCode, exists := e.Details["status_code"]; exists {
			if code, ok := statusCode.(int); ok {
				// 5xx错误可重试
				return code >= 500 && code < 600
			}
		}
	}

	return false
}

// WrapError 包装标准错误
func WrapError(err error) error {
	if err == nil {
		return nil
	}

	// 如果已经是HTTPClientError，直接返回
	if _, ok := err.(*HTTPClientError); ok {
		return err
	}

	// 根据错误类型进行包装
	if netErr, ok := err.(net.Error); ok {
		if netErr.Timeout() {
			return NewTimeoutError("网络超时", 0).WithDetail("original_error", err.Error())
		}
		return NewConnectionError("网络连接错误", err)
	}

	if _, ok := err.(*net.OpError); ok {
		return NewConnectionError("网络操作错误", err)
	}

	if _, ok := err.(*url.Error); ok {
		return NewRequestError("URL错误", err)
	}

	// 默认包装为请求错误
	return NewRequestError("请求处理错误", err)
}

// ErrorChain 错误链支持
type ErrorChain struct {
	errors []error
}

// NewErrorChain 创建错误链
func NewErrorChain() *ErrorChain {
	return &ErrorChain{
		errors: make([]error, 0),
	}
}

// Add 添加错误到链中
func (ec *ErrorChain) Add(err error) *ErrorChain {
	if err != nil {
		ec.errors = append(ec.errors, err)
	}
	return ec
}

// HasErrors 检查是否有错误
func (ec *ErrorChain) HasErrors() bool {
	return len(ec.errors) > 0
}

// Error 实现error接口
func (ec *ErrorChain) Error() string {
	if len(ec.errors) == 0 {
		return ""
	}

	if len(ec.errors) == 1 {
		return ec.errors[0].Error()
	}

	var messages []string
	for i, err := range ec.errors {
		messages = append(messages, fmt.Sprintf("%d: %s", i+1, err.Error()))
	}

	return fmt.Sprintf("多个错误: [%s]", strings.Join(messages, "; "))
}

// Errors 获取所有错误
func (ec *ErrorChain) Errors() []error {
	return ec.errors
}

// First 获取第一个错误
func (ec *ErrorChain) First() error {
	if len(ec.errors) == 0 {
		return nil
	}
	return ec.errors[0]
}

// Last 获取最后一个错误
func (ec *ErrorChain) Last() error {
	if len(ec.errors) == 0 {
		return nil
	}
	return ec.errors[len(ec.errors)-1]
}

// ToError 转换为单个错误
func (ec *ErrorChain) ToError() error {
	if !ec.HasErrors() {
		return nil
	}

	if len(ec.errors) == 1 {
		return ec.errors[0]
	}

	return ec
}
