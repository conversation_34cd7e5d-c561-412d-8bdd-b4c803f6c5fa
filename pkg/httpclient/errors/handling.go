// Package errors 错误处理逻辑
package errors

import (
	"context"
	"fmt"
	"net"
	"syscall"
	"time"
)

// ErrorHandler 错误处理器接口
type ErrorHandler interface {
	HandleError(err error) error
	ShouldRetry(err error) bool
	GetRetryDelay(err error, attempt int) time.Duration
}

// DefaultErrorHandler 默认错误处理器
type DefaultErrorHandler struct {
	maxRetries    int
	baseDelay     time.Duration
	maxDelay      time.Duration
	backoffFactor float64
}

// NewDefaultErrorHandler 创建默认错误处理器
func NewDefaultErrorHandler() *DefaultErrorHandler {
	return &DefaultErrorHandler{
		maxRetries:    3,
		baseDelay:     1 * time.Second,
		maxDelay:      30 * time.Second,
		backoffFactor: 2.0,
	}
}

// HandleError 处理错误
func (deh *DefaultErrorHandler) HandleError(err error) error {
	if err == nil {
		return nil
	}

	// 包装标准错误
	return WrapError(err)
}

// ShouldRetry 判断是否应该重试
func (deh *DefaultErrorHandler) ShouldRetry(err error) bool {
	if err == nil {
		return false
	}

	// 使用全局重试判断函数
	return IsRetryableError(err)
}

// GetRetryDelay 获取重试延迟
func (deh *DefaultErrorHandler) GetRetryDelay(err error, attempt int) time.Duration {
	if attempt <= 0 {
		return deh.baseDelay
	}

	// 指数退避
	delay := float64(deh.baseDelay)
	for i := 0; i < attempt; i++ {
		delay *= deh.backoffFactor
	}

	if delay > float64(deh.maxDelay) {
		delay = float64(deh.maxDelay)
	}

	return time.Duration(delay)
}

// ErrorClassifier 错误分类器
type ErrorClassifier struct{}

// NewErrorClassifier 创建错误分类器
func NewErrorClassifier() *ErrorClassifier {
	return &ErrorClassifier{}
}

// ClassifyError 分类错误
func (ec *ErrorClassifier) ClassifyError(err error) ErrorClassification {
	if err == nil {
		return ErrorClassification{
			Type:       "none",
			Severity:   SeverityInfo,
			Retryable:  false,
			Temporary:  false,
			UserFacing: false,
		}
	}

	// 检查HTTP客户端错误
	if httpErr, ok := err.(*HTTPClientError); ok {
		return ec.classifyHTTPClientError(httpErr)
	}

	// 检查网络错误
	if netErr, ok := err.(net.Error); ok {
		return ec.classifyNetworkError(netErr)
	}

	// 检查系统调用错误
	if syscallErr, ok := err.(*net.OpError); ok {
		return ec.classifySyscallError(syscallErr)
	}

	// 检查context错误
	if err == context.Canceled || err == context.DeadlineExceeded {
		return ec.classifyContextError(err)
	}

	// 默认分类
	return ErrorClassification{
		Type:        "unknown",
		Severity:    SeverityError,
		Retryable:   false,
		Temporary:   false,
		UserFacing:  true,
		Description: "未知错误",
	}
}

// classifyHTTPClientError 分类HTTP客户端错误
func (ec *ErrorClassifier) classifyHTTPClientError(err *HTTPClientError) ErrorClassification {
	classification := ErrorClassification{
		Type:       err.Type,
		UserFacing: true,
	}

	switch err.Type {
	case ErrTypeTimeout:
		classification.Severity = SeverityWarning
		classification.Retryable = true
		classification.Temporary = true
		classification.Description = "请求超时，可能是网络延迟或服务器响应慢"

	case ErrTypeConnection:
		classification.Severity = SeverityError
		classification.Retryable = true
		classification.Temporary = true
		classification.Description = "连接错误，可能是网络问题或服务器不可达"

	case ErrTypeRequest:
		classification.Severity = SeverityError
		classification.Retryable = false
		classification.Temporary = false
		classification.Description = "请求格式错误或参数无效"

	case ErrTypeResponse:
		classification.Severity = SeverityWarning
		classification.Retryable = true
		classification.Temporary = true
		classification.Description = "服务器响应错误"

	case ErrTypeConfig:
		classification.Severity = SeverityError
		classification.Retryable = false
		classification.Temporary = false
		classification.Description = "配置错误"

	case ErrTypeResource:
		classification.Severity = SeverityError
		classification.Retryable = true
		classification.Temporary = true
		classification.Description = "资源管理错误"

	case ErrTypeFingerprint:
		classification.Severity = SeverityWarning
		classification.Retryable = true
		classification.Temporary = true
		classification.Description = "指纹配置错误"

	default:
		classification.Severity = SeverityError
		classification.Retryable = false
		classification.Temporary = false
		classification.Description = "未知的HTTP客户端错误"
	}

	return classification
}

// classifyNetworkError 分类网络错误
func (ec *ErrorClassifier) classifyNetworkError(err net.Error) ErrorClassification {
	classification := ErrorClassification{
		Type:       ErrTypeConnection,
		UserFacing: true,
		Retryable:  true,
		Temporary:  true,
	}

	if err.Timeout() {
		classification.Type = ErrTypeTimeout
		classification.Severity = SeverityWarning
		classification.Description = "网络超时"
	} else {
		classification.Severity = SeverityError
		classification.Description = "网络连接错误"
	}

	return classification
}

// classifySyscallError 分类系统调用错误
func (ec *ErrorClassifier) classifySyscallError(err *net.OpError) ErrorClassification {
	classification := ErrorClassification{
		Type:       ErrTypeConnection,
		Severity:   SeverityError,
		UserFacing: true,
		Temporary:  true,
	}

	if err.Err != nil {
		if syscallErr, ok := err.Err.(*syscall.Errno); ok {
			switch *syscallErr {
			case syscall.ECONNREFUSED:
				classification.Retryable = true
				classification.Description = "连接被拒绝，服务器可能未启动"
			case syscall.ETIMEDOUT:
				classification.Type = ErrTypeTimeout
				classification.Severity = SeverityWarning
				classification.Retryable = true
				classification.Description = "连接超时"
			case syscall.EHOSTUNREACH:
				classification.Retryable = true
				classification.Description = "主机不可达"
			default:
				classification.Retryable = false
				classification.Description = fmt.Sprintf("系统调用错误: %v", syscallErr)
			}
		}
	}

	return classification
}

// classifyContextError 分类context错误
func (ec *ErrorClassifier) classifyContextError(err error) ErrorClassification {
	classification := ErrorClassification{
		UserFacing: true,
		Temporary:  false,
		Retryable:  false,
	}

	if err == context.Canceled {
		classification.Type = "canceled"
		classification.Severity = SeverityInfo
		classification.Description = "操作被取消"
	} else if err == context.DeadlineExceeded {
		classification.Type = ErrTypeTimeout
		classification.Severity = SeverityWarning
		classification.Description = "操作超时"
	}

	return classification
}

// ErrorClassification 错误分类结果
type ErrorClassification struct {
	Type        string        // 错误类型
	Severity    ErrorSeverity // 严重程度
	Retryable   bool          // 是否可重试
	Temporary   bool          // 是否是临时错误
	UserFacing  bool          // 是否面向用户
	Description string        // 错误描述
}

// ErrorSeverity 错误严重程度
type ErrorSeverity int

const (
	SeverityInfo ErrorSeverity = iota
	SeverityWarning
	SeverityError
	SeverityCritical
)

// String 返回严重程度的字符串表示
func (es ErrorSeverity) String() string {
	switch es {
	case SeverityInfo:
		return "info"
	case SeverityWarning:
		return "warning"
	case SeverityError:
		return "error"
	case SeverityCritical:
		return "critical"
	default:
		return "unknown"
	}
}

// ErrorReporter 错误报告器
type ErrorReporter struct {
	classifier *ErrorClassifier
	handlers   []ErrorReportHandler
}

// ErrorReportHandler 错误报告处理器
type ErrorReportHandler func(classification ErrorClassification, err error)

// NewErrorReporter 创建错误报告器
func NewErrorReporter() *ErrorReporter {
	return &ErrorReporter{
		classifier: NewErrorClassifier(),
		handlers:   make([]ErrorReportHandler, 0),
	}
}

// AddHandler 添加错误报告处理器
func (er *ErrorReporter) AddHandler(handler ErrorReportHandler) {
	er.handlers = append(er.handlers, handler)
}

// ReportError 报告错误
func (er *ErrorReporter) ReportError(err error) {
	if err == nil {
		return
	}

	classification := er.classifier.ClassifyError(err)

	for _, handler := range er.handlers {
		handler(classification, err)
	}
}

// 全局错误处理器实例
var (
	globalErrorHandler  *DefaultErrorHandler
	globalErrorReporter *ErrorReporter
)

// GetGlobalErrorHandler 获取全局错误处理器
func GetGlobalErrorHandler() *DefaultErrorHandler {
	if globalErrorHandler == nil {
		globalErrorHandler = NewDefaultErrorHandler()
	}
	return globalErrorHandler
}

// GetGlobalErrorReporter 获取全局错误报告器
func GetGlobalErrorReporter() *ErrorReporter {
	if globalErrorReporter == nil {
		globalErrorReporter = NewErrorReporter()
	}
	return globalErrorReporter
}

// HandleGlobalError 处理全局错误
func HandleGlobalError(err error) error {
	handler := GetGlobalErrorHandler()
	reporter := GetGlobalErrorReporter()

	// 报告错误
	reporter.ReportError(err)

	// 处理错误
	return handler.HandleError(err)
}
