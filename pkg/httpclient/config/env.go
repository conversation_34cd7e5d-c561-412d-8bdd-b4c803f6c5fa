// Package config 环境变量配置支持
package config

import (
	"os"
	"strconv"
	"strings"
	"time"

	"go-monitor/pkg/httpclient/foundation"
)

// getEnvString 获取字符串环境变量
func getEnvString(key string) string {
	return os.Getenv(key)
}

// getEnvInt 获取整数环境变量
func getEnvInt(key string) int {
	value := os.Getenv(key)
	if value == "" {
		return 0
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return 0
	}

	return intValue
}

// getEnvFloat 获取浮点数环境变量
func getEnvFloat(key string) float64 {
	value := os.Getenv(key)
	if value == "" {
		return 0.0
	}

	floatValue, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return 0.0
	}

	return floatValue
}

// getEnvBool 获取布尔环境变量
func getEnvBool(key string, defaultValue bool) bool {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	// 支持多种布尔值表示
	value = strings.ToLower(value)
	switch value {
	case "true", "1", "yes", "on", "enabled":
		return true
	case "false", "0", "no", "off", "disabled":
		return false
	default:
		return defaultValue
	}
}

// getEnvDuration 获取时间间隔环境变量
func getEnvDuration(key string) time.Duration {
	value := os.Getenv(key)
	if value == "" {
		return 0
	}

	duration, err := time.ParseDuration(value)
	if err != nil {
		return 0
	}

	return duration
}

// getEnvStringSlice 获取字符串切片环境变量（逗号分隔）
func getEnvStringSlice(key string) []string {
	value := os.Getenv(key)
	if value == "" {
		return nil
	}

	// 按逗号分割并去除空白
	parts := strings.Split(value, ",")
	var result []string
	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	return result
}

// setEnvDefaults 设置环境变量默认值
func setEnvDefaults() {
	defaults := map[string]string{
		"HTTPCLIENT_TIMEOUT":            "30s",
		"HTTPCLIENT_MAX_IDLE_CONNS":     "100",
		"HTTPCLIENT_MAX_CONNS_PER_HOST": "10",
		"HTTPCLIENT_USER_AGENT":         "Go-Monitor-HTTPClient/1.0",
		"HTTPCLIENT_ENABLE_MONITORING":  "false",
		"HTTPCLIENT_ENABLE_FINGERPRINT": "false",
		"HTTPCLIENT_ENABLE_RETRY":       "false",
		"HTTPCLIENT_MAX_RETRIES":        "3",
		"HTTPCLIENT_RETRY_DELAY":        "1s",
		"HTTPCLIENT_RETRY_BACKOFF":      "2.0",
	}

	for key, defaultValue := range defaults {
		if os.Getenv(key) == "" {
			os.Setenv(key, defaultValue)
		}
	}
}

// LoadFromEnvWithDefaults 从环境变量加载配置（带默认值）
func LoadFromEnvWithDefaults() *foundation.Config {
	// 设置默认值
	setEnvDefaults()

	// 加载配置
	return LoadFromEnv()
}

// GetEnvConfigHelp 获取环境变量配置帮助信息
func GetEnvConfigHelp() map[string]string {
	return map[string]string{
		"HTTPCLIENT_TIMEOUT":            "HTTP请求超时时间 (例如: 30s, 1m)",
		"HTTPCLIENT_MAX_IDLE_CONNS":     "最大空闲连接数 (例如: 100)",
		"HTTPCLIENT_MAX_CONNS_PER_HOST": "每个主机的最大连接数 (例如: 10)",
		"HTTPCLIENT_USER_AGENT":         "用户代理字符串",
		"HTTPCLIENT_ENABLE_MONITORING":  "启用监控功能 (true/false)",
		"HTTPCLIENT_ENABLE_FINGERPRINT": "启用指纹功能 (true/false)",
		"HTTPCLIENT_ENABLE_RETRY":       "启用重试功能 (true/false)",
		"HTTPCLIENT_MAX_RETRIES":        "最大重试次数 (例如: 3)",
		"HTTPCLIENT_RETRY_DELAY":        "重试延迟时间 (例如: 1s, 500ms)",
		"HTTPCLIENT_RETRY_BACKOFF":      "重试退避因子 (例如: 2.0)",
	}
}

// ValidateEnvConfig 验证环境变量配置
func ValidateEnvConfig() []string {
	var errors []string

	// 验证超时时间
	if timeout := getEnvDuration("HTTPCLIENT_TIMEOUT"); timeout <= 0 {
		if os.Getenv("HTTPCLIENT_TIMEOUT") != "" {
			errors = append(errors, "HTTPCLIENT_TIMEOUT 必须是有效的时间间隔")
		}
	}

	// 验证连接数
	if maxIdleConns := getEnvInt("HTTPCLIENT_MAX_IDLE_CONNS"); maxIdleConns < 0 {
		if os.Getenv("HTTPCLIENT_MAX_IDLE_CONNS") != "" {
			errors = append(errors, "HTTPCLIENT_MAX_IDLE_CONNS 必须是非负整数")
		}
	}

	if maxConnsPerHost := getEnvInt("HTTPCLIENT_MAX_CONNS_PER_HOST"); maxConnsPerHost < 0 {
		if os.Getenv("HTTPCLIENT_MAX_CONNS_PER_HOST") != "" {
			errors = append(errors, "HTTPCLIENT_MAX_CONNS_PER_HOST 必须是非负整数")
		}
	}

	// 验证重试配置
	if maxRetries := getEnvInt("HTTPCLIENT_MAX_RETRIES"); maxRetries < 0 {
		if os.Getenv("HTTPCLIENT_MAX_RETRIES") != "" {
			errors = append(errors, "HTTPCLIENT_MAX_RETRIES 必须是非负整数")
		}
	}

	if retryDelay := getEnvDuration("HTTPCLIENT_RETRY_DELAY"); retryDelay <= 0 {
		if os.Getenv("HTTPCLIENT_RETRY_DELAY") != "" {
			errors = append(errors, "HTTPCLIENT_RETRY_DELAY 必须是有效的时间间隔")
		}
	}

	if retryBackoff := getEnvFloat("HTTPCLIENT_RETRY_BACKOFF"); retryBackoff <= 1.0 {
		if os.Getenv("HTTPCLIENT_RETRY_BACKOFF") != "" {
			errors = append(errors, "HTTPCLIENT_RETRY_BACKOFF 必须大于1.0")
		}
	}

	return errors
}
