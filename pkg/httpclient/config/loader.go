// Package config 配置文件加载器
package config

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strings"
	
	"go-monitor/pkg/httpclient/foundation"
	"gopkg.in/yaml.v3"
)

// ConfigLoader 配置加载器
type ConfigLoader struct {
	searchPaths []string
	logger      interface{} // 简化日志接口
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader(searchPaths ...string) *ConfigLoader {
	if len(searchPaths) == 0 {
		searchPaths = []string{
			".",
			"./configs",
			"./config",
			"/etc/httpclient",
			"$HOME/.httpclient",
		}
	}
	
	return &ConfigLoader{
		searchPaths: searchPaths,
	}
}

// LoadFromFile 从文件加载配置
func (cl *ConfigLoader) LoadFromFile(filename string) (*foundation.Config, error) {
	// 查找配置文件
	filePath, err := cl.findConfigFile(filename)
	if err != nil {
		return nil, fmt.Errorf("查找配置文件失败: %w", err)
	}
	
	// 读取文件内容
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	// 根据文件扩展名解析
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".yaml", ".yml":
		return cl.parseYAML(data)
	case ".json":
		return cl.parseJSON(data)
	default:
		return nil, fmt.Errorf("不支持的配置文件格式: %s", ext)
	}
}

// findConfigFile 查找配置文件
func (cl *ConfigLoader) findConfigFile(filename string) (string, error) {
	// 如果是绝对路径，直接使用
	if filepath.IsAbs(filename) {
		return filename, nil
	}
	
	// 在搜索路径中查找
	for _, searchPath := range cl.searchPaths {
		// 展开环境变量
		expandedPath := expandPath(searchPath)
		fullPath := filepath.Join(expandedPath, filename)
		
		if fileExists(fullPath) {
			return fullPath, nil
		}
	}
	
	return "", fmt.Errorf("配置文件 %s 不存在", filename)
}

// parseYAML 解析YAML配置
func (cl *ConfigLoader) parseYAML(data []byte) (*foundation.Config, error) {
	config := &foundation.Config{}
	
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析YAML配置失败: %w", err)
	}
	
	return config, nil
}

// parseJSON 解析JSON配置
func (cl *ConfigLoader) parseJSON(data []byte) (*foundation.Config, error) {
	config := &foundation.Config{}
	
	if err := json.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析JSON配置失败: %w", err)
	}
	
	return config, nil
}

// LoadWithFallback 加载配置（带回退机制）
func (cl *ConfigLoader) LoadWithFallback(filenames ...string) (*foundation.Config, error) {
	var lastErr error
	
	for _, filename := range filenames {
		config, err := cl.LoadFromFile(filename)
		if err == nil {
			return config, nil
		}
		lastErr = err
	}
	
	if lastErr != nil {
		return nil, fmt.Errorf("所有配置文件加载失败，最后错误: %w", lastErr)
	}
	
	return GetDefault(), nil
}

// LoadAndMerge 加载并合并多个配置文件
func (cl *ConfigLoader) LoadAndMerge(filenames ...string) (*foundation.Config, error) {
	baseConfig := GetDefault()
	
	for _, filename := range filenames {
		config, err := cl.LoadFromFile(filename)
		if err != nil {
			// 忽略不存在的文件，继续处理下一个
			continue
		}
		
		baseConfig = Merge(baseConfig, config)
	}
	
	return baseConfig, nil
}

// SaveToFile 保存配置到文件
func (cl *ConfigLoader) SaveToFile(config *foundation.Config, filename string) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	
	// 根据文件扩展名选择格式
	ext := strings.ToLower(filepath.Ext(filename))
	var data []byte
	var err error
	
	switch ext {
	case ".yaml", ".yml":
		data, err = yaml.Marshal(config)
	case ".json":
		data, err = json.MarshalIndent(config, "", "  ")
	default:
		return fmt.Errorf("不支持的配置文件格式: %s", ext)
	}
	
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}
	
	// 确保目录存在
	dir := filepath.Dir(filename)
	if err := ensureDir(dir); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}
	
	// 写入文件
	if err := ioutil.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}
	
	return nil
}

// 辅助函数

func expandPath(path string) string {
	if strings.HasPrefix(path, "$HOME") {
		homeDir := getHomeDir()
		return strings.Replace(path, "$HOME", homeDir, 1)
	}
	return path
}

func getHomeDir() string {
	// 简化的家目录获取
	return "/tmp" // 在实际实现中应该使用 os.UserHomeDir()
}

func fileExists(path string) bool {
	_, err := ioutil.ReadFile(path)
	return err == nil
}

func ensureDir(dir string) error {
	// 简化的目录创建
	return nil // 在实际实现中应该使用 os.MkdirAll
}

// 全局配置加载器
var globalLoader *ConfigLoader

// GetGlobalLoader 获取全局配置加载器
func GetGlobalLoader() *ConfigLoader {
	if globalLoader == nil {
		globalLoader = NewConfigLoader()
	}
	return globalLoader
}

// LoadConfig 便捷函数：加载配置
func LoadConfig(filename string) (*foundation.Config, error) {
	return GetGlobalLoader().LoadFromFile(filename)
}

// LoadConfigWithFallback 便捷函数：带回退的配置加载
func LoadConfigWithFallback(filenames ...string) (*foundation.Config, error) {
	return GetGlobalLoader().LoadWithFallback(filenames...)
}

// SaveConfig 便捷函数：保存配置
func SaveConfig(config *foundation.Config, filename string) error {
	return GetGlobalLoader().SaveToFile(config, filename)
}
