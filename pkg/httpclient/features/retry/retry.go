// Package retry 提供HTTP请求重试功能
package retry

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"
	
	"go-monitor/pkg/httpclient/errors"
	"go-monitor/pkg/httpclient/foundation"
	"go-monitor/pkg/logging"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries      int           // 最大重试次数
	InitialDelay    time.Duration // 初始延迟
	MaxDelay        time.Duration // 最大延迟
	BackoffFactor   float64       // 退避因子
	Jitter          bool          // 是否添加随机抖动
	RetryableErrors []string      // 可重试的错误类型
}

// RetryManager 重试管理器
type RetryManager struct {
	config RetryConfig
	logger logging.Logger
}

// NewRetryManager 创建重试管理器
func NewRetryManager(config RetryConfig) *RetryManager {
	if config.MaxRetries <= 0 {
		config.MaxRetries = 3
	}
	if config.InitialDelay <= 0 {
		config.InitialDelay = 1 * time.Second
	}
	if config.MaxDelay <= 0 {
		config.MaxDelay = 30 * time.Second
	}
	if config.BackoffFactor <= 1.0 {
		config.BackoffFactor = 2.0
	}
	
	return &RetryManager{
		config: config,
		logger: logging.GetLogger("httpclient.retry"),
	}
}

// ExecuteWithRetry 执行带重试的操作
func (rm *RetryManager) ExecuteWithRetry(ctx context.Context, operation RetryableOperation) (*foundation.Response, error) {
	var lastErr error
	
	for attempt := 0; attempt <= rm.config.MaxRetries; attempt++ {
		// 检查context是否已取消
		if ctx.Err() != nil {
			return nil, fmt.Errorf("操作被取消: %w", ctx.Err())
		}
		
		// 执行操作
		resp, err := operation(ctx, attempt)
		
		// 成功则返回
		if err == nil {
			if attempt > 0 {
				rm.logger.Info("重试成功", "attempt", attempt, "total_attempts", attempt+1)
			}
			return resp, nil
		}
		
		lastErr = err
		
		// 检查是否可重试
		if !rm.shouldRetry(err, attempt) {
			rm.logger.Debug("错误不可重试或达到最大重试次数", "error", err.Error(), "attempt", attempt)
			break
		}
		
		// 计算延迟时间
		delay := rm.calculateDelay(attempt)
		
		rm.logger.Warn("请求失败，准备重试", "error", err.Error(), "attempt", attempt+1, 
			"max_retries", rm.config.MaxRetries, "delay", delay)
		
		// 等待延迟
		if err := rm.waitWithContext(ctx, delay); err != nil {
			return nil, fmt.Errorf("等待重试时被取消: %w", err)
		}
	}
	
	return nil, fmt.Errorf("重试失败，已达到最大重试次数 %d: %w", rm.config.MaxRetries, lastErr)
}

// shouldRetry 判断是否应该重试
func (rm *RetryManager) shouldRetry(err error, attempt int) bool {
	// 检查是否达到最大重试次数
	if attempt >= rm.config.MaxRetries {
		return false
	}
	
	// 使用错误包装器判断是否可重试
	if errors.IsRetryableError(err) {
		return true
	}
	
	// 检查配置的可重试错误类型
	if len(rm.config.RetryableErrors) > 0 {
		errorType := getErrorType(err)
		for _, retryableType := range rm.config.RetryableErrors {
			if errorType == retryableType {
				return true
			}
		}
	}
	
	return false
}

// calculateDelay 计算延迟时间
func (rm *RetryManager) calculateDelay(attempt int) time.Duration {
	// 指数退避算法
	delay := float64(rm.config.InitialDelay) * math.Pow(rm.config.BackoffFactor, float64(attempt))
	
	// 限制最大延迟
	if delay > float64(rm.config.MaxDelay) {
		delay = float64(rm.config.MaxDelay)
	}
	
	// 添加随机抖动
	if rm.config.Jitter {
		jitter := rand.Float64() * 0.1 * delay // 10%的随机抖动
		delay += jitter
	}
	
	return time.Duration(delay)
}

// waitWithContext 带context的等待
func (rm *RetryManager) waitWithContext(ctx context.Context, delay time.Duration) error {
	timer := time.NewTimer(delay)
	defer timer.Stop()
	
	select {
	case <-timer.C:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// RetryableOperation 可重试的操作函数类型
type RetryableOperation func(ctx context.Context, attempt int) (*foundation.Response, error)

// getErrorType 获取错误类型
func getErrorType(err error) string {
	if httpErr, ok := err.(*errors.HTTPClientError); ok {
		return httpErr.Type
	}
	return "unknown"
}

// 预定义的重试配置

// DefaultRetryConfig 默认重试配置
var DefaultRetryConfig = RetryConfig{
	MaxRetries:    3,
	InitialDelay:  1 * time.Second,
	MaxDelay:      30 * time.Second,
	BackoffFactor: 2.0,
	Jitter:        true,
	RetryableErrors: []string{
		errors.ErrTypeTimeout,
		errors.ErrTypeConnection,
	},
}

// AggressiveRetryConfig 激进重试配置
var AggressiveRetryConfig = RetryConfig{
	MaxRetries:    5,
	InitialDelay:  500 * time.Millisecond,
	MaxDelay:      60 * time.Second,
	BackoffFactor: 1.8,
	Jitter:        true,
	RetryableErrors: []string{
		errors.ErrTypeTimeout,
		errors.ErrTypeConnection,
		errors.ErrTypeResponse,
	},
}

// ConservativeRetryConfig 保守重试配置
var ConservativeRetryConfig = RetryConfig{
	MaxRetries:    2,
	InitialDelay:  2 * time.Second,
	MaxDelay:      20 * time.Second,
	BackoffFactor: 2.5,
	Jitter:        false,
	RetryableErrors: []string{
		errors.ErrTypeTimeout,
	},
}

// NewDefaultRetryManager 创建默认重试管理器
func NewDefaultRetryManager() *RetryManager {
	return NewRetryManager(DefaultRetryConfig)
}

// NewAggressiveRetryManager 创建激进重试管理器
func NewAggressiveRetryManager() *RetryManager {
	return NewRetryManager(AggressiveRetryConfig)
}

// NewConservativeRetryManager 创建保守重试管理器
func NewConservativeRetryManager() *RetryManager {
	return NewRetryManager(ConservativeRetryConfig)
}
