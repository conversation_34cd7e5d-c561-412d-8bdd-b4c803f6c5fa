// Package retry 退避策略实现
package retry

import (
	"math"
	"math/rand"
	"time"
)

// BackoffStrategy 退避策略接口
type BackoffStrategy interface {
	NextDelay(attempt int) time.Duration
	Reset()
}

// ExponentialBackoff 指数退避策略
type ExponentialBackoff struct {
	InitialDelay  time.Duration
	MaxDelay      time.Duration
	BackoffFactor float64
	Jitter        bool
}

// NewExponentialBackoff 创建指数退避策略
func NewExponentialBackoff(initialDelay, maxDelay time.Duration, backoffFactor float64, jitter bool) *ExponentialBackoff {
	return &ExponentialBackoff{
		InitialDelay:  initialDelay,
		MaxDelay:      maxDelay,
		BackoffFactor: backoffFactor,
		Jitter:        jitter,
	}
}

// NextDelay 计算下次延迟时间
func (eb *ExponentialBackoff) NextDelay(attempt int) time.Duration {
	delay := float64(eb.InitialDelay) * math.Pow(eb.BackoffFactor, float64(attempt))
	
	// 限制最大延迟
	if delay > float64(eb.MaxDelay) {
		delay = float64(eb.MaxDelay)
	}
	
	// 添加随机抖动
	if eb.Jitter {
		jitter := rand.Float64() * 0.1 * delay // 10%的随机抖动
		delay += jitter
	}
	
	return time.Duration(delay)
}

// Reset 重置退避策略
func (eb *ExponentialBackoff) Reset() {
	// 指数退避策略无需重置状态
}

// LinearBackoff 线性退避策略
type LinearBackoff struct {
	InitialDelay time.Duration
	MaxDelay     time.Duration
	Increment    time.Duration
	Jitter       bool
}

// NewLinearBackoff 创建线性退避策略
func NewLinearBackoff(initialDelay, maxDelay, increment time.Duration, jitter bool) *LinearBackoff {
	return &LinearBackoff{
		InitialDelay: initialDelay,
		MaxDelay:     maxDelay,
		Increment:    increment,
		Jitter:       jitter,
	}
}

// NextDelay 计算下次延迟时间
func (lb *LinearBackoff) NextDelay(attempt int) time.Duration {
	delay := lb.InitialDelay + time.Duration(attempt)*lb.Increment
	
	// 限制最大延迟
	if delay > lb.MaxDelay {
		delay = lb.MaxDelay
	}
	
	// 添加随机抖动
	if lb.Jitter {
		jitter := time.Duration(rand.Float64() * 0.1 * float64(delay))
		delay += jitter
	}
	
	return delay
}

// Reset 重置退避策略
func (lb *LinearBackoff) Reset() {
	// 线性退避策略无需重置状态
}

// FixedBackoff 固定延迟退避策略
type FixedBackoff struct {
	Delay  time.Duration
	Jitter bool
}

// NewFixedBackoff 创建固定延迟退避策略
func NewFixedBackoff(delay time.Duration, jitter bool) *FixedBackoff {
	return &FixedBackoff{
		Delay:  delay,
		Jitter: jitter,
	}
}

// NextDelay 计算下次延迟时间
func (fb *FixedBackoff) NextDelay(attempt int) time.Duration {
	delay := fb.Delay
	
	// 添加随机抖动
	if fb.Jitter {
		jitter := time.Duration(rand.Float64() * 0.1 * float64(delay))
		delay += jitter
	}
	
	return delay
}

// Reset 重置退避策略
func (fb *FixedBackoff) Reset() {
	// 固定延迟策略无需重置状态
}

// CustomBackoff 自定义退避策略
type CustomBackoff struct {
	DelayFunc func(attempt int) time.Duration
}

// NewCustomBackoff 创建自定义退避策略
func NewCustomBackoff(delayFunc func(attempt int) time.Duration) *CustomBackoff {
	return &CustomBackoff{
		DelayFunc: delayFunc,
	}
}

// NextDelay 计算下次延迟时间
func (cb *CustomBackoff) NextDelay(attempt int) time.Duration {
	if cb.DelayFunc != nil {
		return cb.DelayFunc(attempt)
	}
	return time.Second // 默认1秒
}

// Reset 重置退避策略
func (cb *CustomBackoff) Reset() {
	// 自定义策略的重置由用户实现决定
}

// 预定义的退避策略

// DefaultExponentialBackoff 默认指数退避
var DefaultExponentialBackoff = NewExponentialBackoff(
	1*time.Second,  // 初始延迟
	30*time.Second, // 最大延迟
	2.0,            // 退避因子
	true,           // 启用抖动
)

// FastExponentialBackoff 快速指数退避
var FastExponentialBackoff = NewExponentialBackoff(
	500*time.Millisecond, // 初始延迟
	10*time.Second,       // 最大延迟
	1.5,                  // 退避因子
	true,                 // 启用抖动
)

// SlowLinearBackoff 慢速线性退避
var SlowLinearBackoff = NewLinearBackoff(
	2*time.Second,  // 初始延迟
	20*time.Second, // 最大延迟
	2*time.Second,  // 增量
	false,          // 禁用抖动
)

// QuickFixedBackoff 快速固定延迟
var QuickFixedBackoff = NewFixedBackoff(
	1*time.Second, // 固定延迟
	true,          // 启用抖动
)
