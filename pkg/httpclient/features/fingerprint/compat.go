// Package fingerprint 向后兼容层
package fingerprint

import (
	"context"
	"crypto/tls"
	"fmt"
	"sync"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/logging"
)

// 向后兼容的类型别名和接口

// Profile 向后兼容的指纹配置类型
type Profile = core.Profile

// FingerprintRequest 向后兼容的请求类型
type FingerprintRequest = core.FingerprintRequest

// SessionCacheStrategy 向后兼容的会话缓存策略
type SessionCacheStrategy = core.SessionCacheStrategy

// FingerprintManager 向后兼容的管理器接口
type FingerprintManager interface {
	// 初始化
	Initialize(configPath string) error

	// 指纹选择
	SelectFingerprint(ctx context.Context, req *FingerprintRequest) (*Profile, error)

	// TLS配置创建
	CreateTLSConfig(profile *Profile, serverName string) (*tls.Config, error)

	// 统计信息
	GetStats() map[string]interface{}

	// 关闭
	Close() error
}

// CompatManager 兼容性管理器，包装新的Manager
type CompatManager struct {
	manager *Manager
	logger  logging.Logger
}

// NewCompatManager 创建兼容性管理器
func NewCompatManager() *CompatManager {
	return &CompatManager{
		manager: NewManager(),
		logger:  logging.GetLogger("fingerprint.compat"),
	}
}

// Initialize 初始化兼容性管理器
func (c *CompatManager) Initialize(configPath string) error {
	return c.manager.Initialize(configPath)
}

// SelectFingerprint 选择指纹（兼容接口）
func (c *CompatManager) SelectFingerprint(ctx context.Context, req *FingerprintRequest) (*Profile, error) {
	return c.manager.SelectFingerprint(ctx, req)
}

// CreateTLSConfig 创建TLS配置（兼容接口）
func (c *CompatManager) CreateTLSConfig(profile *Profile, serverName string) (*tls.Config, error) {
	return c.manager.CreateTLSConfig(profile, serverName)
}

// GetStats 获取统计信息（兼容接口）
func (c *CompatManager) GetStats() map[string]interface{} {
	return c.manager.GetStats()
}

// Close 关闭管理器（兼容接口）
func (c *CompatManager) Close() error {
	return c.manager.Close()
}

// SpiderFingerprintPool 简化的爬虫专用指纹池（向后兼容）
type SpiderFingerprintPool struct {
	spiderName string
	profiles   []*Profile
	mu         sync.RWMutex
	lastUsed   map[string]time.Time // 指纹名称 -> 最后使用时间
	logger     logging.Logger
}

// NewSpiderFingerprintPool 创建新的爬虫指纹池
func NewSpiderFingerprintPool(spiderName string) *SpiderFingerprintPool {
	return &SpiderFingerprintPool{
		spiderName: spiderName,
		profiles:   make([]*Profile, 0),
		lastUsed:   make(map[string]time.Time),
		logger:     logging.GetLogger("httpclient.fingerprint.spider"),
	}
}

// AddProfile 添加指纹到池中
func (p *SpiderFingerprintPool) AddProfile(profile *Profile) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 检查是否已存在
	for _, existing := range p.profiles {
		if existing.Name == profile.Name {
			return // 已存在，不重复添加
		}
	}

	p.profiles = append(p.profiles, profile)
	p.logger.Debug("添加指纹到爬虫池", "spider", p.spiderName, "profile", profile.Name, "total", len(p.profiles))
}

// GetProfile 从池中获取指纹
func (p *SpiderFingerprintPool) GetProfile(excludeProfiles []string) (*Profile, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if len(p.profiles) == 0 {
		return nil, fmt.Errorf("爬虫指纹池为空: %s", p.spiderName)
	}

	// 过滤排除的指纹
	var candidates []*Profile
	for _, profile := range p.profiles {
		excluded := false
		for _, excludeName := range excludeProfiles {
			if profile.Name == excludeName {
				excluded = true
				break
			}
		}
		if !excluded {
			candidates = append(candidates, profile)
		}
	}

	if len(candidates) == 0 {
		return nil, fmt.Errorf("没有可用的指纹（所有指纹都被排除）")
	}

	// 选择最少使用的指纹
	var selected *Profile
	var oldestTime time.Time = time.Now()

	for _, candidate := range candidates {
		lastUsed, exists := p.lastUsed[candidate.Name]
		if !exists || lastUsed.Before(oldestTime) {
			selected = candidate
			oldestTime = lastUsed
		}
	}

	if selected != nil {
		p.lastUsed[selected.Name] = time.Now()
		p.logger.Debug("从爬虫池选择指纹", "spider", p.spiderName, "profile", selected.Name)
	}

	return selected, nil
}

// GetStats 获取池统计信息
func (p *SpiderFingerprintPool) GetStats() map[string]interface{} {
	p.mu.RLock()
	defer p.mu.RUnlock()

	stats := map[string]interface{}{
		"spider_name":    p.spiderName,
		"total_profiles": len(p.profiles),
		"last_used":      p.lastUsed,
	}

	return stats
}

// 向后兼容的全局函数

// GetCompatGlobalManager 获取全局管理器实例（向后兼容）
func GetCompatGlobalManager() FingerprintManager {
	return &CompatManager{
		manager: getGlobalManagerInstance(),
		logger:  logging.GetLogger("fingerprint.compat.global"),
	}
}

// getGlobalManagerInstance 获取全局管理器实例的内部方法
func getGlobalManagerInstance() *Manager {
	// 使用新的全局管理器
	if manager := GetGlobalManager(); manager != nil {
		return manager
	}

	// 如果全局管理器未初始化，使用延迟初始化
	return GetOrCreateGlobalManager()
}

// 向后兼容的常量和错误

// 健康状态常量（向后兼容）
const (
	HealthStatusHealthy  = core.HealthStatusHealthy
	HealthStatusDegraded = core.HealthStatusDegraded
	HealthStatusBlocked  = core.HealthStatusBlocked
)

// 浏览器类型常量（向后兼容）
const (
	BrowserTypeChrome  = core.BrowserTypeChrome
	BrowserTypeFirefox = core.BrowserTypeFirefox
	BrowserTypeSafari  = core.BrowserTypeSafari
	BrowserTypeEdge    = core.BrowserTypeEdge
)

// 操作系统类型常量（向后兼容）
const (
	OSTypeWindows = core.OSTypeWindows
	OSTypeMacOS   = core.OSTypeMacOS
	OSTypeLinux   = core.OSTypeLinux
	OSTypeAndroid = core.OSTypeAndroid
	OSTypeIOS     = core.OSTypeIOS
)

// 向后兼容的错误变量
var (
	ErrProfileNotFound       = core.ErrProfileNotFound
	ErrPoolNotFound          = core.ErrPoolNotFound
	ErrInvalidConfig         = core.ErrInvalidConfig
	ErrManagerNotInitialized = core.ErrManagerNotInitialized
	ErrProfileInvalid        = core.ErrProfileInvalid
	ErrProfileBlocked        = core.ErrProfileBlocked
	ErrProfileExpired        = core.ErrProfileExpired
	ErrNoHealthyProfiles     = core.ErrNoHealthyProfiles
)

// 向后兼容的工具函数

// IsBlockedResponseCompat 检查响应是否表示被风控（向后兼容）
func IsBlockedResponseCompat(statusCode int, err error) bool {
	return core.IsBlockedResponse(statusCode, err)
}

// AnalyzeRiskControlCompat 分析风控情况（向后兼容）
func AnalyzeRiskControlCompat(statusCode int, err error) *core.RiskControlInfo {
	return core.AnalyzeRiskControl(statusCode, err)
}

// CreateSmartSessionCacheCompat 创建智能会话缓存（向后兼容）
func CreateSmartSessionCacheCompat(strategy SessionCacheStrategy) tls.ClientSessionCache {
	return core.CreateSmartSessionCache(strategy)
}

// 向后兼容的类型转换函数

// ConvertToNewProfile 将旧版本Profile转换为新版本（如果需要）
func ConvertToNewProfile(oldProfile interface{}) *core.Profile {
	// 如果已经是新版本，直接返回
	if profile, ok := oldProfile.(*core.Profile); ok {
		return profile
	}

	// 这里可以添加从旧版本转换的逻辑
	// 目前直接返回nil，因为我们使用的是类型别名
	return nil
}

// ConvertToOldProfile 将新版本Profile转换为旧版本（如果需要）
func ConvertToOldProfile(newProfile *core.Profile) interface{} {
	// 由于使用类型别名，直接返回
	return newProfile
}
