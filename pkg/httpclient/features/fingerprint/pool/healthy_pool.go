// Package pool 健康指纹池
package pool

import (
	"fmt"
	"sync"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/logging"
)

// HealthyPool 健康指纹池
type HealthyPool struct {
	// 分层存储
	profiles map[core.ProfileTier][]*core.PoolProfile

	// 配置
	config core.PoolConfig

	// 统计信息
	stats *core.PoolStats

	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger

	// 生命周期管理
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// NewHealthyPool 创建健康指纹池
func NewHealthyPool(config core.PoolConfig) *HealthyPool {
	return &HealthyPool{
		profiles: make(map[core.ProfileTier][]*core.PoolProfile),
		config:   config,
		stats: &core.PoolStats{
			TierDistribution: make(map[core.ProfileTier]int),
		},
		logger: logging.GetLogger("fingerprint.pool.healthy"),
		stopCh: make(chan struct{}),
	}
}

// Initialize 初始化健康池
func (p *HealthyPool) Initialize() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 初始化各层级
	p.profiles[core.TierVerified] = make([]*core.PoolProfile, 0)
	p.profiles[core.TierCandidate] = make([]*core.PoolProfile, 0)
	p.profiles[core.TierBackup] = make([]*core.PoolProfile, 0)

	// 启动维护任务
	p.wg.Add(1)
	go p.maintenanceLoop()

	p.logger.Info("健康指纹池初始化完成")
	return nil
}

// GetProfile 获取健康指纹
func (p *HealthyPool) GetProfile(excludeNames []string) (*core.PoolProfile, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// 按优先级尝试各层级
	tiers := []core.ProfileTier{core.TierVerified, core.TierCandidate, core.TierBackup}

	for _, tier := range tiers {
		if profile := p.selectFromTier(tier, excludeNames); profile != nil {
			p.updateUsageStats(profile)
			return profile, nil
		}
	}

	return nil, core.NewPoolError(core.ErrorTypePoolEmpty, "没有可用的健康指纹", "healthy", nil)
}

// selectFromTier 从指定层级选择指纹
func (p *HealthyPool) selectFromTier(tier core.ProfileTier, excludeNames []string) *core.PoolProfile {
	profiles := p.profiles[tier]
	if len(profiles) == 0 {
		return nil
	}

	// 过滤排除的指纹
	var candidates []*core.PoolProfile
	for _, profile := range profiles {
		excluded := false
		for _, excludeName := range excludeNames {
			if profile.Profile.Name == excludeName {
				excluded = true
				break
			}
		}
		if !excluded && p.isProfileHealthy(profile) {
			candidates = append(candidates, profile)
		}
	}

	if len(candidates) == 0 {
		return nil
	}

	// 选择最少使用的
	var selected *core.PoolProfile
	for _, candidate := range candidates {
		if selected == nil || candidate.LastUsed.Before(selected.LastUsed) {
			selected = candidate
		}
	}

	return selected
}

// isProfileHealthy 检查指纹是否健康
func (p *HealthyPool) isProfileHealthy(profile *core.PoolProfile) bool {
	if profile.Health == nil {
		return true
	}

	return profile.Health.Status == core.HealthStatusHealthy &&
		profile.SuccessRate >= p.config.DemotionSuccessRate
}

// AddProfile 添加指纹到池
func (p *HealthyPool) AddProfile(profile *core.Profile, tier core.ProfileTier) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 检查是否已存在
	if p.findProfile(profile.Name) != nil {
		return fmt.Errorf("指纹已存在: %s", profile.Name)
	}

	// 检查容量限制
	if p.getTotalCount() >= p.config.MaxProfiles {
		// 尝试清理空间
		p.cleanupProfiles()
		if p.getTotalCount() >= p.config.MaxProfiles {
			return core.NewPoolError(core.ErrorTypePoolFull, "健康池已满", "healthy", nil)
		}
	}

	// 创建池配置
	poolProfile := &core.PoolProfile{
		Profile:     profile,
		Tier:        tier,
		Health:      &core.HealthStatus{ProfileName: profile.Name, Status: core.HealthStatusHealthy},
		AddedAt:     time.Now(),
		LastUsed:    time.Time{},
		UseCount:    0,
		SuccessRate: profile.SuccessRate,
	}

	// 添加到对应层级
	p.profiles[tier] = append(p.profiles[tier], poolProfile)
	p.updateStats()

	p.logger.Debug("添加指纹到健康池", "name", profile.Name, "tier", tier.String())
	return nil
}

// RemoveProfile 移除指纹
func (p *HealthyPool) RemoveProfile(profileName string) bool {
	p.mu.Lock()
	defer p.mu.Unlock()

	for tier, profiles := range p.profiles {
		for i, profile := range profiles {
			if profile.Profile.Name == profileName {
				// 移除指纹
				p.profiles[tier] = append(profiles[:i], profiles[i+1:]...)
				p.updateStats()
				p.logger.Debug("从健康池移除指纹", "name", profileName, "tier", tier.String())
				return true
			}
		}
	}

	return false
}

// PromoteProfile 晋升指纹
func (p *HealthyPool) PromoteProfile(profileName string) bool {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 查找指纹
	profile := p.findProfile(profileName)
	if profile == nil {
		return false
	}

	// 检查是否可以晋升
	if profile.Tier == core.TierVerified {
		return false // 已经是最高级
	}

	// 检查晋升条件
	if !p.canPromote(profile) {
		return false
	}

	// 执行晋升
	oldTier := profile.Tier
	newTier := p.getNextTier(oldTier)

	// 从旧层级移除
	p.removeFromTier(profileName, oldTier)

	// 添加到新层级
	profile.Tier = newTier
	p.profiles[newTier] = append(p.profiles[newTier], profile)

	p.updateStats()
	p.logger.Info("指纹晋升", "name", profileName, "from", oldTier.String(), "to", newTier.String())
	return true
}

// DemoteProfile 降级指纹
func (p *HealthyPool) DemoteProfile(profileName string) bool {
	p.mu.Lock()
	defer p.mu.Unlock()

	profile := p.findProfile(profileName)
	if profile == nil {
		return false
	}

	if profile.Tier == core.TierBackup {
		// 已经是最低级，直接移除
		return p.RemoveProfile(profileName)
	}

	// 执行降级
	oldTier := profile.Tier
	newTier := p.getPrevTier(oldTier)

	p.removeFromTier(profileName, oldTier)
	profile.Tier = newTier
	p.profiles[newTier] = append(p.profiles[newTier], profile)

	p.updateStats()
	p.logger.Info("指纹降级", "name", profileName, "from", oldTier.String(), "to", newTier.String())
	return true
}

// GetStats 获取统计信息
func (p *HealthyPool) GetStats() *core.PoolStats {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// 创建副本
	stats := *p.stats
	stats.TierDistribution = make(map[core.ProfileTier]int)
	for tier, count := range p.stats.TierDistribution {
		stats.TierDistribution[tier] = count
	}

	return &stats
}

// 内部方法

// findProfile 查找指纹
func (p *HealthyPool) findProfile(profileName string) *core.PoolProfile {
	for _, profiles := range p.profiles {
		for _, profile := range profiles {
			if profile.Profile.Name == profileName {
				return profile
			}
		}
	}
	return nil
}

// removeFromTier 从层级中移除指纹
func (p *HealthyPool) removeFromTier(profileName string, tier core.ProfileTier) {
	profiles := p.profiles[tier]
	for i, profile := range profiles {
		if profile.Profile.Name == profileName {
			p.profiles[tier] = append(profiles[:i], profiles[i+1:]...)
			break
		}
	}
}

// canPromote 检查是否可以晋升
func (p *HealthyPool) canPromote(profile *core.PoolProfile) bool {
	return profile.SuccessRate >= p.config.PromotionSuccessRate &&
		profile.UseCount >= int64(p.config.PromotionMinUses)
}

// getNextTier 获取下一个层级
func (p *HealthyPool) getNextTier(current core.ProfileTier) core.ProfileTier {
	switch current {
	case core.TierBackup:
		return core.TierCandidate
	case core.TierCandidate:
		return core.TierVerified
	default:
		return current
	}
}

// getPrevTier 获取上一个层级
func (p *HealthyPool) getPrevTier(current core.ProfileTier) core.ProfileTier {
	switch current {
	case core.TierVerified:
		return core.TierCandidate
	case core.TierCandidate:
		return core.TierBackup
	default:
		return current
	}
}

// getTotalCount 获取总数量
func (p *HealthyPool) getTotalCount() int {
	total := 0
	for _, profiles := range p.profiles {
		total += len(profiles)
	}
	return total
}

// updateUsageStats 更新使用统计
func (p *HealthyPool) updateUsageStats(profile *core.PoolProfile) {
	profile.LastUsed = time.Now()
	profile.UseCount++
}

// updateStats 更新统计信息
func (p *HealthyPool) updateStats() {
	p.stats.TotalProfiles = p.getTotalCount()
	p.stats.VerifiedCount = len(p.profiles[core.TierVerified])
	p.stats.CandidateCount = len(p.profiles[core.TierCandidate])
	p.stats.BackupCount = len(p.profiles[core.TierBackup])

	// 更新层级分布
	p.stats.TierDistribution[core.TierVerified] = p.stats.VerifiedCount
	p.stats.TierDistribution[core.TierCandidate] = p.stats.CandidateCount
	p.stats.TierDistribution[core.TierBackup] = p.stats.BackupCount
}

// cleanupProfiles 清理指纹
func (p *HealthyPool) cleanupProfiles() {
	for tier, profiles := range p.profiles {
		var kept []*core.PoolProfile
		for _, profile := range profiles {
			if profile.SuccessRate >= p.config.CleanupSuccessRate {
				kept = append(kept, profile)
			}
		}
		p.profiles[tier] = kept
	}
	p.updateStats()
}

// maintenanceLoop 维护循环
func (p *HealthyPool) maintenanceLoop() {
	defer p.wg.Done()

	ticker := time.NewTicker(p.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.performMaintenance()
		case <-p.stopCh:
			return
		}
	}
}

// performMaintenance 执行维护
func (p *HealthyPool) performMaintenance() {
	p.mu.Lock()
	defer p.mu.Unlock()

	p.cleanupProfiles()
	p.logger.Debug("健康池维护完成", "total", p.getTotalCount())
}

// Close 关闭健康池
func (p *HealthyPool) Close() error {
	close(p.stopCh)
	p.wg.Wait()
	p.logger.Info("健康指纹池已关闭")
	return nil
}
