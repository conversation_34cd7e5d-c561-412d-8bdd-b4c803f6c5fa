// Package pool 指纹池管理
package pool

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/config"
	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/logging"
)

// Manager 指纹池管理器
type Manager struct {
	// 命名指纹池
	namedPools  map[string]*NamedPool // 池名称 -> 指纹池
	defaultPool *NamedPool            // 默认指纹池

	// 配置和依赖
	configLoader  *config.Loader
	registry      core.ProfileRegistry
	healthTracker core.HealthTracker
	logger        logging.Logger

	// 并发控制
	mu sync.RWMutex

	// 生命周期管理
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// NamedPool 命名指纹池
type NamedPool struct {
	Name      string                           // 池名称
	Config    config.FingerprintPoolDefinition // 池配置
	Profiles  map[string]*core.PoolProfile     // 池中的指纹
	Selection SelectionStrategy                // 选择策略
	Stats     *core.PoolStats                  // 统计信息
	LastUsed  time.Time                        // 最后使用时间
	mu        sync.RWMutex                     // 池级别锁
}

// SelectionStrategy 选择策略接口
type SelectionStrategy interface {
	Select(profiles map[string]*core.PoolProfile, excludeNames []string) (*core.PoolProfile, error)
	GetName() string
}

// NewManager 创建指纹池管理器
func NewManager(configLoader *config.Loader, registry core.ProfileRegistry, healthTracker core.HealthTracker) *Manager {
	return &Manager{
		namedPools:    make(map[string]*NamedPool),
		configLoader:  configLoader,
		registry:      registry,
		healthTracker: healthTracker,
		logger:        logging.GetLogger("fingerprint.pool.manager"),
		stopCh:        make(chan struct{}),
	}
}

// Initialize 初始化池管理器
func (m *Manager) Initialize() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 创建默认池
	if err := m.createDefaultPool(); err != nil {
		return fmt.Errorf("创建默认池失败: %w", err)
	}

	// 创建配置的命名池
	if err := m.createNamedPools(); err != nil {
		return fmt.Errorf("创建命名池失败: %w", err)
	}

	// 启动维护任务
	m.wg.Add(1)
	go m.maintenanceLoop()

	m.logger.Debug("指纹池管理器初始化完成", "pools", len(m.namedPools))
	return nil
}

// GetFingerprint 获取指纹
func (m *Manager) GetFingerprint(ctx context.Context, req *core.FingerprintRequest) (*core.Profile, error) {
	// 确定使用哪个池
	poolName := m.determinePoolName(req)

	// 获取池
	pool := m.getPool(poolName)
	if pool == nil {
		return nil, core.NewPoolError(core.ErrorTypePoolNotFound, "指纹池未找到", poolName, nil)
	}

	// 从池中选择指纹
	poolProfile, err := m.selectFromPool(pool, req.ExcludeProfiles)
	if err != nil {
		return nil, err
	}

	// 更新使用统计
	m.updateUsageStats(pool, poolProfile)

	return poolProfile.Profile, nil
}

// determinePoolName 确定使用的池名称
func (m *Manager) determinePoolName(req *core.FingerprintRequest) string {
	// 优先使用请求中指定的池名称
	if req.PoolName != "" {
		return req.PoolName
	}

	// 向后兼容：使用fingerprint字段作为池名称
	if req.FingerprintName != "" {
		return req.FingerprintName
	}

	// 使用默认池
	return m.configLoader.GetDefaultPoolName()
}

// getPool 获取指定名称的池
func (m *Manager) getPool(poolName string) *NamedPool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if pool, exists := m.namedPools[poolName]; exists {
		return pool
	}

	// 如果找不到指定池，返回默认池
	return m.defaultPool
}

// selectFromPool 从池中选择指纹
func (m *Manager) selectFromPool(pool *NamedPool, excludeNames []string) (*core.PoolProfile, error) {
	pool.mu.RLock()
	defer pool.mu.RUnlock()

	if len(pool.Profiles) == 0 {
		return nil, core.NewPoolError(core.ErrorTypePoolEmpty, "指纹池为空", pool.Name, nil)
	}

	// 记录排除信息
	if len(excludeNames) > 0 {
		m.logger.Debug("选择指纹时排除列表", "pool", pool.Name, "exclude", excludeNames, "total_profiles", len(pool.Profiles))
	}

	// 使用选择策略
	poolProfile, err := pool.Selection.Select(pool.Profiles, excludeNames)
	if err != nil {
		m.logger.Debug("选择指纹失败", "pool", pool.Name, "exclude", excludeNames, "error", err)
		return nil, core.NewPoolError(core.ErrorTypePoolEmpty, "没有可用的指纹", pool.Name, err)
	}

	m.logger.Debug("成功选择指纹", "pool", pool.Name, "selected", poolProfile.Profile.Name, "exclude", excludeNames)
	return poolProfile, nil
}

// updateUsageStats 更新使用统计
func (m *Manager) updateUsageStats(pool *NamedPool, poolProfile *core.PoolProfile) {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	now := time.Now()
	pool.LastUsed = now
	poolProfile.LastUsed = now
	poolProfile.UseCount++

	// 更新池统计
	pool.Stats.TotalProfiles = len(pool.Profiles)
}

// createDefaultPool 创建默认池
func (m *Manager) createDefaultPool() error {
	defaultConfig := m.configLoader.GetDefaultPoolName()
	poolDef, exists := m.configLoader.GetPoolConfig(defaultConfig)
	if !exists {
		// 使用内置默认配置
		poolDef = &config.FingerprintPoolDefinition{
			Name:              "默认指纹池",
			Description:       "系统默认指纹池",
			MaxProfiles:       15,
			SelectionStrategy: "least_recently_used",
			QualityRequirements: config.QualityRequirements{
				RequireVerified: true,
				MinSuccessRate:  0.9,
				ExcludeHighRisk: true,
			},
			ForceCharlesOnly: true,
		}
	}

	pool, err := m.createPool("default", *poolDef)
	if err != nil {
		return err
	}

	m.defaultPool = pool
	m.namedPools["default"] = pool

	return nil
}

// createNamedPools 创建配置的命名池
func (m *Manager) createNamedPools() error {
	globalConfig := m.configLoader.GetGlobal()

	for poolName, poolDef := range globalConfig.FingerprintPools.PoolDefinitions {
		if poolName == "default" {
			continue // 默认池已经创建
		}

		pool, err := m.createPool(poolName, poolDef)
		if err != nil {
			m.logger.Warn("创建指纹池失败", "pool", poolName, "error", err)
			continue
		}

		m.namedPools[poolName] = pool
		m.logger.Debug("创建指纹池", "pool", poolName, "max_profiles", poolDef.MaxProfiles)
	}

	return nil
}

// createPool 创建单个池
func (m *Manager) createPool(poolName string, poolDef config.FingerprintPoolDefinition) (*NamedPool, error) {
	// 创建选择策略
	strategy, err := m.createSelectionStrategy(poolDef.SelectionStrategy)
	if err != nil {
		return nil, fmt.Errorf("创建选择策略失败: %w", err)
	}

	// 创建池
	pool := &NamedPool{
		Name:      poolName,
		Config:    poolDef,
		Profiles:  make(map[string]*core.PoolProfile),
		Selection: strategy,
		Stats: &core.PoolStats{
			TierDistribution: make(map[core.ProfileTier]int),
		},
		LastUsed: time.Now(),
	}

	// 填充池
	if err := m.populatePool(pool); err != nil {
		return nil, fmt.Errorf("填充池失败: %w", err)
	}

	return pool, nil
}

// populatePool 填充池
func (m *Manager) populatePool(pool *NamedPool) error {
	// 获取所有可用指纹
	allProfiles := m.registry.GetAll()

	// 根据池配置筛选指纹
	var candidates []*core.Profile
	for _, profile := range allProfiles {
		if m.isProfileSuitableForPool(profile, pool.Config) {
			candidates = append(candidates, profile)
		}
	}

	// 添加到池中
	added := 0
	for _, profile := range candidates {
		if added >= pool.Config.MaxProfiles {
			break
		}

		poolProfile := &core.PoolProfile{
			Profile:     profile,
			Tier:        core.TierVerified, // 默认为已验证
			Health:      m.healthTracker.GetHealth(profile.Name),
			AddedAt:     time.Now(),
			LastUsed:    time.Time{},
			UseCount:    0,
			SuccessRate: profile.SuccessRate,
		}

		pool.Profiles[profile.Name] = poolProfile
		added++
	}

	m.logger.Debug("填充指纹池", "pool", pool.Name, "added", added, "candidates", len(candidates))
	return nil
}

// isProfileSuitableForPool 检查指纹是否适合池
func (m *Manager) isProfileSuitableForPool(profile *core.Profile, poolConfig config.FingerprintPoolDefinition) bool {
	// 检查Charles专用要求
	if poolConfig.ForceCharlesOnly {
		if !m.isCharlesProfile(profile) {
			return false
		}
	}

	// 检查成功率要求
	if profile.SuccessRate < poolConfig.QualityRequirements.MinSuccessRate {
		return false
	}

	// 检查偏好的指纹类型
	if len(poolConfig.PreferredProfileTypes) > 0 {
		found := false
		for _, preferred := range poolConfig.PreferredProfileTypes {
			if profile.Name == preferred {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

// isCharlesProfile 检查是否为Charles相关指纹
func (m *Manager) isCharlesProfile(profile *core.Profile) bool {
	charlesKeywords := []string{"Charles", "Proxy", "Minimal", "PopMart_Charles"}
	for _, keyword := range charlesKeywords {
		if contains(profile.Name, keyword) {
			return true
		}
	}
	return false
}

// createSelectionStrategy 创建选择策略
func (m *Manager) createSelectionStrategy(strategyName string) (SelectionStrategy, error) {
	switch strategyName {
	case "random":
		return NewRandomStrategy(), nil
	case "least_recently_used":
		return NewLeastRecentlyUsedStrategy(), nil
	case "success_rate":
		return NewSuccessRateStrategy(), nil
	case "round_robin":
		return NewRoundRobinStrategy(), nil
	default:
		return NewLeastRecentlyUsedStrategy(), nil // 默认策略
	}
}

// maintenanceLoop 维护循环
func (m *Manager) maintenanceLoop() {
	defer m.wg.Done()

	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.performMaintenance()
		case <-m.stopCh:
			return
		}
	}
}

// performMaintenance 执行维护任务
func (m *Manager) performMaintenance() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for _, pool := range m.namedPools {
		m.cleanupPool(pool)
	}
}

// cleanupPool 清理池
func (m *Manager) cleanupPool(pool *NamedPool) {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	// 移除过期或不健康的指纹
	for name, poolProfile := range pool.Profiles {
		if m.shouldRemoveProfile(poolProfile) {
			delete(pool.Profiles, name)
			m.logger.Debug("移除不健康指纹", "pool", pool.Name, "profile", name)
		}
	}
}

// shouldRemoveProfile 检查是否应该移除指纹
func (m *Manager) shouldRemoveProfile(poolProfile *core.PoolProfile) bool {
	// 检查健康状态
	if poolProfile.Health != nil && poolProfile.Health.Status == core.HealthStatusBlocked {
		return true
	}

	// 检查成功率
	if poolProfile.SuccessRate < 0.3 {
		return true
	}

	return false
}

// GetPoolStats 获取池统计信息
func (m *Manager) GetPoolStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := make(map[string]interface{})
	stats["total_pools"] = len(m.namedPools)

	poolStats := make(map[string]interface{})
	for name, pool := range m.namedPools {
		pool.mu.RLock()
		poolStats[name] = map[string]interface{}{
			"total_profiles": len(pool.Profiles),
			"last_used":      pool.LastUsed,
			"config":         pool.Config.Name,
		}
		pool.mu.RUnlock()
	}
	stats["pools"] = poolStats

	return stats
}

// Cleanup 清理过期指纹
func (m *Manager) Cleanup() error {
	m.performMaintenance()
	return nil
}

// Close 关闭池管理器
func (m *Manager) Close() error {
	close(m.stopCh)
	m.wg.Wait()
	m.logger.Debug("指纹池管理器已关闭")
	return nil
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
