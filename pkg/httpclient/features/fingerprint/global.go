// Package fingerprint 全局指纹管理器
package fingerprint

import (
	"context"
	"sync"
	"time"

	"go-monitor/pkg/logging"
)

var (
	globalManager     *Manager
	globalManagerOnce sync.Once
	globalInitialized bool
	globalMutex       sync.RWMutex
)

// InitializeGlobalManager 初始化全局指纹管理器
func InitializeGlobalManager() error {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	if globalInitialized {
		return nil
	}

	logger := logging.GetLogger("fingerprint.global")
	logger.Info("开始初始化全局指纹管理器")

	startTime := time.Now()

	// 创建全局管理器
	globalManager = NewManager()

	// 初始化管理器
	if err := globalManager.Initialize(""); err != nil {
		logger.Error("初始化全局指纹管理器失败", "error", err)
		return err
	}

	globalInitialized = true
	duration := time.Since(startTime)
	logger.Info("全局指纹管理器初始化完成", "duration", duration)

	return nil
}

// GetGlobalManager 获取全局指纹管理器
func GetGlobalManager() *Manager {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	if globalInitialized && globalManager != nil {
		return globalManager
	}

	// 如果未初始化，返回nil
	return nil
}

// IsGlobalManagerInitialized 检查全局管理器是否已初始化
func IsGlobalManagerInitialized() bool {
	globalMutex.RLock()
	defer globalMutex.RUnlock()
	return globalInitialized
}

// CloseGlobalManager 关闭全局指纹管理器
func CloseGlobalManager() error {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	if !globalInitialized || globalManager == nil {
		return nil
	}

	logger := logging.GetLogger("fingerprint.global")
	logger.Info("关闭全局指纹管理器")

	err := globalManager.Close()
	if err != nil {
		logger.Error("关闭全局指纹管理器失败", "error", err)
	} else {
		logger.Info("全局指纹管理器已关闭")
	}

	globalManager = nil
	globalInitialized = false

	return err
}

// TryInitializeGlobalManager 尝试初始化全局管理器（不阻塞）
func TryInitializeGlobalManager(ctx context.Context) {
	go func() {
		logger := logging.GetLogger("fingerprint.global.async")
		
		select {
		case <-ctx.Done():
			logger.Debug("上下文取消，跳过全局指纹管理器初始化")
			return
		default:
		}

		if err := InitializeGlobalManager(); err != nil {
			logger.Warn("异步初始化全局指纹管理器失败", "error", err)
		} else {
			logger.Info("异步初始化全局指纹管理器成功")
		}
	}()
}

// GetOrCreateGlobalManager 获取或创建全局管理器（延迟初始化）
func GetOrCreateGlobalManager() *Manager {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	if globalInitialized && globalManager != nil {
		return globalManager
	}

	logger := logging.GetLogger("fingerprint.global.lazy")
	logger.Info("触发全局指纹管理器延迟初始化")

	// 延迟初始化
	globalManager = NewManager()
	if err := globalManager.Initialize(""); err != nil {
		logger.Error("延迟初始化全局指纹管理器失败", "error", err)
		return nil
	}

	globalInitialized = true
	logger.Info("全局指纹管理器延迟初始化完成")

	return globalManager
}

// GlobalManagerStats 获取全局管理器统计信息
func GlobalManagerStats() map[string]interface{} {
	manager := GetGlobalManager()
	if manager == nil {
		return map[string]interface{}{
			"initialized": false,
			"error":       "全局管理器未初始化",
		}
	}

	stats := manager.GetStats()
	stats["initialized"] = true
	return stats
}

// PrewarmGlobalManager 预热全局管理器
func PrewarmGlobalManager(ctx context.Context) error {
	if IsGlobalManagerInitialized() {
		return nil
	}

	logger := logging.GetLogger("fingerprint.global.prewarm")
	logger.Info("开始预热全局指纹管理器")

	// 在超时上下文中初始化
	initCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	done := make(chan error, 1)
	go func() {
		done <- InitializeGlobalManager()
	}()

	select {
	case err := <-done:
		if err != nil {
			logger.Error("预热全局指纹管理器失败", "error", err)
			return err
		}
		logger.Info("预热全局指纹管理器成功")
		return nil
	case <-initCtx.Done():
		logger.Warn("预热全局指纹管理器超时")
		return initCtx.Err()
	}
}
