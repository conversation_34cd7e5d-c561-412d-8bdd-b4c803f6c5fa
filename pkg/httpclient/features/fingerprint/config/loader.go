// Package config 配置加载器
package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Loader 配置加载器
type Loader struct {
	configPath string
	global     *GlobalConfig
	domains    map[string]*DomainConfig
}

// NewLoader 创建配置加载器
func NewLoader() *Loader {
	return &Loader{
		domains: make(map[string]*DomainConfig),
	}
}

// Load 加载配置文件
func (l *Loader) Load(configPath string) error {
	l.configPath = configPath

	// 加载全局配置
	if err := l.loadGlobalConfig(); err != nil {
		return fmt.Errorf("加载全局配置失败: %w", err)
	}

	// 加载域名配置
	if err := l.loadDomainConfigs(); err != nil {
		return fmt.Errorf("加载域名配置失败: %w", err)
	}

	return nil
}

// loadGlobalConfig 加载全局配置
func (l *Loader) loadGlobalConfig() error {
	if l.configPath == "" {
		l.global = GetDefaultGlobalConfig()
		return nil
	}

	// 检查文件是否存在
	if _, err := os.Stat(l.configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", l.configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(l.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML
	config := &GlobalConfig{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证配置
	if err := l.validateGlobalConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 合并默认配置
	l.global = l.mergeWithDefaults(config)

	return nil
}

// loadDomainConfigs 加载域名配置
func (l *Loader) loadDomainConfigs() error {
	// 目前域名配置可以从全局配置中获取
	// 或者从单独的域名配置文件中加载
	// 这里先实现简单版本
	return nil
}

// validateGlobalConfig 验证全局配置
func (l *Loader) validateGlobalConfig(config *GlobalConfig) error {
	// 验证指纹池配置
	if config.FingerprintPools.GlobalSettings.MaxPools <= 0 {
		return fmt.Errorf("最大池数量必须大于0")
	}

	// 验证池定义
	for name, pool := range config.FingerprintPools.PoolDefinitions {
		if pool.MaxProfiles <= 0 {
			return fmt.Errorf("池 %s 的最大指纹数必须大于0", name)
		}

		if pool.QualityRequirements.MinSuccessRate < 0 || pool.QualityRequirements.MinSuccessRate > 1 {
			return fmt.Errorf("池 %s 的最小成功率必须在0-1之间", name)
		}
	}

	// 验证健康池配置
	if config.HealthyFingerprintPool.Enabled {
		if config.HealthyFingerprintPool.PoolSize.MaxTotalCount <= 0 {
			return fmt.Errorf("健康池最大总数必须大于0")
		}

		if config.HealthyFingerprintPool.QualityThresholds.PromotionSuccessRate < 0 ||
			config.HealthyFingerprintPool.QualityThresholds.PromotionSuccessRate > 1 {
			return fmt.Errorf("晋升成功率必须在0-1之间")
		}
	}

	return nil
}

// mergeWithDefaults 与默认配置合并
func (l *Loader) mergeWithDefaults(config *GlobalConfig) *GlobalConfig {
	defaults := GetDefaultGlobalConfig()

	// 如果某些字段为空，使用默认值
	if config.FingerprintPools.GlobalSettings.DefaultPoolName == "" {
		config.FingerprintPools.GlobalSettings.DefaultPoolName = defaults.FingerprintPools.GlobalSettings.DefaultPoolName
	}

	if config.FingerprintPools.GlobalSettings.MaxPools == 0 {
		config.FingerprintPools.GlobalSettings.MaxPools = defaults.FingerprintPools.GlobalSettings.MaxPools
	}

	// 合并池定义
	if config.FingerprintPools.PoolDefinitions == nil {
		config.FingerprintPools.PoolDefinitions = make(map[string]FingerprintPoolDefinition)
	}

	// 确保默认池存在
	if _, exists := config.FingerprintPools.PoolDefinitions["default"]; !exists {
		config.FingerprintPools.PoolDefinitions["default"] = defaults.FingerprintPools.PoolDefinitions["default"]
	}

	// 合并性能配置
	if config.Performance.Caching.MaxCachedConfigs == 0 {
		config.Performance.Caching.MaxCachedConfigs = defaults.Performance.Caching.MaxCachedConfigs
	}

	return config
}

// GetGlobal 获取全局配置
func (l *Loader) GetGlobal() *GlobalConfig {
	if l.global == nil {
		return GetDefaultGlobalConfig()
	}
	return l.global
}

// GetDomain 获取域名配置
func (l *Loader) GetDomain(domain string) *DomainConfig {
	if config, exists := l.domains[domain]; exists {
		return config
	}
	return nil
}

// Reload 重新加载配置
func (l *Loader) Reload() error {
	return l.Load(l.configPath)
}

// GetPoolConfig 获取指纹池配置
func (l *Loader) GetPoolConfig(poolName string) (*FingerprintPoolDefinition, bool) {
	if l.global == nil {
		return nil, false
	}

	pool, exists := l.global.FingerprintPools.PoolDefinitions[poolName]
	return &pool, exists
}

// GetDefaultPoolName 获取默认池名称
func (l *Loader) GetDefaultPoolName() string {
	if l.global == nil {
		return "default"
	}
	return l.global.FingerprintPools.GlobalSettings.DefaultPoolName
}

// IsPoolEnabled 检查指纹池是否启用
func (l *Loader) IsPoolEnabled() bool {
	if l.global == nil {
		return true
	}
	return l.global.FingerprintPools.GlobalSettings.Enabled
}

// GetHealthyPoolConfig 获取健康池配置
func (l *Loader) GetHealthyPoolConfig() *HealthyPoolConfig {
	if l.global == nil {
		config := GetDefaultHealthyPoolConfig()
		return &config
	}
	return &l.global.HealthyFingerprintPool
}

// GetPerformanceConfig 获取性能配置
func (l *Loader) GetPerformanceConfig() *PerformanceConfig {
	if l.global == nil {
		config := GetDefaultPerformanceConfig()
		return &config
	}
	return &l.global.Performance
}

// GetMonitoringConfig 获取监控配置
func (l *Loader) GetMonitoringConfig() *MonitoringConfig {
	if l.global == nil {
		config := GetDefaultMonitoringConfig()
		return &config
	}
	return &l.global.Monitoring
}

// SaveConfig 保存配置到文件
func (l *Loader) SaveConfig(path string) error {
	if l.global == nil {
		return fmt.Errorf("没有配置可保存")
	}

	// 创建目录
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 序列化为YAML
	data, err := yaml.Marshal(l.global)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// UpdatePoolDefinition 更新池定义
func (l *Loader) UpdatePoolDefinition(poolName string, definition FingerprintPoolDefinition) {
	if l.global == nil {
		l.global = GetDefaultGlobalConfig()
	}

	if l.global.FingerprintPools.PoolDefinitions == nil {
		l.global.FingerprintPools.PoolDefinitions = make(map[string]FingerprintPoolDefinition)
	}

	l.global.FingerprintPools.PoolDefinitions[poolName] = definition
}
