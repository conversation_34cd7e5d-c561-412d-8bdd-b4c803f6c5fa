// Package config 提供指纹管理的配置功能
package config

import (
	"time"
)

// GlobalConfig 全局指纹优化配置
type GlobalConfig struct {
	// 指纹池配置系统
	FingerprintPools FingerprintPoolsConfig `yaml:"fingerprint_pools"`

	// 默认指纹池配置
	DefaultFingerprintPool DefaultPoolConfig `yaml:"default_fingerprint_pool"`

	// 健康指纹池配置
	HealthyFingerprintPool HealthyPoolConfig `yaml:"healthy_fingerprint_pool"`

	// 性能优化
	Performance PerformanceConfig `yaml:"performance"`

	// 监控和告警
	Monitoring MonitoringConfig `yaml:"monitoring"`
}

// FingerprintPoolsConfig 指纹池配置系统
type FingerprintPoolsConfig struct {
	GlobalSettings  FingerprintPoolGlobalSettings        `yaml:"global_settings"`
	PoolDefinitions map[string]FingerprintPoolDefinition `yaml:"pool_definitions"`
}

// FingerprintPoolGlobalSettings 指纹池全局设置
type FingerprintPoolGlobalSettings struct {
	Enabled                   bool   `yaml:"enabled"`
	UseMiddlewareDomainConfig bool   `yaml:"use_middleware_domain_config"`
	DefaultPoolName           string `yaml:"default_pool_name"`
	AutoCreatePools           bool   `yaml:"auto_create_pools"`
	MaxPools                  int    `yaml:"max_pools"`
}

// FingerprintPoolDefinition 指纹池定义
type FingerprintPoolDefinition struct {
	Name                  string              `yaml:"name"`
	Description           string              `yaml:"description"`
	MaxProfiles           int                 `yaml:"max_profiles"`
	SelectionStrategy     string              `yaml:"selection_strategy"`
	QualityRequirements   QualityRequirements `yaml:"quality_requirements"`
	ForceCharlesOnly      bool                `yaml:"force_charles_only"`
	PreferredProfileTypes []string            `yaml:"preferred_profile_types"`
}

// QualityRequirements 质量要求
type QualityRequirements struct {
	RequireVerified bool    `yaml:"require_verified"`
	MinSuccessRate  float64 `yaml:"min_success_rate"`
	ExcludeHighRisk bool    `yaml:"exclude_high_risk"`
}

// DefaultPoolConfig 默认指纹池配置
type DefaultPoolConfig struct {
	Enabled             bool                     `yaml:"enabled"`
	SelectionStrategy   DefaultSelectionStrategy `yaml:"selection_strategy"`
	QualityRequirements QualityRequirements      `yaml:"quality_requirements"`
}

// DefaultSelectionStrategy 默认选择策略
type DefaultSelectionStrategy struct {
	ForceCharlesOnly bool   `yaml:"force_charles_only"`
	MaxProfiles      int    `yaml:"max_profiles"`
	RotationStrategy string `yaml:"rotation_strategy"`
}

// PoolConfig 通用池配置
type PoolConfig struct {
	MaxProfiles           int           `yaml:"max_profiles"`
	SelectionStrategy     string        `yaml:"selection_strategy"`
	EnableSmartGeneration bool          `yaml:"enable_smart_generation"`
	PromotionSuccessRate  float64       `yaml:"promotion_success_rate"`
	PromotionMinUses      int           `yaml:"promotion_min_uses"`
	DemotionSuccessRate   float64       `yaml:"demotion_success_rate"`
	CleanupSuccessRate    float64       `yaml:"cleanup_success_rate"`
	CleanupInterval       time.Duration `yaml:"cleanup_interval"`
	ObservationPeriod     time.Duration `yaml:"observation_period"`
	PromotionCooldown     time.Duration `yaml:"promotion_cooldown"`
	GenerationCooldown    time.Duration `yaml:"generation_cooldown"`
	VariationStrength     float64       `yaml:"variation_strength"`
	MaxFailedAttempts     int           `yaml:"max_failed_attempts"`
	BaseOnSuccessful      bool          `yaml:"base_on_successful"`
}

// HealthyPoolConfig 健康指纹池配置
type HealthyPoolConfig struct {
	Enabled           bool                    `yaml:"enabled"`
	PoolSize          PoolSizeConfig          `yaml:"pool_size"`
	Timing            TimingConfig            `yaml:"timing"`
	QualityThresholds QualityThresholdsConfig `yaml:"quality_thresholds"`
	Generation        GenerationConfig        `yaml:"generation"`
	Rotation          RotationConfig          `yaml:"rotation"`
	Maintenance       MaintenanceConfig       `yaml:"maintenance"`
}

// PoolSizeConfig 池大小配置
type PoolSizeConfig struct {
	MinVerifiedCount  int `yaml:"min_verified_count"`
	MinCandidateCount int `yaml:"min_candidate_count"`
	MinBackupCount    int `yaml:"min_backup_count"`
	MaxTotalCount     int `yaml:"max_total_count"`
}

// TimingConfig 时间配置
type TimingConfig struct {
	ObservationPeriod  time.Duration `yaml:"observation_period"`
	PromotionCooldown  time.Duration `yaml:"promotion_cooldown"`
	GenerationCooldown time.Duration `yaml:"generation_cooldown"`
	CleanupInterval    time.Duration `yaml:"cleanup_interval"`
}

// QualityThresholdsConfig 质量阈值配置
type QualityThresholdsConfig struct {
	PromotionSuccessRate float64 `yaml:"promotion_success_rate"`
	PromotionMinUses     int     `yaml:"promotion_min_uses"`
	DemotionSuccessRate  float64 `yaml:"demotion_success_rate"`
	CleanupSuccessRate   float64 `yaml:"cleanup_success_rate"`
}

// GenerationConfig 生成配置
type GenerationConfig struct {
	EnableSmartGeneration bool    `yaml:"enable_smart_generation"`
	VariationStrength     float64 `yaml:"variation_strength"`
	MaxFailedAttempts     int     `yaml:"max_failed_attempts"`
	BaseOnSuccessful      bool    `yaml:"base_on_successful"`
}

// RotationConfig 轮换配置
type RotationConfig struct {
	Enabled             bool          `yaml:"enabled"`
	MaxConsecutiveUses  int           `yaml:"max_consecutive_uses"`
	RotationInterval    time.Duration `yaml:"rotation_interval"`
	RotationProbability float64       `yaml:"rotation_probability"`
}

// MaintenanceConfig 维护配置
type MaintenanceConfig struct {
	AutoCleanup      bool          `yaml:"auto_cleanup"`
	CleanupInterval  time.Duration `yaml:"cleanup_interval"`
	MaxIdleTime      time.Duration `yaml:"max_idle_time"`
	CompactThreshold int           `yaml:"compact_threshold"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	Caching     CachingConfig     `yaml:"caching"`
	Concurrency ConcurrencyConfig `yaml:"concurrency"`
	Timeouts    TimeoutsConfig    `yaml:"timeouts"`
}

// CachingConfig 缓存配置
type CachingConfig struct {
	EnableProfileCache bool          `yaml:"enable_profile_cache"`
	MaxCachedConfigs   int           `yaml:"max_cached_configs"`
	CacheExpiration    time.Duration `yaml:"cache_expiration"`
	CleanupInterval    time.Duration `yaml:"cleanup_interval"`
}

// ConcurrencyConfig 并发配置
type ConcurrencyConfig struct {
	MaxConcurrentRequests   int `yaml:"max_concurrent_requests"`
	MaxConcurrentGeneration int `yaml:"max_concurrent_generation"`
	WorkerPoolSize          int `yaml:"worker_pool_size"`
}

// TimeoutsConfig 超时配置
type TimeoutsConfig struct {
	SelectionTimeout   time.Duration `yaml:"selection_timeout"`
	GenerationTimeout  time.Duration `yaml:"generation_timeout"`
	HealthCheckTimeout time.Duration `yaml:"health_check_timeout"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled         bool              `yaml:"enabled"`
	MetricsInterval time.Duration     `yaml:"metrics_interval"`
	HealthCheck     HealthCheckConfig `yaml:"health_check"`
	Alerting        AlertingConfig    `yaml:"alerting"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled           bool          `yaml:"enabled"`
	CheckInterval     time.Duration `yaml:"check_interval"`
	FailureThreshold  int           `yaml:"failure_threshold"`
	RecoveryThreshold int           `yaml:"recovery_threshold"`
}

// AlertingConfig 告警配置
type AlertingConfig struct {
	Enabled            bool    `yaml:"enabled"`
	LowHealthThreshold float64 `yaml:"low_health_threshold"`
	HighFailureRate    float64 `yaml:"high_failure_rate"`
	PoolEmptyAlert     bool    `yaml:"pool_empty_alert"`
}

// ManagerConfig 管理器配置
type ManagerConfig struct {
	MaxRequestsPerProfile int           `yaml:"max_requests_per_profile"`
	MaxProfileDuration    time.Duration `yaml:"max_profile_duration"`
	RotationProbability   float64       `yaml:"rotation_probability"`
	EnableDynamic         bool          `yaml:"enable_dynamic"`
	EnableHealthCheck     bool          `yaml:"enable_health_check"`
	CleanupInterval       time.Duration `yaml:"cleanup_interval"`
}

// DomainConfig 域名特定配置
type DomainConfig struct {
	Domain                string   `yaml:"domain"`
	PreferredProfiles     []string `yaml:"preferred_profiles"`
	ExcludedProfiles      []string `yaml:"excluded_profiles"`
	CustomPoolName        string   `yaml:"custom_pool_name"`
	RotationProbability   float64  `yaml:"rotation_probability"`
	MaxRequestsPerProfile int      `yaml:"max_requests_per_profile"`
}
