// Package detection 检测模式管理
package detection

import (
	"strings"
	"sync"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/logging"
)

// PatternManager 检测模式管理器
type PatternManager struct {
	// 模式存储
	patterns map[string]*core.DetectionPattern

	// 配置
	config PatternConfig

	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger
}

// PatternConfig 模式配置
type PatternConfig struct {
	MaxPatterns    int           // 最大模式数
	UpdateInterval time.Duration // 更新间隔
	MinSampleSize  int           // 最小样本量
	AutoLearn      bool          // 自动学习
	LearningRate   float64       // 学习率
}

// NewPatternManager 创建模式管理器
func NewPatternManager(config PatternConfig) *PatternManager {
	return &PatternManager{
		patterns: make(map[string]*core.DetectionPattern),
		config:   config,
		logger:   logging.GetLogger("fingerprint.detection.patterns"),
	}
}

// Initialize 初始化模式管理器
func (pm *PatternManager) Initialize() error {
	// 加载预定义模式
	pm.loadPredefinedPatterns()

	pm.logger.Info("检测模式管理器初始化完成", "patterns", len(pm.patterns))
	return nil
}

// RegisterPattern 注册检测模式
func (pm *PatternManager) RegisterPattern(pattern *core.DetectionPattern) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pattern.ServerName == "" {
		return core.NewConfigError(core.ErrorTypeConfigValidation, "服务器名称不能为空", "", nil)
	}

	// 设置默认值
	if pattern.Sensitivity == 0 {
		pattern.Sensitivity = 0.7
	}

	pattern.LastUpdated = time.Now()
	pm.patterns[pattern.ServerName] = pattern

	pm.logger.Debug("注册检测模式", "server", pattern.ServerName, "sensitivity", pattern.Sensitivity)
	return nil
}

// GetPattern 获取检测模式
func (pm *PatternManager) GetPattern(serverName string) (*core.DetectionPattern, bool) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	// 优先查找精确匹配
	if pattern, exists := pm.patterns[serverName]; exists {
		return pm.copyPattern(pattern), true
	}

	// 查找通配符模式
	if pattern, exists := pm.patterns["*"]; exists {
		return pm.copyPattern(pattern), true
	}

	return nil, false
}

// GetAllPatterns 获取所有模式
func (pm *PatternManager) GetAllPatterns() map[string]*core.DetectionPattern {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	result := make(map[string]*core.DetectionPattern)
	for name, pattern := range pm.patterns {
		result[name] = pm.copyPattern(pattern)
	}

	return result
}

// UpdatePattern 更新模式
func (pm *PatternManager) UpdatePattern(serverName string, statusCode int, responseBody string, isBlocked bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	pattern, exists := pm.patterns[serverName]
	if !exists {
		// 如果不存在，创建新模式
		pattern = &core.DetectionPattern{
			ServerName:         serverName,
			BlockedStatusCodes: []int{},
			BlockedKeywords:    []string{},
			DetectionMethods:   []string{"status_code", "response_body"},
			Sensitivity:        0.7,
			LastUpdated:        time.Now(),
			SampleCount:        0,
		}
		pm.patterns[serverName] = pattern
	}

	// 更新样本计数
	pattern.SampleCount++
	pattern.LastUpdated = time.Now()

	if isBlocked {
		// 学习新的风控模式
		pm.learnBlockedPattern(pattern, statusCode, responseBody)
	}

	pm.logger.Debug("更新检测模式", "server", serverName, "blocked", isBlocked, "samples", pattern.SampleCount)
}

// IsBlocked 检查是否被风控
func (pm *PatternManager) IsBlocked(serverName string, statusCode int, responseBody string) bool {
	pattern, exists := pm.GetPattern(serverName)
	if !exists {
		// 使用默认检测逻辑
		return pm.defaultBlockedCheck(statusCode, responseBody)
	}

	return pm.checkWithPattern(pattern, statusCode, responseBody)
}

// GetBlockedProbability 获取被风控的概率
func (pm *PatternManager) GetBlockedProbability(serverName string, statusCode int, responseBody string) float64 {
	pattern, exists := pm.GetPattern(serverName)
	if !exists {
		return pm.defaultBlockedProbability(statusCode, responseBody)
	}

	return pm.calculateBlockedProbability(pattern, statusCode, responseBody)
}

// RemovePattern 移除模式
func (pm *PatternManager) RemovePattern(serverName string) bool {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if _, exists := pm.patterns[serverName]; exists {
		delete(pm.patterns, serverName)
		pm.logger.Debug("移除检测模式", "server", serverName)
		return true
	}

	return false
}

// GetStats 获取统计信息
func (pm *PatternManager) GetStats() map[string]interface{} {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	stats := map[string]interface{}{
		"total_patterns": len(pm.patterns),
		"patterns":       make(map[string]interface{}),
	}

	for name, pattern := range pm.patterns {
		stats["patterns"].(map[string]interface{})[name] = map[string]interface{}{
			"sample_count":     pattern.SampleCount,
			"sensitivity":      pattern.Sensitivity,
			"blocked_codes":    len(pattern.BlockedStatusCodes),
			"blocked_keywords": len(pattern.BlockedKeywords),
			"last_updated":     pattern.LastUpdated,
		}
	}

	return stats
}

// 内部方法

// loadPredefinedPatterns 加载预定义模式
func (pm *PatternManager) loadPredefinedPatterns() {
	now := time.Now()

	// PopMart模式
	popmartPattern := &core.DetectionPattern{
		ServerName:         "prod-intl-api.popmart.com",
		BlockedStatusCodes: []int{470, 471, 418, 403, 429},
		BlockedKeywords:    []string{"blocked", "forbidden", "rate limit", "too many requests", "access denied"},
		DetectionMethods:   []string{"status_code", "response_body", "response_time"},
		Sensitivity:        0.8,
		LastUpdated:        now,
		SampleCount:        0,
	}
	pm.patterns["prod-intl-api.popmart.com"] = popmartPattern

	// 通用模式
	genericPattern := &core.DetectionPattern{
		ServerName:         "*",
		BlockedStatusCodes: []int{403, 429, 418, 470, 471, 503, 502},
		BlockedKeywords:    []string{"blocked", "banned", "forbidden", "rate limit", "captcha", "verification", "access denied", "too many requests"},
		DetectionMethods:   []string{"status_code", "response_body"},
		Sensitivity:        0.7,
		LastUpdated:        now,
		SampleCount:        0,
	}
	pm.patterns["*"] = genericPattern

	// 亚马逊模式
	amazonPattern := &core.DetectionPattern{
		ServerName:         "*.amazon.com",
		BlockedStatusCodes: []int{503, 429, 403},
		BlockedKeywords:    []string{"robot", "captcha", "blocked", "unusual traffic"},
		DetectionMethods:   []string{"status_code", "response_body"},
		Sensitivity:        0.75,
		LastUpdated:        now,
		SampleCount:        0,
	}
	pm.patterns["*.amazon.com"] = amazonPattern
}

// learnBlockedPattern 学习被风控的模式
func (pm *PatternManager) learnBlockedPattern(pattern *core.DetectionPattern, statusCode int, responseBody string) {
	if !pm.config.AutoLearn {
		return
	}

	// 学习状态码
	if statusCode > 0 && !pm.containsStatusCode(pattern.BlockedStatusCodes, statusCode) {
		pattern.BlockedStatusCodes = append(pattern.BlockedStatusCodes, statusCode)
		pm.logger.Debug("学习新的风控状态码", "server", pattern.ServerName, "code", statusCode)
	}

	// 学习关键词
	if responseBody != "" {
		keywords := pm.extractKeywords(responseBody)
		for _, keyword := range keywords {
			if !pm.containsKeyword(pattern.BlockedKeywords, keyword) {
				pattern.BlockedKeywords = append(pattern.BlockedKeywords, keyword)
				pm.logger.Debug("学习新的风控关键词", "server", pattern.ServerName, "keyword", keyword)
			}
		}
	}

	// 限制数组大小
	if len(pattern.BlockedStatusCodes) > 20 {
		pattern.BlockedStatusCodes = pattern.BlockedStatusCodes[:20]
	}
	if len(pattern.BlockedKeywords) > 50 {
		pattern.BlockedKeywords = pattern.BlockedKeywords[:50]
	}
}

// checkWithPattern 使用模式检查
func (pm *PatternManager) checkWithPattern(pattern *core.DetectionPattern, statusCode int, responseBody string) bool {
	// 检查状态码
	for _, blockedCode := range pattern.BlockedStatusCodes {
		if statusCode == blockedCode {
			return true
		}
	}

	// 检查关键词
	if responseBody != "" {
		lowerBody := strings.ToLower(responseBody)
		for _, keyword := range pattern.BlockedKeywords {
			if strings.Contains(lowerBody, strings.ToLower(keyword)) {
				return true
			}
		}
	}

	return false
}

// calculateBlockedProbability 计算被风控概率
func (pm *PatternManager) calculateBlockedProbability(pattern *core.DetectionPattern, statusCode int, responseBody string) float64 {
	probability := 0.0

	// 状态码匹配
	for _, blockedCode := range pattern.BlockedStatusCodes {
		if statusCode == blockedCode {
			probability += 0.8
			break
		}
	}

	// 关键词匹配
	if responseBody != "" {
		lowerBody := strings.ToLower(responseBody)
		matchCount := 0
		for _, keyword := range pattern.BlockedKeywords {
			if strings.Contains(lowerBody, strings.ToLower(keyword)) {
				matchCount++
			}
		}

		if matchCount > 0 {
			keywordProbability := float64(matchCount) / float64(len(pattern.BlockedKeywords))
			probability += keywordProbability * 0.6
		}
	}

	// 应用敏感度
	probability *= pattern.Sensitivity

	// 确保在0-1范围内
	if probability > 1.0 {
		probability = 1.0
	}

	return probability
}

// defaultBlockedCheck 默认风控检查
func (pm *PatternManager) defaultBlockedCheck(statusCode int, responseBody string) bool {
	// 默认风控状态码
	blockedCodes := []int{403, 429, 418, 470, 471, 503}
	for _, code := range blockedCodes {
		if statusCode == code {
			return true
		}
	}

	// 默认风控关键词
	if responseBody != "" {
		lowerBody := strings.ToLower(responseBody)
		blockedKeywords := []string{"blocked", "forbidden", "rate limit", "captcha"}
		for _, keyword := range blockedKeywords {
			if strings.Contains(lowerBody, keyword) {
				return true
			}
		}
	}

	return false
}

// defaultBlockedProbability 默认风控概率
func (pm *PatternManager) defaultBlockedProbability(statusCode int, responseBody string) float64 {
	if pm.defaultBlockedCheck(statusCode, responseBody) {
		return 0.8
	}
	return 0.0
}

// extractKeywords 提取关键词
func (pm *PatternManager) extractKeywords(responseBody string) []string {
	// 简化的关键词提取
	suspiciousWords := []string{"block", "ban", "forbidden", "denied", "limit", "captcha", "verify", "unusual", "suspicious"}
	var keywords []string

	lowerBody := strings.ToLower(responseBody)
	for _, word := range suspiciousWords {
		if strings.Contains(lowerBody, word) {
			keywords = append(keywords, word)
		}
	}

	return keywords
}

// containsStatusCode 检查是否包含状态码
func (pm *PatternManager) containsStatusCode(codes []int, code int) bool {
	for _, c := range codes {
		if c == code {
			return true
		}
	}
	return false
}

// containsKeyword 检查是否包含关键词
func (pm *PatternManager) containsKeyword(keywords []string, keyword string) bool {
	for _, k := range keywords {
		if strings.EqualFold(k, keyword) {
			return true
		}
	}
	return false
}

// copyPattern 复制模式
func (pm *PatternManager) copyPattern(pattern *core.DetectionPattern) *core.DetectionPattern {
	copied := &core.DetectionPattern{
		ServerName:         pattern.ServerName,
		BlockedStatusCodes: make([]int, len(pattern.BlockedStatusCodes)),
		BlockedKeywords:    make([]string, len(pattern.BlockedKeywords)),
		DetectionMethods:   make([]string, len(pattern.DetectionMethods)),
		Sensitivity:        pattern.Sensitivity,
		LastUpdated:        pattern.LastUpdated,
		SampleCount:        pattern.SampleCount,
	}

	copy(copied.BlockedStatusCodes, pattern.BlockedStatusCodes)
	copy(copied.BlockedKeywords, pattern.BlockedKeywords)
	copy(copied.DetectionMethods, pattern.DetectionMethods)

	return copied
}
