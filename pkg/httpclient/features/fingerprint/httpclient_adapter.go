// Package fingerprint HTTP客户端适配器
package fingerprint

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/httpclient/foundation"
	"go-monitor/pkg/logging"
)

// HTTPClientAdapter HTTP客户端适配器
// 提供新指纹模块与现有HTTP客户端架构的无缝集成
type HTTPClientAdapter struct {
	// 核心组件
	manager *Manager

	// 配置
	config *AdapterConfig

	// 状态管理
	initialized bool
	mu          sync.RWMutex
	logger      logging.Logger

	// 轮转状态跟踪
	lastUsedProfiles map[string]string // serverName -> profileName
}

// AdapterConfig 适配器配置
type AdapterConfig struct {
	// 指纹配置
	EnableFingerprint bool   `yaml:"enable_fingerprint"`
	DefaultPoolName   string `yaml:"default_pool_name"`
	ConfigPath        string `yaml:"config_path"`

	// 集成配置
	AutoInitialize      bool          `yaml:"auto_initialize"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`

	// 风控配置
	EnableRiskDetection bool `yaml:"enable_risk_detection"`
	AutoSwitchOnBlock   bool `yaml:"auto_switch_on_block"`
}

// NewHTTPClientAdapter 创建HTTP客户端适配器
func NewHTTPClientAdapter(config *AdapterConfig) *HTTPClientAdapter {
	if config == nil {
		config = getDefaultAdapterConfig()
	}

	return &HTTPClientAdapter{
		config:           config,
		logger:           logging.GetLogger("fingerprint.httpclient.adapter"),
		lastUsedProfiles: make(map[string]string),
	}
}

// Initialize 初始化适配器
func (a *HTTPClientAdapter) Initialize() error {
	a.mu.Lock()
	defer a.mu.Unlock()

	if a.initialized {
		a.logger.Debug("指纹适配器已经初始化，跳过重复初始化")
		return nil
	}

	startTime := time.Now()
	a.logger.Info("开始初始化HTTP客户端指纹适配器")

	// 尝试使用全局管理器
	if globalManager := GetGlobalManager(); globalManager != nil {
		a.manager = globalManager
		a.logger.Info("使用全局指纹管理器")
	} else {
		// 创建新的指纹管理器
		a.manager = NewManager()

		// 初始化指纹管理器
		configPath := a.config.ConfigPath
		if configPath == "" {
			configPath = "" // 使用默认配置
			a.logger.Debug("使用默认指纹配置")
		} else {
			a.logger.Debug("使用指定指纹配置", "config_path", configPath)
		}

		if err := a.manager.Initialize(configPath); err != nil {
			a.logger.Error("初始化指纹管理器失败", "error", err, "config_path", configPath)
			return fmt.Errorf("初始化指纹管理器失败: %w", err)
		}
		a.logger.Info("创建新的指纹管理器")
	}

	a.initialized = true
	initDuration := time.Since(startTime)
	a.logger.Info("HTTP客户端指纹适配器初始化完成",
		"duration", initDuration,
		"auto_initialize", a.config.AutoInitialize,
		"default_pool", a.config.DefaultPoolName)
	return nil
}

// IsEnabled 检查指纹功能是否启用
func (a *HTTPClientAdapter) IsEnabled() bool {
	return a.config.EnableFingerprint
}

// SelectFingerprint 为请求选择指纹
func (a *HTTPClientAdapter) SelectFingerprint(ctx context.Context, req *foundation.Request) (*core.Profile, error) {
	if !a.IsEnabled() {
		return nil, nil
	}

	// 检查是否有指纹字段
	fingerprintPool := a.extractFingerprintPool(req)
	if fingerprintPool == "" {
		a.logger.Debug("跳过指纹处理，使用普通HTTP请求", "url", req.URL)
		return nil, nil
	}

	a.logger.Debug("开始选择指纹", "pool", fingerprintPool, "url", req.URL)

	// 检查初始化状态
	a.mu.RLock()
	initialized := a.initialized
	a.mu.RUnlock()

	if !initialized {
		if a.config.AutoInitialize {
			if err := a.Initialize(); err != nil {
				return nil, fmt.Errorf("自动初始化失败: %w", err)
			}
		} else {
			return nil, fmt.Errorf("适配器未初始化")
		}
	}

	// 获取服务器名称
	serverName := a.extractServerName(req.URL)

	// 获取上次使用的指纹
	a.mu.RLock()
	lastUsedProfile := a.lastUsedProfiles[serverName]
	a.mu.RUnlock()

	// 构建指纹请求
	fingerprintReq := &core.FingerprintRequest{
		PoolName:        fingerprintPool,
		ServerName:      serverName,
		ExcludeProfiles: a.extractExcludeProfiles(req),
		ForceRotation:   true,            // 每次请求都强制轮转指纹
		LastUsedProfile: lastUsedProfile, // 传递上次使用的指纹
	}

	a.logger.Debug("使用指定指纹池处理请求", "pool", fingerprintPool, "url", req.URL)

	// 选择指纹
	profile, err := a.manager.SelectFingerprint(ctx, fingerprintReq)
	if err != nil {
		a.logger.Error("选择指纹失败", "error", err, "pool", fingerprintPool, "url", req.URL)
		return nil, err
	}

	if profile != nil {
		// 更新上次使用的指纹记录
		a.mu.Lock()
		a.lastUsedProfiles[serverName] = profile.Name
		a.mu.Unlock()

		// 将选择的指纹信息添加到请求元数据中，以便传递到响应
		if req.Meta == nil {
			req.Meta = make(map[string]interface{})
		}
		req.Meta["selected_fingerprint"] = profile.Name
		req.Meta["is_dynamic"] = profile.IsDynamic
		req.Meta["fingerprint_pool"] = fingerprintPool

		a.logger.Debug("成功选择指纹", "profile", profile.Name, "pool", fingerprintPool, "url", req.URL, "last_used", lastUsedProfile)
	} else {
		a.logger.Warn("未找到合适的指纹", "pool", fingerprintPool, "url", req.URL)
	}

	return profile, nil
}

// CreateTLSConfig 创建TLS配置
func (a *HTTPClientAdapter) CreateTLSConfig(profile *core.Profile, serverName string) (*tls.Config, error) {
	if !a.IsEnabled() || profile == nil {
		return nil, nil
	}

	a.mu.RLock()
	defer a.mu.RUnlock()

	if !a.initialized {
		return nil, fmt.Errorf("适配器未初始化")
	}

	return a.manager.CreateTLSConfig(profile, serverName)
}

// AnalyzeResponse 分析响应（风控检测）
func (a *HTTPClientAdapter) AnalyzeResponse(serverName string, statusCode int, body string, responseTimeNs int64, profileName string) {
	if !a.IsEnabled() || !a.config.EnableRiskDetection {
		return
	}

	a.mu.RLock()
	defer a.mu.RUnlock()

	if !a.initialized {
		return
	}

	responseTime := time.Duration(responseTimeNs)
	a.manager.AnalyzeResponse(serverName, statusCode, body, responseTime, profileName)
}

// UpdateHealth 更新指纹健康状态
func (a *HTTPClientAdapter) UpdateHealth(profileName string, success bool, responseTime time.Duration) {
	if !a.IsEnabled() {
		return
	}

	a.mu.RLock()
	defer a.mu.RUnlock()

	if !a.initialized {
		return
	}

	a.manager.UpdateHealth(profileName, success, responseTime)
}

// GetStats 获取统计信息
func (a *HTTPClientAdapter) GetStats() map[string]interface{} {
	if !a.IsEnabled() {
		return nil
	}

	a.mu.RLock()
	defer a.mu.RUnlock()

	if !a.initialized {
		return nil
	}

	return a.manager.GetStats()
}

// Close 关闭适配器
func (a *HTTPClientAdapter) Close() error {
	a.mu.Lock()
	defer a.mu.Unlock()

	if !a.initialized {
		return nil
	}

	if a.manager != nil {
		if err := a.manager.Close(); err != nil {
			a.logger.Error("关闭指纹管理器失败", "error", err)
			return err
		}
	}

	a.initialized = false
	a.logger.Info("HTTP客户端指纹适配器已关闭")
	return nil
}

// extractFingerprintPool 提取指纹池名称
func (a *HTTPClientAdapter) extractFingerprintPool(req *foundation.Request) string {
	if req.Meta == nil {
		return ""
	}

	// 只使用 fingerprint 字段
	if fingerprint, ok := req.Meta["fingerprint"].(string); ok && fingerprint != "" {
		return fingerprint
	}

	return ""
}

// extractServerName 提取服务器名称
func (a *HTTPClientAdapter) extractServerName(requestURL string) string {
	if parsedURL, err := url.Parse(requestURL); err == nil {
		return parsedURL.Host
	}
	return requestURL
}

// extractExcludeProfiles 提取需要排除的指纹列表
func (a *HTTPClientAdapter) extractExcludeProfiles(req *foundation.Request) []string {
	var excludeProfiles []string

	if req.Meta == nil {
		return excludeProfiles
	}

	// 检查是否需要切换指纹
	if switchRequired, ok := req.Meta["fingerprint_switch_required"].(bool); ok && switchRequired {
		// 如果需要切换指纹，排除当前使用的指纹
		if currentProfile, ok := req.Meta["selected_fingerprint"].(string); ok && currentProfile != "" {
			excludeProfiles = append(excludeProfiles, currentProfile)
			a.logger.Debug("检测到指纹切换需求，排除当前指纹", "excluded", currentProfile, "url", req.URL)
		}

		// 检查风控级别，根据严重程度排除更多指纹
		if riskLevel, ok := req.Meta["tls_risk_level"].(string); ok {
			switch riskLevel {
			case "severe":
				// 严重风控：排除同类型的所有指纹
				excludeProfiles = append(excludeProfiles, a.getSimilarProfiles(req.Meta["selected_fingerprint"])...)
			case "moderate":
				// 中等风控：排除最近使用的指纹
				excludeProfiles = append(excludeProfiles, a.getRecentlyUsedProfiles()...)
			}
		}
	}

	// 检查显式排除列表
	if excludeList, ok := req.Meta["exclude_fingerprints"].([]string); ok {
		excludeProfiles = append(excludeProfiles, excludeList...)
	}

	// 检查被风控的指纹
	if blockedProfiles := a.getBlockedProfiles(); len(blockedProfiles) > 0 {
		excludeProfiles = append(excludeProfiles, blockedProfiles...)
	}

	return a.deduplicateStrings(excludeProfiles)
}

// MarkUnhealthy 标记指纹为不健康
func (a *HTTPClientAdapter) MarkUnhealthy(profileName string, statusCode int, riskLevel string) {
	if !a.IsEnabled() || a.manager == nil || profileName == "" {
		return
	}

	a.logger.Debug("标记指纹为不健康",
		"profile", profileName,
		"status_code", statusCode,
		"risk_level", riskLevel)

	// 委托给管理器标记不健康指纹
	a.manager.MarkProfileUnhealthy(profileName, statusCode)
}

// MarkUnhealthyDirect 直接标记指纹为不健康（跳过重复检测和日志）
func (a *HTTPClientAdapter) MarkUnhealthyDirect(profileName string, statusCode int) {
	if !a.IsEnabled() || a.manager == nil || profileName == "" {
		return
	}

	// 直接委托给管理器，避免重复的风控检测和日志
	a.manager.MarkProfileUnhealthyDirect(profileName, statusCode)
}

// extractMarkUnhealthy 提取需要标记为不健康的指纹
func (a *HTTPClientAdapter) extractMarkUnhealthy(req *foundation.Request) string {
	if req.Meta == nil {
		return ""
	}

	// 检查是否有指纹需要标记为不健康
	if markUnhealthy, ok := req.Meta["mark_unhealthy"].(string); ok {
		return markUnhealthy
	}

	return ""
}

// extractStatusCode 提取状态码
func (a *HTTPClientAdapter) extractStatusCode(req *foundation.Request) int {
	if req.Meta == nil {
		return 0
	}

	// 检查是否有状态码信息
	if statusCode, ok := req.Meta["status_code"].(int); ok {
		return statusCode
	}

	return 0
}

// getSimilarProfiles 获取相似的指纹（同类型、同浏览器等）
func (a *HTTPClientAdapter) getSimilarProfiles(selectedProfile interface{}) []string {
	if a.manager == nil {
		return []string{}
	}

	profileName, ok := selectedProfile.(string)
	if !ok || profileName == "" {
		return []string{}
	}

	// 这里可以根据指纹名称的模式来判断相似性
	// 例如：Chrome_xxx 系列、Firefox_xxx 系列等
	var similar []string

	// 简单的相似性判断：相同前缀的指纹
	if strings.Contains(profileName, "Chrome") {
		similar = append(similar, "Chrome_Latest", "Chrome_Stable", "Chrome_Beta")
	} else if strings.Contains(profileName, "Firefox") {
		similar = append(similar, "Firefox_Latest", "Firefox_ESR")
	} else if strings.Contains(profileName, "PopMart") {
		similar = append(similar, "PopMart_Charles_Ultra_Exact", "PopMart_Charles_Ultra")
	}

	return similar
}

// getRecentlyUsedProfiles 获取最近使用的指纹
func (a *HTTPClientAdapter) getRecentlyUsedProfiles() []string {
	if a.manager == nil {
		return []string{}
	}

	// 从健康追踪器获取最近使用的指纹
	stats := a.manager.GetStats()
	if healthStats, ok := stats["health_stats"].(map[string]interface{}); ok {
		// 这里可以实现更复杂的逻辑来获取最近使用的指纹
		// 暂时返回空列表
		_ = healthStats
	}

	return []string{}
}

// getBlockedProfiles 获取被风控的指纹
func (a *HTTPClientAdapter) getBlockedProfiles() []string {
	if a.manager == nil {
		return []string{}
	}

	var blocked []string
	stats := a.manager.GetStats()

	if healthStats, ok := stats["health_stats"].(map[string]interface{}); ok {
		if blockedProfiles, ok := healthStats["blocked_profiles"].([]string); ok {
			blocked = append(blocked, blockedProfiles...)
		}
	}

	return blocked
}

// deduplicateStrings 去重字符串切片
func (a *HTTPClientAdapter) deduplicateStrings(input []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, item := range input {
		if item != "" && !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}

// getDefaultAdapterConfig 获取默认适配器配置
func getDefaultAdapterConfig() *AdapterConfig {
	return &AdapterConfig{
		EnableFingerprint:   true,
		DefaultPoolName:     "default",
		ConfigPath:          "",
		AutoInitialize:      true,
		HealthCheckInterval: 30 * time.Second,
		EnableRiskDetection: true,
		AutoSwitchOnBlock:   true,
	}
}

// SimpleFingerprintAdapter 简化的指纹适配器（向后兼容）
type SimpleFingerprintAdapter struct {
	adapter *HTTPClientAdapter
	enabled bool
}

// NewSimpleFingerprintAdapter 创建简化指纹适配器
func NewSimpleFingerprintAdapter(enabled bool) *SimpleFingerprintAdapter {
	config := getDefaultAdapterConfig()
	config.EnableFingerprint = enabled

	adapter := NewHTTPClientAdapter(config)
	if enabled {
		// 自动初始化
		if err := adapter.Initialize(); err != nil {
			// 记录错误但不阻止创建
			logger := logging.GetLogger("fingerprint.simple.adapter")
			logger.Error("初始化简化指纹适配器失败", "error", err)
		}
	}

	return &SimpleFingerprintAdapter{
		adapter: adapter,
		enabled: enabled,
	}
}

// IsEnabled 检查是否启用
func (s *SimpleFingerprintAdapter) IsEnabled() bool {
	return s.enabled
}

// SelectFingerprint 选择指纹
func (s *SimpleFingerprintAdapter) SelectFingerprint(req *foundation.Request) (*core.Profile, error) {
	if s.adapter != nil {
		return s.adapter.SelectFingerprint(context.Background(), req)
	}
	return nil, nil
}

// AnalyzeResponse 分析响应
func (s *SimpleFingerprintAdapter) AnalyzeResponse(serverName string, statusCode int, body string, responseTimeNs int64, profileName string) {
	if s.adapter != nil {
		s.adapter.AnalyzeResponse(serverName, statusCode, body, responseTimeNs, profileName)
	}
}

// UpdateHealth 更新指纹健康状态
func (s *SimpleFingerprintAdapter) UpdateHealth(profileName string, success bool, responseTime time.Duration) {
	if s.adapter != nil {
		s.adapter.UpdateHealth(profileName, success, responseTime)
	}
}

// MarkUnhealthy 标记指纹为不健康
func (s *SimpleFingerprintAdapter) MarkUnhealthy(profileName string, statusCode int, riskLevel string) {
	if s.adapter != nil {
		s.adapter.MarkUnhealthy(profileName, statusCode, riskLevel)
	}
}

// MarkUnhealthyDirect 直接标记指纹为不健康（跳过重复检测和日志）
func (s *SimpleFingerprintAdapter) MarkUnhealthyDirect(profileName string, statusCode int) {
	if s.adapter != nil {
		s.adapter.MarkUnhealthyDirect(profileName, statusCode)
	}
}

// Close 关闭适配器
func (s *SimpleFingerprintAdapter) Close() error {
	if s.adapter != nil {
		return s.adapter.Close()
	}
	return nil
}
