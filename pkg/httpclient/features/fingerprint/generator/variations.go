// Package generator 变异算法
package generator

import (
	"crypto/tls"
	"math/rand"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/logging"
)

// VariationEngine 变异引擎
type VariationEngine struct {
	// 变异策略
	strategies map[string]VariationStrategy

	// 配置
	config VariationConfig

	// 日志记录
	logger logging.Logger
}

// VariationStrategy 变异策略接口
type VariationStrategy interface {
	Apply(profile *core.Profile, strength float64) *core.Profile
	GetName() string
	GetDescription() string
}

// VariationConfig 变异配置
type VariationConfig struct {
	MaxVariationLevel int     // 最大变异级别
	DefaultStrength   float64 // 默认变异强度
	EnableRandomSeed  bool    // 启用随机种子
}

// NewVariationEngine 创建变异引擎
func NewVariationEngine(config VariationConfig) *VariationEngine {
	engine := &VariationEngine{
		strategies: make(map[string]VariationStrategy),
		config:     config,
		logger:     logging.GetLogger("fingerprint.generator.variations"),
	}

	// 注册内置策略
	engine.registerBuiltinStrategies()

	return engine
}

// ApplyVariation 应用变异
func (ve *VariationEngine) ApplyVariation(profile *core.Profile, strategyName string, strength float64) *core.Profile {
	strategy, exists := ve.strategies[strategyName]
	if !exists {
		ve.logger.Warn("变异策略不存在", "strategy", strategyName)
		return profile
	}

	// 应用变异
	varied := strategy.Apply(profile, strength)

	ve.logger.Debug("应用变异", "strategy", strategyName, "strength", strength, "original", profile.Name, "varied", varied.Name)
	return varied
}

// ApplyMultipleVariations 应用多种变异
func (ve *VariationEngine) ApplyMultipleVariations(profile *core.Profile, strategies []string, strength float64) *core.Profile {
	result := profile

	for _, strategyName := range strategies {
		result = ve.ApplyVariation(result, strategyName, strength)
	}

	return result
}

// GetAvailableStrategies 获取可用策略
func (ve *VariationEngine) GetAvailableStrategies() []string {
	var strategies []string
	for name := range ve.strategies {
		strategies = append(strategies, name)
	}
	return strategies
}

// RegisterStrategy 注册策略
func (ve *VariationEngine) RegisterStrategy(strategy VariationStrategy) {
	ve.strategies[strategy.GetName()] = strategy
	ve.logger.Debug("注册变异策略", "name", strategy.GetName())
}

// 内置变异策略

// registerBuiltinStrategies 注册内置策略
func (ve *VariationEngine) registerBuiltinStrategies() {
	ve.RegisterStrategy(&CipherShuffleStrategy{})
	ve.RegisterStrategy(&CurveShuffleStrategy{})
	ve.RegisterStrategy(&SessionCacheStrategy{})
	ve.RegisterStrategy(&VersionVariationStrategy{})
	ve.RegisterStrategy(&ALPNVariationStrategy{})
	ve.RegisterStrategy(&MinimalVariationStrategy{})
}

// CipherShuffleStrategy 密码套件打乱策略
type CipherShuffleStrategy struct{}

func (s *CipherShuffleStrategy) GetName() string {
	return "cipher_shuffle"
}

func (s *CipherShuffleStrategy) GetDescription() string {
	return "打乱密码套件顺序"
}

func (s *CipherShuffleStrategy) Apply(profile *core.Profile, strength float64) *core.Profile {
	varied := s.copyProfile(profile)

	if len(varied.CipherSuites) <= 1 || strength <= 0 {
		return varied
	}

	// 根据强度决定打乱程度
	if rand.Float64() < strength {
		// 完全打乱
		for i := len(varied.CipherSuites) - 1; i > 0; i-- {
			j := rand.Intn(i + 1)
			varied.CipherSuites[i], varied.CipherSuites[j] = varied.CipherSuites[j], varied.CipherSuites[i]
		}
	} else if rand.Float64() < strength*2 {
		// 部分打乱（只交换相邻元素）
		for i := 0; i < len(varied.CipherSuites)-1; i++ {
			if rand.Float64() < 0.3 {
				varied.CipherSuites[i], varied.CipherSuites[i+1] = varied.CipherSuites[i+1], varied.CipherSuites[i]
			}
		}
	}

	varied.Name = s.generateVariedName(profile.Name, "CipherShuffle")
	varied.VariationLevel++
	return varied
}

func (s *CipherShuffleStrategy) copyProfile(profile *core.Profile) *core.Profile {
	varied := *profile
	varied.CipherSuites = make([]uint16, len(profile.CipherSuites))
	copy(varied.CipherSuites, profile.CipherSuites)
	varied.CurvePreferences = make([]tls.CurveID, len(profile.CurvePreferences))
	copy(varied.CurvePreferences, profile.CurvePreferences)
	varied.NextProtos = make([]string, len(profile.NextProtos))
	copy(varied.NextProtos, profile.NextProtos)
	varied.CreatedAt = time.Now()
	varied.IsDynamic = true
	return &varied
}

func (s *CipherShuffleStrategy) generateVariedName(baseName, strategy string) string {
	timestamp := time.Now().Unix() % 10000
	return baseName + "_" + strategy + "_" + string(rune(timestamp))
}

// CurveShuffleStrategy 曲线打乱策略
type CurveShuffleStrategy struct{}

func (s *CurveShuffleStrategy) GetName() string {
	return "curve_shuffle"
}

func (s *CurveShuffleStrategy) GetDescription() string {
	return "打乱椭圆曲线偏好顺序"
}

func (s *CurveShuffleStrategy) Apply(profile *core.Profile, strength float64) *core.Profile {
	varied := s.copyProfile(profile)

	if len(varied.CurvePreferences) <= 1 || strength <= 0 {
		return varied
	}

	if rand.Float64() < strength {
		for i := len(varied.CurvePreferences) - 1; i > 0; i-- {
			j := rand.Intn(i + 1)
			varied.CurvePreferences[i], varied.CurvePreferences[j] = varied.CurvePreferences[j], varied.CurvePreferences[i]
		}
	}

	varied.Name = s.generateVariedName(profile.Name, "CurveShuffle")
	varied.VariationLevel++
	return varied
}

func (s *CurveShuffleStrategy) copyProfile(profile *core.Profile) *core.Profile {
	return (&CipherShuffleStrategy{}).copyProfile(profile)
}

func (s *CurveShuffleStrategy) generateVariedName(baseName, strategy string) string {
	return (&CipherShuffleStrategy{}).generateVariedName(baseName, strategy)
}

// SessionCacheStrategy 会话缓存变异策略
type SessionCacheStrategy struct{}

func (s *SessionCacheStrategy) GetName() string {
	return "session_cache"
}

func (s *SessionCacheStrategy) GetDescription() string {
	return "变异会话缓存配置"
}

func (s *SessionCacheStrategy) Apply(profile *core.Profile, strength float64) *core.Profile {
	varied := (&CipherShuffleStrategy{}).copyProfile(profile)

	if strength <= 0 {
		return varied
	}

	// 变异缓存大小
	if rand.Float64() < strength {
		if varied.SessionCache.Size > 0 {
			variation := int(float64(varied.SessionCache.Size) * strength * 0.5)
			if variation == 0 {
				variation = 1
			}
			varied.SessionCache.Size += rand.Intn(variation*2) - variation

			// 确保在合理范围内
			if varied.SessionCache.Size < 8 {
				varied.SessionCache.Size = 8
			}
			if varied.SessionCache.Size > 256 {
				varied.SessionCache.Size = 256
			}
		}
	}

	// 变异清理概率
	if rand.Float64() < strength*0.5 {
		varied.SessionCache.ClearProbability = rand.Float64() * 0.3
	}

	// 变异缓存年龄
	if rand.Float64() < strength*0.3 {
		varied.SessionCache.MaxCacheAge = 60 + rand.Intn(540) // 60-600秒
	}

	varied.Name = (&CipherShuffleStrategy{}).generateVariedName(profile.Name, "SessionCache")
	varied.VariationLevel++
	return varied
}

// VersionVariationStrategy 版本变异策略
type VersionVariationStrategy struct{}

func (s *VersionVariationStrategy) GetName() string {
	return "version_variation"
}

func (s *VersionVariationStrategy) GetDescription() string {
	return "变异版本信息"
}

func (s *VersionVariationStrategy) Apply(profile *core.Profile, strength float64) *core.Profile {
	varied := (&CipherShuffleStrategy{}).copyProfile(profile)

	if strength <= 0 {
		return varied
	}

	if rand.Float64() < strength {
		// 简单的版本变异
		timestamp := time.Now().Unix() % 1000
		varied.Version = varied.Version + "_v" + string(rune(timestamp))
	}

	varied.Name = (&CipherShuffleStrategy{}).generateVariedName(profile.Name, "Version")
	varied.VariationLevel++
	return varied
}

// ALPNVariationStrategy ALPN变异策略
type ALPNVariationStrategy struct{}

func (s *ALPNVariationStrategy) GetName() string {
	return "alpn_variation"
}

func (s *ALPNVariationStrategy) GetDescription() string {
	return "变异ALPN协议列表"
}

func (s *ALPNVariationStrategy) Apply(profile *core.Profile, strength float64) *core.Profile {
	varied := (&CipherShuffleStrategy{}).copyProfile(profile)

	if len(varied.NextProtos) <= 1 || strength <= 0 {
		return varied
	}

	if rand.Float64() < strength {
		// 打乱ALPN顺序
		for i := len(varied.NextProtos) - 1; i > 0; i-- {
			j := rand.Intn(i + 1)
			varied.NextProtos[i], varied.NextProtos[j] = varied.NextProtos[j], varied.NextProtos[i]
		}
	}

	// 有时候移除ALPN
	if rand.Float64() < strength*0.2 {
		varied.NextProtos = nil
	}

	varied.Name = (&CipherShuffleStrategy{}).generateVariedName(profile.Name, "ALPN")
	varied.VariationLevel++
	return varied
}

// MinimalVariationStrategy 极简变异策略
type MinimalVariationStrategy struct{}

func (s *MinimalVariationStrategy) GetName() string {
	return "minimal_variation"
}

func (s *MinimalVariationStrategy) GetDescription() string {
	return "极简变异，只做最小改动"
}

func (s *MinimalVariationStrategy) Apply(profile *core.Profile, strength float64) *core.Profile {
	varied := (&CipherShuffleStrategy{}).copyProfile(profile)

	if strength <= 0 {
		return varied
	}

	// 只做最小的改动
	if rand.Float64() < strength {
		// 只调整会话缓存大小
		if varied.SessionCache.Size > 0 {
			variation := 1 + rand.Intn(3) // 1-3的变化
			if rand.Float64() < 0.5 {
				varied.SessionCache.Size += variation
			} else {
				varied.SessionCache.Size -= variation
			}

			if varied.SessionCache.Size < 8 {
				varied.SessionCache.Size = 8
			}
			if varied.SessionCache.Size > 128 {
				varied.SessionCache.Size = 128
			}
		}
	}

	varied.Name = (&CipherShuffleStrategy{}).generateVariedName(profile.Name, "Minimal")
	varied.VariationLevel++
	return varied
}

// CompositeVariationStrategy 复合变异策略
type CompositeVariationStrategy struct {
	strategies []VariationStrategy
	weights    []float64
}

func NewCompositeVariationStrategy(strategies []VariationStrategy, weights []float64) *CompositeVariationStrategy {
	return &CompositeVariationStrategy{
		strategies: strategies,
		weights:    weights,
	}
}

func (s *CompositeVariationStrategy) GetName() string {
	return "composite_variation"
}

func (s *CompositeVariationStrategy) GetDescription() string {
	return "复合变异策略，组合多种变异方法"
}

func (s *CompositeVariationStrategy) Apply(profile *core.Profile, strength float64) *core.Profile {
	result := profile

	for i, strategy := range s.strategies {
		weight := 1.0
		if i < len(s.weights) {
			weight = s.weights[i]
		}

		adjustedStrength := strength * weight
		if rand.Float64() < adjustedStrength {
			result = strategy.Apply(result, adjustedStrength)
		}
	}

	return result
}

// AdaptiveVariationStrategy 自适应变异策略
type AdaptiveVariationStrategy struct {
	successRates map[string]float64 // 各策略的成功率
	usageCounts  map[string]int64   // 各策略的使用次数
}

func NewAdaptiveVariationStrategy() *AdaptiveVariationStrategy {
	return &AdaptiveVariationStrategy{
		successRates: make(map[string]float64),
		usageCounts:  make(map[string]int64),
	}
}

func (s *AdaptiveVariationStrategy) GetName() string {
	return "adaptive_variation"
}

func (s *AdaptiveVariationStrategy) GetDescription() string {
	return "自适应变异策略，根据历史成功率调整"
}

func (s *AdaptiveVariationStrategy) Apply(profile *core.Profile, strength float64) *core.Profile {
	// 根据历史成功率选择最佳策略
	bestStrategy := s.selectBestStrategy()

	if bestStrategy != nil {
		return bestStrategy.Apply(profile, strength)
	}

	// 如果没有历史数据，使用默认策略
	return (&CipherShuffleStrategy{}).Apply(profile, strength)
}

func (s *AdaptiveVariationStrategy) selectBestStrategy() VariationStrategy {
	// 简化实现，实际应该根据成功率选择
	return &CipherShuffleStrategy{}
}

func (s *AdaptiveVariationStrategy) UpdateSuccessRate(strategyName string, success bool) {
	s.usageCounts[strategyName]++

	if success {
		if rate, exists := s.successRates[strategyName]; exists {
			// 使用指数移动平均更新成功率
			alpha := 0.1
			s.successRates[strategyName] = rate*(1-alpha) + alpha
		} else {
			s.successRates[strategyName] = 1.0
		}
	} else {
		if rate, exists := s.successRates[strategyName]; exists {
			alpha := 0.1
			s.successRates[strategyName] = rate * (1 - alpha)
		} else {
			s.successRates[strategyName] = 0.0
		}
	}
}
