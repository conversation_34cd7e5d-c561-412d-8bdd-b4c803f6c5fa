// Package profiles PopMart专用指纹配置
package profiles

import (
	"crypto/tls"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"

	"github.com/google/uuid"
)

// GetPopMartProfiles 获取PopMart专用指纹配置
func GetPopMartProfiles() []*core.Profile {
	now := time.Now()

	return []*core.Profile{
		// PopMart Charles超精确匹配 - 最高优先级
		{
			Name:        uuid.New().String(), // 原名: PopMart_Charles_Ultra_Exact
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeMacOS,
			Version:     "*********",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			// 完整的54个cipher suites，完全匹配Charles抓包顺序
			CipherSuites: []uint16{
				// TLS 1.3 cipher suites (服务器选择第1个)
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				// ECDHE cipher suites
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             128,
				DisableOnBurst:   false,
				MaxCacheAge:      600,
				ClearProbability: 0.02,
			},
			NextProtos:     nil, // ALPN为空，匹配抓包
			ForceHTTP2:     true,
			IsDynamic:      false,
			BaseTemplate:   "popmart_charles_exact",
			SuccessRate:    1.0,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 0,
		},

		// PopMart Chrome稳定版 - 通用版本
		{
			Name:        uuid.New().String(), // 原名: PopMart_Chrome_Stable
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeWindows,
			Version:     "120.0.6099.109",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             64,
				DisableOnBurst:   false,
				MaxCacheAge:      480,
				ClearProbability: 0.05,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     true,
			IsDynamic:      false,
			BaseTemplate:   "popmart_chrome_stable",
			SuccessRate:    1.0,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 1,
		},

		// PopMart Chrome变异版本1
		{
			Name:        uuid.New().String(), // 原名: PopMart_Chrome_Variant_1
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeWindows,
			Version:     "120.0.6099.224",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.CurveP256, tls.X25519, tls.CurveP384},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             96,
				DisableOnBurst:   false,
				MaxCacheAge:      360,
				ClearProbability: 0.08,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     true,
			IsDynamic:      false,
			BaseTemplate:   "popmart_chrome_variant",
			SuccessRate:    0.95,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 2,
		},

		// PopMart Chrome变异版本2
		{
			Name:        uuid.New().String(), // 原名: PopMart_Chrome_Variant_2
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeMacOS,
			Version:     "121.0.6167.85",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP384, tls.CurveP256},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             32,
				DisableOnBurst:   true,
				MaxCacheAge:      240,
				ClearProbability: 0.12,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     true,
			IsDynamic:      false,
			BaseTemplate:   "popmart_chrome_variant",
			SuccessRate:    0.92,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 2,
		},

		// PopMart Edge兼容版本
		{
			Name:        uuid.New().String(), // 原名: PopMart_Edge_Compatible
			BrowserType: core.BrowserTypeEdge,
			OSType:      core.OSTypeWindows,
			Version:     "120.0.2210.91",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             80,
				DisableOnBurst:   false,
				MaxCacheAge:      420,
				ClearProbability: 0.06,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     true,
			IsDynamic:      false,
			BaseTemplate:   "popmart_edge_compatible",
			SuccessRate:    0.88,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 1,
		},

		// PopMart Firefox备用版本
		{
			Name:        uuid.New().String(), // 原名: PopMart_Firefox_Backup
			BrowserType: core.BrowserTypeFirefox,
			OSType:      core.OSTypeWindows,
			Version:     "121.0",
			MinVersion:  tls.VersionTLS13,
			MaxVersion:  tls.VersionTLS13,
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: core.SessionCacheStrategy{
				Enabled:          true,
				Size:             48,
				DisableOnBurst:   false,
				MaxCacheAge:      300,
				ClearProbability: 0.04,
			},
			NextProtos:     []string{"h2", "http/1.1"},
			ForceHTTP2:     true,
			IsDynamic:      false,
			BaseTemplate:   "popmart_firefox_backup",
			SuccessRate:    0.85,
			LastUsed:       now,
			CreatedAt:      now,
			VariationLevel: 1,
		},
	}
}

// GetPopMartCharlesProfiles 获取PopMart Charles专用指纹
func GetPopMartCharlesProfiles() []*core.Profile {
	profiles := GetPopMartProfiles()
	var result []*core.Profile

	for _, profile := range profiles {
		if profile.Name == "PopMart_Charles_Ultra_Exact" {
			result = append(result, profile)
		}
	}

	return result
}

// GetPopMartChromeProfiles 获取PopMart Chrome系列指纹
func GetPopMartChromeProfiles() []*core.Profile {
	profiles := GetPopMartProfiles()
	var result []*core.Profile

	for _, profile := range profiles {
		if profile.BrowserType == core.BrowserTypeChrome {
			result = append(result, profile)
		}
	}

	return result
}

// GetPopMartHighSuccessRateProfiles 获取高成功率PopMart指纹
func GetPopMartHighSuccessRateProfiles() []*core.Profile {
	profiles := GetPopMartProfiles()
	var result []*core.Profile

	for _, profile := range profiles {
		if profile.SuccessRate >= 0.9 {
			result = append(result, profile)
		}
	}

	return result
}
