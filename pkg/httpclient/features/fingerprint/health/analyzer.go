// Package health 健康分析器
package health

import (
	"fmt"
	"sync"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/logging"
)

// Analyzer 健康分析器
type Analyzer struct {
	// 依赖组件
	tracker   *Tracker
	collector *MetricsCollector

	// 配置
	config AnalyzerConfig

	// 分析结果缓存
	analysisCache map[string]*AnalysisResult
	cacheExpiry   map[string]time.Time

	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger

	// 生命周期管理
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// AnalyzerConfig 分析器配置
type AnalyzerConfig struct {
	AnalysisInterval      time.Duration // 分析间隔
	CacheExpiry           time.Duration // 缓存过期时间
	HealthThreshold       float64       // 健康阈值
	DegradedThreshold     float64       // 降级阈值
	MinSampleSize         int64         // 最小样本量
	ConsecutiveErrorLimit int64         // 连续错误限制
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	ProfileName       string
	CurrentStatus     string
	RecommendedStatus string
	HealthScore       float64
	Confidence        float64
	Issues            []HealthIssue
	Recommendations   []string
	LastAnalyzed      time.Time
}

// HealthIssue 健康问题
type HealthIssue struct {
	Type        string
	Severity    string
	Description string
	Impact      string
	Suggestion  string
}

// NewAnalyzer 创建健康分析器
func NewAnalyzer(tracker *Tracker, collector *MetricsCollector, config AnalyzerConfig) *Analyzer {
	return &Analyzer{
		tracker:       tracker,
		collector:     collector,
		config:        config,
		analysisCache: make(map[string]*AnalysisResult),
		cacheExpiry:   make(map[string]time.Time),
		logger:        logging.GetLogger("fingerprint.health.analyzer"),
		stopCh:        make(chan struct{}),
	}
}

// Initialize 初始化分析器
func (a *Analyzer) Initialize() error {
	// 启动分析任务
	a.wg.Add(1)
	go a.analysisLoop()

	a.logger.Debug("健康分析器初始化完成")
	return nil
}

// AnalyzeProfile 分析单个指纹
func (a *Analyzer) AnalyzeProfile(profileName string) (*AnalysisResult, error) {
	a.mu.Lock()
	defer a.mu.Unlock()

	// 检查缓存
	if result, exists := a.analysisCache[profileName]; exists {
		if time.Now().Before(a.cacheExpiry[profileName]) {
			return a.copyAnalysisResult(result), nil
		}
	}

	// 执行分析
	result, err := a.performAnalysis(profileName)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	a.analysisCache[profileName] = result
	a.cacheExpiry[profileName] = time.Now().Add(a.config.CacheExpiry)

	return a.copyAnalysisResult(result), nil
}

// AnalyzeAllProfiles 分析所有指纹
func (a *Analyzer) AnalyzeAllProfiles() (map[string]*AnalysisResult, error) {
	allHealth := a.tracker.GetAllHealth()
	results := make(map[string]*AnalysisResult)

	for profileName := range allHealth {
		result, err := a.AnalyzeProfile(profileName)
		if err != nil {
			a.logger.Warn("分析指纹失败", "profile", profileName, "error", err)
			continue
		}
		results[profileName] = result
	}

	return results, nil
}

// GetHealthSummary 获取健康摘要
func (a *Analyzer) GetHealthSummary() (*HealthSummary, error) {
	globalMetrics := a.collector.GetGlobalMetrics()

	summary := &HealthSummary{
		TotalProfiles:      globalMetrics.TotalProfiles,
		HealthyProfiles:    globalMetrics.HealthyProfiles,
		DegradedProfiles:   globalMetrics.DegradedProfiles,
		BlockedProfiles:    globalMetrics.BlockedProfiles,
		OverallHealthScore: a.calculateOverallHealthScore(globalMetrics),
		LastUpdated:        time.Now(),
	}

	// 分析趋势
	summary.Trends = a.analyzeTrends()

	// 识别关键问题
	summary.CriticalIssues = a.identifyCriticalIssues()

	// 生成建议
	summary.Recommendations = a.generateGlobalRecommendations(summary)

	return summary, nil
}

// HealthSummary 健康摘要
type HealthSummary struct {
	TotalProfiles      int
	HealthyProfiles    int
	DegradedProfiles   int
	BlockedProfiles    int
	OverallHealthScore float64
	Trends             *HealthTrends
	CriticalIssues     []CriticalIssue
	Recommendations    []string
	LastUpdated        time.Time
}

// HealthTrends 健康趋势
type HealthTrends struct {
	SuccessRateTrend  string // "improving", "stable", "declining"
	ResponseTimeTrend string
	ErrorRateTrend    string
	BlockRateTrend    string
}

// CriticalIssue 关键问题
type CriticalIssue struct {
	Type             string
	Description      string
	AffectedProfiles []string
	Impact           string
	Urgency          string
}

// performAnalysis 执行分析
func (a *Analyzer) performAnalysis(profileName string) (*AnalysisResult, error) {
	// 获取健康状态和指标
	health := a.tracker.GetHealth(profileName)
	metrics := a.collector.GetProfileMetrics(profileName)

	if health == nil {
		return nil, fmt.Errorf("未找到指纹健康状态: %s", profileName)
	}

	result := &AnalysisResult{
		ProfileName:   profileName,
		CurrentStatus: health.Status,
		LastAnalyzed:  time.Now(),
	}

	// 计算健康评分
	result.HealthScore = a.calculateHealthScore(health, metrics)

	// 确定推荐状态
	result.RecommendedStatus = a.determineRecommendedStatus(result.HealthScore, health)

	// 计算置信度
	result.Confidence = a.calculateConfidence(health, metrics)

	// 识别问题
	result.Issues = a.identifyIssues(health, metrics)

	// 生成建议
	result.Recommendations = a.generateRecommendations(result)

	return result, nil
}

// calculateHealthScore 计算健康评分
func (a *Analyzer) calculateHealthScore(health *core.HealthStatus, metrics *ProfileMetrics) float64 {
	score := 0.0

	// 成功率权重 (40%)
	successRateScore := health.SuccessRate * 0.4
	score += successRateScore

	// 连续错误权重 (20%)
	errorScore := 0.0
	if health.ConsecutiveErrors == 0 {
		errorScore = 0.2
	} else if health.ConsecutiveErrors < a.config.ConsecutiveErrorLimit {
		errorScore = 0.2 * (1.0 - float64(health.ConsecutiveErrors)/float64(a.config.ConsecutiveErrorLimit))
	}
	score += errorScore

	// 响应时间权重 (20%)
	responseTimeScore := 0.2
	if health.AvgResponseTime > 5*time.Second {
		responseTimeScore *= 0.5
	} else if health.AvgResponseTime > 2*time.Second {
		responseTimeScore *= 0.8
	}
	score += responseTimeScore

	// 使用频率权重 (10%)
	usageScore := 0.1
	if metrics != nil && metrics.UseCount > 0 {
		// 最近使用过的指纹得分更高
		timeSinceUse := time.Since(health.LastUsed).Hours()
		if timeSinceUse < 1 {
			usageScore = 0.1
		} else if timeSinceUse < 24 {
			usageScore = 0.1 * (1.0 - timeSinceUse/24.0)
		} else {
			usageScore = 0.05
		}
	}
	score += usageScore

	// 状态权重 (10%)
	statusScore := 0.0
	switch health.Status {
	case core.HealthStatusHealthy:
		statusScore = 0.1
	case core.HealthStatusDegraded:
		statusScore = 0.05
	case core.HealthStatusBlocked:
		statusScore = 0.0
	}
	score += statusScore

	return score
}

// determineRecommendedStatus 确定推荐状态
func (a *Analyzer) determineRecommendedStatus(healthScore float64, health *core.HealthStatus) string {
	// 基于健康评分确定状态
	if healthScore >= a.config.HealthThreshold {
		return core.HealthStatusHealthy
	} else if healthScore >= a.config.DegradedThreshold {
		return core.HealthStatusDegraded
	} else {
		return core.HealthStatusBlocked
	}
}

// calculateConfidence 计算置信度
func (a *Analyzer) calculateConfidence(health *core.HealthStatus, metrics *ProfileMetrics) float64 {
	confidence := 0.5 // 基础置信度

	// 样本量影响置信度
	if health.TotalRequests >= a.config.MinSampleSize {
		confidence += 0.3
	} else if health.TotalRequests > 0 {
		confidence += 0.3 * (float64(health.TotalRequests) / float64(a.config.MinSampleSize))
	}

	// 时间因素影响置信度
	timeSinceUse := time.Since(health.LastUsed).Hours()
	if timeSinceUse < 1 {
		confidence += 0.2
	} else if timeSinceUse < 24 {
		confidence += 0.2 * (1.0 - timeSinceUse/24.0)
	}

	// 确保置信度在0-1范围内
	if confidence > 1.0 {
		confidence = 1.0
	}
	if confidence < 0.0 {
		confidence = 0.0
	}

	return confidence
}

// identifyIssues 识别问题
func (a *Analyzer) identifyIssues(health *core.HealthStatus, metrics *ProfileMetrics) []HealthIssue {
	var issues []HealthIssue

	// 检查成功率问题
	if health.SuccessRate < a.config.DegradedThreshold {
		issues = append(issues, HealthIssue{
			Type:        "low_success_rate",
			Severity:    "high",
			Description: fmt.Sprintf("成功率过低: %.2f%%", health.SuccessRate*100),
			Impact:      "影响请求成功率",
			Suggestion:  "考虑更换指纹或检查网络环境",
		})
	}

	// 检查连续错误问题
	if health.ConsecutiveErrors >= a.config.ConsecutiveErrorLimit {
		issues = append(issues, HealthIssue{
			Type:        "consecutive_errors",
			Severity:    "high",
			Description: fmt.Sprintf("连续错误数过多: %d", health.ConsecutiveErrors),
			Impact:      "可能导致指纹被封禁",
			Suggestion:  "立即停止使用该指纹",
		})
	}

	// 检查响应时间问题
	if health.AvgResponseTime > 5*time.Second {
		issues = append(issues, HealthIssue{
			Type:        "slow_response",
			Severity:    "medium",
			Description: fmt.Sprintf("响应时间过慢: %v", health.AvgResponseTime),
			Impact:      "影响请求效率",
			Suggestion:  "检查网络连接或考虑更换指纹",
		})
	}

	// 检查被封问题
	if health.Status == core.HealthStatusBlocked {
		issues = append(issues, HealthIssue{
			Type:        "blocked_status",
			Severity:    "critical",
			Description: "指纹已被风控系统封禁",
			Impact:      "无法正常使用",
			Suggestion:  "停止使用并等待恢复或更换新指纹",
		})
	}

	return issues
}

// generateRecommendations 生成建议
func (a *Analyzer) generateRecommendations(result *AnalysisResult) []string {
	var recommendations []string

	// 基于健康评分生成建议
	if result.HealthScore < 0.3 {
		recommendations = append(recommendations, "建议立即停止使用该指纹")
		recommendations = append(recommendations, "考虑从指纹池中移除")
	} else if result.HealthScore < 0.6 {
		recommendations = append(recommendations, "建议降低使用频率")
		recommendations = append(recommendations, "监控后续表现")
	} else if result.HealthScore > 0.8 {
		recommendations = append(recommendations, "表现良好，可以继续使用")
		recommendations = append(recommendations, "考虑增加使用频率")
	}

	// 基于具体问题生成建议
	for _, issue := range result.Issues {
		switch issue.Type {
		case "low_success_rate":
			recommendations = append(recommendations, "检查指纹配置是否正确")
		case "consecutive_errors":
			recommendations = append(recommendations, "立即轮换到其他指纹")
		case "slow_response":
			recommendations = append(recommendations, "检查网络环境")
		case "blocked_status":
			recommendations = append(recommendations, "等待24小时后重试")
		}
	}

	return recommendations
}

// calculateOverallHealthScore 计算整体健康评分
func (a *Analyzer) calculateOverallHealthScore(metrics *GlobalMetrics) float64 {
	if metrics.TotalProfiles == 0 {
		return 0.0
	}

	// 基于健康指纹比例计算
	healthyRatio := float64(metrics.HealthyProfiles) / float64(metrics.TotalProfiles)
	degradedRatio := float64(metrics.DegradedProfiles) / float64(metrics.TotalProfiles)

	// 健康指纹权重1.0，降级指纹权重0.5，封禁指纹权重0.0
	score := healthyRatio*1.0 + degradedRatio*0.5

	// 考虑整体成功率
	if metrics.OverallSuccessRate > 0 {
		score = (score + metrics.OverallSuccessRate) / 2.0
	}

	return score
}

// analyzeTrends 分析趋势
func (a *Analyzer) analyzeTrends() *HealthTrends {
	// 简化实现，实际应该基于历史数据
	return &HealthTrends{
		SuccessRateTrend:  "stable",
		ResponseTimeTrend: "stable",
		ErrorRateTrend:    "stable",
		BlockRateTrend:    "stable",
	}
}

// identifyCriticalIssues 识别关键问题
func (a *Analyzer) identifyCriticalIssues() []CriticalIssue {
	var issues []CriticalIssue

	// 检查被封指纹比例
	blockedProfiles := a.tracker.GetBlockedProfiles()
	if len(blockedProfiles) > 0 {
		issues = append(issues, CriticalIssue{
			Type:             "high_block_rate",
			Description:      fmt.Sprintf("有%d个指纹被封禁", len(blockedProfiles)),
			AffectedProfiles: blockedProfiles,
			Impact:           "影响系统可用性",
			Urgency:          "high",
		})
	}

	return issues
}

// generateGlobalRecommendations 生成全局建议
func (a *Analyzer) generateGlobalRecommendations(summary *HealthSummary) []string {
	var recommendations []string

	healthyRatio := float64(summary.HealthyProfiles) / float64(summary.TotalProfiles)

	if healthyRatio < 0.5 {
		recommendations = append(recommendations, "健康指纹比例过低，建议增加新指纹")
	}

	if summary.BlockedProfiles > 0 {
		recommendations = append(recommendations, "存在被封指纹，建议检查使用策略")
	}

	if summary.OverallHealthScore > 0.8 {
		recommendations = append(recommendations, "整体健康状况良好")
	}

	return recommendations
}

// copyAnalysisResult 复制分析结果
func (a *Analyzer) copyAnalysisResult(result *AnalysisResult) *AnalysisResult {
	copied := &AnalysisResult{
		ProfileName:       result.ProfileName,
		CurrentStatus:     result.CurrentStatus,
		RecommendedStatus: result.RecommendedStatus,
		HealthScore:       result.HealthScore,
		Confidence:        result.Confidence,
		LastAnalyzed:      result.LastAnalyzed,
	}

	// 复制问题列表
	copied.Issues = make([]HealthIssue, len(result.Issues))
	copy(copied.Issues, result.Issues)

	// 复制建议列表
	copied.Recommendations = make([]string, len(result.Recommendations))
	copy(copied.Recommendations, result.Recommendations)

	return copied
}

// analysisLoop 分析循环
func (a *Analyzer) analysisLoop() {
	defer a.wg.Done()

	ticker := time.NewTicker(a.config.AnalysisInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			a.performPeriodicAnalysis()
		case <-a.stopCh:
			return
		}
	}
}

// performPeriodicAnalysis 执行定期分析
func (a *Analyzer) performPeriodicAnalysis() {
	a.mu.Lock()
	defer a.mu.Unlock()

	// 清理过期缓存
	now := time.Now()
	for profileName, expiry := range a.cacheExpiry {
		if now.After(expiry) {
			delete(a.analysisCache, profileName)
			delete(a.cacheExpiry, profileName)
		}
	}

	a.logger.Debug("定期分析完成", "cached_results", len(a.analysisCache))
}

// Close 关闭分析器
func (a *Analyzer) Close() error {
	close(a.stopCh)
	a.wg.Wait()
	a.logger.Debug("健康分析器已关闭")
	return nil
}
