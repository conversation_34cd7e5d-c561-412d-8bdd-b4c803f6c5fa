// Package health 健康状态追踪
package health

import (
	"sync"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"
	"go-monitor/pkg/logging"
)

// Tracker 健康状态追踪器
type Tracker struct {
	// 健康状态存储
	healthMap map[string]*core.HealthStatus

	// 配置
	config TrackerConfig

	// 并发控制
	mu     sync.RWMutex
	logger logging.Logger

	// 生命周期管理
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// TrackerConfig 追踪器配置
type TrackerConfig struct {
	MaxConsecutiveErrors int           // 最大连续错误数
	HealthCheckInterval  time.Duration // 健康检查间隔
	CleanupInterval      time.Duration // 清理间隔
	MaxIdleTime          time.Duration // 最大空闲时间
}

// NewTracker 创建健康状态追踪器
func NewTracker(config TrackerConfig) *Tracker {
	return &Tracker{
		healthMap: make(map[string]*core.HealthStatus),
		config:    config,
		logger:    logging.GetLogger("fingerprint.health.tracker"),
		stopCh:    make(chan struct{}),
	}
}

// Initialize 初始化追踪器
func (t *Tracker) Initialize() error {
	// 启动维护任务
	t.wg.Add(1)
	go t.maintenanceLoop()

	t.logger.Debug("健康状态追踪器初始化完成")
	return nil
}

// RecordSuccess 记录成功请求
func (t *Tracker) RecordSuccess(profileName string, responseTime time.Duration) {
	t.mu.Lock()
	defer t.mu.Unlock()

	health := t.getOrCreateHealth(profileName)

	// 更新统计
	health.TotalRequests++
	health.SuccessRequests++
	health.LastUsed = time.Now()

	// 重置连续错误计数
	health.ConsecutiveErrors = 0

	// 更新平均响应时间
	t.updateAvgResponseTime(health, responseTime)

	// 更新成功率
	t.updateSuccessRate(health)

	// 更新状态
	t.updateHealthStatus(health)

	t.logger.Debug("记录成功请求", "profile", profileName, "response_time", responseTime)
}

// RecordFailure 记录失败请求
func (t *Tracker) RecordFailure(profileName string, err error) {
	t.mu.Lock()
	defer t.mu.Unlock()

	health := t.getOrCreateHealth(profileName)

	// 更新统计
	health.TotalRequests++
	health.FailedRequests++
	health.ConsecutiveErrors++
	health.LastUsed = time.Now()

	// 记录错误信息
	if err != nil {
		health.LastError = err.Error()
	}

	// 更新成功率
	t.updateSuccessRate(health)

	// 更新状态
	t.updateHealthStatus(health)

	t.logger.Debug("记录失败请求", "profile", profileName, "error", err)
}

// RecordBlocked 记录风控
func (t *Tracker) RecordBlocked(profileName string, statusCode int) {
	t.mu.Lock()
	defer t.mu.Unlock()

	health := t.getOrCreateHealth(profileName)

	// 更新统计
	health.TotalRequests++
	health.BlockedRequests++
	health.ConsecutiveErrors++
	health.LastUsed = time.Now()

	// 记录被风控时间
	health.LastBlockedAt = time.Now()

	// 强制设置为被封状态
	health.Status = core.HealthStatusBlocked

	t.logger.Debug("记录风控事件", "profile", profileName, "status_code", statusCode)
}

// GetHealth 获取健康状态
func (t *Tracker) GetHealth(profileName string) *core.HealthStatus {
	t.mu.RLock()
	defer t.mu.RUnlock()

	if health, exists := t.healthMap[profileName]; exists {
		// 返回副本
		return t.copyHealthStatus(health)
	}

	// 返回默认健康状态
	return &core.HealthStatus{
		ProfileName:     profileName,
		Status:          core.HealthStatusHealthy,
		SuccessRate:     1.0,
		AvgResponseTime: 0,
		LastUsed:        time.Now(),
	}
}

// ShouldRotate 检查是否应该轮换
func (t *Tracker) ShouldRotate(profileName string) bool {
	t.mu.RLock()
	defer t.mu.RUnlock()

	health, exists := t.healthMap[profileName]
	if !exists {
		return false
	}

	// 检查连续错误数
	if health.ConsecutiveErrors >= int64(t.config.MaxConsecutiveErrors) {
		return true
	}

	// 检查是否被风控
	if health.Status == core.HealthStatusBlocked {
		return true
	}

	// 检查成功率
	if health.SuccessRate < 0.5 && health.TotalRequests > 10 {
		return true
	}

	return false
}

// GetAllHealth 获取所有健康状态
func (t *Tracker) GetAllHealth() map[string]*core.HealthStatus {
	t.mu.RLock()
	defer t.mu.RUnlock()

	result := make(map[string]*core.HealthStatus)
	for name, health := range t.healthMap {
		result[name] = t.copyHealthStatus(health)
	}

	return result
}

// GetHealthyProfiles 获取健康的指纹列表
func (t *Tracker) GetHealthyProfiles() []string {
	t.mu.RLock()
	defer t.mu.RUnlock()

	var healthy []string
	for name, health := range t.healthMap {
		if health.Status == core.HealthStatusHealthy {
			healthy = append(healthy, name)
		}
	}

	return healthy
}

// GetBlockedProfiles 获取被封的指纹列表
func (t *Tracker) GetBlockedProfiles() []string {
	t.mu.RLock()
	defer t.mu.RUnlock()

	var blocked []string
	for name, health := range t.healthMap {
		if health.Status == core.HealthStatusBlocked {
			blocked = append(blocked, name)
		}
	}

	return blocked
}

// ResetHealth 重置健康状态
func (t *Tracker) ResetHealth(profileName string) {
	t.mu.Lock()
	defer t.mu.Unlock()

	if health, exists := t.healthMap[profileName]; exists {
		health.TotalRequests = 0
		health.SuccessRequests = 0
		health.FailedRequests = 0
		health.ConsecutiveErrors = 0
		health.BlockedRequests = 0
		health.SuccessRate = 1.0
		health.Status = core.HealthStatusHealthy
		health.LastError = ""
		health.LastBlockedAt = time.Time{}
		health.LastUsed = time.Now()

		t.logger.Info("重置健康状态", "profile", profileName)
	}
}

// 内部方法

// getOrCreateHealth 获取或创建健康状态
func (t *Tracker) getOrCreateHealth(profileName string) *core.HealthStatus {
	if health, exists := t.healthMap[profileName]; exists {
		return health
	}

	// 创建新的健康状态
	health := &core.HealthStatus{
		ProfileName:       profileName,
		TotalRequests:     0,
		SuccessRequests:   0,
		FailedRequests:    0,
		ConsecutiveErrors: 0,
		BlockedRequests:   0,
		SuccessRate:       1.0,
		AvgResponseTime:   0,
		Status:            core.HealthStatusHealthy,
		LastUsed:          time.Now(),
		LastError:         "",
		LastBlockedAt:     time.Time{},
	}

	t.healthMap[profileName] = health
	return health
}

// updateAvgResponseTime 更新平均响应时间
func (t *Tracker) updateAvgResponseTime(health *core.HealthStatus, responseTime time.Duration) {
	if health.SuccessRequests == 1 {
		health.AvgResponseTime = responseTime
	} else {
		// 计算移动平均
		alpha := 0.1 // 平滑因子
		health.AvgResponseTime = time.Duration(float64(health.AvgResponseTime)*(1-alpha) + float64(responseTime)*alpha)
	}
}

// updateSuccessRate 更新成功率
func (t *Tracker) updateSuccessRate(health *core.HealthStatus) {
	if health.TotalRequests > 0 {
		health.SuccessRate = float64(health.SuccessRequests) / float64(health.TotalRequests)
	}
}

// updateHealthStatus 更新健康状态
func (t *Tracker) updateHealthStatus(health *core.HealthStatus) {
	// 如果已经被标记为封禁，需要时间恢复
	if health.Status == core.HealthStatusBlocked {
		// 检查是否可以恢复
		if time.Since(health.LastBlockedAt) > 30*time.Minute && health.ConsecutiveErrors == 0 {
			health.Status = core.HealthStatusDegraded
		}
		return
	}

	// 根据连续错误数和成功率判断状态
	if health.ConsecutiveErrors >= int64(t.config.MaxConsecutiveErrors) {
		health.Status = core.HealthStatusDegraded
	} else if health.SuccessRate < 0.7 && health.TotalRequests > 5 {
		health.Status = core.HealthStatusDegraded
	} else {
		health.Status = core.HealthStatusHealthy
	}
}

// copyHealthStatus 复制健康状态
func (t *Tracker) copyHealthStatus(health *core.HealthStatus) *core.HealthStatus {
	return &core.HealthStatus{
		ProfileName:       health.ProfileName,
		TotalRequests:     health.TotalRequests,
		SuccessRequests:   health.SuccessRequests,
		FailedRequests:    health.FailedRequests,
		ConsecutiveErrors: health.ConsecutiveErrors,
		BlockedRequests:   health.BlockedRequests,
		SuccessRate:       health.SuccessRate,
		AvgResponseTime:   health.AvgResponseTime,
		Status:            health.Status,
		LastUsed:          health.LastUsed,
		LastError:         health.LastError,
		LastBlockedAt:     health.LastBlockedAt,
	}
}

// maintenanceLoop 维护循环
func (t *Tracker) maintenanceLoop() {
	defer t.wg.Done()

	ticker := time.NewTicker(t.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			t.performMaintenance()
		case <-t.stopCh:
			return
		}
	}
}

// performMaintenance 执行维护
func (t *Tracker) performMaintenance() {
	t.mu.Lock()
	defer t.mu.Unlock()

	now := time.Now()
	var toDelete []string

	// 清理长时间未使用的健康状态
	for name, health := range t.healthMap {
		if now.Sub(health.LastUsed) > t.config.MaxIdleTime {
			toDelete = append(toDelete, name)
		}
	}

	// 删除过期的健康状态
	for _, name := range toDelete {
		delete(t.healthMap, name)
	}

	if len(toDelete) > 0 {
		t.logger.Debug("清理过期健康状态", "count", len(toDelete))
	}
}

// Close 关闭追踪器
func (t *Tracker) Close() error {
	close(t.stopCh)
	t.wg.Wait()
	t.logger.Debug("健康状态追踪器已关闭")
	return nil
}
