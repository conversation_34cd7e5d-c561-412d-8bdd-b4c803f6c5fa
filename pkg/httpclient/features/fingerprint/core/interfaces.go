// Package core 定义指纹管理的核心接口和抽象
package core

import (
	"context"
	"crypto/tls"
	"time"
)

// FingerprintManager 指纹管理器核心接口
type FingerprintManager interface {
	// 初始化
	Initialize(configPath string) error

	// 指纹选择
	SelectFingerprint(ctx context.Context, req *FingerprintRequest) (*Profile, error)

	// TLS配置创建
	CreateTLSConfig(profile *Profile, serverName string) (*tls.Config, error)

	// 统计信息
	GetStats() map[string]interface{}

	// 关闭
	Close() error
}

// PoolManager 指纹池管理器接口
type PoolManager interface {
	// 获取指纹
	GetFingerprint(ctx context.Context, req *FingerprintRequest) (*Profile, error)

	// 更新指纹健康状态
	UpdateHealth(profileName string, success bool, responseTime time.Duration)

	// 获取池统计信息
	GetPoolStats() map[string]interface{}

	// 清理过期指纹
	Cleanup() error
}

// HealthTracker 健康状态追踪器接口
type HealthTracker interface {
	// 记录成功请求
	RecordSuccess(profileName string, responseTime time.Duration)

	// 记录失败请求
	RecordFailure(profileName string, err error)

	// 记录风控
	RecordBlocked(profileName string, statusCode int)

	// 获取健康状态
	GetHealth(profileName string) *HealthStatus

	// 检查是否应该轮换
	ShouldRotate(profileName string) bool
}

// ProfileRegistry 指纹注册表接口
type ProfileRegistry interface {
	// 注册指纹
	Register(profile *Profile) error

	// 获取指纹
	Get(name string) (*Profile, bool)

	// 获取所有指纹
	GetAll() map[string]*Profile

	// 按条件查找指纹
	Find(filter ProfileFilter) []*Profile
}

// DynamicGenerator 动态指纹生成器接口
type DynamicGenerator interface {
	// 生成指纹
	Generate(ctx context.Context, req *GenerationRequest) (*Profile, error)

	// 生成变异指纹
	GenerateVariation(base *Profile, strength float64) (*Profile, error)

	// 更新成功率
	UpdateSuccessRate(profileName string, rate float64)
}

// AntiDetectionEngine 反风控引擎接口
type AntiDetectionEngine interface {
	// 分析响应
	AnalyzeResponse(serverName string, statusCode int, body string, responseTime time.Duration, profileName string)

	// 检查指纹是否被封
	IsBlocked(profileName string) bool

	// 获取推荐策略
	GetRecommendedStrategy(serverName string) *DetectionStrategy
}

// ConfigManager 配置管理器接口
type ConfigManager interface {
	// 加载配置
	Load(path string) error

	// 获取全局配置
	GetGlobal() *GlobalConfig

	// 获取域名配置
	GetDomain(domain string) *DomainConfig

	// 重新加载配置
	Reload() error
}

// FingerprintRequest 指纹请求结构体
type FingerprintRequest struct {
	ServerName      string   // 服务器名称
	PoolName        string   // 指纹池名称
	FingerprintName string   // 具体指纹名称（向后兼容）
	ExcludeProfiles []string // 排除的指纹列表
	Region          string   // 地区偏好
	ForceProfile    string   // 强制使用的指纹

	// 轮转控制
	ForceRotation   bool   // 强制轮转指纹（每次请求都使用不同指纹）
	LastUsedProfile string // 上次使用的指纹（用于轮转排除）
}

// Profile 指纹配置结构体（简化版）
type Profile struct {
	Name             string
	MinVersion       uint16
	MaxVersion       uint16
	CipherSuites     []uint16
	CurvePreferences []tls.CurveID
	SessionCache     SessionCacheStrategy
	NextProtos       []string
	ForceHTTP2       bool

	// 元数据
	BrowserType    string
	OSType         string
	Version        string
	IsDynamic      bool
	BaseTemplate   string
	SuccessRate    float64
	LastUsed       time.Time
	CreatedAt      time.Time
	VariationLevel int
}

// SessionCacheStrategy 会话缓存策略
type SessionCacheStrategy struct {
	Enabled          bool
	Size             int
	DisableOnBurst   bool
	MaxCacheAge      int
	ClearProbability float64
}

// HealthStatus 健康状态
type HealthStatus struct {
	ProfileName       string
	TotalRequests     int64
	SuccessRequests   int64
	FailedRequests    int64
	ConsecutiveErrors int64
	BlockedRequests   int64
	SuccessRate       float64
	AvgResponseTime   time.Duration
	Status            string
	LastUsed          time.Time
	LastError         string
	LastBlockedAt     time.Time
}

// ProfileFilter 指纹过滤器
type ProfileFilter struct {
	BrowserType    string
	OSType         string
	MinSuccessRate float64
	ExcludeNames   []string
	Region         string
	IsDynamic      *bool
}

// GenerationRequest 生成请求
type GenerationRequest struct {
	Region         string
	BrowserType    string
	OSType         string
	BaseTemplate   string
	VariationLevel int
	ServerName     string
}

// DetectionStrategy 检测策略
type DetectionStrategy struct {
	RecommendedProfiles []string
	AvoidProfiles       []string
	CooldownDuration    time.Duration
	SwitchProbability   float64
}

// GlobalConfig 全局配置（简化版）
type GlobalConfig struct {
	EnableTLSFingerprint bool
	DefaultPoolName      string
	MaxPoolSize          int
	RotationInterval     time.Duration
}

// DomainConfig 域名配置
type DomainConfig struct {
	Domain              string
	PreferredProfiles   []string
	ExcludedProfiles    []string
	CustomPoolName      string
	RotationProbability float64
}

// 常量定义
const (
	// 健康状态
	HealthStatusHealthy  = "healthy"
	HealthStatusDegraded = "degraded"
	HealthStatusBlocked  = "blocked"

	// 选择策略
	SelectionStrategyRandom            = "random"
	SelectionStrategyLeastRecentlyUsed = "least_recently_used"
	SelectionStrategySuccessRate       = "success_rate"
	SelectionStrategyRoundRobin        = "round_robin"

	// 浏览器类型
	BrowserTypeChrome  = "chrome"
	BrowserTypeFirefox = "firefox"
	BrowserTypeSafari  = "safari"
	BrowserTypeEdge    = "edge"

	// 操作系统类型
	OSTypeWindows = "windows"
	OSTypeMacOS   = "macos"
	OSTypeLinux   = "linux"
	OSTypeAndroid = "android"
	OSTypeIOS     = "ios"
)
