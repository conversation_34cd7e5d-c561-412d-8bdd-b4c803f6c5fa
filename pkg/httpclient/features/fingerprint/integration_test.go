// Package fingerprint 集成测试
package fingerprint

import (
	"context"
	"testing"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint/core"
)

// TestManagerIntegration 测试管理器集成
func TestManagerIntegration(t *testing.T) {
	// 创建管理器
	manager := NewManager()

	// 初始化（使用空配置路径，将使用默认配置）
	err := manager.Initialize("")
	if err != nil {
		t.Fatalf("初始化管理器失败: %v", err)
	}
	defer manager.Close()

	// 测试指纹选择
	t.Run("指纹选择测试", func(t *testing.T) {
		req := &core.FingerprintRequest{
			PoolName: "default",
		}

		profile, err := manager.SelectFingerprint(context.Background(), req)
		if err != nil {
			t.Errorf("选择指纹失败: %v", err)
			return
		}

		if profile == nil {
			t.Error("返回的指纹为空")
			return
		}

		t.Logf("选择的指纹: %s", profile.Name)
	})

	// 测试TLS配置创建
	t.Run("TLS配置创建测试", func(t *testing.T) {
		req := &core.FingerprintRequest{
			PoolName: "default",
		}

		profile, err := manager.SelectFingerprint(context.Background(), req)
		if err != nil {
			t.Errorf("选择指纹失败: %v", err)
			return
		}

		tlsConfig, err := manager.CreateTLSConfig(profile, "example.com")
		if err != nil {
			t.Errorf("创建TLS配置失败: %v", err)
			return
		}

		if tlsConfig.ServerName != "example.com" {
			t.Errorf("TLS配置服务器名称错误: 期望 example.com, 实际 %s", tlsConfig.ServerName)
		}

		if len(tlsConfig.CipherSuites) == 0 {
			t.Error("TLS配置密码套件为空")
		}

		t.Logf("TLS配置创建成功，密码套件数量: %d", len(tlsConfig.CipherSuites))
	})

	// 测试健康状态更新
	t.Run("健康状态更新测试", func(t *testing.T) {
		profileName := "test_profile"

		// 记录成功请求
		manager.UpdateHealth(profileName, true, 100*time.Millisecond)

		// 记录失败请求
		manager.UpdateHealth(profileName, false, 200*time.Millisecond)

		// 等待一下让指标收集器处理
		time.Sleep(100 * time.Millisecond)

		t.Log("健康状态更新测试完成")
	})

	// 测试响应分析
	t.Run("响应分析测试", func(t *testing.T) {
		serverName := "test.example.com"
		profileName := "test_profile"

		// 测试正常响应
		manager.AnalyzeResponse(serverName, 200, "success", 100*time.Millisecond, profileName)

		// 测试风控响应
		manager.AnalyzeResponse(serverName, 403, "forbidden", 50*time.Millisecond, profileName)

		// 测试PopMart风控响应
		manager.AnalyzeResponse("prod-intl-api.popmart.com", 470, "blocked", 30*time.Millisecond, profileName)

		t.Log("响应分析测试完成")
	})

	// 测试统计信息
	t.Run("统计信息测试", func(t *testing.T) {
		stats := manager.GetStats()

		if stats == nil {
			t.Error("统计信息为空")
			return
		}

		// 检查必要的统计字段
		if _, exists := stats["pool_stats"]; !exists {
			t.Error("缺少池统计信息")
		}

		if _, exists := stats["health_stats"]; !exists {
			t.Error("缺少健康统计信息")
		}

		if _, exists := stats["generator_stats"]; !exists {
			t.Error("缺少生成器统计信息")
		}

		if _, exists := stats["registry_stats"]; !exists {
			t.Error("缺少注册表统计信息")
		}

		t.Logf("统计信息: %+v", stats)
	})
}

// TestPoolSelection 测试池选择
func TestPoolSelection(t *testing.T) {
	manager := NewManager()
	err := manager.Initialize("")
	if err != nil {
		t.Fatalf("初始化管理器失败: %v", err)
	}
	defer manager.Close()

	// 测试不同池的选择
	pools := []string{"default", "popmart-us", "popmart-eu", "popmart-asia"}

	for _, poolName := range pools {
		t.Run("池选择测试_"+poolName, func(t *testing.T) {
			req := &core.FingerprintRequest{
				PoolName: poolName,
			}

			profile, err := manager.SelectFingerprint(context.Background(), req)
			if err != nil {
				// 某些池可能不存在，这是正常的
				t.Logf("池 %s 选择失败（可能不存在）: %v", poolName, err)
				return
			}

			if profile == nil {
				t.Errorf("池 %s 返回的指纹为空", poolName)
				return
			}

			t.Logf("池 %s 选择的指纹: %s", poolName, profile.Name)
		})
	}
}

// TestCompatibility 测试向后兼容性
func TestCompatibility(t *testing.T) {
	// 测试兼容性管理器
	compatManager := NewCompatManager()
	err := compatManager.Initialize("")
	if err != nil {
		t.Fatalf("初始化兼容性管理器失败: %v", err)
	}
	defer compatManager.Close()

	// 测试兼容接口
	t.Run("兼容接口测试", func(t *testing.T) {
		req := &core.FingerprintRequest{
			PoolName: "default",
		}

		profile, err := compatManager.SelectFingerprint(context.Background(), req)
		if err != nil {
			t.Errorf("兼容接口选择指纹失败: %v", err)
			return
		}

		if profile == nil {
			t.Error("兼容接口返回的指纹为空")
			return
		}

		// 测试TLS配置创建
		tlsConfig, err := compatManager.CreateTLSConfig(profile, "example.com")
		if err != nil {
			t.Errorf("兼容接口创建TLS配置失败: %v", err)
			return
		}

		if tlsConfig == nil {
			t.Error("兼容接口返回的TLS配置为空")
			return
		}

		t.Logf("兼容接口测试成功，指纹: %s", profile.Name)
	})

	// 测试爬虫指纹池
	t.Run("爬虫指纹池测试", func(t *testing.T) {
		spiderPool := NewSpiderFingerprintPool("test_spider")

		// 添加一些测试指纹
		testProfile := &core.Profile{
			Name:        "test_profile_1",
			BrowserType: core.BrowserTypeChrome,
			OSType:      core.OSTypeWindows,
		}

		spiderPool.AddProfile(testProfile)

		// 获取指纹
		profile, err := spiderPool.GetProfile([]string{})
		if err != nil {
			t.Errorf("从爬虫池获取指纹失败: %v", err)
			return
		}

		if profile == nil {
			t.Error("爬虫池返回的指纹为空")
			return
		}

		if profile.Name != "test_profile_1" {
			t.Errorf("爬虫池返回的指纹名称错误: 期望 test_profile_1, 实际 %s", profile.Name)
		}

		// 测试统计信息
		stats := spiderPool.GetStats()
		if stats == nil {
			t.Error("爬虫池统计信息为空")
			return
		}

		t.Logf("爬虫池测试成功，统计信息: %+v", stats)
	})
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	manager := NewManager()

	// 测试未初始化的管理器
	t.Run("未初始化管理器测试", func(t *testing.T) {
		req := &core.FingerprintRequest{
			PoolName: "default",
		}

		_, err := manager.SelectFingerprint(context.Background(), req)
		if err == nil {
			t.Error("未初始化的管理器应该返回错误")
		}

		if err != core.ErrManagerNotInitialized {
			t.Errorf("错误类型不正确: 期望 %v, 实际 %v", core.ErrManagerNotInitialized, err)
		}
	})

	// 测试无效配置
	t.Run("无效配置测试", func(t *testing.T) {
		// 这里可以测试无效的配置文件路径
		err := manager.Initialize("/invalid/path/config.yaml")
		if err == nil {
			t.Error("无效配置路径应该返回错误")
		}

		t.Logf("无效配置错误: %v", err)
	})
}

// BenchmarkFingerprintSelection 指纹选择性能测试
func BenchmarkFingerprintSelection(b *testing.B) {
	manager := NewManager()
	err := manager.Initialize("")
	if err != nil {
		b.Fatalf("初始化管理器失败: %v", err)
	}
	defer manager.Close()

	req := &core.FingerprintRequest{
		PoolName: "default",
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := manager.SelectFingerprint(context.Background(), req)
		if err != nil {
			b.Errorf("选择指纹失败: %v", err)
		}
	}
}

// BenchmarkTLSConfigCreation TLS配置创建性能测试
func BenchmarkTLSConfigCreation(b *testing.B) {
	manager := NewManager()
	err := manager.Initialize("")
	if err != nil {
		b.Fatalf("初始化管理器失败: %v", err)
	}
	defer manager.Close()

	req := &core.FingerprintRequest{
		PoolName: "default",
	}

	profile, err := manager.SelectFingerprint(context.Background(), req)
	if err != nil {
		b.Fatalf("选择指纹失败: %v", err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := manager.CreateTLSConfig(profile, "example.com")
		if err != nil {
			b.Errorf("创建TLS配置失败: %v", err)
		}
	}
}
