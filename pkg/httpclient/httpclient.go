// Package httpclient 提供重新设计的HTTP客户端
package httpclient

import (
	"context"
	"sync"

	"go-monitor/pkg/httpclient/client"
	"go-monitor/pkg/httpclient/config"
	"go-monitor/pkg/httpclient/foundation"
)

// 重新导出核心类型
type (
	// 基础类型
	Request  = foundation.Request
	Response = foundation.Response
	Config   = foundation.Config

	// 客户端接口
	HTTPClient = foundation.HTTPClient

	// 监控类型
	MonitorStats    = foundation.MonitorStats
	ConnectionStat  = foundation.ConnectionStat
	ConnectionEvent = foundation.ConnectionEvent

	// 资源管理
	ResourceManager   = foundation.ResourceManager
	ConnectionManager = foundation.ConnectionManager
	Resource          = foundation.Resource
	Connection        = foundation.Connection
)

// ClientMode 客户端模式
type ClientMode int

const (
	// ClientModeNew 每次创建新客户端（默认，避免风控）
	ClientModeNew ClientMode = iota
	// ClientModePool 使用连接池（高性能，但可能被风控）
	ClientModePool
)

// RequestOptions 请求选项
type RequestOptions struct {
	Mode ClientMode // 客户端模式
}

// 全局连接池客户端（延迟初始化）
var (
	globalPoolClient *client.AdvancedClient
	poolClientOnce   sync.Once
	poolClientMutex  sync.RWMutex
)

// getPoolClient 获取全局连接池客户端
func getPoolClient() HTTPClient {
	poolClientMutex.RLock()
	if globalPoolClient != nil && !globalPoolClient.IsClosed() {
		defer poolClientMutex.RUnlock()
		return globalPoolClient
	}
	poolClientMutex.RUnlock()

	// 需要创建或重新创建客户端
	poolClientMutex.Lock()
	defer poolClientMutex.Unlock()

	// 双重检查
	if globalPoolClient != nil && !globalPoolClient.IsClosed() {
		return globalPoolClient
	}

	// 创建新的全局客户端
	config := &foundation.Config{
		EnableMonitoring:  true,
		EnableRetry:       true,
		EnableFingerprint: false,
		MaxRetries:        2,
	}
	globalPoolClient = client.NewAdvancedClient(config)
	return globalPoolClient
}

// NewBasicClient 创建基础HTTP客户端
func NewBasicClient(cfg ...*Config) HTTPClient {
	var config *Config
	if len(cfg) > 0 && cfg[0] != nil {
		config = cfg[0]
	} else {
		config = GetDefaultConfig()
	}

	return client.NewBasicClient(config)
}

// NewAdvancedClient 创建高级HTTP客户端（支持指纹、监控、重试等功能）
func NewAdvancedClient(cfg ...*Config) HTTPClient {
	var config *Config
	if len(cfg) > 0 && cfg[0] != nil {
		config = cfg[0]
	} else {
		config = GetDefaultConfig()
	}

	return client.NewAdvancedClient(config)
}

// NewClient 创建HTTP客户端（别名）
func NewClient(cfg ...*Config) HTTPClient {
	return NewBasicClient(cfg...)
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *Config {
	return config.GetDefault()
}

// NewBasicConfig 创建基础配置
func NewBasicConfig() *Config {
	return config.NewBasicConfig()
}

// NewAdvancedConfig 创建高级配置
func NewAdvancedConfig() *Config {
	return config.NewAdvancedConfig()
}

// NewSpiderConfig 创建爬虫配置
func NewSpiderConfig() *Config {
	return config.NewSpiderConfig()
}

// ValidateConfig 验证配置
func ValidateConfig(cfg *Config) error {
	return config.Validate(cfg)
}

// MergeConfig 合并配置
func MergeConfig(base, override *Config) *Config {
	return config.Merge(base, override)
}

// 便捷函数

// getClient 根据选项获取客户端
func getClient(opts *RequestOptions) (HTTPClient, func()) {
	if opts != nil && opts.Mode == ClientModePool {
		// 使用连接池模式（高性能，但可能被风控）
		return getPoolClient(), func() {} // 连接池客户端不需要关闭
	}
	// 默认使用新建客户端模式（避免风控）
	client := NewBasicClient()
	return client, func() { client.Close() }
}

// Get 发起GET请求
// opts: 可选的请求选项，默认使用新建客户端模式（避免风控）
func Get(ctx context.Context, url string, headers map[string]string, opts ...*RequestOptions) (*Response, error) {
	var options *RequestOptions
	if len(opts) > 0 {
		options = opts[0]
	}

	client, cleanup := getClient(options)
	defer cleanup()
	return client.Get(ctx, url, headers)
}

// Post 发起POST请求
// opts: 可选的请求选项，默认使用新建客户端模式（避免风控）
func Post(ctx context.Context, url string, body []byte, headers map[string]string, opts ...*RequestOptions) (*Response, error) {
	var options *RequestOptions
	if len(opts) > 0 {
		options = opts[0]
	}

	client, cleanup := getClient(options)
	defer cleanup()
	return client.Post(ctx, url, body, headers)
}

// Put 发起PUT请求
// opts: 可选的请求选项，默认使用新建客户端模式（避免风控）
func Put(ctx context.Context, url string, body []byte, headers map[string]string, opts ...*RequestOptions) (*Response, error) {
	var options *RequestOptions
	if len(opts) > 0 {
		options = opts[0]
	}

	client, cleanup := getClient(options)
	defer cleanup()
	return client.Put(ctx, url, body, headers)
}

// Delete 发起DELETE请求
// opts: 可选的请求选项，默认使用新建客户端模式（避免风控）
func Delete(ctx context.Context, url string, headers map[string]string, opts ...*RequestOptions) (*Response, error) {
	var options *RequestOptions
	if len(opts) > 0 {
		options = opts[0]
	}

	client, cleanup := getClient(options)
	defer cleanup()
	return client.Delete(ctx, url, headers)
}

// Do 执行HTTP请求
// opts: 可选的请求选项，默认使用新建客户端模式（避免风控）
func Do(ctx context.Context, req *Request, opts ...*RequestOptions) (*Response, error) {
	var options *RequestOptions
	if len(opts) > 0 {
		options = opts[0]
	}

	client, cleanup := getClient(options)
	defer cleanup()
	return client.Do(ctx, req)
}

// 便捷选项创建函数

// WithNewClient 使用新建客户端模式（默认，避免风控）
func WithNewClient() *RequestOptions {
	return &RequestOptions{Mode: ClientModeNew}
}

// WithPoolClient 使用连接池模式（高性能，但可能被风控）
func WithPoolClient() *RequestOptions {
	return &RequestOptions{Mode: ClientModePool}
}

// 配置便捷函数

// WithHTTP2 创建HTTP/2优化配置
func WithHTTP2() *Config {
	return config.NewHTTP2Config()
}

// WithProxy 创建代理配置
func WithProxy(proxyType foundation.ProxyType, proxyURL string, auth *foundation.ProxyAuth) *Config {
	return config.NewProxyConfig(proxyType, proxyURL, auth)
}

// WithSOCKS5 创建SOCKS5代理配置
func WithSOCKS5(proxyURL, username, password string) *Config {
	return config.NewSOCKS5Config(proxyURL, username, password)
}

// WithHTTPProxy 创建HTTP代理配置
func WithHTTPProxy(proxyURL string) *Config {
	return config.NewProxyConfig(foundation.ProxyTypeHTTP, proxyURL, nil)
}

// WithHTTPSProxy 创建HTTPS代理配置
func WithHTTPSProxy(proxyURL string) *Config {
	return config.NewProxyConfig(foundation.ProxyTypeHTTPS, proxyURL, nil)
}

// CloseGlobalPool 关闭全局连接池（程序退出时调用）
func CloseGlobalPool() error {
	poolClientMutex.Lock()
	defer poolClientMutex.Unlock()

	if globalPoolClient != nil && !globalPoolClient.IsClosed() {
		err := globalPoolClient.Close()
		globalPoolClient = nil // 清空引用
		return err
	}
	return nil
}
