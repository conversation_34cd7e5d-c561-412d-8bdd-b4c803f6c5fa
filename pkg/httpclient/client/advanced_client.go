// Package client 高级HTTP客户端实现
package client

import (
	"context"
	"fmt"
	"time"

	"go-monitor/pkg/httpclient/features/fingerprint"
	"go-monitor/pkg/httpclient/features/monitor"
	"go-monitor/pkg/httpclient/features/retry"
	"go-monitor/pkg/httpclient/foundation"
	"go-monitor/pkg/logging"
)

// AdvancedClient 高级HTTP客户端
type AdvancedClient struct {
	// 基础客户端
	basicClient *BasicClient

	// 功能模块
	connectionMonitor  *monitor.ConnectionMonitor
	performanceMonitor *monitor.PerformanceMonitor
	retryManager       *retry.RetryManager
	fingerprintAdapter *fingerprint.SimpleFingerprintAdapter

	// 配置
	config *foundation.Config
	logger logging.Logger

	// 状态
	closed bool
}

// NewAdvancedClient 创建高级HTTP客户端
func NewAdvancedClient(config *foundation.Config) *AdvancedClient {
	if config == nil {
		config = getDefaultAdvancedConfig()
	}

	// 创建基础客户端
	basicClient := NewBasicClient(config)

	client := &AdvancedClient{
		basicClient: basicClient,
		config:      config,
		logger:      logging.GetLogger("httpclient.advanced"),
	}

	// 初始化功能模块
	client.initializeFeatures()

	return client
}

// initializeFeatures 初始化功能模块
func (ac *AdvancedClient) initializeFeatures() {
	ctx := context.Background()

	// 初始化监控模块
	if ac.config.EnableMonitoring {
		ac.connectionMonitor = monitor.NewConnectionMonitor(24*time.Hour, 1000)
		ac.performanceMonitor = monitor.NewPerformanceMonitor(24 * time.Hour)

		// 启动监控器
		if err := ac.connectionMonitor.Start(ctx); err != nil {
			ac.logger.Warn("启动连接监控器失败", "error", err.Error())
		}

		if err := ac.performanceMonitor.Start(ctx); err != nil {
			ac.logger.Warn("启动性能监控器失败", "error", err.Error())
		}

		ac.logger.Info("监控功能已启用")
	}

	// 初始化重试模块
	if ac.config.EnableRetry {
		retryConfig := retry.RetryConfig{
			MaxRetries:    ac.config.MaxRetries,
			InitialDelay:  ac.config.RetryDelay,
			BackoffFactor: ac.config.RetryBackoff,
			Jitter:        true,
		}
		ac.retryManager = retry.NewRetryManager(retryConfig)
		ac.logger.Info("重试功能已启用", "max_retries", ac.config.MaxRetries)
	}

	// 初始化指纹模块
	if ac.config.EnableFingerprint {
		ac.fingerprintAdapter = fingerprint.NewSimpleFingerprintAdapter(true)
		ac.logger.Info("指纹功能已启用")
	}

	// 记录代理和HTTP版本配置
	if ac.config.ProxyType != foundation.ProxyTypeNone && ac.config.ProxyURL != "" {
		ac.logger.Info("代理已配置",
			"type", ac.config.ProxyType.String(),
			"url", ac.config.ProxyURL,
			"auth", ac.config.ProxyAuth != nil)
	}

	ac.logger.Info("HTTP版本配置",
		"version", ac.config.HTTPVersion.String(),
		"http2_push", ac.config.EnableHTTP2Push)
}

// Get 发起GET请求
func (ac *AdvancedClient) Get(ctx context.Context, url string, headers map[string]string) (*foundation.Response, error) {
	req := &foundation.Request{
		URL:     url,
		Method:  "GET",
		Headers: headers,
	}
	return ac.Do(ctx, req)
}

// Post 发起POST请求
func (ac *AdvancedClient) Post(ctx context.Context, url string, body []byte, headers map[string]string) (*foundation.Response, error) {
	req := &foundation.Request{
		URL:     url,
		Method:  "POST",
		Headers: headers,
		Body:    body,
	}
	return ac.Do(ctx, req)
}

// Put 发起PUT请求
func (ac *AdvancedClient) Put(ctx context.Context, url string, body []byte, headers map[string]string) (*foundation.Response, error) {
	req := &foundation.Request{
		URL:     url,
		Method:  "PUT",
		Headers: headers,
		Body:    body,
	}
	return ac.Do(ctx, req)
}

// Delete 发起DELETE请求
func (ac *AdvancedClient) Delete(ctx context.Context, url string, headers map[string]string) (*foundation.Response, error) {
	req := &foundation.Request{
		URL:     url,
		Method:  "DELETE",
		Headers: headers,
	}
	return ac.Do(ctx, req)
}

// Do 执行HTTP请求（集成所有功能）
func (ac *AdvancedClient) Do(ctx context.Context, req *foundation.Request) (*foundation.Response, error) {
	if ac.closed {
		return nil, fmt.Errorf("客户端已关闭")
	}

	if req == nil {
		return nil, fmt.Errorf("请求不能为空")
	}

	// 记录性能监控开始
	var requestID string
	if ac.performanceMonitor != nil {
		requestID = ac.performanceMonitor.RecordRequestStart(req)
	}

	// 执行请求（带重试）
	var resp *foundation.Response
	var err error

	if ac.retryManager != nil {
		// 使用重试机制
		operation := func(ctx context.Context, attempt int) (*foundation.Response, error) {
			return ac.executeRequest(ctx, req, attempt)
		}
		resp, err = ac.retryManager.ExecuteWithRetry(ctx, operation)
	} else {
		// 直接执行
		resp, err = ac.executeRequest(ctx, req, 0)
	}

	// 记录性能监控结束
	if ac.performanceMonitor != nil && requestID != "" {
		ac.performanceMonitor.RecordRequestEnd(requestID, resp, err)
	}

	// 记录连接监控
	if ac.connectionMonitor != nil {
		ac.connectionMonitor.RecordRequest(req, resp, err)
	}

	// 分析响应（指纹风控检测）
	if ac.fingerprintAdapter != nil && resp != nil {
		go ac.analyzeResponse(req, resp)
	}

	return resp, err
}

// executeRequest 执行单次请求
func (ac *AdvancedClient) executeRequest(ctx context.Context, req *foundation.Request, attempt int) (*foundation.Response, error) {
	// 应用指纹配置
	if ac.fingerprintAdapter != nil && ac.fingerprintAdapter.IsEnabled() {
		ac.logger.Debug("开始应用指纹配置", "url", req.URL, "attempt", attempt)

		// 选择指纹
		profile, err := ac.fingerprintAdapter.SelectFingerprint(req)
		if err != nil {
			ac.logger.Warn("选择指纹失败", "error", err, "url", req.URL)
		} else if profile != nil {
			ac.logger.Debug("指纹选择成功", "profile", profile.Name, "url", req.URL)
		}
	}

	// 执行基础请求
	resp, err := ac.basicClient.Do(ctx, req)

	// 分析响应
	if err == nil && resp != nil {
		ac.analyzeResponse(req, resp)
	}

	return resp, err
}

// analyzeResponse 分析响应
func (ac *AdvancedClient) analyzeResponse(req *foundation.Request, resp *foundation.Response) {
	if ac.fingerprintAdapter == nil || !ac.fingerprintAdapter.IsEnabled() {
		return
	}

	// 提取服务器名称
	serverName := extractServerName(req.URL)

	// 提取指纹名称
	profileName := extractProfileName(req)

	// 检查是否为风控状态码
	riskLevel := ac.detectRiskByStatusCode(resp.StatusCode)
	isRiskDetected := riskLevel != ""

	// 更新指纹健康状态
	success := resp.StatusCode >= 200 && resp.StatusCode < 400 && !isRiskDetected
	ac.fingerprintAdapter.UpdateHealth(profileName, success, resp.ResponseTime)

	// 如果检测到风控，标记指纹为不健康
	if isRiskDetected && profileName != "" {
		ac.logger.Debug("检测到风控响应，标记指纹为不健康",
			"profile", profileName,
			"status_code", resp.StatusCode,
			"risk_level", riskLevel,
			"url", req.URL)

		// 直接标记不健康指纹，避免重复检测和日志
		ac.fingerprintAdapter.MarkUnhealthyDirect(profileName, resp.StatusCode)
	}

	// 分析响应（保持原有逻辑）
	ac.fingerprintAdapter.AnalyzeResponse(
		serverName,
		resp.StatusCode,
		string(resp.Body),
		resp.ResponseTime.Nanoseconds(),
		profileName,
	)
}

// detectRiskByStatusCode 根据状态码检测风控
func (ac *AdvancedClient) detectRiskByStatusCode(statusCode int) string {
	// 风控状态码映射（基于配置文件）
	riskStatusCodes := map[int]string{
		473: "HIGH",   // 自定义风控状态码
		472: "HIGH",   // 另一个风控状态码
		471: "HIGH",   // 自定义风控状态码
		470: "HIGH",   // 另一个风控状态码
		418: "MEDIUM", // I'm a teapot
		403: "MEDIUM", // Forbidden
		429: "LOW",    // Too Many Requests
	}

	if riskLevel, exists := riskStatusCodes[statusCode]; exists {
		return riskLevel
	}

	return "" // 无风控
}

// GetConfig 获取配置
func (ac *AdvancedClient) GetConfig() *foundation.Config {
	return ac.config
}

// UpdateConfig 更新配置
func (ac *AdvancedClient) UpdateConfig(config *foundation.Config) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	ac.config = config

	// 更新基础客户端配置
	if err := ac.basicClient.UpdateConfig(config); err != nil {
		return fmt.Errorf("更新基础客户端配置失败: %w", err)
	}

	// 重新初始化功能模块
	ac.reinitializeFeatures()

	ac.logger.Debug("高级客户端配置已更新")
	return nil
}

// reinitializeFeatures 重新初始化功能模块
func (ac *AdvancedClient) reinitializeFeatures() {
	// 停止现有模块
	if ac.connectionMonitor != nil {
		ac.connectionMonitor.Stop()
	}
	if ac.performanceMonitor != nil {
		ac.performanceMonitor.Stop()
	}
	if ac.fingerprintAdapter != nil {
		ac.fingerprintAdapter.Close()
	}

	// 重新初始化
	ac.initializeFeatures()
}

// GetStats 获取统计信息
func (ac *AdvancedClient) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if ac.connectionMonitor != nil {
		stats["connection"] = ac.connectionMonitor.GetStats()
	}

	if ac.performanceMonitor != nil {
		stats["performance"] = ac.performanceMonitor.GetPerformanceStats()
	}

	return stats
}

// IsClosed 检查客户端是否已关闭
func (ac *AdvancedClient) IsClosed() bool {
	return ac.closed
}

// Close 关闭客户端
func (ac *AdvancedClient) Close() error {
	if ac.closed {
		return nil
	}

	ac.closed = true

	// 关闭功能模块
	if ac.connectionMonitor != nil {
		if err := ac.connectionMonitor.Stop(); err != nil {
			ac.logger.Warn("停止连接监控器失败", "error", err.Error())
		}
	}

	if ac.performanceMonitor != nil {
		if err := ac.performanceMonitor.Stop(); err != nil {
			ac.logger.Warn("停止性能监控器失败", "error", err.Error())
		}
	}

	if ac.fingerprintAdapter != nil {
		if err := ac.fingerprintAdapter.Close(); err != nil {
			ac.logger.Warn("关闭指纹适配器失败", "error", err.Error())
		}
	}

	// 关闭基础客户端
	if err := ac.basicClient.Close(); err != nil {
		ac.logger.Warn("关闭基础客户端失败", "error", err.Error())
	}

	ac.logger.Info("高级客户端已关闭")
	return nil
}

// 辅助函数

func getDefaultAdvancedConfig() *foundation.Config {
	return &foundation.Config{
		Timeout:         30 * time.Second,
		MaxIdleConns:    200,
		MaxConnsPerHost: 20,
		UserAgent:       "Go-Monitor-AdvancedClient/1.0",

		EnableMonitoring:  true,
		EnableFingerprint: false,
		EnableRetry:       true,

		MaxRetries:   3,
		RetryDelay:   1 * time.Second,
		RetryBackoff: 2.0,
	}
}

func extractServerName(url string) string {
	// 简单提取服务器名称的实现
	// 实际实现应该解析URL
	return url
}

func extractProfileName(req *foundation.Request) string {
	if req.Meta != nil {
		if profileName, ok := req.Meta["fingerprint_name"].(string); ok {
			return profileName
		}
	}
	return "default"
}
