// Package client 爬虫专用HTTP客户端实现
package client

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"
	
	"go-monitor/pkg/httpclient/foundation"
	"go-monitor/pkg/logging"
)

// SpiderClient 爬虫专用HTTP客户端
type SpiderClient struct {
	// 高级客户端
	advancedClient *AdvancedClient
	
	// 爬虫配置
	spiderConfig SpiderConfig
	logger       logging.Logger
	
	// 状态
	closed bool
}

// SpiderConfig 爬虫配置接口（兼容现有接口）
type SpiderConfig interface {
	GetPlatform() string
	GetName() string
	GetCountry() string
	GetSpiderSettings() map[string]interface{}
}

// NewSpiderClient 创建爬虫客户端
func NewSpiderClient(config *foundation.Config, spiderConfig SpiderConfig) *SpiderClient {
	if config == nil {
		config = getDefaultSpiderConfig()
	}
	
	// 确保启用爬虫所需的功能
	config.EnableMonitoring = true
	config.EnableFingerprint = true
	config.EnableRetry = true
	
	// 创建高级客户端
	advancedClient := NewAdvancedClient(config)
	
	return &SpiderClient{
		advancedClient: advancedClient,
		spiderConfig:   spiderConfig,
		logger:         logging.GetLogger("httpclient.spider"),
	}
}

// Get 发起GET请求
func (sc *SpiderClient) Get(ctx context.Context, url string, headers map[string]string) (*foundation.Response, error) {
	req := sc.NewRequestBuilder(url).
		SetMethod("GET").
		SetHeaders(headers).
		Build()
	
	return sc.Do(ctx, req)
}

// Post 发起POST请求
func (sc *SpiderClient) Post(ctx context.Context, url string, body []byte, headers map[string]string) (*foundation.Response, error) {
	req := sc.NewRequestBuilder(url).
		SetMethod("POST").
		SetHeaders(headers).
		SetBody(body).
		Build()
	
	return sc.Do(ctx, req)
}

// Put 发起PUT请求
func (sc *SpiderClient) Put(ctx context.Context, url string, body []byte, headers map[string]string) (*foundation.Response, error) {
	req := sc.NewRequestBuilder(url).
		SetMethod("PUT").
		SetHeaders(headers).
		SetBody(body).
		Build()
	
	return sc.Do(ctx, req)
}

// Delete 发起DELETE请求
func (sc *SpiderClient) Delete(ctx context.Context, url string, headers map[string]string) (*foundation.Response, error) {
	req := sc.NewRequestBuilder(url).
		SetMethod("DELETE").
		SetHeaders(headers).
		Build()
	
	return sc.Do(ctx, req)
}

// Do 执行HTTP请求
func (sc *SpiderClient) Do(ctx context.Context, req *foundation.Request) (*foundation.Response, error) {
	if sc.closed {
		return nil, fmt.Errorf("爬虫客户端已关闭")
	}
	
	// 应用爬虫特定的元数据
	sc.applySpiderMetadata(req)
	
	// 执行请求
	return sc.advancedClient.Do(ctx, req)
}

// applySpiderMetadata 应用爬虫特定的元数据
func (sc *SpiderClient) applySpiderMetadata(req *foundation.Request) {
	if req.Meta == nil {
		req.Meta = make(map[string]interface{})
	}
	
	// 添加爬虫信息
	req.Meta["spider_platform"] = sc.spiderConfig.GetPlatform()
	req.Meta["spider_name"] = sc.spiderConfig.GetName()
	req.Meta["spider_country"] = sc.spiderConfig.GetCountry()
	
	// 应用爬虫设置
	settings := sc.spiderConfig.GetSpiderSettings()
	for key, value := range settings {
		req.Meta[key] = value
	}
	
	// 设置指纹池（如果未指定）
	if _, exists := req.Meta["fingerprint_pool"]; !exists {
		req.Meta["fingerprint_pool"] = sc.getDefaultFingerprintPool()
	}
}

// getDefaultFingerprintPool 获取默认指纹池
func (sc *SpiderClient) getDefaultFingerprintPool() string {
	platform := sc.spiderConfig.GetPlatform()
	country := sc.spiderConfig.GetCountry()
	
	// 根据平台和国家选择指纹池
	if country != "" {
		return fmt.Sprintf("%s-%s", platform, strings.ToLower(country))
	}
	return platform
}

// NewRequestBuilder 创建请求构建器
func (sc *SpiderClient) NewRequestBuilder(baseURL string) *SpiderRequestBuilder {
	return &SpiderRequestBuilder{
		request: &foundation.Request{
			URL:     baseURL,
			Method:  "GET",
			Headers: make(map[string]string),
			Params:  make(map[string]string),
			Cookies: make(map[string]string),
			Meta:    make(map[string]interface{}),
		},
		spiderConfig: sc.spiderConfig,
	}
}

// GetConfig 获取配置
func (sc *SpiderClient) GetConfig() *foundation.Config {
	return sc.advancedClient.GetConfig()
}

// UpdateConfig 更新配置
func (sc *SpiderClient) UpdateConfig(config *foundation.Config) error {
	return sc.advancedClient.UpdateConfig(config)
}

// GetStats 获取统计信息
func (sc *SpiderClient) GetStats() map[string]interface{} {
	stats := sc.advancedClient.GetStats()
	
	// 添加爬虫特定统计
	stats["spider"] = map[string]interface{}{
		"platform": sc.spiderConfig.GetPlatform(),
		"name":     sc.spiderConfig.GetName(),
		"country":  sc.spiderConfig.GetCountry(),
	}
	
	return stats
}

// Close 关闭客户端
func (sc *SpiderClient) Close() error {
	if sc.closed {
		return nil
	}
	
	sc.closed = true
	
	if err := sc.advancedClient.Close(); err != nil {
		sc.logger.Warn("关闭高级客户端失败", "error", err.Error())
	}
	
	sc.logger.Info("爬虫客户端已关闭", "platform", sc.spiderConfig.GetPlatform())
	return nil
}

// SpiderRequestBuilder 爬虫请求构建器
type SpiderRequestBuilder struct {
	request      *foundation.Request
	spiderConfig SpiderConfig
}

// SetMethod 设置请求方法
func (srb *SpiderRequestBuilder) SetMethod(method string) *SpiderRequestBuilder {
	srb.request.Method = method
	return srb
}

// SetHeaders 设置请求头
func (srb *SpiderRequestBuilder) SetHeaders(headers map[string]string) *SpiderRequestBuilder {
	for k, v := range headers {
		srb.request.Headers[k] = v
	}
	return srb
}

// SetHeader 设置单个请求头
func (srb *SpiderRequestBuilder) SetHeader(key, value string) *SpiderRequestBuilder {
	srb.request.Headers[key] = value
	return srb
}

// SetParams 设置查询参数
func (srb *SpiderRequestBuilder) SetParams(params map[string]string) *SpiderRequestBuilder {
	for k, v := range params {
		srb.request.Params[k] = v
	}
	return srb
}

// SetParam 设置单个查询参数
func (srb *SpiderRequestBuilder) SetParam(key, value string) *SpiderRequestBuilder {
	srb.request.Params[key] = value
	return srb
}

// SetCookies 设置Cookies
func (srb *SpiderRequestBuilder) SetCookies(cookies map[string]string) *SpiderRequestBuilder {
	for k, v := range cookies {
		srb.request.Cookies[k] = v
	}
	return srb
}

// SetCookie 设置单个Cookie
func (srb *SpiderRequestBuilder) SetCookie(key, value string) *SpiderRequestBuilder {
	srb.request.Cookies[key] = value
	return srb
}

// SetBody 设置请求体
func (srb *SpiderRequestBuilder) SetBody(body []byte) *SpiderRequestBuilder {
	srb.request.Body = body
	return srb
}

// SetJSONBody 设置JSON请求体
func (srb *SpiderRequestBuilder) SetJSONBody(data interface{}) *SpiderRequestBuilder {
	if jsonData, err := json.Marshal(data); err == nil {
		srb.request.Body = jsonData
		srb.request.Headers["Content-Type"] = "application/json"
	}
	return srb
}

// SetFormBody 设置表单请求体
func (srb *SpiderRequestBuilder) SetFormBody(data map[string]string) *SpiderRequestBuilder {
	values := url.Values{}
	for k, v := range data {
		values.Set(k, v)
	}
	srb.request.Body = []byte(values.Encode())
	srb.request.Headers["Content-Type"] = "application/x-www-form-urlencoded"
	return srb
}

// SetTimeout 设置超时时间
func (srb *SpiderRequestBuilder) SetTimeout(timeout time.Duration) *SpiderRequestBuilder {
	srb.request.Timeout = timeout
	return srb
}

// SetProxy 设置代理
func (srb *SpiderRequestBuilder) SetProxy(proxy string) *SpiderRequestBuilder {
	srb.request.Proxy = proxy
	return srb
}

// SetMeta 设置元数据
func (srb *SpiderRequestBuilder) SetMeta(key string, value interface{}) *SpiderRequestBuilder {
	srb.request.Meta[key] = value
	return srb
}

// SetSpiderMetadata 设置爬虫元数据
func (srb *SpiderRequestBuilder) SetSpiderMetadata(productID string) *SpiderRequestBuilder {
	srb.request.Meta["product_id"] = productID
	srb.request.Meta["spider_platform"] = srb.spiderConfig.GetPlatform()
	srb.request.Meta["spider_name"] = srb.spiderConfig.GetName()
	srb.request.Meta["spider_country"] = srb.spiderConfig.GetCountry()
	return srb
}

// SetCommonHeaders 设置通用请求头
func (srb *SpiderRequestBuilder) SetCommonHeaders() *SpiderRequestBuilder {
	srb.request.Headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
	srb.request.Headers["Accept-Language"] = "en-US,en;q=0.5"
	srb.request.Headers["Accept-Encoding"] = "gzip, deflate, br"
	srb.request.Headers["DNT"] = "1"
	srb.request.Headers["Connection"] = "keep-alive"
	srb.request.Headers["Upgrade-Insecure-Requests"] = "1"
	return srb
}

// SetAPIHeaders 设置API请求头
func (srb *SpiderRequestBuilder) SetAPIHeaders() *SpiderRequestBuilder {
	srb.request.Headers["Accept"] = "application/json, text/plain, */*"
	srb.request.Headers["Content-Type"] = "application/json"
	return srb
}

// SetFormHeaders 设置表单请求头
func (srb *SpiderRequestBuilder) SetFormHeaders() *SpiderRequestBuilder {
	srb.request.Headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8"
	return srb
}

// Build 构建请求对象
func (srb *SpiderRequestBuilder) Build() *foundation.Request {
	return srb.request
}

// 辅助函数

func getDefaultSpiderConfig() *foundation.Config {
	return &foundation.Config{
		Timeout:         45 * time.Second,
		MaxIdleConns:    150,
		MaxConnsPerHost: 15,
		UserAgent:       "Mozilla/5.0 (compatible; Go-Monitor-Spider/1.0)",
		
		EnableMonitoring:   true,
		EnableFingerprint:  true,
		EnableRetry:        true,
		
		MaxRetries:   3,
		RetryDelay:   1 * time.Second,
		RetryBackoff: 1.8,
	}
}
