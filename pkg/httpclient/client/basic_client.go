// Package client 提供HTTP客户端实现
package client

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go-monitor/pkg/httpclient/foundation"
	"go-monitor/pkg/logging"

	"github.com/andybalholm/brotli"
)

// BasicClient 基础HTTP客户端实现
type BasicClient struct {
	// 配置
	config *foundation.Config

	// 核心组件
	resourceManager   foundation.ResourceManager
	connectionManager foundation.ConnectionManager

	// 日志
	logger logging.Logger

	// 状态
	closed bool
}

// NewBasicClient 创建基础HTTP客户端
func NewBasicClient(config *foundation.Config) *BasicClient {
	if config == nil {
		config = getDefaultConfig()
	}

	// 创建资源管理器
	resourceManager := foundation.NewResourceManager(
		30*time.Second, // 清理间隔
		5*time.Minute,  // 最大空闲时间
	)

	// 创建连接管理器
	connectionManager := foundation.NewConnectionManager(
		config.MaxConnsPerHost*10, // 最大连接数
		5*time.Minute,             // 最大空闲时间
		30*time.Second,            // 清理间隔
	)

	client := &BasicClient{
		config:            config,
		resourceManager:   resourceManager,
		connectionManager: connectionManager,
		logger:            logging.GetLogger("httpclient.basic"),
	}

	// 启动管理器
	ctx := context.Background()
	if err := resourceManager.Start(ctx); err != nil {
		client.logger.Warn("启动资源管理器失败", "error", err.Error())
	}

	if err := connectionManager.Start(ctx); err != nil {
		client.logger.Warn("启动连接管理器失败", "error", err.Error())
	}

	return client
}

// Get 发起GET请求
func (c *BasicClient) Get(ctx context.Context, url string, headers map[string]string) (*foundation.Response, error) {
	req := &foundation.Request{
		URL:     url,
		Method:  "GET",
		Headers: headers,
	}
	return c.Do(ctx, req)
}

// Post 发起POST请求
func (c *BasicClient) Post(ctx context.Context, url string, body []byte, headers map[string]string) (*foundation.Response, error) {
	req := &foundation.Request{
		URL:     url,
		Method:  "POST",
		Headers: headers,
		Body:    body,
	}
	return c.Do(ctx, req)
}

// Put 发起PUT请求
func (c *BasicClient) Put(ctx context.Context, url string, body []byte, headers map[string]string) (*foundation.Response, error) {
	req := &foundation.Request{
		URL:     url,
		Method:  "PUT",
		Headers: headers,
		Body:    body,
	}
	return c.Do(ctx, req)
}

// Delete 发起DELETE请求
func (c *BasicClient) Delete(ctx context.Context, url string, headers map[string]string) (*foundation.Response, error) {
	req := &foundation.Request{
		URL:     url,
		Method:  "DELETE",
		Headers: headers,
	}
	return c.Do(ctx, req)
}

// Do 执行HTTP请求
func (c *BasicClient) Do(ctx context.Context, req *foundation.Request) (*foundation.Response, error) {
	if c.closed {
		return nil, fmt.Errorf("客户端已关闭")
	}

	if req == nil {
		return nil, fmt.Errorf("请求不能为空")
	}

	startTime := time.Now()

	// 应用请求超时
	if req.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, req.Timeout)
		defer cancel()
	} else if c.config.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, c.config.Timeout)
		defer cancel()
	}

	// 构建完整URL
	fullURL := c.buildURL(req)

	// 创建HTTP请求
	httpReq, err := c.createHTTPRequest(ctx, req, fullURL)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 获取或创建连接
	conn, err := c.getConnection(fullURL, req)
	if err != nil {
		return nil, fmt.Errorf("获取连接失败: %w", err)
	}

	// 执行请求
	httpResp, err := conn.Do(httpReq)
	if err != nil {
		// 检查是否为超时或取消错误
		if ctx.Err() != nil {
			return nil, fmt.Errorf("请求被取消或超时: %w", ctx.Err())
		}
		return nil, fmt.Errorf("执行请求失败: %w", err)
	}
	defer httpResp.Body.Close()

	// 读取并解压缩响应体
	body, err := c.readAndDecompressResponse(httpResp)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 构建响应
	resp := &foundation.Response{
		StatusCode:   httpResp.StatusCode,
		Headers:      httpResp.Header,
		Body:         body,
		URL:          fullURL,
		ResponseTime: time.Since(startTime),
		Request:      req,
	}

	c.logger.Debug("请求完成", "url", fullURL, "method", req.Method,
		"status", httpResp.StatusCode, "response_time", resp.ResponseTime,
		"body_size", len(body))

	return resp, nil
}

// GetConfig 获取配置
func (c *BasicClient) GetConfig() *foundation.Config {
	return c.config
}

// UpdateConfig 更新配置
func (c *BasicClient) UpdateConfig(config *foundation.Config) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	c.config = config
	c.logger.Debug("配置已更新")
	return nil
}

// Close 关闭客户端
func (c *BasicClient) Close() error {
	if c.closed {
		return nil
	}

	c.closed = true

	// 停止管理器
	if err := c.connectionManager.Stop(); err != nil {
		c.logger.Warn("停止连接管理器失败", "error", err.Error())
	}

	if err := c.resourceManager.Stop(); err != nil {
		c.logger.Warn("停止资源管理器失败", "error", err.Error())
	}

	c.logger.Info("基础客户端已关闭")
	return nil
}

// buildURL 构建完整URL
func (c *BasicClient) buildURL(req *foundation.Request) string {
	baseURL := req.URL

	// 添加查询参数
	if len(req.Params) > 0 {
		u, err := url.Parse(baseURL)
		if err != nil {
			return baseURL
		}

		q := u.Query()
		for k, v := range req.Params {
			q.Add(k, v)
		}
		u.RawQuery = q.Encode()

		return u.String()
	}

	return baseURL
}

// createHTTPRequest 创建HTTP请求
func (c *BasicClient) createHTTPRequest(ctx context.Context, req *foundation.Request, fullURL string) (*http.Request, error) {
	var body io.Reader
	if len(req.Body) > 0 {
		body = bytes.NewReader(req.Body)
	}

	httpReq, err := http.NewRequestWithContext(ctx, req.Method, fullURL, body)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	c.setHeaders(httpReq, req)

	// 设置Cookies
	c.setCookies(httpReq, req)

	return httpReq, nil
}

// setHeaders 设置请求头
func (c *BasicClient) setHeaders(httpReq *http.Request, req *foundation.Request) {
	// 设置默认User-Agent
	if c.config.UserAgent != "" {
		httpReq.Header.Set("User-Agent", c.config.UserAgent)
	}

	// 设置自定义请求头
	for k, v := range req.Headers {
		httpReq.Header.Set(k, v)
	}

	// 设置Content-Type（如果有请求体且未设置）
	if len(req.Body) > 0 && httpReq.Header.Get("Content-Type") == "" {
		httpReq.Header.Set("Content-Type", "application/octet-stream")
	}
}

// setCookies 设置Cookies
func (c *BasicClient) setCookies(httpReq *http.Request, req *foundation.Request) {
	for name, value := range req.Cookies {
		cookie := &http.Cookie{
			Name:  name,
			Value: value,
		}
		httpReq.AddCookie(cookie)
	}
}

// getConnection 获取或创建连接
func (c *BasicClient) getConnection(fullURL string, req *foundation.Request) (foundation.Connection, error) {
	// 解析URL获取主机
	u, err := url.Parse(fullURL)
	if err != nil {
		return nil, fmt.Errorf("解析URL失败: %w", err)
	}

	target := u.Host
	if u.Port() == "" {
		if u.Scheme == "https" {
			target += ":443"
		} else {
			target += ":80"
		}
	}

	// 尝试获取现有连接（根据目标地址查找）
	if conn, exists := c.connectionManager.GetConnectionByTarget(target); exists {
		if conn.IsActive() {
			return conn, nil
		}
		// 连接不活跃，关闭并创建新连接
		actualConnID := conn.(*foundation.ManagedConnection).GetID()
		c.connectionManager.CloseConnection(actualConnID)
	}

	// 创建新连接
	config := &foundation.ConnectionConfig{
		Target:      target,
		Timeout:     c.config.Timeout,
		KeepAlive:   30 * time.Second,
		MaxIdleTime: 5 * time.Minute,

		// 代理配置
		ProxyType: c.config.ProxyType,
		ProxyURL:  req.Proxy, // 请求级代理优先
		ProxyAuth: c.config.ProxyAuth,

		// HTTP版本配置
		HTTPVersion:     c.config.HTTPVersion,
		EnableHTTP2Push: c.config.EnableHTTP2Push,
		HTTP2Settings:   c.config.HTTP2Settings,
	}

	// 如果请求没有指定代理，使用全局配置
	if config.ProxyURL == "" {
		config.ProxyURL = c.config.ProxyURL
	}

	conn, err := c.connectionManager.CreateConnection(config)
	if err != nil {
		return nil, fmt.Errorf("创建连接失败: %w", err)
	}

	// 获取连接的实际ID
	actualConnID := conn.(*foundation.ManagedConnection).GetID()

	// 注册连接到资源管理器
	if err := c.resourceManager.RegisterResource(actualConnID, conn); err != nil {
		c.logger.Warn("注册连接到资源管理器失败", "conn_id", actualConnID, "error", err.Error())
		// 如果注册失败，关闭连接以避免泄漏
		conn.Close()
		return nil, fmt.Errorf("注册连接失败: %w", err)
	}

	return conn, nil
}

// getProxyURL 获取代理URL
func (c *BasicClient) getProxyURL() string {
	// 这里可以根据配置返回代理URL
	return ""
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *foundation.Config {
	return &foundation.Config{
		Timeout:           30 * time.Second,
		MaxIdleConns:      100,
		MaxConnsPerHost:   10,
		UserAgent:         "Go-Monitor-HTTPClient/1.0",
		EnableMonitoring:  false,
		EnableFingerprint: false,
		EnableRetry:       false,
		MaxRetries:        3,
		RetryDelay:        1 * time.Second,
		RetryBackoff:      2.0,
	}
}

// readAndDecompressResponse 读取并解压缩HTTP响应体
func (c *BasicClient) readAndDecompressResponse(httpResp *http.Response) ([]byte, error) {
	// 获取Content-Encoding头
	contentEncoding := strings.ToLower(httpResp.Header.Get("Content-Encoding"))

	// 根据编码类型处理响应体
	switch {
	case strings.Contains(contentEncoding, "gzip"):
		return c.readGzipResponse(httpResp)
	case strings.Contains(contentEncoding, "deflate"):
		return c.readDeflateResponse(httpResp)
	case strings.Contains(contentEncoding, "br"):
		return c.readBrotliResponse(httpResp)
	default:
		// 无压缩，直接读取
		return io.ReadAll(httpResp.Body)
	}
}

// readGzipResponse 读取gzip压缩的响应
func (c *BasicClient) readGzipResponse(httpResp *http.Response) ([]byte, error) {
	gzipReader, err := gzip.NewReader(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("创建gzip reader失败: %w", err)
	}
	defer gzipReader.Close()

	body, err := io.ReadAll(gzipReader)
	if err != nil {
		return nil, fmt.Errorf("读取gzip解压缩数据失败: %w", err)
	}

	c.logger.Debug("成功解压缩gzip响应",
		"原始大小", httpResp.ContentLength,
		"解压后大小", len(body))

	return body, nil
}

// readDeflateResponse 读取deflate压缩的响应
func (c *BasicClient) readDeflateResponse(httpResp *http.Response) ([]byte, error) {
	deflateReader := flate.NewReader(httpResp.Body)
	defer deflateReader.Close()

	body, err := io.ReadAll(deflateReader)
	if err != nil {
		return nil, fmt.Errorf("读取deflate解压缩数据失败: %w", err)
	}

	c.logger.Debug("成功解压缩deflate响应",
		"原始大小", httpResp.ContentLength,
		"解压后大小", len(body))

	return body, nil
}

// readBrotliResponse 读取Brotli压缩的响应
func (c *BasicClient) readBrotliResponse(httpResp *http.Response) ([]byte, error) {
	brotliReader := brotli.NewReader(httpResp.Body)

	body, err := io.ReadAll(brotliReader)
	if err != nil {
		return nil, fmt.Errorf("读取Brotli解压缩数据失败: %w", err)
	}

	c.logger.Debug("成功解压缩Brotli响应",
		"原始大小", httpResp.ContentLength,
		"解压后大小", len(body))

	return body, nil
}
