// Package examples 演示HTTP/2和代理功能的使用
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"go-monitor/pkg/httpclient"
	"go-monitor/pkg/httpclient/foundation"
)

func main() {
	ctx := context.Background()

	// 示例1: HTTP/2 优化配置
	fmt.Println("=== HTTP/2 示例 ===")
	http2Client := httpclient.NewAdvancedClient(httpclient.WithHTTP2())

	resp, err := http2Client.Get(ctx, "https://httpbin.org/get", nil)
	if err != nil {
		log.Printf("HTTP/2 请求失败: %v", err)
	} else {
		fmt.Printf("HTTP/2 响应状态: %d, 响应时间: %v\n", resp.StatusCode, resp.ResponseTime)
	}
	http2Client.Close()

	// 示例2: HTTP 代理配置
	fmt.Println("\n=== HTTP 代理示例 ===")
	proxyClient := httpclient.NewAdvancedClient(httpclient.WithHTTPProxy("http://proxy.example.com:8080"))

	// 注意：这个示例可能会失败，因为代理地址是示例地址
	resp, err = proxyClient.Get(ctx, "https://httpbin.org/ip", nil)
	if err != nil {
		log.Printf("代理请求失败（预期的，因为使用了示例代理地址）: %v", err)
	} else {
		fmt.Printf("代理响应状态: %d\n", resp.StatusCode)
	}
	proxyClient.Close()

	// 示例3: SOCKS5 代理配置
	fmt.Println("\n=== SOCKS5 代理示例 ===")
	socksClient := httpclient.NewAdvancedClient(httpclient.WithSOCKS5("socks5://127.0.0.1:1080", "", ""))

	// 注意：这个示例可能会失败，因为本地可能没有SOCKS5代理
	resp, err = socksClient.Get(ctx, "https://httpbin.org/ip", nil)
	if err != nil {
		log.Printf("SOCKS5请求失败（预期的，因为本地可能没有SOCKS5代理）: %v", err)
	} else {
		fmt.Printf("SOCKS5响应状态: %d\n", resp.StatusCode)
	}
	socksClient.Close()

	// 示例4: 手动配置
	fmt.Println("\n=== 手动配置示例 ===")
	customConfig := &httpclient.Config{
		Timeout:         10 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Custom-HTTP-Client/1.0",

		EnableMonitoring:  true,
		EnableFingerprint: false,
		EnableRetry:       true,

		MaxRetries:   2,
		RetryDelay:   500 * time.Millisecond,
		RetryBackoff: 1.5,

		// HTTP/2 配置
		HTTPVersion:     foundation.HTTPVersionHTTP2Preferred,
		EnableHTTP2Push: true,
		HTTP2Settings: map[string]uint32{
			"MAX_CONCURRENT_STREAMS": 50,
			"INITIAL_WINDOW_SIZE":    32768,
		},

		// 代理配置（无代理）
		ProxyType: foundation.ProxyTypeNone,
		ProxyURL:  "",
		ProxyAuth: nil,
	}

	customClient := httpclient.NewAdvancedClient(customConfig)

	resp, err = customClient.Get(ctx, "https://httpbin.org/user-agent", nil)
	if err != nil {
		log.Printf("自定义配置请求失败: %v", err)
	} else {
		fmt.Printf("自定义配置响应状态: %d, 响应时间: %v\n", resp.StatusCode, resp.ResponseTime)
		fmt.Printf("响应体长度: %d bytes\n", len(resp.Body))
	}
	customClient.Close()

	// 示例5: 便捷函数使用
	fmt.Println("\n=== 便捷函数示例 ===")

	// 使用便捷函数发起请求（无需创建客户端）
	resp, err = httpclient.Get(ctx, "https://httpbin.org/get", nil)
	if err != nil {
		log.Printf("便捷函数请求失败: %v", err)
	} else {
		fmt.Printf("便捷函数响应状态: %d, 响应时间: %v\n", resp.StatusCode, resp.ResponseTime)
	}

	fmt.Println("\n=== 所有示例完成 ===")
}
