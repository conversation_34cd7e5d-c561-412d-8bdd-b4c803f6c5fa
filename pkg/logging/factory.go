package logging

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"go-monitor/internal/config"
	"go-monitor/pkg/logging/handlers"
)

// Factory 现代化日志器工厂
type Factory struct {
	config  *Config
	loggers map[string]Logger
	mu      sync.RWMutex

	// 处理器缓存
	consoleHandler slog.Handler
	fileHandler    slog.Handler
	spiderHandlers map[string]slog.Handler
}

// NewFactory 创建日志器工厂
func NewFactory(cfg *Config) (*Factory, error) {
	if cfg == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	if err := cfg.EnsureLogDir(); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %w", err)
	}

	factory := &Factory{
		config:         cfg,
		loggers:        make(map[string]Logger),
		spiderHandlers: make(map[string]slog.Handler),
	}

	// 初始化处理器
	if err := factory.initHandlers(); err != nil {
		return nil, fmt.Errorf("初始化处理器失败: %w", err)
	}

	return factory, nil
}

// NewFactoryFromConfig 从配置文件创建工厂
func NewFactoryFromConfig(loggingConfig *config.LoggingConfig) (*Factory, error) {
	adapter := NewConfigAdapter()
	cfg, err := adapter.Convert(loggingConfig)
	if err != nil {
		return nil, fmt.Errorf("转换配置失败: %w", err)
	}

	return NewFactory(cfg)
}

// initHandlers 初始化处理器
func (f *Factory) initHandlers() error {
	// 初始化控制台处理器
	if f.config.Console.Enabled {
		var handler slog.Handler
		if f.config.Console.Format == "json" {
			handler = slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
				Level: f.config.Console.Level,
			})
		} else if f.config.Console.Format == "space" {
			handler = handlers.NewSpaceTextHandler(os.Stdout, &slog.HandlerOptions{
				Level: f.config.Console.Level,
			})
		} else {
			handler = slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
				Level: f.config.Console.Level,
			})
		}
		f.consoleHandler = handler
	}

	// 初始化文件处理器
	if f.config.File.Enabled {
		handler, err := f.createFileHandler(f.config.File.Path, f.config.File.Format, f.config.File.Level)
		if err != nil {
			return fmt.Errorf("创建文件处理器失败: %w", err)
		}
		f.fileHandler = handler
	}

	return nil
}

// createFileHandler 创建文件处理器（支持轮转）
func (f *Factory) createFileHandler(path, format string, level slog.Level) (slog.Handler, error) {
	// 确保目录存在
	dir := filepath.Dir(path)
	if err := ensureDir(dir); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	// 检查是否启用轮转
	if f.config.File.Rotation.Enabled {
		// 使用轮转文件处理器
		return handlers.NewRotatingFileHandler(
			path,
			f.config.File.Rotation.MaxSize,
			f.config.File.Rotation.MaxBackups,
			f.config.File.Rotation.MaxAge,
			f.config.File.Rotation.Compress,
			format,
			level,
		)
	}

	// 使用普通文件处理器
	file, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}

	// 创建处理器
	var handler slog.Handler
	if format == "json" {
		handler = slog.NewJSONHandler(file, &slog.HandlerOptions{
			Level: level,
		})
	} else if format == "space" {
		// 使用自定义的空格分隔处理器
		handler = handlers.NewSpaceTextHandler(file, &slog.HandlerOptions{
			Level: level,
		})
	} else {
		// 默认使用标准文本处理器
		handler = slog.NewTextHandler(file, &slog.HandlerOptions{
			Level: level,
		})
	}

	return handler, nil
}

// GetLogger 获取或创建日志器
func (f *Factory) GetLogger(name string) Logger {
	f.mu.Lock()
	defer f.mu.Unlock()

	// 如果已存在，直接返回
	if logger, exists := f.loggers[name]; exists {
		return logger
	}

	// 创建新的日志器
	logger := f.createLogger(name)
	f.loggers[name] = logger

	return logger
}

// GetSpiderLogger 获取爬虫日志器
func (f *Factory) GetSpiderLogger(spiderType string) Logger {
	if !f.config.Components.Spider.SeparateFiles {
		// 不分文件时，使用普通日志器
		return f.GetLogger(fmt.Sprintf("spider.%s", spiderType))
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	loggerName := fmt.Sprintf("spider.%s", spiderType)

	// 如果已存在，直接返回
	if logger, exists := f.loggers[loggerName]; exists {
		return logger
	}

	// 创建爬虫特定的处理器
	handler, err := f.getSpiderHandler(spiderType)
	if err != nil {
		// 如果创建失败，回退到默认处理器
		logger := f.createLogger(loggerName)
		f.loggers[loggerName] = logger
		return logger
	}

	// 创建带有特定处理器的日志器
	logger := NewLogger(loggerName, handler)
	logger.SetLevel(f.config.Components.Spider.Level)

	f.loggers[loggerName] = logger
	return logger
}

// getSpiderHandler 获取爬虫处理器
func (f *Factory) getSpiderHandler(spiderType string) (slog.Handler, error) {
	// 检查缓存
	if handler, exists := f.spiderHandlers[spiderType]; exists {
		return handler, nil
	}

	// 创建多输出处理器（控制台+文件）
	var handlers []slog.Handler

	// 添加控制台处理器（如果启用）
	if f.consoleHandler != nil {
		handlers = append(handlers, f.consoleHandler)
	}

	// 创建spider专用文件处理器
	path := f.config.GetSpiderLogPath(spiderType)
	fileHandler, err := f.createFileHandler(path, f.config.File.Format, f.config.Components.Spider.Level)
	if err != nil {
		return nil, err
	}
	handlers = append(handlers, fileHandler)

	// 创建最终处理器
	var handler slog.Handler
	if len(handlers) == 1 {
		handler = handlers[0]
	} else {
		handler = NewMultiHandler(handlers...)
	}

	// 缓存处理器
	f.spiderHandlers[spiderType] = handler
	return handler, nil
}

// createLogger 创建日志器
func (f *Factory) createLogger(name string) Logger {
	// 确定使用的处理器
	var handler slog.Handler

	// 创建多输出处理器
	var handlers []slog.Handler

	if f.consoleHandler != nil {
		handlers = append(handlers, f.consoleHandler)
	}

	if f.fileHandler != nil {
		handlers = append(handlers, f.fileHandler)
	}

	if len(handlers) == 0 {
		// 如果没有配置任何处理器，使用默认控制台处理器
		handler = slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		})
	} else if len(handlers) == 1 {
		handler = handlers[0]
	} else {
		handler = NewMultiHandler(handlers...)
	}

	// 包装在平台路由处理器中
	handler = NewPlatformRoutingHandler(handler, f)

	// 创建日志器
	logger := NewLogger(name, handler)

	// 设置组件特定的级别
	componentType := extractComponentType(name)
	level := f.config.GetComponentLevel(componentType)
	logger.SetLevel(level)

	return logger
}

// extractComponentType 从名称中提取组件类型
func extractComponentType(name string) string {
	parts := strings.Split(name, ".")
	if len(parts) > 0 {
		return parts[0]
	}
	return "general"
}

// UpdateConfig 更新配置
func (f *Factory) UpdateConfig(cfg *Config) error {
	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	if err := cfg.Validate(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	// 更新配置
	f.config = cfg

	// 重新初始化处理器
	if err := f.initHandlers(); err != nil {
		return fmt.Errorf("重新初始化处理器失败: %w", err)
	}

	// 更新现有日志器的级别
	for name, logger := range f.loggers {
		componentType := extractComponentType(name)
		level := f.config.GetComponentLevel(componentType)
		logger.SetLevel(level)
	}

	return nil
}

// Close 关闭工厂
func (f *Factory) Close() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	// 清理资源
	f.loggers = make(map[string]Logger)
	f.spiderHandlers = make(map[string]slog.Handler)

	return nil
}

// 全局工厂实例
var globalFactory *Factory

// SetGlobalFactory 设置全局日志工厂（应用启动时调用）
func SetGlobalFactory(factory *Factory) {
	globalFactory = factory
}

// GetLogger 统一的全局日志器获取函数
func GetLogger(name string) Logger {
	if globalFactory != nil {
		return globalFactory.GetLogger(name)
	}
	return &ModernLogger{} // 默认回退
}

// ensureDir 确保目录存在
func ensureDir(dir string) error {
	if dir == "" || dir == "." {
		return nil
	}
	return os.MkdirAll(dir, 0755)
}

// MultiHandler 多输出处理器
type MultiHandler struct {
	handlers []slog.Handler
}

// NewMultiHandler 创建多输出处理器
func NewMultiHandler(handlers ...slog.Handler) *MultiHandler {
	return &MultiHandler{
		handlers: handlers,
	}
}

// Enabled 检查是否启用指定级别
func (h *MultiHandler) Enabled(ctx context.Context, level slog.Level) bool {
	// 只要有一个处理器启用，就返回true
	for _, handler := range h.handlers {
		if handler.Enabled(ctx, level) {
			return true
		}
	}
	return false
}

// Handle 处理日志记录
func (h *MultiHandler) Handle(ctx context.Context, record slog.Record) error {
	// 向所有启用的处理器发送日志记录
	for _, handler := range h.handlers {
		if handler.Enabled(ctx, record.Level) {
			if err := handler.Handle(ctx, record); err != nil {
				// 记录错误但继续处理其他处理器
				// 这里可以考虑添加错误处理逻辑
				continue
			}
		}
	}
	return nil
}

// WithAttrs 添加属性
func (h *MultiHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	newHandlers := make([]slog.Handler, len(h.handlers))
	for i, handler := range h.handlers {
		newHandlers[i] = handler.WithAttrs(attrs)
	}
	return &MultiHandler{handlers: newHandlers}
}

// WithGroup 添加组
func (h *MultiHandler) WithGroup(name string) slog.Handler {
	newHandlers := make([]slog.Handler, len(h.handlers))
	for i, handler := range h.handlers {
		newHandlers[i] = handler.WithGroup(name)
	}
	return &MultiHandler{handlers: newHandlers}
}

// PlatformRoutingHandler 根据platform字段路由日志的处理器
type PlatformRoutingHandler struct {
	defaultHandler slog.Handler
	spiderHandlers map[string]slog.Handler
	factory        *Factory
	mu             sync.RWMutex
}

// NewPlatformRoutingHandler 创建平台路由处理器
func NewPlatformRoutingHandler(defaultHandler slog.Handler, factory *Factory) *PlatformRoutingHandler {
	return &PlatformRoutingHandler{
		defaultHandler: defaultHandler,
		spiderHandlers: make(map[string]slog.Handler),
		factory:        factory,
	}
}

// Enabled 检查是否启用指定级别
func (h *PlatformRoutingHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.defaultHandler.Enabled(ctx, level)
}

// Handle 处理日志记录
func (h *PlatformRoutingHandler) Handle(ctx context.Context, record slog.Record) error {
	// 提取platform字段
	var platform string
	record.Attrs(func(attr slog.Attr) bool {
		if attr.Key == "platform" {
			platform = attr.Value.String()
			return false // 找到了，停止遍历
		}
		return true
	})

	// 根据platform选择处理器
	var handler slog.Handler
	var finalRecord slog.Record

	if platform != "" && platform != "monitor" {
		// 直接根据platform参数路由到spider处理器
		handler = h.getOrCreateSpiderHandler(platform)
		// 为spider平台创建不包含platform字段的新记录
		finalRecord = h.createRecordWithoutPlatform(record)
	} else {
		handler = h.defaultHandler
		finalRecord = record
	}

	return handler.Handle(ctx, finalRecord)
}

// createRecordWithoutPlatform 创建不包含platform字段的新记录
func (h *PlatformRoutingHandler) createRecordWithoutPlatform(original slog.Record) slog.Record {
	// 创建新的记录，复制基本信息
	newRecord := slog.NewRecord(original.Time, original.Level, original.Message, original.PC)

	// 复制所有属性，除了platform字段
	original.Attrs(func(attr slog.Attr) bool {
		if attr.Key != "platform" {
			newRecord.AddAttrs(attr)
		}
		return true
	})

	return newRecord
}

// getOrCreateSpiderHandler 获取或创建spider处理器
func (h *PlatformRoutingHandler) getOrCreateSpiderHandler(platform string) slog.Handler {
	// 先尝试读锁
	h.mu.RLock()
	if handler, exists := h.spiderHandlers[platform]; exists {
		h.mu.RUnlock()
		return handler
	}
	h.mu.RUnlock()

	// 需要创建，使用写锁
	h.mu.Lock()
	defer h.mu.Unlock()

	// 双重检查，防止在获取写锁期间其他goroutine已经创建
	if handler, exists := h.spiderHandlers[platform]; exists {
		return handler
	}

	// 创建spider处理器
	if handler, err := h.factory.getSpiderHandler(platform); err == nil {
		h.spiderHandlers[platform] = handler
		return handler
	}

	// 创建失败，使用默认处理器
	return h.defaultHandler
}

// WithAttrs 添加属性
func (h *PlatformRoutingHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &PlatformRoutingHandler{
		defaultHandler: h.defaultHandler.WithAttrs(attrs),
		spiderHandlers: h.spiderHandlers, // 共享spider处理器
		factory:        h.factory,
	}
}

// WithGroup 添加组
func (h *PlatformRoutingHandler) WithGroup(name string) slog.Handler {
	return &PlatformRoutingHandler{
		defaultHandler: h.defaultHandler.WithGroup(name),
		spiderHandlers: h.spiderHandlers, // 共享spider处理器
		factory:        h.factory,
	}
}
