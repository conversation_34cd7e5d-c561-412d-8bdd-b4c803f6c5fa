package logging

import (
	"fmt"
	"go-monitor/internal/config"
	"log/slog"
	"path/filepath"
	"strings"
	"time"
	// "go-monitor/internal/config"
)

// Config 现代化日志配置
type Config struct {
	// 全局配置
	Level  slog.Level
	Format string

	// 输出配置
	Console ConsoleConfig
	File    FileConfig

	// 组件配置
	Components ComponentsConfig

	// 性能配置
	Performance PerformanceConfig
}

// ConsoleConfig 控制台配置
type ConsoleConfig struct {
	Enabled bool
	Format  string
	Level   slog.Level
}

// FileConfig 文件配置
type FileConfig struct {
	Enabled  bool
	Path     string
	Format   string
	Level    slog.Level
	Rotation RotationConfig
}

// RotationConfig 文件轮转配置
type RotationConfig struct {
	MaxSize    int // MB
	MaxBackups int
	MaxAge     int // days
	Compress   bool
	Enabled    bool
}

// ComponentsConfig 组件配置
type ComponentsConfig struct {
	Spider     SpiderConfig
	Service    ComponentConfig
	Middleware ComponentConfig
	Pipeline   ComponentConfig
}

// SpiderConfig 爬虫配置
type SpiderConfig struct {
	Level         slog.Level
	SeparateFiles bool
	FilePattern   string
}

// ComponentConfig 通用组件配置
type ComponentConfig struct {
	Level slog.Level
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	AsyncWrite    bool
	BufferSize    int
	FlushInterval time.Duration
}

// ConfigAdapter 配置适配器 - 将internal/config转换为logging/config
type ConfigAdapter struct{}

// NewConfigAdapter 创建配置适配器
func NewConfigAdapter() *ConfigAdapter {
	return &ConfigAdapter{}
}

// Convert 转换配置
func (a *ConfigAdapter) Convert(loggingConfig *config.LoggingConfig) (*Config, error) {
	if loggingConfig == nil {
		return a.DefaultConfig(), nil
	}

	cfg := &Config{
		Level:  ParseLevel(loggingConfig.Level),
		Format: loggingConfig.Format,
	}

	// 转换控制台配置
	cfg.Console = ConsoleConfig{
		Enabled: loggingConfig.Outputs.Console.Enabled,
		Format:  loggingConfig.Outputs.Console.Format,
		Level:   ParseLevel(loggingConfig.Outputs.Console.Level),
	}

	// 转换文件配置
	cfg.File = FileConfig{
		Enabled: loggingConfig.Outputs.File.Enabled,
		Path:    loggingConfig.Outputs.File.Path,
		Format:  loggingConfig.Outputs.File.Format,
		Level:   ParseLevel(loggingConfig.Outputs.File.Level),
		Rotation: RotationConfig{
			MaxSize:    loggingConfig.Outputs.File.Rotation.MaxSize,
			MaxBackups: loggingConfig.Outputs.File.Rotation.MaxBackups,
			MaxAge:     loggingConfig.Outputs.File.Rotation.MaxAge,
			Compress:   loggingConfig.Outputs.File.Rotation.Compress,
			Enabled:    loggingConfig.Outputs.File.Rotation.Enabled,
		},
	}

	// 转换组件配置
	cfg.Components = ComponentsConfig{
		Spider: SpiderConfig{
			Level:         ParseLevel(loggingConfig.Components.Spider.Level),
			SeparateFiles: loggingConfig.Components.Spider.SeparateFiles,
			FilePattern:   loggingConfig.Components.Spider.FilePattern,
		},
		Service: ComponentConfig{
			Level: ParseLevel(loggingConfig.Components.Service.Level),
		},
		Middleware: ComponentConfig{
			Level: ParseLevel(loggingConfig.Components.Middleware.Level),
		},
		Pipeline: ComponentConfig{
			Level: ParseLevel(loggingConfig.Components.Pipeline.Level),
		},
	}

	// 转换性能配置
	flushInterval, err := time.ParseDuration(loggingConfig.Performance.FlushInterval)
	if err != nil {
		flushInterval = time.Second // 默认1秒
	}

	cfg.Performance = PerformanceConfig{
		AsyncWrite:    loggingConfig.Performance.AsyncWrite,
		BufferSize:    loggingConfig.Performance.BufferSize,
		FlushInterval: flushInterval,
	}

	return cfg, nil
}

// DefaultConfig 创建默认配置
func (a *ConfigAdapter) DefaultConfig() *Config {
	return &Config{
		Level:  slog.LevelInfo,
		Format: "json",
		Console: ConsoleConfig{
			Enabled: true,
			Format:  "text",
			Level:   slog.LevelInfo,
		},
		File: FileConfig{
			Enabled: true,
			Path:    "logs/monitor.log",
			Format:  "json",
			Level:   slog.LevelInfo,
			Rotation: RotationConfig{
				MaxSize:    100,
				MaxBackups: 7,
				MaxAge:     30,
				Compress:   true,
				Enabled:    true,
			},
		},
		Components: ComponentsConfig{
			Spider: SpiderConfig{
				Level:         slog.LevelInfo,
				SeparateFiles: true,
				FilePattern:   "logs/spider_{type}.log",
			},
			Service: ComponentConfig{
				Level: slog.LevelInfo,
			},
			Middleware: ComponentConfig{
				Level: slog.LevelWarn,
			},
			Pipeline: ComponentConfig{
				Level: slog.LevelWarn,
			},
		},
		Performance: PerformanceConfig{
			AsyncWrite:    true,
			BufferSize:    1024,
			FlushInterval: time.Second,
		},
	}
}

// Validate 验证配置
func (cfg *Config) Validate() error {
	// 验证格式
	if cfg.Format != "json" && cfg.Format != "text" && cfg.Format != "space" {
		return fmt.Errorf("无效的日志格式: %s", cfg.Format)
	}

	// 验证控制台配置
	if cfg.Console.Format != "json" && cfg.Console.Format != "text" && cfg.Console.Format != "space" {
		return fmt.Errorf("无效的控制台日志格式: %s", cfg.Console.Format)
	}

	// 验证文件配置
	if cfg.File.Enabled {
		if cfg.File.Path == "" {
			return fmt.Errorf("文件输出启用时必须指定文件路径")
		}

		if cfg.File.Format != "json" && cfg.File.Format != "text" && cfg.File.Format != "space" {
			return fmt.Errorf("无效的文件日志格式: %s", cfg.File.Format)
		}

		// 验证轮转配置
		if cfg.File.Rotation.Enabled {
			if cfg.File.Rotation.MaxSize <= 0 {
				return fmt.Errorf("文件轮转启用时最大文件大小必须大于0")
			}
		}
	}

	// 验证爬虫配置
	if cfg.Components.Spider.SeparateFiles && cfg.Components.Spider.FilePattern == "" {
		return fmt.Errorf("爬虫分文件启用时必须指定文件模式")
	}

	// 验证性能配置
	if cfg.Performance.BufferSize <= 0 {
		return fmt.Errorf("缓冲区大小必须大于0")
	}

	if cfg.Performance.FlushInterval <= 0 {
		return fmt.Errorf("刷新间隔必须大于0")
	}

	return nil
}

// GetSpiderLogPath 获取爬虫日志文件路径
func (cfg *Config) GetSpiderLogPath(spiderType string) string {
	if !cfg.Components.Spider.SeparateFiles {
		return cfg.File.Path
	}

	pattern := cfg.Components.Spider.FilePattern
	if pattern == "" {
		pattern = "logs/spider_{type}.log"
	}

	return strings.ReplaceAll(pattern, "{type}", spiderType)
}

// GetComponentLevel 获取组件日志级别
func (cfg *Config) GetComponentLevel(component string) slog.Level {
	switch component {
	case "spider":
		return cfg.Components.Spider.Level
	case "service":
		return cfg.Components.Service.Level
	case "middleware":
		return cfg.Components.Middleware.Level
	case "pipeline":
		return cfg.Components.Pipeline.Level
	default:
		return cfg.Level
	}
}

// EnsureLogDir 确保日志目录存在
func (cfg *Config) EnsureLogDir() error {
	if cfg.File.Enabled && cfg.File.Path != "" {
		dir := filepath.Dir(cfg.File.Path)
		if err := ensureDir(dir); err != nil {
			return fmt.Errorf("创建日志目录失败: %w", err)
		}
	}

	// 确保爬虫日志目录存在
	if cfg.Components.Spider.SeparateFiles && cfg.Components.Spider.FilePattern != "" {
		dir := filepath.Dir(cfg.Components.Spider.FilePattern)
		if err := ensureDir(dir); err != nil {
			return fmt.Errorf("创建爬虫日志目录失败: %w", err)
		}
	}

	return nil
}
