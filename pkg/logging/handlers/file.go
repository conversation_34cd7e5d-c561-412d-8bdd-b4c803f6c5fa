package handlers

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"sync"

	"gopkg.in/natefinch/lumberjack.v2"
)

// FileHandler 文件处理器 - 支持日志轮转
type FileHandler struct {
	handler slog.Handler
	writer  io.WriteCloser
	mu      sync.Mutex
}

// FileConfig 文件处理器配置
type FileConfig struct {
	Path       string
	MaxSize    int // MB
	MaxBackups int
	MaxAge     int // days
	Compress   bool
	Rotate     bool
	Format     string // "json" 或 "text"
	Level      slog.Level
}

// NewFileHandler 创建文件处理器
func NewFileHandler(config *FileConfig) (*FileHandler, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	// 确保目录存在
	dir := filepath.Dir(config.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	var writer io.WriteCloser

	if config.Rotate {
		// 使用 lumberjack 进行日志轮转
		writer = &lumberjack.Logger{
			Filename:   config.Path,
			MaxSize:    config.MaxSize,
			MaxBackups: config.MaxBackups,
			MaxAge:     config.MaxAge,
			Compress:   config.Compress,
		}
	} else {
		// 使用普通文件
		file, err := os.OpenFile(config.Path, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			return nil, fmt.Errorf("打开文件失败: %w", err)
		}
		writer = file
	}

	// 创建 slog 处理器
	var handler slog.Handler
	handlerOptions := &slog.HandlerOptions{
		Level: config.Level,
	}

	if config.Format == "json" {
		handler = slog.NewJSONHandler(writer, handlerOptions)
	} else {
		handler = slog.NewTextHandler(writer, handlerOptions)
	}

	return &FileHandler{
		handler: handler,
		writer:  writer,
	}, nil
}

// Enabled 检查是否启用指定级别
func (h *FileHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.handler.Enabled(ctx, level)
}

// Handle 处理日志记录
func (h *FileHandler) Handle(ctx context.Context, record slog.Record) error {
	return h.handler.Handle(ctx, record)
}

// WithAttrs 添加属性
func (h *FileHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &FileHandler{
		handler: h.handler.WithAttrs(attrs),
		writer:  h.writer,
	}
}

// WithGroup 添加组
func (h *FileHandler) WithGroup(name string) slog.Handler {
	return &FileHandler{
		handler: h.handler.WithGroup(name),
		writer:  h.writer,
	}
}

// Close 关闭处理器
func (h *FileHandler) Close() error {
	h.mu.Lock()
	defer h.mu.Unlock()

	if h.writer != nil {
		return h.writer.Close()
	}
	return nil
}

// Sync 同步缓冲区
func (h *FileHandler) Sync() error {
	h.mu.Lock()
	defer h.mu.Unlock()

	// 如果是 lumberjack.Logger，调用其 Rotate 方法来强制同步
	if lj, ok := h.writer.(*lumberjack.Logger); ok {
		// lumberjack 没有直接的 Sync 方法，但我们可以通过 Rotate 来强制刷新
		_ = lj.Rotate()
		return nil
	}

	// 如果是普通文件，尝试同步
	if file, ok := h.writer.(*os.File); ok {
		return file.Sync()
	}

	return nil
}

// RotatingFileHandler 轮转文件处理器 - 简化版本
type RotatingFileHandler struct {
	*FileHandler
	lumberjack *lumberjack.Logger
}

// NewRotatingFileHandler 创建轮转文件处理器
func NewRotatingFileHandler(path string, maxSize, maxBackups, maxAge int, compress bool, format string, level slog.Level) (*RotatingFileHandler, error) {
	// 确保目录存在
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	// 创建 lumberjack logger
	lj := &lumberjack.Logger{
		Filename:   path,
		MaxSize:    maxSize,
		MaxBackups: maxBackups,
		MaxAge:     maxAge,
		Compress:   compress,
	}

	// 创建 slog 处理器
	var handler slog.Handler
	handlerOptions := &slog.HandlerOptions{
		Level: level,
	}

	if format == "json" {
		handler = slog.NewJSONHandler(lj, handlerOptions)
	} else if format == "space" {
		// 使用自定义的空格分隔处理器
		handler = NewSpaceTextHandler(lj, handlerOptions)
	} else {
		handler = slog.NewTextHandler(lj, handlerOptions)
	}

	fileHandler := &FileHandler{
		handler: handler,
		writer:  lj,
	}

	return &RotatingFileHandler{
		FileHandler: fileHandler,
		lumberjack:  lj,
	}, nil
}

// Rotate 手动轮转日志文件
func (h *RotatingFileHandler) Rotate() error {
	if h.lumberjack != nil {
		return h.lumberjack.Rotate()
	}
	return nil
}

// GetCurrentFilename 获取当前日志文件名
func (h *RotatingFileHandler) GetCurrentFilename() string {
	if h.lumberjack != nil {
		return h.lumberjack.Filename
	}
	return ""
}

// AsyncFileHandler 异步文件处理器
type AsyncFileHandler struct {
	handler    slog.Handler
	writer     io.WriteCloser
	bufferChan chan slog.Record
	done       chan struct{}
	wg         sync.WaitGroup
	mu         sync.Mutex
	closed     bool
}

// NewAsyncFileHandler 创建异步文件处理器
func NewAsyncFileHandler(config *FileConfig, bufferSize int) (*AsyncFileHandler, error) {
	// 创建同步文件处理器
	syncHandler, err := NewFileHandler(config)
	if err != nil {
		return nil, err
	}

	handler := &AsyncFileHandler{
		handler:    syncHandler.handler,
		writer:     syncHandler.writer,
		bufferChan: make(chan slog.Record, bufferSize),
		done:       make(chan struct{}),
	}

	// 启动后台写入协程
	handler.wg.Add(1)
	go handler.writeLoop()

	return handler, nil
}

// writeLoop 后台写入循环
func (h *AsyncFileHandler) writeLoop() {
	defer h.wg.Done()

	for {
		select {
		case record := <-h.bufferChan:
			// 写入日志记录
			_ = h.handler.Handle(context.Background(), record)
		case <-h.done:
			// 处理剩余的日志记录
			for {
				select {
				case record := <-h.bufferChan:
					_ = h.handler.Handle(context.Background(), record)
				default:
					return
				}
			}
		}
	}
}

// Enabled 检查是否启用指定级别
func (h *AsyncFileHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.handler.Enabled(ctx, level)
}

// Handle 处理日志记录（异步）
func (h *AsyncFileHandler) Handle(ctx context.Context, record slog.Record) error {
	h.mu.Lock()
	defer h.mu.Unlock()

	if h.closed {
		return fmt.Errorf("处理器已关闭")
	}

	select {
	case h.bufferChan <- record:
		return nil
	default:
		// 缓冲区满时，直接写入（避免阻塞）
		return h.handler.Handle(ctx, record)
	}
}

// WithAttrs 添加属性
func (h *AsyncFileHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &AsyncFileHandler{
		handler:    h.handler.WithAttrs(attrs),
		writer:     h.writer,
		bufferChan: h.bufferChan,
		done:       h.done,
	}
}

// WithGroup 添加组
func (h *AsyncFileHandler) WithGroup(name string) slog.Handler {
	return &AsyncFileHandler{
		handler:    h.handler.WithGroup(name),
		writer:     h.writer,
		bufferChan: h.bufferChan,
		done:       h.done,
	}
}

// Close 关闭异步处理器
func (h *AsyncFileHandler) Close() error {
	h.mu.Lock()
	defer h.mu.Unlock()

	if h.closed {
		return nil
	}

	h.closed = true
	close(h.done)
	h.wg.Wait()

	if h.writer != nil {
		return h.writer.Close()
	}

	return nil
}
