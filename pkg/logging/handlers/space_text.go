package handlers

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"strings"
	"time"
)

// SpaceTextHandler 空格分隔的文本处理器
type SpaceTextHandler struct {
	writer io.Writer
	level  *slog.LevelVar
	attrs  []slog.Attr
	groups []string
}

// NewSpaceTextHandler 创建空格分隔文本处理器
func NewSpaceTextHandler(w io.Writer, opts *slog.HandlerOptions) *SpaceTextHandler {
	if opts == nil {
		opts = &slog.HandlerOptions{}
	}

	levelVar := &slog.LevelVar{}
	if opts.Level != nil {
		levelVar.Set(opts.Level.Level())
	} else {
		levelVar.Set(slog.LevelInfo)
	}

	return &SpaceTextHandler{
		writer: w,
		level:  levelVar,
		attrs:  make([]slog.Attr, 0),
		groups: make([]string, 0),
	}
}

// Enabled 检查是否启用指定级别
func (h *SpaceTextHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return level >= h.level.Level()
}

// Handle 处理日志记录
func (h *SpaceTextHandler) Handle(ctx context.Context, record slog.Record) error {
	if !h.Enabled(ctx, record.Level) {
		return nil
	}

	// 构建日志行
	var parts []string

	// 1. 时间戳 (简化格式)
	timeStr := record.Time.Format("2006-01-02T15:04:05.000")
	parts = append(parts, timeStr)

	// 2. 级别
	parts = append(parts, record.Level.String())

	// 3. 消息
	if record.Message != "" {
		parts = append(parts, record.Message)
	}

	// 4. 属性 (空格分隔的 key value 格式)
	attrs := make([]string, 0)

	// 添加处理器级别的属性
	for _, attr := range h.attrs {
		formatted := h.formatAttr(attr)
		if formatted != "" { // 过滤空字符串
			attrs = append(attrs, formatted)
		}
	}

	// 添加记录级别的属性
	record.Attrs(func(attr slog.Attr) bool {
		formatted := h.formatAttr(attr)
		if formatted != "" { // 过滤空字符串
			attrs = append(attrs, formatted)
		}
		return true
	})

	// 将属性添加到parts
	parts = append(parts, attrs...)

	// 组合并写入
	line := strings.Join(parts, " ") + "\n"
	_, err := h.writer.Write([]byte(line))
	return err
}

// formatAttr 格式化属性为空格分隔格式
func (h *SpaceTextHandler) formatAttr(attr slog.Attr) string {
	key := attr.Key
	value := attr.Value

	// 过滤掉 platform=monitor
	if key == "platform" && value.String() == "monitor" {
		return ""
	}

	// 转换 logger 名称为自然语言
	if key == "logger" {
		translatedName := h.translateLoggerName(value.String())
		if strings.Contains(translatedName, " ") {
			return fmt.Sprintf("模块 \"%s\"", translatedName)
		}
		return fmt.Sprintf("模块 %s", translatedName)
	}

	// 处理不同类型的值
	switch value.Kind() {
	case slog.KindString:
		// 字符串值，如果包含空格则用引号包围
		str := value.String()
		if strings.Contains(str, " ") {
			return fmt.Sprintf("%s \"%s\"", key, str)
		}
		return fmt.Sprintf("%s %s", key, str)
	case slog.KindInt64:
		return fmt.Sprintf("%s %d", key, value.Int64())
	case slog.KindFloat64:
		return fmt.Sprintf("%s %.3f", key, value.Float64())
	case slog.KindBool:
		return fmt.Sprintf("%s %t", key, value.Bool())
	case slog.KindTime:
		return fmt.Sprintf("%s %s", key, value.Time().Format(time.RFC3339))
	case slog.KindDuration:
		return fmt.Sprintf("%s %s", key, value.Duration().String())
	default:
		// 其他类型转为字符串
		str := value.String()
		if strings.Contains(str, " ") {
			return fmt.Sprintf("%s \"%s\"", key, str)
		}
		return fmt.Sprintf("%s %s", key, str)
	}
}

// translateLoggerName 将技术名称转换为中文自然语言
func (h *SpaceTextHandler) translateLoggerName(name string) string {
	// 精确匹配映射表
	exactMappings := map[string]string{
		"main":                          "主程序",
		"scheduler":                     "调度器",
		"service.manager":               "服务管理器",
		"service.request":               "请求服务",
		"service.redis_manager":         "Redis管理器",
		"service.resource-manager":      "资源管理器",
		"service.resource-scheduler":    "资源调度器",
		"service.pipeline-manager":      "管道管理器",
		"service.pipeline-validation":   "验证管道",
		"service.pipeline-duplicates":   "去重管道",
		"service.pipeline-notification": "通知管道",
		"service.spider-manager":        "爬虫管理器",
		"spider.amazon":                 "Amazon爬虫",
		"spider.amazon-wishlist":        "Amazon心愿单爬虫",
		"spider.popmart":                "PopMart爬虫",
		"spider.aliexpress":             "AliExpress爬虫",
	}

	// 精确匹配
	if translated, exists := exactMappings[name]; exists {
		return translated
	}

	// 模式匹配
	if strings.HasPrefix(name, "amazon-") {
		return "Amazon处理器"
	}
	if strings.HasPrefix(name, "service.") {
		return "服务组件"
	}
	if strings.HasPrefix(name, "spider.") {
		return "爬虫组件"
	}
	if strings.HasPrefix(name, "pipeline.") {
		return "管道组件"
	}

	// 未匹配的保持原样
	return name
}

// WithAttrs 添加属性
func (h *SpaceTextHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	newAttrs := make([]slog.Attr, len(h.attrs)+len(attrs))
	copy(newAttrs, h.attrs)
	copy(newAttrs[len(h.attrs):], attrs)

	return &SpaceTextHandler{
		writer: h.writer,
		level:  h.level,
		attrs:  newAttrs,
		groups: h.groups,
	}
}

// WithGroup 添加组
func (h *SpaceTextHandler) WithGroup(name string) slog.Handler {
	newGroups := make([]string, len(h.groups)+1)
	copy(newGroups, h.groups)
	newGroups[len(h.groups)] = name

	return &SpaceTextHandler{
		writer: h.writer,
		level:  h.level,
		attrs:  h.attrs,
		groups: newGroups,
	}
}
