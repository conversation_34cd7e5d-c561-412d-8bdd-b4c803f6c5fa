package handlers

import (
	"context"
	"io"
	"log/slog"
	"os"
)

// ConsoleHandler 控制台处理器
type ConsoleHandler struct {
	handler slog.Handler
	writer  io.Writer
}

// ConsoleConfig 控制台处理器配置
type ConsoleConfig struct {
	Writer io.Writer  // 输出目标，默认为 os.Stdout
	Format string     // "json" 或 "text"
	Level  slog.Level // 日志级别
	Color  bool       // 是否启用颜色（仅对text格式有效）
}

// NewConsoleHandler 创建控制台处理器
func NewConsoleHandler(config *ConsoleConfig) *ConsoleHandler {
	if config == nil {
		config = &ConsoleConfig{
			Writer: os.Stdout,
			Format: "text",
			Level:  slog.LevelInfo,
			Color:  true,
		}
	}

	// 设置默认值
	if config.Writer == nil {
		config.Writer = os.Stdout
	}

	// 创建 slog 处理器
	var handler slog.Handler
	handlerOptions := &slog.HandlerOptions{
		Level: config.Level,
	}

	if config.Format == "json" {
		handler = slog.NewJSONHandler(config.Writer, handlerOptions)
	} else {
		// 对于文本格式，如果启用颜色且输出到终端，使用彩色处理器
		if config.Color && isTerminal(config.Writer) {
			handler = NewColorTextHandler(config.Writer, handlerOptions)
		} else {
			handler = slog.NewTextHandler(config.Writer, handlerOptions)
		}
	}

	return &ConsoleHandler{
		handler: handler,
		writer:  config.Writer,
	}
}

// Enabled 检查是否启用指定级别
func (h *ConsoleHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.handler.Enabled(ctx, level)
}

// Handle 处理日志记录
func (h *ConsoleHandler) Handle(ctx context.Context, record slog.Record) error {
	return h.handler.Handle(ctx, record)
}

// WithAttrs 添加属性
func (h *ConsoleHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &ConsoleHandler{
		handler: h.handler.WithAttrs(attrs),
		writer:  h.writer,
	}
}

// WithGroup 添加组
func (h *ConsoleHandler) WithGroup(name string) slog.Handler {
	return &ConsoleHandler{
		handler: h.handler.WithGroup(name),
		writer:  h.writer,
	}
}

// isTerminal 检查输出是否为终端
func isTerminal(w io.Writer) bool {
	switch w {
	case os.Stdout, os.Stderr:
		// 简单检查，实际项目中可以使用更复杂的终端检测
		return true
	default:
		return false
	}
}

// ColorTextHandler 彩色文本处理器
type ColorTextHandler struct {
	handler slog.Handler
}

// NewColorTextHandler 创建彩色文本处理器
func NewColorTextHandler(w io.Writer, opts *slog.HandlerOptions) *ColorTextHandler {
	// 这里可以实现自定义的彩色文本格式
	// 为了简化，我们先使用标准的文本处理器
	handler := slog.NewTextHandler(w, opts)
	
	return &ColorTextHandler{
		handler: handler,
	}
}

// Enabled 检查是否启用指定级别
func (h *ColorTextHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.handler.Enabled(ctx, level)
}

// Handle 处理日志记录
func (h *ColorTextHandler) Handle(ctx context.Context, record slog.Record) error {
	// TODO: 实现彩色输出
	// 这里可以添加ANSI颜色代码来美化输出
	return h.handler.Handle(ctx, record)
}

// WithAttrs 添加属性
func (h *ColorTextHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &ColorTextHandler{
		handler: h.handler.WithAttrs(attrs),
	}
}

// WithGroup 添加组
func (h *ColorTextHandler) WithGroup(name string) slog.Handler {
	return &ColorTextHandler{
		handler: h.handler.WithGroup(name),
	}
}

// StdoutHandler 标准输出处理器（便捷方法）
func StdoutHandler(format string, level slog.Level) slog.Handler {
	config := &ConsoleConfig{
		Writer: os.Stdout,
		Format: format,
		Level:  level,
		Color:  true,
	}
	return NewConsoleHandler(config)
}

// StderrHandler 标准错误处理器（便捷方法）
func StderrHandler(format string, level slog.Level) slog.Handler {
	config := &ConsoleConfig{
		Writer: os.Stderr,
		Format: format,
		Level:  level,
		Color:  true,
	}
	return NewConsoleHandler(config)
}

// DefaultConsoleHandler 默认控制台处理器
func DefaultConsoleHandler() slog.Handler {
	return StdoutHandler("text", slog.LevelInfo)
}

// JSONConsoleHandler JSON格式控制台处理器
func JSONConsoleHandler(level slog.Level) slog.Handler {
	return StdoutHandler("json", level)
}

// TextConsoleHandler 文本格式控制台处理器
func TextConsoleHandler(level slog.Level) slog.Handler {
	return StdoutHandler("text", level)
}
