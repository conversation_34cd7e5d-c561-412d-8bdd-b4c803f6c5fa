package logging

import (
	"context"
	"log/slog"
	"os"
	"sync"
)

// Logger 现代化日志接口 - 基于Go 1.21+ slog
type Logger interface {
	// 基础日志方法
	Debug(msg string, args ...any)
	Info(msg string, args ...any)
	Warn(msg string, args ...any)
	Error(msg string, args ...any)
	Fatal(msg string, args ...any)

	// 结构化日志方法
	DebugContext(ctx context.Context, msg string, args ...any)
	InfoContext(ctx context.Context, msg string, args ...any)
	WarnContext(ctx context.Context, msg string, args ...any)
	ErrorContext(ctx context.Context, msg string, args ...any)
	FatalContext(ctx context.Context, msg string, args ...any)

	// 日志器操作
	With(args ...any) Logger
	WithGroup(name string) Logger

	// 配置和管理
	SetLevel(level slog.Level)
	GetLevel() slog.Level
	GetName() string

	// 兼容性方法
	Named(name string) Logger
}

// ModernLogger 现代化日志器实现
type ModernLogger struct {
	name    string
	slogger *slog.Logger
	level   *slog.LevelVar
	mu      sync.RWMutex
}

// NewLogger 创建新的现代化日志器
func NewLogger(name string, handler slog.Handler) *ModernLogger {
	levelVar := &slog.LevelVar{}
	levelVar.Set(slog.LevelInfo) // 默认INFO级别

	// 创建带有级别控制的处理器
	levelHandler := &LevelHandler{
		Handler: handler,
		Level:   levelVar,
	}

	logger := &ModernLogger{
		name:    name,
		slogger: slog.New(levelHandler),
		level:   levelVar,
	}

	// 添加名称到所有日志记录
	if name != "" {
		logger.slogger = logger.slogger.With("logger", name)
	}

	return logger
}

// ensureInitialized 确保logger已初始化
func (l *ModernLogger) ensureInitialized() {
	if l.slogger == nil {
		// 创建默认的slog.Logger
		l.slogger = slog.Default()
	}
}

// Debug 记录调试级别日志
func (l *ModernLogger) Debug(msg string, args ...any) {
	l.ensureInitialized()
	// 检测最后的参数是否为 platform
	finalArgs := l.processPlatformArgs(args)
	l.slogger.Debug(msg, finalArgs...)
}

// Info 记录信息级别日志
func (l *ModernLogger) Info(msg string, args ...any) {
	l.ensureInitialized()
	// 检测最后的参数是否为 platform
	finalArgs := l.processPlatformArgs(args)
	l.slogger.Info(msg, finalArgs...)
}

// Warn 记录警告级别日志
func (l *ModernLogger) Warn(msg string, args ...any) {
	l.ensureInitialized()
	// 检测最后的参数是否为 platform
	finalArgs := l.processPlatformArgs(args)
	l.slogger.Warn(msg, finalArgs...)
}

// Error 记录错误级别日志
func (l *ModernLogger) Error(msg string, args ...any) {
	l.ensureInitialized()
	// 检测最后的参数是否为 platform
	finalArgs := l.processPlatformArgs(args)
	l.slogger.Error(msg, finalArgs...)
}

// Fatal 记录致命错误日志并退出程序
func (l *ModernLogger) Fatal(msg string, args ...any) {
	l.slogger.Error(msg, args...) // slog没有Fatal，使用Error
	os.Exit(1)
}

// DebugContext 记录带上下文的调试日志
func (l *ModernLogger) DebugContext(ctx context.Context, msg string, args ...any) {
	l.slogger.DebugContext(ctx, msg, args...)
}

// InfoContext 记录带上下文的信息日志
func (l *ModernLogger) InfoContext(ctx context.Context, msg string, args ...any) {
	l.slogger.InfoContext(ctx, msg, args...)
}

// WarnContext 记录带上下文的警告日志
func (l *ModernLogger) WarnContext(ctx context.Context, msg string, args ...any) {
	l.slogger.WarnContext(ctx, msg, args...)
}

// ErrorContext 记录带上下文的错误日志
func (l *ModernLogger) ErrorContext(ctx context.Context, msg string, args ...any) {
	l.slogger.ErrorContext(ctx, msg, args...)
}

// FatalContext 记录带上下文的致命错误日志并退出程序
func (l *ModernLogger) FatalContext(ctx context.Context, msg string, args ...any) {
	l.slogger.ErrorContext(ctx, msg, args...)
	os.Exit(1)
}

// With 添加字段到日志器
func (l *ModernLogger) With(args ...any) Logger {
	return &ModernLogger{
		name:    l.name,
		slogger: l.slogger.With(args...),
		level:   l.level,
	}
}

// WithGroup 添加组到日志器
func (l *ModernLogger) WithGroup(name string) Logger {
	return &ModernLogger{
		name:    l.name,
		slogger: l.slogger.WithGroup(name),
		level:   l.level,
	}
}

// SetLevel 设置日志级别
func (l *ModernLogger) SetLevel(level slog.Level) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.level.Set(level)
}

// GetLevel 获取当前日志级别
func (l *ModernLogger) GetLevel() slog.Level {
	l.mu.RLock()
	defer l.mu.RUnlock()
	return l.level.Level()
}

// GetName 获取日志器名称
func (l *ModernLogger) GetName() string {
	return l.name
}

// Named 创建命名的子日志器（兼容性方法）
func (l *ModernLogger) Named(name string) Logger {
	fullName := l.name
	if fullName != "" {
		fullName = fullName + "." + name
	} else {
		fullName = name
	}

	return &ModernLogger{
		name:    fullName,
		slogger: l.slogger.With("logger", fullName),
		level:   l.level,
	}
}

// processPlatformArgs 处理 platform 参数，检测最后的参数是否为 platform
func (l *ModernLogger) processPlatformArgs(args []any) []any {
	// 如果参数少于2个，无法构成 platform 参数对
	if len(args) < 2 {
		// 添加默认 platform 参数
		return append(args, "platform", "monitor")
	}

	// 检查最后两个参数是否为 "platform", "平台名"
	lastKey, lastKeyOk := args[len(args)-2].(string)
	lastValue, lastValueOk := args[len(args)-1].(string)

	if lastKeyOk && lastValueOk && lastKey == "platform" && lastValue != "" {
		// 已经有 platform 参数，直接返回
		return args
	}

	// 没有 platform 参数，添加默认值
	return append(args, "platform", "monitor")
}

// LevelHandler 级别控制处理器
type LevelHandler struct {
	Handler slog.Handler
	Level   *slog.LevelVar
}

// Enabled 检查是否启用指定级别
func (h *LevelHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return level >= h.Level.Level()
}

// Handle 处理日志记录
func (h *LevelHandler) Handle(ctx context.Context, record slog.Record) error {
	return h.Handler.Handle(ctx, record)
}

// WithAttrs 添加属性
func (h *LevelHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &LevelHandler{
		Handler: h.Handler.WithAttrs(attrs),
		Level:   h.Level,
	}
}

// WithGroup 添加组
func (h *LevelHandler) WithGroup(name string) slog.Handler {
	return &LevelHandler{
		Handler: h.Handler.WithGroup(name),
		Level:   h.Level,
	}
}

// ParseLevel 解析字符串级别为slog.Level
func ParseLevel(levelStr string) slog.Level {
	switch levelStr {
	case "DEBUG":
		return slog.LevelDebug
	case "INFO":
		return slog.LevelInfo
	case "WARN", "WARNING":
		return slog.LevelWarn
	case "ERROR":
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

// LevelToString 将slog.Level转换为字符串
func LevelToString(level slog.Level) string {
	switch level {
	case slog.LevelDebug:
		return "DEBUG"
	case slog.LevelInfo:
		return "INFO"
	case slog.LevelWarn:
		return "WARN"
	case slog.LevelError:
		return "ERROR"
	default:
		return "INFO"
	}
}
